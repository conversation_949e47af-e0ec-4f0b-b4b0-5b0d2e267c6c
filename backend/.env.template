DB_NAME=black_p_test
JWT_SECRET=YourSecretHere
BP_FrontEndURL=http://localhost:5173
Environment=dev
SENDGRID_API=your_api
MONGODB_URI="mongodb+srv://localhost:27017/black_p_test"
AWS_SECRET_ACCESS_KEY=.........
AWS_ACCESS_KEY_ID=............
CACHE_ENDPOINT=your-redis-endpoint


KNOWLEDGE_BASE_ID=
AWS_REGION=
KNOWLEDGE_BASE_ID=
REDIS_HOST=....
REDIS_PORT=18364
REDIS_PASSWORD=KNVks78...


STRIPE_SECRET_KEY=sk_test_...  
STRIPE_WEBHOOK_SECRET=whsec_...  
PRIVATE_KEY=...

CONSUMER_KEY=GOaBfPtwPnd08ABqApuovoGA4ooGty7Gfcy5h0GhJlmlN4YD
CONSUMER_SECRET=7gq2cKBH1ftxL0yGkEjd9pKB0SKWz6RlRUKqKNYUZpZRG8FHzSRXD7BrwWrGcGhi
MPESA_SHORTCODE=174379
MPESA_PASSKEY=bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
# MPESA_CALLBACK_URL= https://d4c2-102-220-12-82.ngrok-free.app
# MPESA_CALLBACK_URL= https://d4c2-102-220-12-82.ngrok-free.app
MPESA_CALLBACK_URL=https://ea24-102-220-12-82.ngrok-free.app/api/payments/stkcallback



# Security Configuration 

# Redis for distributed rate limiting (optional)
SECURITY_REDIS_ENABLED=true

# reCAPTCHA v3 Minimum Score (optional, default: 0.5)
RECAPTCHA_MIN_SCORE=0.5

# Optional: Explicitly enable CAPTCHA (auto-enabled if keys are present)
SECURITY_CAPTCHA_ENABLED=true


# Set to 'false' to disable specific features
SECURITY_ENABLED=true
SECURITY_RATE_LIMITING_ENABLED=true
SECURITY_ANTI_BOT_ENABLED=true
SECURITY_CAPTCHA_ENABLED=true
SECURITY_MONITORING_ENABLED=true
SECURITY_REQUEST_SIZE_LIMIT_ENABLED=true

CAPTCHA_TYPE=recaptcha
# CAPTCHA Keys (only if SECURITY_CAPTCHA_ENABLED=true)
RECAPTCHA_SITE_KEY=6LfRTX4rAAAAA...
RECAPTCHA_SECRET_KEY=6LfRTX4rAAAA...

# Request size limit
SECURITY_MAX_REQUEST_SIZE=10mb

# Rate Limiting Configuration
RATE_LIMIT_AUTH_WINDOW_MS=900000
RATE_LIMIT_AUTH_MAX_ATTEMPTS=5
RATE_LIMIT_API_WINDOW_MS=900000
RATE_LIMIT_API_MAX_REQUESTS=100
RATE_LIMIT_PAYMENT_WINDOW_MS=3600000
RATE_LIMIT_PAYMENT_MAX_ATTEMPTS=10
RATE_LIMIT_EMAIL_WINDOW_MS=3600000
RATE_LIMIT_EMAIL_MAX_REQUESTS=5