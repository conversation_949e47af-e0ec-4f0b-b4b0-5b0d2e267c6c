const express = require('express');
const router = express.Router();
const eventController = require('../controllers/event');
const checkAuth = require("../middleware/check-auth");

// List all events (optionally filter by ?type=upcoming|past)
router.get('/list', checkAuth, eventController.listEvents);

// Get single event
router.get('/details/:eventId', checkAuth, eventController.getEvent);

// Create event
router.post('/create', checkAuth, eventController.createEvent);

// Update event
router.put('/update/:eventId', checkAuth, eventController.updateEvent);

// Delete event
router.delete('/delete/:eventId', checkAuth, eventController.deleteEvent);

// Attend event
router.post('/attend/:eventId', checkAuth, eventController.attendEvent);

// Submit question
router.post('/question/:eventId', checkAuth, eventController.submitQuestion);

router.post('/duplicate/:eventId', checkAuth, eventController.duplicateEvent);

router.post('/:eventId/subscribe-reminder', checkAuth, eventController.subscribeReminder);
router.post('/:eventId/unsubscribe-reminder', checkAuth, eventController.unsubscribeReminder);


router.post('/:eventId/recording-link', checkAuth, eventController.addRecordingLink);
router.put('/:eventId/recording-link',checkAuth,  eventController.editRecordingLink);


module.exports = router;