const express = require("express");
const checkAuth = require("../middleware/check-auth");
const AffiliateController = require("../controllers/affiliate");

const router = express.Router();
router.post("/generate-code", checkAuth, AffiliateController.generateAffiliateCode);
router.post("/regenerate-code", checkAuth, AffiliateController.regenerateAffiliateCode);
router.get("/stats", checkAuth, AffiliateController.getAffiliateStats);
router.get("/validate/:code", AffiliateController.validateAffiliateCode);
router.get("/pending-claims", checkAuth, AffiliateController.getPendingClaims);
router.post("/regenerate-token", checkAuth, AffiliateController.regenerateAffiliateToken);
router.post("/generate-community-code", checkAuth, AffiliateController.generateAffiliateCodeComunity);
router.put("/update-community-policy", checkAuth, AffiliateController.updateCommunityAffiliatePolicy);
router.put("/update-community-settings", checkAuth, AffiliateController.updateCommunityAffiliateSettings);
router.post("/verify-claim-reward", checkAuth, AffiliateController.verifyClaimAffiliateReward);
router.get("/validate-community/:code/:communityId", AffiliateController.validateCommunityAffiliateCode);
router.get("/community-stats/:communityId", checkAuth, AffiliateController.getCommunityAffiliateStats);
router.get("/community-links", checkAuth, AffiliateController.getAllCommunityAffiliateLinks);
router.post("/regenerate-community-token/:communityId", checkAuth, AffiliateController.regenerateCommunityAffiliateToken);

// Validate affiliate link
router.get('/validate', AffiliateController.validateAffiliateLink);

// Process affiliate signup
router.post('/process-signup', AffiliateController.processAffiliateSignup);

// Get affiliate link stats
router.get('/stats/:ref', checkAuth, AffiliateController.getAffiliateLinkStats);

module.exports = router;
