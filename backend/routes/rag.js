const express = require("express");
const router = express.Router();
const { askQuestion, getSuggestions, getHistory } = require('../controllers/ragController');


/**
 * @swagger
 * /api/rag/ask:
 *   post:
 *     summary: Ask a question to the RAG system
 *     description: |
 *       Retrieval-Augmented Generation (RAG) endpoint that:
 *       1. Retrieves relevant information from the knowledge base
 *       2. Generates a contextual response using AWS Bedrock
 *       3. Maintains conversation history
 *     tags: [RAG]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - message
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Unique identifier for the user
 *               message:
 *                 type: string
 *                 description: User's question or message
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Assistant's response
 *                 history:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Message'
 *                   description: Conversation history
 *                 userId:
 *                   type: string
 *                   description: Unique user identifier (with timestamp for new users)
 *       400:
 *         description: Bad request - Missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error - AWS or Redis issues
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/ask', askQuestion);

/**
 * @swagger
 * /api/rag/suggest:
 *   get:
 *     summary: Get follow-up question suggestions
 *     description: |
 *       Generates contextual follow-up questions based on:
 *       1. User's conversation history
 *       2. Recent messages
 *       3. Available knowledge base content
 *     tags: [RAG]
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique user identifier
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 suggestions:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of suggested follow-up questions
 *       400:
 *         description: Bad request - Missing userId
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error - AWS or Redis issues
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/suggest', getSuggestions);

/**
 * @swagger
 * /api/rag/history:
 *   get:
 *     summary: Get chat history for a user
 *     description: |
 *       Retrieves the user's conversation history from Redis.
 *       History is limited to the last 3 messages per user.
 *     tags: [RAG]
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique user identifier
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 history:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Message'
 *                   description: User's conversation history
 *       400:
 *         description: Bad request - Missing userId
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error - Redis connection issues
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/history', getHistory);

module.exports = router; 