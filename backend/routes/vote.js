const express = require("express");
const VoteController = require("../controllers/vote");
const checkAuth = require("../middleware/check-auth");
const setFileUrl = require("../middleware/file-uploads");

const router = express.Router();

// Core routes
router.post("/create", checkAuth, setFileUrl, VoteController.CreateVote);
router.get("/get_vote", checkAuth, VoteController.GetAllVote); 
router.patch("/extend_vote/:id", checkAuth, VoteController.extendVote);
router.get("/get_active_votes", checkAuth, VoteController.GetActiveVotes);
router.get("/get_ended_votes", checkAuth, VoteController.GetEndedVotes);

// Voting endpoints
router.post("/submit_multi_vote/:voteId", checkAuth, VoteController.submitMultiVote);
router.post("/submit_ranked_vote/:voteId", checkA<PERSON>, VoteController.submitRankedVote);
router.post("/end_vote_early", checkAuth, VoteController.EndVoteEarly);
router.patch('/update_vote_details/:id', checkAuth, VoteController.UpdateVoteDetails);
// Project endpoints
router.post("/start_project", checkAuth, VoteController.StartProject);
router.put("/:id/image", checkAuth, setFileUrl, VoteController.updateProjectImage);

module.exports = router;


/**
 * @swagger
 * /api/vote/create:
 *   post:
 *     tags: [Votes]
 *     summary: Create a new vote
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - votingType
 *               - startDate
 *               - endDate
 *               - fundingAllocation
 *               - projects
 *             properties:
 *               votingType:
 *                 type: string
 *                 enum: [Direct Voting]
 *                 default: Direct Voting
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               endDate:
 *                 type: string
 *                 format: date-time
 *               fundingAllocation:
 *                 type: number
 *               projects:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     image:
 *                       type: string
 *     responses:
 *       201:
 *         description: Vote created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/vote/get_vote:
 *   get:
 *     tags: [Votes]
 *     summary: Get vote details
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Vote details retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Vote not found
 */

/**
 * @swagger
 * /api/vote/save_selected_vote:
 *   post:
 *     tags: [Votes]
 *     summary: Save a user's vote selection
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - voteId
 *               - projectId
 *             properties:
 *               voteId:
 *                 type: string
 *               projectId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Vote saved successfully
 *       400:
 *         description: Invalid input or already voted
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/vote/get_ongoing_votes:
 *   get:
 *     tags: [Votes]
 *     summary: Get all ongoing votes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ongoing votes retrieved successfully
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/vote/increment_vote_count:
 *   post:
 *     tags: [Votes]
 *     summary: Increment vote count for a project
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - voteId
 *               - projectId
 *             properties:
 *               voteId:
 *                 type: string
 *               projectId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Vote count incremented successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/vote/end_vote_early:
 *   post:
 *     tags: [Votes]
 *     summary: End a vote before its scheduled end date
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - voteId
 *             properties:
 *               voteId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Vote ended successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Vote not found
 */

/**
 * @swagger
 * /api/vote/get_ended_votes:
 *   get:
 *     tags: [Votes]
 *     summary: Get all ended votes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ended votes retrieved successfully
 *       401:
 *         description: Unauthorized
 */

/**
 * @swagger
 * /api/vote/start_project:
 *   post:
 *     tags: [Votes]
 *     summary: Start a project after voting has ended
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - voteId
 *             properties:
 *               voteId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Project started successfully
 *       400:
 *         description: Invalid input or vote is still ongoing
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Vote not found
 */
