const express = require("express");
const MiniGameController = require("../controllers/miniGame");
const checkAuth = require("../middleware/check-auth");

const router = express.Router();

router.get("get_my_minigame_info_by_userId", checkAuth, MiniGameController.GetMyMiniGameInfoByUserId)
router.put("update_users_coins", checkAuth, MiniGameController.UpdateCoinsByUserId)
router.put("update_users_earn_per_tap", checkAuth, MiniGameController.UpdateEarnPerTapByUserId)
router.get("check_and_update_users_level", checkAuth, MiniGameController.CheckAndUpdateLevel)
router.put("update_users_profit_per_day", checkAuth, MiniGameController.UpdateProfitPerDayByUserId)
router.put("update_users_energy_limit", checkAuth, MiniGameController.UpdateEnergyLimitByUserId)

module.exports = router;



/**
 * @swagger
 * tags:
 *   name: MiniGame
 *   description: API for managing the in-app mini game features and user progress
 */

/**
 * @swagger
 * /api/miniGame/get_my_minigame_info_by_userId:
 *   get:
 *     tags: [MiniGame]
 *     description: Retrieves all mini game information for the authenticated user
 *     summary: Get user's mini game information
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Mini game information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Fetched successfully"
 *                 records:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       email:
 *                         type: string
 *                       coins:
 *                         type: number
 *                       earnPerTap:
 *                         type: number
 *                       level:
 *                         type: number
 *                       coinsToLevelUp:
 *                         type: string
 *                       profitPerDay:
 *                         type: number
 *                       energyLimit:
 *                         type: number
 *       404:
 *         description: No records found for this user
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/miniGame/update_users_coins:
 *   put:
 *     tags: [MiniGame]
 *     description: Updates the number of coins for the authenticated user
 *     summary: Update user's coins
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - coins
 *             properties:
 *               coins:
 *                 type: number
 *                 example: 1000
 *     responses:
 *       200:
 *         description: Coins updated successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/miniGame/update_users_earn_per_tap:
 *   put:
 *     tags: [MiniGame]
 *     description: Updates the earn-per-tap rate and deducts the required coins
 *     summary: Update user's earn per tap rate
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - earnPerTap
 *               - numberOfCoinsToPay
 *             properties:
 *               earnPerTap:
 *                 type: number
 *                 example: 2
 *               numberOfCoinsToPay:
 *                 type: number
 *                 example: 500
 *     responses:
 *       200:
 *         description: Earn per tap updated successfully and coins deducted
 *       400:
 *         description: Insufficient coins or missing required fields
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/miniGame/check_and_update_users_level:
 *   get:
 *     tags: [MiniGame]
 *     description: Checks if user has enough coins to level up and updates level if eligible
 *     summary: Check and update user's level
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Level checked and updated if eligible
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Level updated to 3"
 *                 user:
 *                   type: object
 *                   properties:
 *                     level:
 *                       type: number
 *                     coinsToLevelUp:
 *                       type: string
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/miniGame/update_users_profit_per_day:
 *   put:
 *     tags: [MiniGame]
 *     description: Updates the profit per day value for the authenticated user
 *     summary: Update user's profit per day
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - profitPerDay
 *             properties:
 *               profitPerDay:
 *                 type: number
 *                 example: 100
 *     responses:
 *       200:
 *         description: Profit per day updated successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/miniGame/update_users_energy_limit:
 *   put:
 *     tags: [MiniGame]
 *     description: Updates the energy limit and deducts the required coins
 *     summary: Update user's energy limit
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - energyLimit
 *               - numberOfCoinsToPay
 *             properties:
 *               energyLimit:
 *                 type: number
 *                 example: 150
 *               numberOfCoinsToPay:
 *                 type: number
 *                 example: 1000
 *     responses:
 *       200:
 *         description: Energy limit updated successfully and coins deducted
 *       400:
 *         description: Insufficient coins or missing required fields
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */