const express = require("express");
const checkAuth = require("../middleware/check-auth");
const UserController = require("../controllers/user");

const router = express.Router();

// Authentication routes with enhanced security
router.post("/signup",  UserController.SignUp);
router.post("/signin",  UserController.SignIn);
router.post("/send_signup_confirm_code", UserController.SendSignupConformationCode);
router.post('/send_signin_code', UserController.SendSigninCode);
router.post('/check_password', UserController.CheckPassword);
router.post("/refresh-token", checkAuth, UserController.RefreshToken);
router.put("/verify_code", UserController.VerifyCode);
router.post("/resend_code", UserController.ResendCode);
router.get("/get_wallet_address", checkAuth, UserController.GetWalletAddress);
router.post("/add_wallet_address", checkAuth, UserController.AddWalletAddress);
router.put("/update_wallet_address", checkAuth, UserController.UpdateWalletAddress);
router.put("/update_points", checkAuth, UserController.UpdateUserPoints);
router.get("/get_user", checkAuth, UserController.GetUser);
router.get("/all", checkAuth, UserController.GetAllUsers);
router.get("/get_all_user_count",  UserController.GetAllUserCount);
router.post("/promote", checkAuth, UserController.PromoteUserToAdmin);
router.post("/demote", checkAuth, UserController.RemoveUser);
router.put("/update_user", checkAuth, UserController.UpdateUser)
router.put("/forgot_password", UserController.ForgotPassword); 
router.put("/change_password", UserController.ChangePassword);
router.post("/resend_password_reset", UserController.ResendResetCode);
router.delete("/delete_account", checkAuth, UserController.DeleteUserAccount);
router.put("/change_password_with_verification", checkAuth, UserController.ChangePasswordWithVerification);


module.exports = router;


/**
 * @swagger
 * /api/user/signup:
 *   post:
 *     tags: [Users]
 *     summary: Register a new user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - email
 *               - password
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               country:
 *                 type: string
 *               walletAddress:
 *                 type: string
 *     responses:
 *       201:
 *         description: User created successfully
 *       203:
 *         description: User already exists
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /api/user/signin:
 *   post:
 *     tags: [Users]
 *     summary: Authenticate user and get token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Authentication failed
 */

/**
 * @swagger
 * /api/user/send_signup_confirm_code:
 *   post:
 *     tags: [Users]
 *     summary: Send confirmation code for signup
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - firstName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               firstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Code sent successfully
 *       404:
 *         description: User already exists
 *       500:
 *         description: Failed to send email
 */

/**
 * @swagger
 * /api/user/verify_code:
 *   put:
 *     tags: [Users]
 *     summary: Verify email confirmation code
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - code
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email verified successfully
 *       400:
 *         description: Invalid or expired code
 */

/**
 * @swagger
 * /api/user/resend_code:
 *   post:
 *     tags: [Users]
 *     summary: Resend confirmation code
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - firstName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               firstName:
 *                 type: string
 *     responses:
 *       200:
 *         description: New code sent successfully
 *       404:
 *         description: No code found for this email
 */

/**
 * @swagger
 * /api/user/get_wallet_address:
 *   get:
 *     tags: [Users]
 *     summary: Get user's wallet address
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Wallet address retrieved successfully
 *       401:
 *         description: Authentication required
 */

/**
 * @swagger
 * /api/user/add_wallet_address:
 *   post:
 *     tags: [Users]
 *     summary: Add wallet address to user profile
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - walletAddress
 *             properties:
 *               walletAddress:
 *                 type: string
 *     responses:
 *       200:
 *         description: Wallet address added successfully
 *       400:
 *         description: Wallet address already exists
 */

/**
 * @swagger
 * /api/user/update_wallet_address:
 *   put:
 *     tags: [Users]
 *     summary: Update user's wallet address
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentWalletAddress
 *               - newWalletAddress
 *             properties:
 *               currentWalletAddress:
 *                 type: string
 *               newWalletAddress:
 *                 type: string
 *     responses:
 *       200:
 *         description: Wallet address updated successfully
 *       404:
 *         description: Current wallet address doesn't match
 */

/**
 * @swagger
 * /api/user/update_points:
 *   put:
 *     tags: [Users]
 *     summary: Update user's points
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - points
 *             properties:
 *               points:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Points updated successfully
 *       403:
 *         description: Failed to update points
 */

/**
 * @swagger
 * /api/user/get_user:
 *   get:
 *     tags: [Users]
 *     summary: Get user profile
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       500:
 *         description: Error occurred
 */

/**
 * @swagger
 * /api/user/update_user:
 *   put:
 *     tags: [Users]
 *     summary: Update user profile
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               country:
 *                 type: string
 *     responses:
 *       200:
 *         description: User details updated successfully
 *       400:
 *         description: Email already in use
 */

/**
 * @swagger
 * /api/user/forgot_password:
 *   put:
 *     tags: [Users]
 *     summary: Request password reset
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Reset code sent successfully
 *       404:
 *         description: No account found with this email
 */

/**
 * @swagger
 * /api/user/change_password:
 *   put:
 *     tags: [Users]
 *     summary: Change password after reset
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - newPassword
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       400:
 *         description: New password must be different
 */

/**
 * @swagger
 * /api/user/resend_password_reset:
 *   post:
 *     tags: [Users]
 *     summary: Resend password reset code
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Reset code resent successfully
 *       404:
 *         description: No reset code found for this email
 */

/**
 * @swagger
 * /api/user/change_password_with_verification:
 *   put:
 *     tags: [Users]
 *     summary: Change password with current password verification
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *               - confirmPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *               confirmPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       400:
 *         description: Passwords don't match or invalid format
 */