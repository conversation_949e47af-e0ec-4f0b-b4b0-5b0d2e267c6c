const express = require('express');
const router = express.Router();
const checkAuth = require('../middleware/check-auth');
const CommunityController = require('../controllers/community');
const setFileUrl = require('../middleware/file-uploads'); // <-- add this line

router.post('/create', checkAuth, setFileUrl, CommunityController.createCommunity);
router.post('/join-request', checkAuth, CommunityController.requestToJoin);
router.get('/:communityId/requests', checkAuth, CommunityController.viewJoinRequests);
router.put('/:communityId/respond-request', checkAuth, CommunityController.acceptOrDeclineRequests);
router.put('/:communityId/edit', checkAuth, CommunityController.editCommunity);

router.get('/get_all_community_info_to_approve', checkAuth, CommunityController.getAllCommunityInfoToApprove);
router.patch('/approve_community/:communityId', checkAuth, CommunityController.approveCommunity);
router.patch('/reject_community/:communityId', checkAuth, CommunityController.rejectCommunity);

module.exports = router;
