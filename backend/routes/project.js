const express = require("express");
const ProjectController = require("../controllers/project");
const checkAuth = require("../middleware/check-auth");
const setFileUrl = require("../middleware/file-uploads");

const router = express.Router();

router.get("/all", checkAuth, ProjectController.GetAllProjects);
router.get('/ongoing', checkAuth, ProjectController.GetOngoingProjects);
router.get('/ongoing/:id', checkAuth, ProjectController.GetOngoingProjectById);
router.get("/completed", checkAuth, ProjectController.GetCompletedProjects);
router.post("/complete", checkAuth, ProjectController.CompleteProject);
router.post("/:id/updates", checkAuth, setFileUrl, ProjectController.addUpdate);
router.put("/:id/updates/:updateId", checkAuth, ProjectController.updateUpdate);
router.delete("/:id/delete/:updateId", checkAuth, ProjectController.deleteUpdate);
router.put("/:projectId/updates/:updateId/image", checkAuth, setFileUrl, ProjectController.updateProjectUpdateImageAndText);

/**
 * @swagger
 * tags:
 *   name: Projects
 *   description: API for managing projects
 */

/**
 * @swagger
 * /api/project/all:
 *   get:
 *     tags: [Projects]
 *     summary: Get all projects
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Projects retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No projects found
 */

/**
 * @swagger
 * /api/project/ongoing:
 *   get:
 *     tags: [Projects]
 *     summary: Get all ongoing projects
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Ongoing projects retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No ongoing projects found
 */

/**
 * @swagger
 * /api/project/completed:
 *   get:
 *     tags: [Projects]
 *     summary: Get all completed projects
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Completed projects retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No completed projects found
 */

/**
 * @swagger
 * /api/project/complete:
 *   post:
 *     tags: [Projects]
 *     summary: Mark a project as completed
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               projectId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Project marked as completed
 *       400:
 *         description: Invalid input or project already completed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Project not found
 */

module.exports = router;