const express = require("express");
const PriceController = require("../controllers/price");

const router = express.Router();

router.get("/get_price",  PriceController.GetCurrentPrice);

module.exports = router;


/**
 * @swagger
 * tags:
 *   name: Price
 *   description: API for retrieving token price information
 */

/**
 * @swagger
 * /api/price/get_price:
 *   get:
 *     tags: [Price]
 *     description: Retrieves the current token price based on the countdown status
 *     summary: Get current token price
 *     responses:
 *       200:
 *         description: Current price retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 currentPrice:
 *                   type: string
 *                   example: "0.0010"
 *       404:
 *         description: Price not found
 *       500:
 *         description: Server error
 */