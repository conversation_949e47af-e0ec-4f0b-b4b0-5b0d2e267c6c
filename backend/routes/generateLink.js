const express = require("express");
const router = express.Router();
const checkAuth = require("../middleware/check-auth");
const GenerateLinkController = require("../controllers/generateLink");

router.post("/generate-link", checkAuth, GenerateLinkController.generateLink);
router.post("/send-invite-emails", checkAuth, GenerateLinkController.sendInviteEmails);
router.get("/verify-link/:linkId", GenerateLinkController.verifyLink);
router.get('/validate-token-access', GenerateLinkController.validateTokenAccess);
// router.get('/consume-invite', GenerateLinkController.consumeInvite);




module.exports = router;