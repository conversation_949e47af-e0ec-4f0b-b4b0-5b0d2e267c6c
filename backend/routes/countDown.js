const express = require("express");
const CountDownController = require("../controllers/countDown");

const router = express.Router();

router.get("/get_countdown",  CountDownController.GetCountDown);

module.exports = router;


/**
 * @swagger
 * tags:
 *   name: Countdown
 *   description: API for managing the token sale countdown timer
 */

/**
 * @swagger
 * /api/countDown/get_countdown:
 *   get:
 *     tags: [Countdown]
 *     description: Retrieves the current countdown end time or creates a new countdown if none exists
 *     summary: Get current countdown timer
 *     responses:
 *       200:
 *         description: Countdown information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 endTime:
 *                   type: string
 *                   format: date-time
 *                   example: "2023-12-31T23:59:59.999Z"
 *       500:
 *         description: Server error
 */