const express = require('express');
const router = express.Router();
const communityTransferController = require('../controllers/communityTransfer');
const checkAuth = require("../middleware/check-auth");

router.post('/initiate', checkAuth, communityTransferController.initiateTransfer);
router.post('/accept', checkAuth, communityTransferController.acceptTransfer);
router.get('/pending', checkAuth, communityTransferController.getPendingTransfers);
router.get('/validate', checkAuth, communityTransferController.validateTransfer);

module.exports = router;