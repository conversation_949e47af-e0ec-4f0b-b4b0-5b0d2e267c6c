const express = require("express");
const router = express.Router();
const checkAuth = require("../middleware/check-auth");

const PaymentController = require("../controllers/payments");


router.post("/mpesaSTK", PaymentController.stkPush)
router.post("/stkcallback", PaymentController.callBackMpesa)
router.get("/mpesaStatus", PaymentController.mpesaStatus)

// router.post("/mpesa-callback", PaymentController.callBackMpesa)




module.exports = router; 