const express = require('express');
const router = express.Router();
const checkAuth = require('../middleware/check-auth');
const {
    generatePaymentIntent,
    activateContribution,
    getContributionStatus,
    handleStripeWebhook,
    cancelContribution,
    verifyCryptoPayment,
    getContributionHistory,
    getUserContributionPaymentHistory,
    updateContribution,
    getUserContributionScheduledPayment,
    getDashboardSummary
} = require('../controllers/contributionController');

/**
 * @swagger
 * /api/contribution/generate-intent:
 *   post:
 *     summary: Generate a payment intent for Stripe subscription
 *     tags: [Contribution]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - userId
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Amount in SHLN or USD
 *               userId:
 *                 type: string
 *                 description: User's ID
 *     responses:
 *       200:
 *         description: Payment intent generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 clientSecret:
 *                   type: string
 *                 paymentReference:
 *                   type: string
 *                 subscriptionId:
 *                   type: string
 *       400:
 *         description: Invalid input
 *       500:
 *         description: Server error
 */
router.post('/generate-intent', generatePaymentIntent);

/**
 * @swagger
 * /api/contribution/activate:
 *   post:
 *     summary: Activate a contribution after payment
 *     tags: [Contribution]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - amount
 *               - currency
 *               - paymentMethod
 *               - setupIntentId
 *               - subscriptionId
 *             properties:
 *               userId:
 *                 type: string
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *                 enum: [SHLN, USD]
 *               paymentMethod:
 *                 type: string
 *                 enum: [stripe, crypto]
 *               setupIntentId:
 *                 type: string
 *               subscriptionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Contribution activated successfully
 *       400:
 *         description: Invalid input or payment verification failed
 *       500:
 *         description: Server error
 */
router.post('/activate',  activateContribution);

/**
 * @swagger
 * /api/contribution/status/{userId}:
 *   get:
 *     summary: Get contribution status for a user
 *     tags: [Contribution]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User's ID
 *     responses:
 *       200:
 *         description: Contribution status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [active, inactive, cancelled]
 *                 contribution:
 *                   type: object
 *                   properties:
 *                     amount:
 *                       type: number
 *                     currency:
 *                       type: string
 *                     tier:
 *                       type: string
 *                     nextPaymentDate:
 *                       type: string
 *                       format: date-time
 *       404:
 *         description: No contribution found
 *       500:
 *         description: Server error
 */
router.get('/status/:userId', getContributionStatus);

/**
 * @swagger
 * /api/contribution/cancel/{userId}:
 *   post:
 *     summary: Cancel a user's contribution
 *     tags: [Contribution]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User's ID
 *     responses:
 *       200:
 *         description: Contribution cancelled successfully
 *       404:
 *         description: No active contribution found
 *       500:
 *         description: Server error
 */
router.post('/cancel/:userId', cancelContribution);

/**
 * @swagger
 * /api/contribution/webhook:
 *   post:
 *     summary: Handle Stripe webhook events
 *     tags: [Contribution]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 description: Event type from Stripe
 *               data:
 *                 type: object
 *                 description: Event data from Stripe
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 *       400:
 *         description: Invalid webhook signature
 *       500:
 *         description: Server error
 */
router.post('/webhook', express.raw({ type: 'application/json' }),  handleStripeWebhook);

/**
 * @swagger
 * /api/contribution/update:
 *   post:
 *     summary: Update a user's contribution
 *     tags: [Contribution]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - amount
 *               - currency
 *               - paymentMethod
 *               - updateType
 *             properties:
 *               userId:
 *                 type: string
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *                 enum: [SHLN, USD]
 *               paymentMethod:
 *                 type: string
 *                 enum: [stripe, crypto]
 *               updateType:
 *                 type: string
 *                 enum: [immediate, scheduled]
 *               subscriptionId:
 *                 type: string
 *               newPlanId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Contribution updated successfully
 *       400:
 *         description: Invalid input or update failed
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Contribution not found
 *       500:
 *         description: Server error
 */
router.post('/update', checkAuth,  updateContribution);


/**
 * @swagger
 * /api/contribution/history:
 *   get:
 *     summary: Get user's contribution payment history
 *     tags: [Contribution]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Payment history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     payments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           amount:
 *                             type: number
 *                           date:
 *                             type: string
 *                             format: date-time
 *                           status:
 *                             type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/summary', getDashboardSummary);
router.get('/history', checkAuth, getUserContributionPaymentHistory);
router.get('/history/:userId',  getContributionHistory);
router.post('/verify-crypto', checkAuth,  verifyCryptoPayment);
router.get('/scheduled-payment', checkAuth, getUserContributionScheduledPayment);

module.exports = router; 