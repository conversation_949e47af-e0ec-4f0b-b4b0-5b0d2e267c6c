exports.emailActivation = (confirmationCode) => {
    return `
      <!DOCTYPE html>
  <html lang="en">
    <head>
      <style>
            .main-container {
          width: 50vw;
          display: block;
          margin-left: auto;
          margin-right: auto;
        }
  
        .header {
          background-color: #1b454b;
          height: 35%;
        }
  
        .logo {
          padding-left: 20px;
          padding-top: 10px;
        }
  
        .top-content {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-bottom: 5px;
        }
  
       .title {
          display: block;
          color: white;
          font-size: 24px;
          margin-left: auto;
          margin-right: auto;
          width: 250px;
          margin-bottom: 5px;
        }
  
       .welcome-img {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-bottom: 10px;
          margin-top: -20px;
        }
  
      .subtitle {
          display: block;
          color: #daeff1;
          font-size: 12px;
          margin-left: auto;
          margin-right: auto;
          width: 255px;
          padding-bottom: 20px;
        }
  
         .content {
          background-color: #f8f5ed;
          text-align: center;
          margin-top: -8px;
          padding-bottom: 25px;
          padding-top: 5px;
        }
  
        .content .text {
          font-size: 14px;
          color: gray;
          width: 70%;
          margin: 20px auto;
  
          margin-bottom: 20px;
        }
  
        .code {
          margin: auto;
          border-radius: 5px;
          color: white;
          font-size: 24px;
          padding: 25px;
          height: 30px;
          width: 15%;
          background-color: hsla(187, 47%, 35%, 0.5);
        }
  
        .sub-text {
          font-size: 12px;
          color: gray;
          width: 50%;
          margin: auto;
          margin-top: 30px;
        }
  
        .sub-text span {
          color: #1b454b;
        }
  
      .button {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-top: 10px;
        }
  
        .button p {
          color: #1b454b;
          margin-right: 25px;
          font-size: 12px;
        }
  
         .submit {
          background-color: white;
          border: 1px solid#1B454B;
          border-radius: 15px;
          color: #1b454b;
          padding: 5px 52px 5px 52px;
          text-align: center;
          text-decoration: none;
          display: inline-block;
          font-size: 14px;
          margin: 4px 2px;
          cursor: pointer;
          transition-duration: 0.4s;
        }
  
        .submit:hover {
          background-color: #1b454b;
          color: white;
        }
  
        .footer {
          background-color: #ebe3cc;
          height: 20%;
        }
  
        .social {
          display: block;
          margin-left: auto;
          margin-right: auto;
          width: 160px;
          padding-top: 20px;
        }
  
        .facebook,
        .twitter,
        .linkedin {
          margin-right: 15px;
        }
  
        .text-content {
          width: 60%;
          margin: 10px auto;
          color: grey;
        }
  
        @media only screen and (max-width: 767px) {
          .main-container {
            width: 100vw;
            margin: 0 auto;
          }
  
          .header {
            background-color: #1b454b;
            /* height: 20vh; */
          }
  
          .logo {
            padding: 10px;
          }
  
          .top-content {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-bottom: 5px;
        }
  
          .title {
            color: white;
            font-size: 14px;
            text-align: center;
            margin-bottom: 5px;
          }
  
          .welcome-img {
            display: block;
            margin: 0 auto;
            margin-bottom: 10px;
          }
  
          .subtitle {
            color: #daeff1;
            font-size: 10px;
            text-align: center;
            margin-bottom: 20px;
          }
  
         .content {
          background-color: #f8f5ed;
          text-align: center;
          margin-top: -8px;
          padding-bottom: 25px;
          padding-top: 5px;
        }
  
          .content .text {
            font-size: 10px;
            color: gray;
            width: 80%;
            margin: 0 auto;
            margin-top: 10px;
            margin-bottom: 20px;
          }
  
          .code {
            border-radius: 5px;
            color: white;
            font-size: 18px;
            padding: 15px;
            height: 50px;
            width: 45%;
            margin: 0 auto;
            background-color: hsla(187, 47%, 35%, 0.5);
          }
  
          .sub-text {
            display: flex;
            justify-content: center;
            font-size: 10px;
            color: gray;
            width: 80%;
            margin: 0 auto;
            margin-top: 20px;
          }
  
          .sub-text span {
            color: #1b454b;
          }
  
     .button {
          display: block;
          margin-left: auto;
          margin-right: auto;
          margin-top: 10px;
        }
  
          .button p {
            color: #1b454b;
            margin-right: 10px;
            font-size: 10px;
          }
  
          .submit {
            background-color: white;
            border: 1px solid #1b454b;
            border-radius: 10px;
            color: #1b454b;
            padding: 5px 50px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
          }
  
          .footer {
            background-color: #ebe3cc;
            display: block;
          }
  
       .social {
          display: block;
          margin-left: auto;
          margin-right: auto;
          width: 175px;
          padding-top: 20px;
        }
  
  
          .social a {
             margin-left: auto;
          margin-right: auto;
          }
  
          .text-content {
            width: 80%;
            margin: 20px auto;
            color: grey;
            font-size: 10px;
          }
        }
      </style>
      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Verify Email</title>
  
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Golos+Text&display=swap"
        rel="stylesheet"
      />
  
      <link
        href="https://fonts.googleapis.com/css2?family=Lobster&display=swap"
        rel="stylesheet"
      />
  
      <link
        href="https://fonts.googleapis.com/css2?family=Marck+Script&display=swap"
        rel="stylesheet"
      />
    </head>
  
    <body>
      <div class="main-container">
        <div class="header">
          <div class="logo">
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637632/enkaare_new_logo-removebg_djgnoz.png"
              alt="Logo"
              height="60"
              width="70"
              onclick="navigate(AppRoutesPaths.home)"
            />
          </div>
  
          <div class="top-content">
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637623/welcome_qeppu9.png"
              alt="welcome"
              height="50"
              width="40"
              class="welcome-img"
            />
            <p class="title">Welcome to Enkaare</p>
            <p class="subtitle">Enkaare The Platform for Smart Hiring</p>
          </div>
        </div>
  
        <div class="content">
          <p class="text">
          We have received a request to log in with a previously deactivated account. 
          You need to verify your email address so as to reactivate it.
          If you did not make this request, please disregard this email.
          If you'd like to activate your account, 
          please verify your request by using the code below:
          </p>
          <div class="code">${confirmationCode}</div>
          <p class="sub-text">
            <span>Note:</span>The Code expires upon page refreshing
          </p>
          <div class="button">
            <p>Or</p>
            <a href="${process.env.En_FrontEndURL}/login"> <button class="submit">Login</button> </a>
          </div>
        </div>
  
        <div class="footer">
          <div class="social">
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637622/facebook_zvgqsw.png"
              alt="facebook"
              height="25"
              width="25"
              class="facebook"
            />
  
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637623/twitter_yuganw.png"
              alt="twitter"
              height="25"
              width="25"
              class="twitter"
            />
  
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637622/linkedin_aygxbx.png"
              alt="linkedin"
              height="25"
              width="25"
              class="linkedin"
            />
  
            <img
              src="https://res.cloudinary.com/dhcdes5bt/image/upload/v1713637622/instagram_wlzjs2.png"
              alt="instagram"
              height="25"
              width="25"
              class="instagram"
            />
          </div>
          <div class="text-content">
            <p>Baltimore Maryland USA</p>
            <p>&copy; 2024 Enkaare. All Rights Reserved.</p>
            <p>
              Please do not reply to this email. For any enquiries, contact us
              through <span><EMAIL></span>
            </p>
          </div>
        </div>
      </div>
    </body>
  </html>`;
  };