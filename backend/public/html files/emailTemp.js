exports.emailTemp = (content) => {

  const currentYear = new Date().getFullYear();

    return `
    <!DOCTYPE html>
  <html lang="en">
    <head>
      <style>

        body {
            margin: 0 auto;
            padding: 0;
        }

        .main {
          width: 50vw;
          display: block;
          margin: 0 auto;
        }

        /*------------------------- header css starts here -------------------------*/

        .header {
            /* background: #1f2937; */
            background: #111828;
            /* background: #140c24; */
            /* background: #111111; */
            padding: 15px;
        }

        .elogo {
            width: 20%;
        }

        .headerContent {
        color: white;
        font-family: sans-serif;
        }

        .welcomeImgDiv {
            width: 10%;
            margin: 0 auto;
        }

        .welcomeImg {
            width: 100%;
        }

        .headerTitle {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-weight: 500;
            font-size: x-large;
        }

        .headerSubtitle {
            /* color: #7A7A7A; */
            color: #b4b4b4;
            text-align: center;
            font-size: small;
        }


        /*------------------------- header css ends here -------------------------*/


        /*------------------------- mailBodyContent css starts here -------------------------*/

        .mailBodyContent {
            /* background: #1f2937; */
            /* background: #9CA3AF; */
            background: #D1D5DB;
            /* background: #d4d4d4; */
            /* color: #7E8282; */
            /* color: #424240; */
            color: #585858;
            padding: 3em 7em;
            font-family: sans-serif;
            text-align: center;
        }

        .bodyText {
          font-size: medium;
        }

        .bodyTextJ {
          font-size: medium;
          text-align: justify;
          line-height: 1.6;
        }

        .code {
          width: fit-content;
          margin: 2em auto;
          padding: 1em 2em;
          border-radius: 10px;
          background: #111111;
          color: #E1AB0D;
          font-weight: bold;
          font-size: x-large;
        }

        .bodySmallText {
          font-size: smaller;
        }

        .noteSpan {
          /* color: #E1AB0D; */
          color: #111828;
          /* color: #000000; */
        }


        /*------------------------- mailBodyContent css ends here -------------------------*/


        /*------------------------- footer css starts here -------------------------*/

        .footer {
            /* background: #111828; */
            background: #1f2937;
            /* background: #000000; */
            padding: 25px 10%;
        }

        .socialIconsDiv{
            margin: 0 auto;
            width: fit-content;
            text-decoration: none;
        }

        .socialIcon {
            margin: 0 15px;
            width: 30px;
        }

        .footerText, .footerText a {
            color: #b2b7b7;
            font: 1em sans-serif;
        }

        .footerTextL2 {
            text-align: center;
        }

        .underline {
            text-decoration-line: underline;
        }

        /*------------------------- footer css ends here -------------------------*/

        /*------------------------- small screen css starts here -------------------------*/


        @media only screen and (max-width: 767px) {

            .main {
                width: 95vw;
            }

            .elogo {
            width: 25%;
            }

            .welcomeImgDiv {
            width: 10%;
            }

            .headerTitle {
                font-weight: 500;
                font-size: medium;
            }

            .headerSubtitle {
                text-align: center;
                font-size: 9px;
            }

            body {
            width: 75vw;
            font-size: small;
            }

            .mailBodyContent {
            padding: 10px;
            }

            .bodyText {
              font-size: small;
            }

            .bodyTextJ {
              font-size: small;
              padding: 0 5%;
            }

            .code {
              font-size: medium;
            }

            .footer {
            padding: 25px 10%;
            }

            .socialIcon {
            margin: 0 10px;
            width: 25px;
            }
        }

        /*------------------------- small screen css ends here -------------------------*/





      </style>

      <meta charset="UTF-8" />
      <meta http-equiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Email Template</title>

      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
      <link
        href="https://fonts.googleapis.com/css2?family=Golos+Text&display=swap"
        rel="stylesheet"
      />

      <link
        href="https://fonts.googleapis.com/css2?family=Lobster&display=swap"
        rel="stylesheet"
      />

      <link
        href="https://fonts.googleapis.com/css2?family=Marck+Script&display=swap"
        rel="stylesheet"
      />
    </head>

    <body>
      <div class="main">

        <!-- this is header -->

        <div class="header">

            <div class="headerContent">

              <div class="welcomeImgDiv">
                <img class="welcomeImg" src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1745925191/queenpantherlogo3dwhite_sqviea.png" alt="welcome img" />
              </div>

              <p class="headerTitle">Black Panther</p>

              <p class="headerSubtitle">Empowering communities, one project at a time</p>

            </div>

        </div>



        <!-- this is body of mail -->
        <div class="mailBodyContent">

        ${content}

        </div>

        <!-- this is footer -->
        <div class="footer">

            <div class="socialIconsDiv">
                <a href="https://t.me/blackpanthertkn" target="_blank" class=""><img src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1745925264/telegram_zsejyg.png" alt="" class="socialIcon"></a>
                <a href="https://discord.gg/TjGpQXCeS3" target="_blank" class=""><img src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1745925264/discord_g5osoz.png" alt="" class="socialIcon"></a>
                <a href="https://www.tiktok.com/@blackpanthertkn" target="_blank" class=""><img src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1745925265/tikTok_rua1hc.png" alt="" class="socialIcon"></a>
                <a href="https://x.com/bpnthrx" target="_blank" class=""><img src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1745925265/x_tmvtnr.png" alt="" class="socialIcon"></a>
                <a href="https://www.youtube.com/@blackpanthertoken" target="_blank" class=""><img src="https://res.cloudinary.com/dbucyy0a4/image/upload/v1746166338/YouTube_l2yz7d.png" alt="" class="socialIcon"></a>
            </div>

            <div class="footerText">
                <!-- <p class="footerTextL1">Baltimore, Maryland, USA</p> -->
                <p class="footerTextL2">Copyright &copy; ${currentYear} Black Panther all rights reserved.</p>
                <p class="footerTextL3">Please do not reply to this email. For any enquiries, please contact us through the information provided on our website <a href=${process.env.BP_FrontEndURL} target="_blank" class="underline">blackpanthertkn.com</a>.</p>
            </div>

        </div>



        

      </div>
    </body>
  </html>`
}