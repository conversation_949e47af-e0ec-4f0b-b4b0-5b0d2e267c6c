const { emailTemp } = require("./emailTemp");

exports.emailInvite = (websiteUrl, message = '') => {
  const content = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.5;">
      <h2 style="color: #2c5282; font-size: 24px; font-weight: 500; margin-bottom: 20px;">
        Welcome to Black Panther DAO
      </h2>
      
      <p style="font-size: 16px; color: #2d3748; margin-bottom: 24px;">
        You've been invited to join our community!
      </p>
      
      ${message ? `
        <div style="margin: 24px 0; padding: 16px; background: #f7fafc; border-left: 4px solid #2c5282; border-radius: 4px;">
          <strong style="color: #2d3748;">Personal message:</strong><br/>
          <p style="color: #4a5568; margin-top: 8px;">${message}</p>
        </div>
      ` : ''}
      
      <div style="margin: 32px 0;">
        <a href="${websiteUrl}" 
           style="display: inline-block; padding: 12px 24px; background: #2c5282; color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500;">
          Join Our Community
        </a>
      </div>

      <p style="color: #4a5568; font-size: 14px; margin-top: 24px;">
        Or copy this link:<br/>
        <span style="color: #2c5282; word-break: break-all;">${websiteUrl}</span>
      </p>
      
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;" />
      
      <p style="color: #718096; font-size: 14px;">
        Have questions? Just reply to this email - we're here to help.<br/>
        Looking forward to seeing you in the community!
      </p>
    </div>
  `;
  return emailTemp(content);
};