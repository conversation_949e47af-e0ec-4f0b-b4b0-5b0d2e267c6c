const { emailTemp } = require("./emailTemp");

/**
 * Generates a branded, actionable community transfer email.
 * @param {Object} params
 * @param {string} params.firstName - New owner's first name
 * @param {string} params.communityName - Name of the community
 * @param {string} params.acceptUrl - Link to accept the transfer
 * @param {Date|string} params.expiresAt - Expiry date/time for the transfer link
 */
exports.communityTransferEmail = ({ firstName, communityName, acceptUrl, expiresAt }) => {
  const content = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 24px; line-height: 1.6; color: #2d3748;">
      <h2 style="color: #2c5282; font-size: 24px; font-weight: 600; margin-bottom: 16px;">
        Community Ownership Transfer
      </h2>
      <p style="font-size: 16px; margin-bottom: 20px;">
        Hi${firstName ? ` ${firstName}` : ''},
        <br><br>
        You have been selected to become the new owner of <strong>${communityName}</strong>.
      </p>
      <div style="margin: 24px 0; padding: 16px; background: #f7fafc; border-left: 4px solid #2c5282; border-radius: 4px;">
        <strong style="color: #2d3748;">What does this mean?</strong>
        <ul style="margin: 12px 0 0 18px; color: #4a5568;">
          <li>Manage members and permissions</li>
          <li>Update community settings</li>
          <li>Transfer ownership in the future</li>
          <li>Oversee community governance</li>
        </ul>
      </div>
      <div style="margin: 32px 0; text-align: center;">
        <a href="${acceptUrl}" 
           style="display: inline-block; padding: 14px 28px; background: #2c5282; color: #fff; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px;">
          Accept Ownership
        </a>
      </div>
      <p style="color: #4a5568; font-size: 14px; margin-top: 24px;">
        Or copy this link:<br>
        <span style="color: #2c5282; word-break: break-all;">${acceptUrl}</span>
      </p>
      <div style="margin: 24px 0; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
        <span style="color: #856404; font-size: 14px;">
          <strong>Note:</strong> This transfer link will expire on <strong>${new Date(expiresAt).toLocaleString()}</strong>.
        </span>
      </div>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;" />
      <p style="color: #718096; font-size: 14px;">
        If you have questions, just reply to this email.<br>
        Thank you for being part of Black Panther DAO!
      </p>
    </div>
  `;
  return emailTemp(content);
};