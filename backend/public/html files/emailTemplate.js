exports.emailTemplate = ({ firstName, code, type }) => {
    const isConfirmation = type === "confirmation";
    const subject = isConfirmation
      ? "Email Verification"
      : "Password Reset Code";
    const message = isConfirmation
      ? `Thank you for joining <strong>Black Panther!</strong> To get started, please verify your email using the code below.`
      : `You requested to reset your password. Use the code below to proceed with resetting your password.`;
  
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body {
                margin: 0;
                padding: 40px 0;
                background-color: #f8f8f8;
                font-family: Arial, sans-serif;
            }
            .email-container {
                max-width: 400px;
                margin: 0 auto;
                padding: 40px;
                color: #333;
            }
            .email-card {
                background-color: #ffffff;
                border-radius: 11px;
                padding: 40px;
                border: 1px solid #eee;
                box-shadow: 5px 10px 28px rgb(235,235,236);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .header h1 {
                color: #000;
                margin-bottom: 5px;
                font-size: 24px;
                font-weight: bold;
            }
            .header p {
                color: #666;
                margin-top: 0;
                font-size: 14px;
            }
            .content p {
                font-size: 16px;
                color: #333;
                margin-bottom: 20px;
            }
            .content strong {
                font-weight: bold;
            }
            .verification-code {
                text-align: center;
                margin: 30px 0;
            }
            .code-box {
                display: inline-block;
                font-size: 24px;
                font-weight: bold;
                letter-spacing: 5px;
                padding: 15px 30px;
                background-color: #000000;
                border-radius: 4px;
                color: #e1ab0d;
                border: 1px solid #ddd;
            }
            .note {
                font-size: 12px;
                color: #666;
                font-style: italic;
                text-align: center;
                margin: 20px 0;
            }
            .footer {
                text-align: center;
                font-size: 14px;
                color: #666;
                margin-bottom: 30px;
            }
            .divider {
                border-top: 1px solid #eee;
                margin: 30px 0;
                width: 100%;
            }
            .help {
                text-align: center;
                font-size: 12px;
                color: #999;
                margin-bottom: 0;
            }
            .help a {
                color: #666;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-card">
                <div class="header">
                    <h1>Black Panther</h1>
                    <p>Empowering communities, one project at a time</p>
                </div>
                
                <div class="content">
                    <p>Hello, <strong>${firstName || 'User'}</strong>,</p>
                    <p>${message}</p>
                </div>
                
                <div class="verification-code">
                    <div class="code-box">${code}</div>
                </div>
                
                <p class="note">This code is valid for a single use.</p>
                
                <p class="footer">If you didn't request this, you can safely ignore this email.</p>
                
                <hr class="divider">
                
                <p class="help">Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>
    </body>
    </html>
    `;
  };