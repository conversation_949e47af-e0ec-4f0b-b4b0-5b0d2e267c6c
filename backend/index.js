const express = require("express");
const helmet = require("helmet");
const cors = require("cors");
const checkOrigin = require('./middleware/check-origin');
const ConnectDB = require("./connect/db");
require("dotenv").config();
const scheduleSupplyJob = require('./cron/supplyCron.js')
const MpesaController = require('./controllers/payments.js')
const {initializeLotteryIfNeeded} = require("./cron/lotteryCron.js")

initializeLotteryIfNeeded()


// Security middleware imports
const { dynamicSecurityMiddleware, securityMonitoring, requestSizeLimit } = require('./middleware/security');
const { logConfigStatus, validateConfig } = require('./config/securityConfig');

// Default values for environment variables
const DEFAULT_PORT = 9080;
const DEFAULT_FRONTEND_URL = 'http://localhost:3000';

// Validate and get environment variables with fallbacks
const getEnvVar = (key, defaultValue) => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    console.warn(`Warning: ${key} environment variable is not set`);
  }
  return value || defaultValue;
};

const PORT = getEnvVar('PORT', DEFAULT_PORT);
const FRONTEND_URL = getEnvVar('BP_FrontEndURL', DEFAULT_FRONTEND_URL);

const Price = require("./models/price");

const userRoutes = require("./routes/user");
const priceRoutes = require("./routes/price");
const countDownRoutes = require("./routes/countDown");
const miniGameRoutes = require("./routes/miniGame");
const voteRoutes = require("./routes/vote");
const supplyRoutes = require("./routes/supply");
const projectRoutes = require("./routes/project");
const ragRoutes = require("./routes/rag");
const inviteRoutes = require('./routes/invite');
const generateLinkRoutes = require('./routes/generateLink');
const eventRoutes = require("./routes/event");
const contributionRoutes = require("./routes/contribution");
const paymentsRoutes = require("./routes/payments");
const communityTransferRoutes = require("./routes/communityTransfer");
const communityRoutes = require("./routes/community");
const affiliateRoutes = require("./routes/affiliate");

const path = require("path");
const fs = require('fs');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger/swagger.js');

const app = express();

// CORS update to allow multiple frontend domains
const allowedOrigins = FRONTEND_URL.split(",");



// const corsOptions = {
//   origin: function (origin, callback) {
//     if (!origin || allowedOrigins.includes(origin)) {
//       callback(null, true);
//     } else {
//       callback(new Error('Not allowed by CORS'));
//     }
//   },
//   credentials: true,
// };

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error(`Not allowed by CORS: ${origin}`));
    }
  },
  credentials: true,
};


app.use(cors(corsOptions));
app.use(helmet()); // system-hardening
app.use(checkOrigin);

// Security configuration validation and logging
const configErrors = validateConfig();
if (configErrors.length > 0) {
    console.warn(' Security Configuration Warnings:');
    configErrors.forEach(error => console.warn(`   ${error}`));
}
logConfigStatus();

// Security middleware
app.use(securityMonitoring);
app.use(requestSizeLimit);
app.use(dynamicSecurityMiddleware);


// Ensure uploads directory exists
const uploadDir = path.join(__dirname, 'public/uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Enable CORS for static file access (like uploaded images)
app.use('/uploads', (req, res, next) => {
//   res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5173');
// res.setHeader('Access-Control-Allow-Credentials', 'true');

  next();
}, express.static(path.join(__dirname, 'public/uploads')));



// parse requests of content-type - application/json
app.use((req, res, next) => {
  if (req.originalUrl === '/api/contribution/webhook') {
    next(); // skip json parser for Stripe webhook
  } else {
    express.json()(req, res, next);
  }
});

// parse requests of content-type - application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: false }));

// Swagger UI route
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs));

// Routes
app.use("/api/user", userRoutes);
app.use("/api/price", priceRoutes);
app.use("/api/countDown", countDownRoutes);
app.use("/api/miniGame", miniGameRoutes);
app.use("/api/vote", voteRoutes);
app.use("/api/supply", supplyRoutes);
app.use("/api/project", projectRoutes);
app.use('/api/invite', inviteRoutes);
app.use('/api/generate-link', generateLinkRoutes);
app.use("/api/rag", ragRoutes);
app.use("/api/contribution", contributionRoutes);
app.use("/api/event", eventRoutes);
app.use("/api/payments", paymentsRoutes);
app.use("/api/community-transfer", communityTransferRoutes);
app.use("/api/community", communityRoutes);
app.use("/api/affiliate", affiliateRoutes);

app.use('/uploads/project-images', express.static('uploads/project-images'));

app.post("/mpesa-callback", MpesaController.callBackMpesa)
// Simple route with link to API docs
app.get("/", (req, res) => {
  res.send(`
    <h1>Welcome to Black Panther API</h1>
    <p>Visit our <a href="/api-docs">API Documentation</a> to explore the available endpoints.</p>
  `);
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    status: 'error',
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

const start = async () => {
  try {
    // Attempt to connect to the database
    await ConnectDB();
    
    // Start the server regardless of database connection status
    const server = app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}.`);

      scheduleSupplyJob();
      initializeLotteryIfNeeded()
      
    });

    // Handle server errors
    server.on('error', (error) => {
      console.error('Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.log(`Port ${PORT} is already in use. Trying port ${PORT + 1}`);
        server.close();
        app.listen(PORT + 1);
      }
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    // Don't exit the process, let it continue running
  }
};

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process
});

start();
module.exports = app;
