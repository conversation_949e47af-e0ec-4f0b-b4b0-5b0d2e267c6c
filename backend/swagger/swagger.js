const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Black Panther API Documentation',
      version: '1.0.0',
      description: "API documentation for Black Panther backend services endpoints. <strong style='font-size: larger;'>done by: <EMAIL></strong>",
    },
    servers: [
      {
        url: "http://localhost:9080", 
        description: 'Local server',
      },
      {
        url: "https://api.staging.blackpanthertkn.com/", 
        description: 'Staging server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./routes/*.js'], // Path to the API routes
};

const specs = swaggerJsdoc(options);
module.exports = specs;