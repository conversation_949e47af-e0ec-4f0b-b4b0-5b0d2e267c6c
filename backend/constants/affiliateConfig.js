// Affiliate System Configuration
const AFFILIATE_CONFIG = {
  // Default reward amounts
  DEFAULT_USD_PER_REFERRAL: 5,        // $5 USD per referral signup
  DEFAULT_TOKENS_PER_REFERRAL: 100000,   // 100000 tokens per referral signup
  
  // Bonus rewards when maximum users reached
  BONUS_USD_AT_MAX: 25,               // Additional $25 when max users hit
  BONUS_TOKENS_AT_MAX: 500,           // Additional 500 tokens when max users hit
  
  // Default settings
  DEFAULT_MAX_USERS: 5,               // Default maximum users per link
  DEFAULT_EXPIRATION_HOURS:24,      // Default  (1hour)
  
  // Link generation
  FRONTEND_BASE_URL: process.env.FRONTEND_URL || 'https://www.blackpanthertkn.com',
  // FRONTEND_BASE_URL: process.env.FRONTEND_URL || 'http://localhost:5173',
  
  // <PERSON><PERSON> types
  REWARD_TYPES: {
    IMMEDIATE: 'immediate',           // Reward given immediately per signup
    MAX_REACHED: 'max_reached'        // Bonus reward when max users reached
  },
  
  // Claim statuses
  CLAIM_STATUS: {
    PENDING: 'pending',
    COMPLETED: 'completed',
    FAILED: 'failed'
  }
};

module.exports = AFFILIATE_CONFIG;
