const Price = require("../models/price")
const CountDown = require("../models/countDown")


exports.GetCurrentPrice = (req, res, next) => {
    CountDown.find({})
        .then(countDownList => {
            const skipCount = countDownList.length - 1;
            return Price.find({})
                .sort({ currentPrice: 1 }) // Sort by currentPrice in ascending order
                .skip(skipCount)
                .limit(1); // Limit to one document
        })
        .then(priceList => {
            if (!priceList || priceList.length === 0) {
                return res.status(404).json({ message: "Price not found" });
            }
            res.status(200).json({ currentPrice: priceList[0].currentPrice });
        })
        .catch(error => {
            res.status(500).json({
                message: "Something went wrong",
                error
            });
        });
};
