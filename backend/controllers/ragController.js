const { BedrockAgentRuntimeClient, RetrieveAndGenerateCommand } = require('@aws-sdk/client-bedrock-agent-runtime');
const Redis = require('ioredis');

// Constants
const MAX_HISTORY_LENGTH = 3;
const REDIS_EXPIRATION_SECONDS = 7 * 24 * 60 * 60; // 7 days
const KNOWLEDGE_BASE_ID = "6SV0EAXXTA";
const MODEL_ARN = "arn:aws:bedrock:us-east-1:637423440398:inference-profile/us.deepseek.r1-v1:0";

// Default values for environment variables
const DEFAULT_REDIS_HOST = 'localhost';
const DEFAULT_REDIS_PORT = 6379;
const DEFAULT_AWS_REGION = 'us-east-1';

// Validate and get environment variables with fallbacks
const getEnvVar = (key, defaultValue) => {
    const value = process.env[key];
    if (!value && defaultValue === undefined) {
        console.warn(`Warning: ${key} environment variable is not set`);
    }
    return value || defaultValue;
};

const REDIS_CONFIG = { 
    host: getEnvVar('REDIS_HOST', DEFAULT_REDIS_HOST),
    port: parseInt(getEnvVar('REDIS_PORT', DEFAULT_REDIS_PORT)),
    password: process.env.REDIS_PASSWORD,
    lazyConnect: true, // Changed to true to prevent connection errors on startup
    connectTimeout: 17000,
    maxRetriesPerRequest: 4,
    retryStrategy: (times) => Math.min(times * 30, 1000),
    reconnectOnError: (error) => {
        const targetErrors = [/READONLY/, /ETIMEDOUT/];
        return targetErrors.some(targetError => targetError.test(error.message));
    }
};

// Helper function to generate unique user ID
const generateUniqueUserId = (baseUserId) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${baseUserId}_${timestamp}`;
};

// Validate AWS credentials
const validateAwsCredentials = () => {
    const requiredEnvVars = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY',
        'AWS_REGION'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.warn(`Missing AWS environment variables: ${missingVars.join(', ')}`);
        return false;
    }
    return true;
};

// Initialize AWS client with credentials
const initializeAwsClient = () => {
    if (!validateAwsCredentials()) {
        console.warn('AWS credentials not properly configured. Some features may be limited.');
        return null;
    }

    try {
        return new BedrockAgentRuntimeClient({
            region: getEnvVar('AWS_REGION', DEFAULT_AWS_REGION),
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
            }
        });
    } catch (error) {
        console.error('Error initializing AWS client:', error);
        return null;
    }
};

// Initialize AWS and Redis
let bedrockClient;
let redis;

const initializeServices = () => {
    try {
        bedrockClient = initializeAwsClient();
        redis = new Redis(REDIS_CONFIG);

        // Add error handling
        redis.on('error', (error) => {
            console.error('Redis connection error:', error);
        });

        redis.on('connect', () => {
            console.log('Successfully connected to Redis');
        });

        redis.on('ready', () => {
            console.log('Redis client is ready');
        });

        redis.on('end', () => {
            console.log('Redis connection ended');
        });

        redis.on('reconnecting', () => {
            console.log('Redis client reconnecting...');
        });

        console.log("Initializing Redis connection...");
    } catch (error) {
        console.error('Initialization error:', error.message);
        // Don't exit the process, let the application continue
    }
};

// Initialize services
initializeServices();

// Redis-based chat history functions with fallback
const getChatHistory = async (userId) => {
    try {
        if (!redis || !redis.status === 'ready') {
            console.warn('Redis not available, returning empty history');
            return [];
        }
        const raw = await redis.get(`history:${userId}`);
        if (!raw) return [];
        const parsed = JSON.parse(raw);
        return parsed;
    } catch (err) {
        console.error(`Redis get error for user ${userId}:`, err);
        return [];
    }
};

const addMessageToHistory = async (userId, message) => {
    try {
        if (!redis || !redis.status === 'ready') {
            console.warn('Redis not available, skipping history update');
            return userId;
        }
        let history = await getChatHistory(userId);
        history.push(message);
        if (history.length > MAX_HISTORY_LENGTH) {
            history = history.slice(-MAX_HISTORY_LENGTH);
        }
        await redis.set(`history:${userId}`, JSON.stringify(history), 'EX', REDIS_EXPIRATION_SECONDS);
        return userId;
    } catch (err) {
        console.error(`Redis set error for user ${userId}:`, err);
        return userId; // Return userId even if Redis operation fails
    }
};

// Format chat for model input
const formatConversation = (messages) => {
    return messages.map(msg => ({
        role: msg.role,
        content: msg.message
    }));
};

const askQuestion = async (req, res) => {
    try {
        const { userId, message } = req.body;

        if (!userId || !message) {
            return res.status(400).json({
                error: 'Missing required fields: userId and message'
            });
        }

        if (!bedrockClient) {
            return res.status(503).json({
                error: 'AWS Bedrock service is currently unavailable',
                message: 'Please try again later'
            });
        }

        // Check if this is the first message for this user
        const history = await getChatHistory(userId);
        const isNewUser = history.length === 0;
        
        // Generate unique ID only for new users
        const finalUserId = isNewUser ? generateUniqueUserId(userId) : userId;
        
        const userMessage = { role: 'user', message };
        let currentHistory = [];
        
        // For new users, we need to store the first message with the new unique ID
        if (isNewUser) {
            // Initialize history with the first message
            currentHistory = [userMessage];
            // Store first message with new unique ID
            await redis.set(`history:${finalUserId}`, JSON.stringify(currentHistory), 'EX', REDIS_EXPIRATION_SECONDS);
            
            const conversation = formatConversation(currentHistory);

            const command = new RetrieveAndGenerateCommand({
                input: { text: message },
                retrieveAndGenerateConfiguration: {
                    type: "KNOWLEDGE_BASE",
                    knowledgeBaseConfiguration: {
                        knowledgeBaseId: KNOWLEDGE_BASE_ID,
                        modelArn: MODEL_ARN,
                        retrievalConfiguration: {
                            vectorSearchConfiguration: {
                                numberOfResults: 3
                            }
                        },
                        generationConfiguration: {
                            promptTemplate: {
                                textPromptTemplate: `You are Black Panther Support, the official AI assistant of Black Panther Token — a cutting-edge Web3 project. Your goal is to assist visitors in understanding our mission, how our token works, and how they can get involved or learn more.

Always answer using only the information available in our company's knowledge base. Do not make up facts or provide speculative investment advice.

Your main tasks include:
1. Explaining the Black Panther Token project, including our vision, use cases, roadmap, and tokenomics.
2. Guiding users on how to set up a wallet to use our platform.
3. Helping developers or partners understand how to integrate with us.
4. Offering suggestions to join our community channels and follow us on social platforms.
5. Suggesting specific links or pages from the knowledge base whenever possible.
6. Answering technical questions about smart contracts, staking, NFTs, and the Web3 ecosystem as they relate to Black Panther Token.

When the user asks something outside your scope (e.g., financial advice or speculation), gently explain that you're focused only on official project support.

Your style should be:
- Friendly, respectful, and proactive
- Enthusiastic but professional
- Clear and concise
- Encouraging for beginners (avoid overly technical terms unless requested)
- Short greetings (max 30 words)
- Do not 

DO NOT:
- Add emojis to your response

Use simple language when helping non-technical users.

Replace Token $PNTHRQ with $BPNTHRQ (Accepted Acronym)

Context from knowledge base:
$search_results$

Conversation History:
{{conversation}}

User: {{input}}

Assistant:`
                            }
                        }
                    }
                }
            });

            console.log('Sending request to Bedrock with command:', JSON.stringify(command, null, 2));

            const response = await bedrockClient.send(command);
            // console.log('Received response from Bedrock:', JSON.stringify(response, null, 2));

            const assistantMessage = response.output?.text;
            const assistantMsg = { role: 'assistant', message: assistantMessage };
            
            // Add assistant message to history
            currentHistory.push(assistantMsg);
            // Update Redis with both messages
            await redis.set(`history:${finalUserId}`, JSON.stringify(currentHistory), 'EX', REDIS_EXPIRATION_SECONDS);

            return res.json({ 
                message: assistantMessage, 
                // history: currentHistory,
                userId: finalUserId
            });
        } else {
            // For existing users, proceed as before
            currentHistory = [...history, userMessage];
            await redis.set(`history:${finalUserId}`, JSON.stringify(currentHistory), 'EX', REDIS_EXPIRATION_SECONDS);

            const conversation = formatConversation(currentHistory);

            const command = new RetrieveAndGenerateCommand({
                input: { text: message },
                retrieveAndGenerateConfiguration: {
                    type: "KNOWLEDGE_BASE",
                    knowledgeBaseConfiguration: {
                        knowledgeBaseId: KNOWLEDGE_BASE_ID,
                        modelArn: MODEL_ARN,
                        retrievalConfiguration: {
                            vectorSearchConfiguration: {
                                numberOfResults: 3
                            }
                        },
                        generationConfiguration: {
                            promptTemplate: {
                                textPromptTemplate: `You are Black Panther Support, the official AI assistant of Black Panther Token — a cutting-edge Web3 project. Your goal is to assist visitors in understanding our mission, how our token works, and how they can get involved or learn more.

Always answer using only the information available in our company's knowledge base. Do not make up facts or provide speculative investment advice.

Your main tasks include:
1. Explaining the Black Panther Token project, including our vision, use cases, roadmap, and tokenomics.
2. Guiding users on how to set up a wallet to use our platform.
3. Helping developers or partners understand how to integrate with us.
4. Offering suggestions to join our community channels and follow us on social platforms.
5. Suggesting specific links or pages from the knowledge base whenever possible.
6. Answering technical questions about smart contracts, staking, NFTs, and the Web3 ecosystem as they relate to Black Panther Token.

When the user asks something outside your scope (e.g., financial advice or speculation), gently explain that you're focused only on official project support.

Your style should be:
- Friendly, respectful, and proactive
- Enthusiastic but professional
- Clear and concise
- Encouraging for beginners (avoid overly technical terms unless requested)
- Short greetings (max 30 words)


DO NOT:
- Add emojis to your response

Use simple language when helping non-technical users.

Context from knowledge base:
$search_results$

Conversation History:
{{conversation}}

User: {{input}}

Assistant:`
                            }
                        }
                    }
                }
            });

            // console.log('Sending request to Bedrock with command:', JSON.stringify(command, null, 2));

            const response = await bedrockClient.send(command);
            // console.log('Received response from Bedrock:', JSON.stringify(response, null, 2));

            const assistantMessage = response.output?.text;
            const assistantMsg = { role: 'assistant', message: assistantMessage };
            
            // Add assistant message to history
            currentHistory.push(assistantMsg);
            // Update Redis with the new history
            await redis.set(`history:${finalUserId}`, JSON.stringify(currentHistory), 'EX', REDIS_EXPIRATION_SECONDS);

            return res.json({ 
                message: assistantMessage, 
                // history: currentHistory,
                userId: finalUserId
            });
        }
    } catch (error) {
        console.error('Error in askQuestion:', error);
        if (error.name === 'CredentialsProviderError') {
            return res.status(503).json({ 
                error: 'AWS credentials not properly configured',
                message: 'Please try again later'
            });
        }
        return res.status(500).json({ 
            error: 'Internal server error', 
            message: 'Please try again later',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

const getSuggestions = async (req, res) => {
    try {
        const { userId } = req.query;

        if (!userId) {
            return res.status(400).json({ error: 'Missing required parameter: userId' });
        }

        const history = await getChatHistory(userId);
        if (history.length < 3) {
            return res.json({ 
                suggestions: [
                    "What is Black Panther Token?",
                    "How can I get started with Black Panther Token?",
                    "What are the main features of Black Panther Token?"
                ]
            });
        }

        // Get last 3 user messages to understand context
        const userMessages = history
            .filter(msg => msg.role === 'user')
            .slice(-3)
            .map(msg => msg.message)
            .join('\n');

        const conversation = formatConversation(history);
        
        const command = new RetrieveAndGenerateCommand({
            input: { 
                text: `Based on the user's recent messages and conversation history, generate exactly 3 follow-up questions that would be most relevant to their interests. Return only the questions in a simple array format, one per line, with no additional text or formatting. Question should not exceed 30 words and generate questions based on user level (in block chain or your niche, e.g by beginner, intermediate and advanced) based on the chat history`
            },
            retrieveAndGenerateConfiguration: {
                type: "KNOWLEDGE_BASE",
                knowledgeBaseConfiguration: {
                    knowledgeBaseId: KNOWLEDGE_BASE_ID,
                    modelArn: MODEL_ARN,
                    retrievalConfiguration: {
                        vectorSearchConfiguration: {
                            numberOfResults: 3
                        }
                    },
                    generationConfiguration: {
                        promptTemplate: {
                            textPromptTemplate: `You are Black Panther Support. Generate exactly 3 follow-up questions based on the user's interests and conversation context.

Recent User Messages:
${userMessages}

Conversation History:
{{conversation}}

Context from knowledge base:
$search_results$

Instructions:
1. Analyze the user's recent messages and conversation history
2. Identify their main interests and topics
3. Generate exactly 3 relevant follow-up questions
4. Return ONLY the questions, one per line
5. Do not include any additional text, formatting, or explanations
6. Do not number the questions
7. Do not use markdown or special characters
8. Use not more than one emoji if you have to use them atall

Questions:`
                        }
                    }
                }
            }
        });

        const response = await bedrockClient.send(command);
        
        // Parse the response to extract suggestions
        const suggestions = response.output?.text
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .slice(0, 3);

        return res.json({ suggestions });
    } catch (error) {
        console.error('Error in getSuggestions:', error);
        if (error.name === 'CredentialsProviderError') {
            return res.status(500).json({ error: 'AWS credentials not properly configured' });
        }
        return res.status(500).json({ error: 'Internal server error', details: error.message });
    }
};

const getHistory = async (req, res) => {
    try {
        const { userId } = req.query;

        if (!userId) {
            return res.status(400).json({ error: 'Missing required parameter: userId' });
        }

        const history = await getChatHistory(userId);
        return res.json({ history });
    } catch (error) {
        console.error('Error in getHistory:', error);
        return res.status(500).json({ error: 'Internal server error', details: error.message });
    }
};

module.exports = {
    askQuestion,
    getSuggestions,
    getHistory
};
