const CommunityTransfer = require('../models/communityTransfer');
const Community = require('../models/community');
const User = require('../models/user');
const sgMail = require("@sendgrid/mail");
const crypto = require('crypto');
const { communityTransferEmail } = require("../public/html files/communityTransferEmail");
require("dotenv").config();

sgMail.setApiKey(process.env.SENDGRID_API);

exports.initiateTransfer = async (req, res) => {
  try {
    const { communityId, newOwnerId } = req.body;
    // Support both req.user and req.userEmailAndId
    const currentUserId = req.user?._id || req.user?.id || req.userEmailAndId?.userId;
    if (!currentUserId) {
      return res.status(401).json({ message: "Unauthorized: user not found in request." });
    }

    // Verify current user is community admin
    const community = await Community.findOne({
      _id: communityId,
      communityAdmin: currentUserId
    });

    if (!community) {
      return res.status(403).json({
        message: "You don't have permission to transfer this community"
      });
    }

    // Verify new owner is a community member
    const isMember = community.registeredMembers.includes(newOwnerId);
    if (!isMember) {
      return res.status(400).json({
        message: "Selected user must be a community member"
      });
    }

    // Generate transfer token and set expiry
    const transferToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

    // Create transfer record
    const transfer = await CommunityTransfer.create({
      communityId,
      currentOwner: currentUserId,
      newOwner: newOwnerId,
      transferToken,
      expiresAt,
      status: 'pending'
    });

    // Fetch new owner details
    const newOwner = await User.findById(newOwnerId);
    if (!newOwner) {
      await CommunityTransfer.findByIdAndDelete(transfer._id);
      return res.status(404).json({ message: "New owner user not found" });
    }

    // Prepare email
    const acceptUrl = `${process.env.BP_FrontEndURL || 'https://www.staging.blackpanthertkn.com'}/community/transfer/accept?token=${transferToken}`;
    const html = communityTransferEmail({
      firstName: newOwner.firstName,
      communityName: community.communityName,
      acceptUrl,
      expiresAt
    });

    const msg = {
      to: {
        email: newOwner.email,
        name: `${newOwner.firstName} ${newOwner.lastName}`
      },
      from: {
        email: "<EMAIL>",
        name: "Black Panther DAO"
      },
      subject: `Community Ownership Transfer - ${community.communityName}`,
      html,
      replyTo: "<EMAIL>",
      headers: {
        "List-Unsubscribe": "<mailto:<EMAIL>>"
      },
      mailSettings: {
        sandboxMode: { enable: false }
      },
      trackingSettings: {
        clickTracking: { enable: false },
        openTracking: { enable: false }
      }
    };

    // Send email
    try {
      await sgMail.send(msg);
    } catch (emailError) {
      await CommunityTransfer.findByIdAndDelete(transfer._id);
      console.error('Transfer email error:', emailError);
      return res.status(500).json({ message: "Failed to send transfer email", error: emailError.message });
    }

    res.status(200).json({
      message: "Transfer initiated successfully",
      transfer: {
        id: transfer._id,
        expiresAt: transfer.expiresAt
      }
    });

  } catch (error) {
    console.error('Transfer initiation error:', error);
    res.status(500).json({
      message: "Failed to initiate transfer",
      error: error.message
    });
  }
};

exports.acceptTransfer = async (req, res) => {
  try {
    const { transferToken } = req.body;
    const currentUserId = req.user?._id || req.user?.id || req.userEmailAndId?.userId;

    // Find valid transfer request
    const transfer = await CommunityTransfer.findOne({
      transferToken,
      status: 'pending',
      newOwner: currentUserId,
      expiresAt: { $gt: new Date() }
    });

    if (!transfer) {
      return res.status(400).json({
        message: "Invalid or expired transfer token"
      });
    }

    // Update community ownership (make new owner the admin)
    await Community.findByIdAndUpdate(transfer.communityId, {
      communityAdmin: currentUserId
    });

    // Update transfer status
    transfer.status = 'accepted';
    await transfer.save();

    res.status(200).json({
      message: "Community ownership transferred successfully"
    });

  } catch (error) {
    console.error('Transfer acceptance error:', error);
    res.status(500).json({
      message: "Failed to complete transfer",
      error: error.message
    });
  }
};

exports.getPendingTransfers = async (req, res) => {
  try {
    const currentUserId = req.user?._id || req.user?.id || req.userEmailAndId?.userId;

    // Find pending transfers for the current user
    const pendingTransfers = await CommunityTransfer.find({
      newOwner: currentUserId,
      status: 'pending',
      expiresAt: { $gt: new Date() }
    })
    .populate('communityId', 'communityName communityDescription')
    .populate('currentOwner', 'firstName lastName email')
    .sort({ createdAt: -1 });

    res.status(200).json({
      transfers: pendingTransfers
    });

  } catch (error) {
    console.error('Get pending transfers error:', error);
    res.status(500).json({
      message: "Failed to fetch pending transfers",
      error: error.message
    });
  }
};

exports.validateTransfer = async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.status(400).json({
        message: "Transfer token is required"
      });
    }

    // Find the transfer request
    const transfer = await CommunityTransfer.findOne({
      transferToken: token,
      status: 'pending',
      expiresAt: { $gt: new Date() }
    })
    .populate('communityId', 'communityName communityDescription')
    .populate('currentOwner', 'firstName lastName email')
    .populate('newOwner', 'firstName lastName email');

    if (!transfer) {
      return res.status(400).json({
        message: "Invalid or expired transfer token"
      });
    }

    res.status(200).json({
      communityName: transfer.communityId.communityName,
      communityDescription: transfer.communityId.communityDescription,
      currentOwnerName: `${transfer.currentOwner.firstName} ${transfer.currentOwner.lastName}`,
      newOwnerName: `${transfer.newOwner.firstName} ${transfer.newOwner.lastName}`,
      expiresAt: transfer.expiresAt,
      transferId: transfer._id
    });

  } catch (error) {
    console.error('Validate transfer error:', error);
    res.status(500).json({
      message: "Failed to validate transfer",
      error: error.message
    });
  }
};