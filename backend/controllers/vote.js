const Vote = require("../models/vote");
const mongoose = require('mongoose');
const Project = require("../models/project");

const DEFAULT_POINT_SYSTEM = { "1": 5, "2": 3, "3": 2, "4": 1, "5": 1 };

exports.CreateVote = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const {
      votingType,
      startDate,
      endDate,
      totalFund,
      voteTitle,
      fundingAllocation,
      fundingDistribution,
      numberOfWinningProjects,
      projects: projectData
    } = req.body;

    // Use either totalFund or fundingAllocation for backward compatibility
    const fund = totalFund || fundingAllocation;
    const projects = typeof projectData === 'string' ? JSON.parse(projectData) : projectData;

    // Validate required fields
    if (!votingType || !startDate || !endDate || !fund) {
      return res.status(400).json({ message: "Missing required fields" });
    }
    if (!Array.isArray(projects) || projects.length === 0) {
      return res.status(400).json({ message: "At least one project is required" });
    }
    if (votingType === 'ranked' && projects.length < 2) {
      return res.status(400).json({ message: "Ranked voting requires at least 2 projects" });
    }
    if (votingType === 'multi') {
      if (!fundingDistribution) {
        return res.status(400).json({ message: "fundingDistribution is required for multi voting" });
      }
      if (!numberOfWinningProjects || Number(numberOfWinningProjects) < 1) {
        return res.status(400).json({ message: "numberOfWinningProjects is required and must be at least 1 for multi voting" });
      }
    }

    // 1. Create the vote first (without projects)
    const now = new Date();
    const start = new Date(startDate);
    let status = 'active';
    if (start > now) {
      status = 'pending';
    }
    const voteData = {
      votingType,
      startDate,
      endDate,
      totalFund: fund,
      voteTitle,
      status, // <-- use computed status
    };
    if (votingType === 'multi') {
      voteData.fundingDistribution = fundingDistribution;
      voteData.numberOfWinningProjects = numberOfWinningProjects;
    } else if (votingType === 'ranked') {
      voteData.pointSystem = DEFAULT_POINT_SYSTEM;
    }

    // Create the vote document (empty projects for now)
    const vote = await Vote.create([voteData], { session });
    const voteId = vote[0]._id;

    // 2. Create projects with voteId and fundingAllocation
    const fileUrls = req.body.fileUrl || [];
const createdProjects = await Promise.all(projects.map(async (project, index) => {
  // Use the file at the same index as the project
  const imageUrl = fileUrls[index]?.url || project.image || undefined;
  return await Project.create([{
    title: project.title,
    description: project.description,
    image: imageUrl,
    voteId: voteId,
    fundingAllocation: fund
  }], { session });
}));

    // Flatten createdProjects (since each is an array)
    const projectIds = createdProjects.map(arr => arr[0]._id);

    // Validate project count for ranked voting
    if (votingType === 'ranked' && projectIds.length < 2) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "Ranked voting requires at least 2 projects" });
    }

    // 3. Update the vote with the project IDs
    vote[0].projects = projectIds;
    await vote[0].save({ session });

    await session.commitTransaction();
    session.endSession();

    res.status(201).json({
      message: "Vote created successfully",
      voteId: voteId
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    console.error("Error creating vote:", error);

    if (error.name === 'ValidationError') {
      const errors = {};
      for (let field in error.errors) {
        errors[field] = error.errors[field].message;
      }
      return res.status(400).json({
        message: "Vote validation failed",
        errors,
        raw: error 
      });
    }

    res.status(500).json({
      message: "Internal Server Error",
      error: error.message,
      stack: error.stack 
    });
  }
};

// Call this at the start of GetAllVote, GetActiveVotes, etc.
async function activatePendingVotes() {
  const now = new Date();
  await Vote.updateMany(
    { status: 'pending', startDate: { $lte: now } },
    { $set: { status: 'active' } }
  );
}

exports.GetVote = async (req, res) => {
  activatePendingVotes(); // Ensure pending votes are activated before fetching
  try {
    const voteId = req.query.id;
    if (!voteId) {
      return res.status(400).json({ message: "Vote ID is required" });
    }

    const vote = await Vote.findById(voteId).populate("projects");
    if (!vote) {
      return res.status(404).json({ message: "Vote not found" });
    }

    res.status(200).json(vote);
  } catch (error) {
    console.error("Error fetching vote:", error);
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

exports.GetAllVote = async (req, res) => {
  activatePendingVotes(); // Ensure pending votes are activated before fetching
  try {
    const votes = await Vote.find().populate("projects");

    if (!votes || votes.length === 0) {
      return res.status(404).json({ message: "No votes found." });
    }

    // Filter project fields based on votingType
    const filteredVotes = votes.map(vote => {
      const votingType = vote.votingType;
      const filteredProjects = vote.projects.map(project => {
        const p = project.toObject();
        if (votingType === 'multi') {
          delete p.points;
          delete p.rankings;
        } else if (votingType === 'ranked') {
          delete p.voteCount;
          delete p.percentages;
        }
        return p;
      });
      return {
        ...vote.toObject(),
        projects: filteredProjects
      };
    });

    return res.status(200).json({
      message: "Votes retrieved successfully.",
      votes: filteredVotes,
    });
  } catch (error) {
    console.error("Error retrieving votes:", error);
    return res.status(500).json({
      message: "Failed to retrieve votes.",
      error: error.message,
    });
  }
};

exports.submitMultiVote = async (req, res) => {
  try {
    const { voteId } = req.params;
    const { projectId, vote } = req.body;
    const userId = req.userEmailAndId?.userId;

    if (!voteId || !mongoose.Types.ObjectId.isValid(voteId)) {
      return res.status(400).json({ error: 'Invalid or missing voteId.' });
    }
    if (!projectId || !mongoose.Types.ObjectId.isValid(projectId)) {
      return res.status(400).json({ error: 'Invalid or missing projectId.' });
    }
    if (!userId) {
      return res.status(401).json({ error: 'User authentication required.' });
    }
    if (vote !== 'yes' && vote !== 'no' && vote !== null && vote !== undefined) {
      return res.status(400).json({ error: 'Invalid vote option.' });
    }

    const voteDoc = await Vote.findById(voteId);
    if (!voteDoc || voteDoc.votingType !== 'multi') {
      return res.status(404).json({ error: 'Multi-vote not found.' });
    }

    const now = new Date();
    if (voteDoc.status !== 'active' || now < voteDoc.startDate || now > voteDoc.endDate) {
      return res.status(400).json({ error: 'Voting is not active.' });
    }

    if (!voteDoc.projects.map(id => id.toString()).includes(projectId)) {
      return res.status(400).json({ error: 'Project is not part of this vote.' });
    }

    const project = await Project.findById(projectId);
    if (!project) {
      return res.status(404).json({ error: 'Project not found.' });
    }

    if (!Array.isArray(project.voters)) project.voters = [];

    const existingVote = project.voters.find(v => v.userId.toString() === userId);

    let newUserVote = vote;

    if (existingVote) {
      if (vote === null || vote === undefined) {
        // Deselect (remove vote)
        project.voters = project.voters.filter(v => v.userId.toString() !== userId);
        newUserVote = null;
      } else if (existingVote.vote === vote) {
        // Already voted same, treat as deselect
        project.voters = project.voters.filter(v => v.userId.toString() !== userId);
        newUserVote = null;
      } else {
        // Switch vote
        existingVote.vote = vote;
        newUserVote = vote;
      }
    } else if (vote === 'yes' || vote === 'no') {
      // First time voting
      project.voters.push({ userId, vote });
      newUserVote = vote;
    } else {
      newUserVote = null;
    }

    // Always recalculate from all voters
    project.voteCount.yes = project.voters.filter(v => v.vote === 'yes').length;
    project.voteCount.no = project.voters.filter(v => v.vote === 'no').length;

    await project.save();

    const total = project.voteCount.yes + project.voteCount.no;
    const yesPercent = total ? Math.round((project.voteCount.yes / total) * 100) : 0;
    const noPercent = total ? 100 - yesPercent : 0;

    res.status(200).json({
      message: 'Vote processed.',
      projectId: project._id,
      voteCount: project.voteCount,
      userVote: newUserVote,
      percentages: { yes: yesPercent, no: noPercent }
    });
  } catch (error) {
    console.error('Error submitting multi-vote:', error);
    res.status(500).json({ error: 'Internal server error.' });
  }
};


exports.GetActiveVotes = async (req, res) => {
  try {
    // 1. Mark expired votes as completed
    const now = new Date();
    await Vote.updateMany(
      { status: "active", endDate: { $lt: now } },
      { $set: { status: "completed" } }
    );

    // 2. Fetch all active votes and populate projects
    const votes = await Vote.find({
      status: "active",
      isDeleted: false,
      endDate: { $gte: now }
    }).populate("projects");

    if (!votes || votes.length === 0) {
      return res.status(404).json({
        message: "No active votes found.",
      });
    }

    // Filter project fields based on votingType
    const filteredVotes = votes.map(vote => {
      const votingType = vote.votingType;
      const filteredProjects = vote.projects.map(project => {
        const p = project.toObject();
        if (votingType === 'multi') {
          delete p.points;
          delete p.rankings;
        } else if (votingType === 'ranked') {
          delete p.voteCount;
          delete p.percentages;
        }
        return p;
      });
      return {
        ...vote.toObject(),
        projects: filteredProjects
      };
    });

    return res.status(200).json({
      message: "Active votes retrieved successfully.",
      votes: filteredVotes,
    });
  } catch (error) {
    console.error("Error retrieving active votes:", error);
    return res.status(500).json({
      message: "Failed to retrieve active votes.",
      error: error.message,
    });
  }
};

exports.EndVoteEarly = async (req, res) => {
  try {
    const { voteId } = req.body;

    // Validate input
    if (!voteId || !voteId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: "Valid Vote ID is required." });
    }

    // Find the vote
    const vote = await Vote.findById(voteId);
    if (!vote) {
      return res.status(404).json({ message: "Vote not found." });
    }

    if (vote.status === 'completed') {
      return res.status(400).json({ message: "Vote is already completed." });
    }

    // Set status to completed and endDate to now
    vote.status = 'completed';
    vote.endDate = new Date();
    await vote.save();

    res.status(200).json({ message: "Vote ended successfully.", vote });
  } catch (error) {
    console.error("Error ending vote early:", error);
    res.status(500).json({ message: "Failed to end vote.", error: error.message });
  }
};

exports.GetEndedVotes = async (req, res) => {
  try {
    // Fetch all votes with status 'completed' and not deleted
    const votes = await Vote.find({ status: 'completed', isDeleted: false }).populate("projects");

    if (!votes || votes.length === 0) {
      return res.status(404).json({ message: "No ended votes found." });
    }

    // Filter project fields based on votingType
    const filteredVotes = votes.map(vote => {
      const votingType = vote.votingType;
      const filteredProjects = vote.projects.map(project => {
        const p = project.toObject();
        if (votingType === 'multi') {
          delete p.points;
          delete p.rankings;
        } else if (votingType === 'ranked') {
          delete p.voteCount;
          delete p.percentages;
        }
        return p;
      });
      return {
        ...vote.toObject(),
        projects: filteredProjects
      };
    });

    res.status(200).json({ message: "Ended votes retrieved successfully.", votes: filteredVotes });
  } catch (error) {
    console.error("Error retrieving ended votes:", error);
    res.status(500).json({ message: "Failed to retrieve ended votes.", error: error.message });
  }
};

exports.submitRankedVote = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const { voteId } = req.params;
    const { rankings } = req.body;
    const userId = req.userEmailAndId?.userId;

    // Validate input
    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ success: false, error: 'Invalid or missing user ID.' });
    }
    if (!Array.isArray(rankings)) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ success: false, error: 'Rankings must be an array.' });
    }

    // Fetch vote with session
    const vote = await Vote.findById(voteId).session(session);
    if (!vote) {
      await session.abortTransaction();
      session.endSession();
      return res.status(404).json({ success: false, error: 'Vote not found.' });
    }
    if (vote.votingType !== 'ranked') {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ success: false, error: 'Not a ranked vote.' });
    }
    const now = new Date();
    if (vote.status !== 'active' || now < vote.startDate || now > vote.endDate) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ success: false, error: 'Voting is not active.' });
    }

    // Remove all previous rankings for this user from all projects in this vote
    await Project.updateMany(
      { _id: { $in: vote.projects } },
      { $pull: { rankings: { userId: userId } } },
      { session }
    );

    // Add new rankings (if any)
    for (const ranking of rankings) {
      await Project.findByIdAndUpdate(
        ranking.projectId,
        { $push: { rankings: { userId, rank: ranking.rank } } },
        { session }
      );
    }

    // Recalculate points for all projects in this vote
    if (typeof vote.calculateResults === "function") {
      await vote.calculateResults(session);
    }

    await session.commitTransaction();
    session.endSession();

    // Fetch updated projects for response
    const updatedProjects = await Project.find({ _id: { $in: vote.projects } });

    return res.status(200).json({
      success: true,
      message: 'Ranked vote submitted successfully',
      projects: updatedProjects
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    return res.status(500).json({ success: false, error: error.message });
  }
};

exports.extendVote = async (req, res) => {
  try {
    const voteId = req.params.id;

    // Validate vote ID
    if (!voteId || !voteId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: "Invalid vote ID" });
    }

    // Fetch vote document
    const vote = await Vote.findById(voteId);
    if (!vote) {
      return res.status(404).json({ message: "Vote not found" });
    }

    // Check vote status
    if (vote.status !== 'active') {
      return res.status(400).json({ message: "Only active votes can be extended" });
    }

    // Check current voting duration limit
    const start = new Date(vote.startDate);
    const end = new Date(vote.endDate);
    const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

    if (diffDays >= 14) {
      return res.status(400).json({ message: "Cannot extend vote beyond 14 days" });
    }

    // Calculate new end date
    const newEndDate = new Date(end.getTime() + 24 * 60 * 60 * 1000); // add 24 hours

    // Ensure the new duration does not exceed 14 days
    const totalDurationDays = Math.ceil((newEndDate - start) / (1000 * 60 * 60 * 24));
    if (totalDurationDays > 14) {
      return res.status(400).json({ 
        message: "Extension would exceed the 14-day voting limit" 
      });
    }

    // Update and save
    vote.endDate = newEndDate;
    await vote.save();

    res.status(200).json({
      message: "Vote duration extended by 24 hours",
      voteId: vote._id,
      newEndDate: vote.endDate
    });
  } catch (error) {
    console.error("Error extending vote:", error);
    res.status(500).json({
      message: "Internal Server Error",
      error: error.message
    });
  }
};


/**
 * PATCH /api/votes/:id/dates
 * Allows editing startDate and endDate for pending votes only.
 * Expects { startDate, endDate } in req.body.
 */
exports.UpdateVoteDates = async (req, res) => {
  try {
    const voteId = req.params.id;
    const { startDate, endDate } = req.body;

    // Validate voteId
    if (!voteId || !voteId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: "Invalid vote ID." });
    }

    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ message: "Both startDate and endDate are required." });
    }
    const newStart = new Date(startDate);
    const newEnd = new Date(endDate);
    if (isNaN(newStart.getTime()) || isNaN(newEnd.getTime())) {
      return res.status(400).json({ message: "Invalid date format." });
    }
    if (newEnd <= newStart) {
      return res.status(400).json({ message: "End date must be after start date." });
    }
    const maxDurationMs = 14 * 24 * 60 * 60 * 1000;
    if ((newEnd - newStart) > maxDurationMs) {
      return res.status(400).json({ message: "Voting period cannot exceed 14 days." });
    }

    // Fetch vote
    const vote = await Vote.findById(voteId);
    if (!vote) {
      return res.status(404).json({ message: "Vote not found." });
    }

    // Only allow editing if vote is pending
    if (vote.status !== 'pending') {
      return res.status(400).json({ message: "Only pending votes can be edited." });
    }

    // Update and save
    vote.startDate = newStart;
    vote.endDate = newEnd;
    await vote.save();

    res.status(200).json({
      message: "Vote dates updated successfully.",
      vote: {
        id: vote.id,
        startDate: vote.startDate,
        endDate: vote.endDate,
        status: vote.status
      }
    });
  } catch (error) {
    console.error("Error updating vote dates:", error);
    res.status(500).json({ message: "Failed to update vote dates.", error: error.message });
  }
};




exports.StartProject = async (req, res) => {
  try {
    const { voteId } = req.body;
    if (!voteId) {
      return res.status(400).json({ message: "Vote ID is required." });
    }

    const vote = await Vote.findById(voteId);
    if (!vote) {
      return res.status(404).json({ message: "Vote not found." });
    }

    if (vote.isOngoing) {
      return res.status(400).json({ message: "Cannot start project for an ongoing vote." });
    }
    if (vote.projectStarted) {
      return res.status(400).json({ message: "This project has already been started." });
    }

    // Fetch all project documents for this vote
    const projects = await Project.find({ _id: { $in: vote.projects } });

    // Find the winning project (by highest voteCount.yes or points for ranked)
    let winningProject = projects[0];
    for (let i = 1; i < projects.length; i++) {
      const currentVotes = winningProject.voteCount?.yes ?? winningProject.points ?? 0;
      const challengerVotes = projects[i].voteCount?.yes ?? projects[i].points ?? 0;
      if (challengerVotes > currentVotes) {
        winningProject = projects[i];
      }
    }

    // Update the winning project's status to "ongoing"
    winningProject.status = "ongoing";
    winningProject.startDate = new Date();
    await winningProject.save();

    // Mark the vote as started
    vote.projectStarted = true;
    await vote.save();

    res.status(200).json({
      message: "Project marked as funded successfully.",
      project: winningProject,
      vote
    });
  } catch (error) {
    console.error("Error starting project:", error);
    res.status(500).json({
      message: "Failed to start project.",
      error: error.message
    });
  }
};

exports.updateProjectImage = async (req, res) => {
  try {
    const { id } = req.params;
    const fileUrls = req.body.fileUrl || [];
    const newImage = fileUrls[0]?.url || null;

    if (!newImage) {
      return res.status(400).json({ message: "No image uploaded." });
    }

    const project = await Project.findByIdAndUpdate(
      id,
      { image: newImage },
      { new: true }
    );

    if (!project) {
      return res.status(404).json({ message: "Project not found." });
    }

    res.status(200).json({
      message: "Project image updated successfully.",
      project
    });
  } catch (error) {
    console.error("Error updating project image:", error);
    res.status(500).json({
      message: "Failed to update project image.",
      error: error.message
    });
  }
};

exports.UpdateVoteDetails = async (req, res) => {
  try {
    const voteId = req.params.id;
    const {
      startDate,
      endDate,
      voteTitle,
      totalFund,
      fundingDistribution,
      numberOfWinningProjects
    } = req.body;

    // Validate voteId
    if (!voteId || !voteId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: "Invalid vote ID." });
    }

    // Fetch vote
    const vote = await Vote.findById(voteId);
    if (!vote) {
      return res.status(404).json({ message: "Vote not found." });
    }

    // Only allow editing for pending or active votes
    if (!['pending', 'active'].includes(vote.status)) {
      return res.status(400).json({ message: "Only pending or active votes can be edited." });
    }

    // Always allow editing voteTitle and totalFund
    if (voteTitle !== undefined) {
      if (typeof voteTitle !== 'string') {
        return res.status(400).json({ message: "Vote title must be a string." });
      }
      vote.voteTitle = voteTitle;
    }
    if (totalFund !== undefined) {
      if (typeof totalFund !== 'number' || totalFund < 0) {
        return res.status(400).json({ message: "Total fund must be a positive number." });
      }
      vote.totalFund = totalFund;
    }

    // Multi-vote specific fields
    if (vote.votingType === 'multi') {
      if (fundingDistribution !== undefined) {
        if (!['total', 'per_project'].includes(fundingDistribution)) {
          return res.status(400).json({ message: "Invalid funding distribution." });
        }
        vote.fundingDistribution = fundingDistribution;
      }
      if (numberOfWinningProjects !== undefined) {
        if (typeof numberOfWinningProjects !== 'number' || numberOfWinningProjects < 1) {
          return res.status(400).json({ message: "Number of winning projects must be at least 1." });
        }
        vote.numberOfWinningProjects = numberOfWinningProjects;
      }
    }

    // Only allow editing dates if pending
    if (vote.status === 'pending') {
      if (startDate !== undefined) {
        const newStart = new Date(startDate);
        if (isNaN(newStart.getTime())) {
          return res.status(400).json({ message: "Invalid start date." });
        }
        vote.startDate = newStart;
      }
      if (endDate !== undefined) {
        const newEnd = new Date(endDate);
        if (isNaN(newEnd.getTime())) {
          return res.status(400).json({ message: "Invalid end date." });
        }
        vote.endDate = newEnd;
      }
      // Validate date logic if both present
      if (vote.startDate && vote.endDate) {
        if (vote.endDate <= vote.startDate) {
          return res.status(400).json({ message: "End date must be after start date." });
        }
        const maxDurationMs = 14 * 24 * 60 * 60 * 1000;
        if ((vote.endDate - vote.startDate) > maxDurationMs) {
          return res.status(400).json({ message: "Voting period cannot exceed 14 days." });
        }
      }
    } else {
      // If active, do not allow editing start/end dates
      if (startDate !== undefined || endDate !== undefined) {
        return res.status(400).json({ message: "Cannot edit start/end dates for active votes." });
      }
    }

    await vote.save();

    res.status(200).json({
      message: "Vote updated successfully.",
      vote: {
        id: vote.id,
        voteTitle: vote.voteTitle,
        totalFund: vote.totalFund,
        fundingDistribution: vote.fundingDistribution,
        numberOfWinningProjects: vote.numberOfWinningProjects,
        startDate: vote.startDate,
        endDate: vote.endDate,
        status: vote.status,
        votingType: vote.votingType
      }
    });
  } catch (error) {
    console.error("Error updating vote details:", error);
    res.status(500).json({ message: "Failed to update vote.", error: error.message });
  }
};

