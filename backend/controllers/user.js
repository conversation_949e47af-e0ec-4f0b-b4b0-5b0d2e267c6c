const User = require("../models/user");
const MiniGame = require("../models/miniGame")
const jwt = require("jsonwebtoken");
const ResetCode = require("../models/resetCode");
const bcrypt = require("bcryptjs");
const sgMail = require("@sendgrid/mail");
require("dotenv").config();
sgMail.setApiKey(process.env.SENDGRID_API);
const { emailTemplate } = require("../public/html files/emailTemplate");
const { emailTemp } = require("../public/html files/emailTemp");
const {Contribution} = require("../models/contribution");
const communityAffiliate = require("../models/communityAffiliate")


// Generate a random random number between 10000 to 99999
let random = (min = 10000, max = 99999) => {
    let dif = max - min;
    let rad = Math.random();
    rad = Math.floor(rad * dif);
    rad = rad + min;
    return rad;
  };
  
  // send a confirmation code 
  // Handles the generation and sending of the OTP
  exports.SendSignupConformationCode = async (req, res, next) => {
    try {
    
    const confirmationCode = random();
    const user = await User.findOne({ email: req.body.email });
  
    if (user) {
      return res.status(404).json({
        message: `user with the email ${req.body.email} is already registered.`,
      });
    }
  

    const emailContent = emailTemplate({

      firstName: req.body.firstName,
      code: confirmationCode,
      type: "confirmation",
  });

  const content = `
      
  <p class="bodyText">Thank you for signing up on Black Panther. To complete your registration use the code below to verify your email</p>

      <p class="code">${confirmationCode}</p>

      <p class="bodySmallText">
        <span class="noteSpan">Note : </span>The Code expires upon page refreshing
      </p>

  `
  
    const msg = {
      to: req.body.email,
      from: "<EMAIL>",
      subject: "Email Confirmation Code from Black Panther",
      // html: confirmEmail(confirmationCode),
          // html: emailContent,
      html: emailTemp(content),
    };
      await sgMail.send(msg);


      await ResetCode.updateOne(
        { email: req.body.email, type: "signup" }, // Find document by email
        {
          $set: {
            code: confirmationCode,
            expirationDate: new Date(Date.now() + 20 * 60 * 1000), // 20 minutes
            type: "signup"
          },
        },
        { upsert: true } // Create a new document if not found
      );

      res.status(200).json({
        message: "Code sent successfully, check your email for the verification code.",
      });
    } catch (e) {
      console.error(e);
      res.status(500).json({
        message: "Failed to send email",
        error: e,
      });
    }
  }


  exports.SendSigninCode = async (req, res) => {
  try {
    const { email } = req.body;
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    const code = random();
    const content = `
      <p class="bodyText">To complete your login, use the code below to verify your email</p>
      <p class="code">${code}</p>
      <p class="bodySmallText">
        <span class="noteSpan">Note : </span>The Code expires in 20 minutes
      </p>
    `;
    const msg = {
      to: email,
      from: "<EMAIL>",
      subject: "Login Verification Code from Black Panther",
      html: emailTemp(content),
    };
    await sgMail.send(msg);

    await ResetCode.updateOne(
      { email, type: "signin" },
      {
        $set: {
          code,
          expirationDate: new Date(Date.now() + 20 * 60 * 1000),
          type: "signin",
          verified: false
        }
      },
      { upsert: true }
    );

    res.status(200).json({ message: "Signin code sent successfully." });
  } catch (e) {
    console.error("SendSigninCode error:", e); 
    res.status(500).json({ message: "Failed to send signin code", error: e });
  }
};

exports.SignUp = async (req, res, next) => {
  const email = sanitizeEmail(req.body.email);

  const verifiedCode = await ResetCode.findOne({ email, verified: true });
  if (!verifiedCode) {
    return res.status(400).json({
      message: "Please verify your email before signing up.",
    });
  }

  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return res.status(203).json({
      message: "Sorry! A user with this email already exists. Please login or reset your password.",
    });
  }

  const hash = await bcrypt.hash(req.body.password, 10);
  const user = new User({
    firstName: req.body.firstName,
    lastName: req.body.lastName,
    email,
    password: hash,
    country: req.body.country || null,
    walletAddress: req.body.walletAddress || null,
    role: "user",
  });

  try {
    await user.save();

    if (req.body.affiliateCode) {
      const affiliate = await communityAffiliate.findOne({ affiliateCode: req.body.affiliateCode });
      if (affiliate && !affiliate.referrals.includes(user._id)) {
        affiliate.referrals.push(user._id);
        await affiliate.save();
      }
    }

    res.status(201).json({ 
      message: "User created successfully",
      user: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName
      }
    });
  } catch (err) {
    res.status(500).json({
      message: "Failed to create user",
      error: err.message,
    });
  }
};


exports.SignIn = async (req, res, next) => {
  const email = sanitizeEmail(req.body.email);
  const { password, affiliateCode } = req.body;

  try {
    const user = await User.findOne({ email, isDeleted: false });
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Wrong username or password" });
    }

    const verifiedCode = await ResetCode.findOne({ email, verified: true, type: "signin" });
    if (!verifiedCode) {
      return res.status(400).json({
        message: "Please verify your email before signing in.",
      });
    }

    await ResetCode.deleteOne({ email, type: "signin" });

    if (affiliateCode) {
      const affiliate = await communityAffiliate.findOne({ affiliateCode });
      if (affiliate && !affiliate.referrals.includes(user._id)) {
        affiliate.referrals.push(user._id);
        await affiliate.save();
      }
    }

    const token = jwt.sign(
      {
        email: user.email,
        userId: user._id,
        role: user.role,
      },
      process.env.JWT_SECRET,
      { expiresIn: "12h" }
    );

    return res.status(200).json({
      token,
      expiresIn: 43200,
      userId: user._id,
      message: "Signed in successfully",
    });
  } catch (err) {
    return res.status(500).json({
      message: "Login failed",
      error: err.message,
    });
  }
};


exports.CheckPassword = async (req, res) => {
  const email = sanitizeEmail(req.body.email);
  const { password } = req.body;

  try {
    const user = await User.findOne({ email, isDeleted: false });
    if (!user) {
      return res.status(200).json({ valid: false }); // Do not reveal user existence
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(200).json({ valid: false });
    }

    return res.status(200).json({ valid: true });
  } catch (err) {
    return res.status(500).json({
      valid: false,
      message: "Server error",
      error: err.message,
    });
  }
};

exports.VerifyCode = async (req, res, next) => {
  const email = sanitizeEmail(req.body.email);
  const { code, type } = req.body; 

  try {
    // Find the code for the correct type (signup, signin, reset)
    const resetCode = await ResetCode.findOne({ email, type });

    if (!resetCode) {
      return res.status(400).json({ message: "No verification code found for this email and type" });
    }
    if (resetCode.code !== code) {
      return res.status(400).json({ message: "Invalid verification code" });
    }
    if (resetCode.expirationDate < new Date()) {
      return res.status(400).json({ message: "Verification code has expired" });
    }
 
    // Mark code as verified
    resetCode.verified = true;
    await resetCode.save();
  

    return res.status(200).json({ message: "Email verified successfully" });
  } catch (error) {
    console.error('Error in verification:', error);
    return res.status(500).json({
      message: "Failed to verify code",
      error: error.message
    });
  }
};


exports.ResendCode = async (req, res) => {
  try {
    const findFormerCode = await ResetCode.findOne({ email: req.body.email });

    if (!findFormerCode) {
      return res.status(404).json({
        success: false,
        message: "No code found with this email. Please verify your email."
      });
    }

    const confirmationCode = random();

     const emailContent = emailTemplate({ 
      firstName: req.body.firstName,
      code: confirmationCode,
      type: "confirmation",
     })

     const content = `
      
      <p class="bodyText">Thank you for signing up on Black Panther. To complete your registration use the code below to verify your email</p>

          <p class="code">${confirmationCode}</p>

          <p class="bodySmallText">
            <span class="noteSpan">Note : </span>The Code expires upon page refreshing
          </p>

      `

    const msg = {
      to: req.body.email,
      from: "<EMAIL>",
      subject: "Email Confirmation Code from Black Panther",
          // html: emailContent,
          html: emailTemp(content),
    };

    await sgMail.send(msg);
    
    // Update code in database
    await ResetCode.updateOne(
      { email: req.body.email }, // Find document by email
      {
        $set: {
          code: confirmationCode,
          expirationDate: new Date(Date.now() + 20 * 60 * 1000), // 20 minutes
        },
      }
    );

    return res.status(200).json({
      success: true,
      message: "A new confirmation code has been sent!"
    });

  } catch (error) {
    console.error("Resend confirmation error: ", error);
    return res.status(500).json({
      success: false,
      message: "Failed to resend confirmation code",
      error: error.message
    });
  }
};


exports.UpdateUserPoints = (req, res, next) => {
    User.findOne({email: req.userEmailAndId.email})
    .then(user => {
        User.updateOne({email: req.userEmailAndId.email},{
            $set: {
                points: parseInt(user.points) + parseInt( req.body.points)
            }
        }).then(document => {
            if(document.modifiedCount > 0){
                res.status(201).json({
                    message: "Points updated"
                })
            } else {
                res.status(403).json({
                    message: "Failed to update points"
                })
            }
        })
    })
    .catch(error => {
        res.status(500).json({
            message: "Something went wrong",
            error
        })
    })
}



exports.GetUser = async (req, res, next) => {
  try {
    // Step 1: Find user
    const user = await User.findOne({
      _id: req.userEmailAndId.userId,
      isDeleted: false,
    });

    if (!user) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    // Step 2: Check contribution status
    let isSubscribed = false;
    const contribution = await Contribution.findOne({ userId: user._id.toString() });
    if (contribution) {
      if (contribution.status === "active") {
        isSubscribed = true;
      } else {
        isSubscribed = false;
      }
    } else {
      isSubscribed = false;
    }

    // Step 3: Prepare user object with isSubscribed
    let _user = {
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      walletAddress: user.walletAddress,
      country: user.country,
      points: user.points,
      role: user.role,
      // isSubscribed: user.isSubscribed,
      isSubscribed, // <-- add isSubscribed boolean here
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    return res.status(200).json({
      user: _user,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error occured",
      error,
    });
  }
};

exports.UpdateUser = async (req, res, next) => {
  try {
    const userId = req.userEmailAndId.userId; // Extract user ID from authenticated request
    const updates = req.body; // Data to update

    // Validate if email is being updated and ensure it's unique
    if (updates.email) {
      const existingUser = await User.findOne({ email: updates.email, _id: { $ne: userId } });
      if (existingUser) {
        return res.status(400).json({
          message: "The email address is already in use by another account.",
        });
      }
    }

    // Update user details
    const updatedUser = await User.findOneAndUpdate(
      { _id: userId, isDeleted: false }, // Find user by ID and ensure not deleted
      { $set: updates }, // Update fields
      { new: true, runValidators: true } // Return updated document and validate fields
    );

    if (!updatedUser) {
      return res.status(404).json({
        message: "User not found or account is deleted.",
      });
    }

    return res.status(200).json({
      message: "User details updated successfully.",
      user: {
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        walletAddress: updatedUser.walletAddress,
        country: updatedUser.country,
        points: updatedUser.points,
        updatedAt: updatedUser.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error updating user details:", error);
    return res.status(500).json({
      message: "Failed to update user details.",
      error: error.message,
    });
  }
};

exports.GetWalletAddress = async (req, res, next) => {
  try {
    if (!req.userEmailAndId || !req.userEmailAndId.userId) {
      return res.status(401).json({
        message: "Authentication required"
      });
    }

    const user = await User.findOne({
      _id: req.userEmailAndId.userId,
      isDeleted: false
    });

    if (!user) {
      return res.status(404).json({
        message: "User not found"
      });
    }

    if (!user.walletAddress) {
      return res.status(200).json({
        message: "Please add your wallet address to your profile",
        hasWallet: false
      });
    }

    return res.status(200).json({
      walletAddress: user.walletAddress,
      hasWallet: true
    });

  } catch (error) {
    console.error("Error fetching wallet address:", error);
    return res.status(500).json({
      message: "Failed to fetch wallet address",
      error: error.message
    });
  }
};

exports.AddWalletAddress = async (req, res, next) => {
  try {
    const userId = req.userEmailAndId.userId;
    const { walletAddress } = req.body;

    if (!walletAddress) {
      return res.status(400).json({
        message: "Wallet address is required"
      });
    }

    // Check if user already has a wallet address
    const user = await User.findOne({ _id: userId, isDeleted: false });
    
    if (!user) {
      return res.status(404).json({
        message: "User not found"
      });
    }

    if (user.walletAddress) {
      return res.status(400).json({
        message: "Wallet address already exists. Use update endpoint to modify.",
        currentWallet: user.walletAddress
      });
    }

    const updatedUser = await User.findOneAndUpdate(
      { 
        _id: userId,
        isDeleted: false 
      },
      { 
        $set: { 
          walletAddress: walletAddress,
          updatedAt: new Date()
        } 
      }
    );

    return res.status(201).json({
      message: "Wallet address added successfully",
      walletAddress: updatedUser.walletAddress
    });

  } catch (error) {
    console.error("Error adding wallet address:", error);
    return res.status(500).json({
      message: "Failed to add wallet address",
      error: error.message
    });
  }
};

exports.UpdateWalletAddress = async (req, res, next) => {
  try {
    const userId = req.userEmailAndId.userId;
    const { currentWalletAddress, newWalletAddress } = req.body;

    if (!currentWalletAddress || !newWalletAddress) {
      return res.status(400).json({
        message: "Both current and new wallet addresses are required"
      });
    }

    // Verify current wallet address matches
    const user = await User.findOne({ 
      _id: userId, 
      isDeleted: false,
      walletAddress: currentWalletAddress 
    });

    if (!user) {
      return res.status(404).json({
        message: "User not found or current wallet address doesn't match"
      });
    }

    const updatedUser = await User.findOneAndUpdate(
      { 
        _id: userId,
        isDeleted: false 
      },
      { 
        $set: { 
          walletAddress: newWalletAddress,
          updatedAt: new Date()
        } 
      }
    );

    return res.status(200).json({
      message: "Wallet address updated successfully",
      oldWalletAddress: currentWalletAddress,
      newWalletAddress: updatedUser.walletAddress
    });

  } catch (error) {
    console.error("Error updating wallet address:", error);
    return res.status(500).json({
      message: "Failed to update wallet address",
      error: error.message
    });
  }
};


// Step 1: ForgotPassword - sends code to email
exports.ForgotPassword = async (req, res) => {
  try {
      const { email } = req.body;

      // Find user
      const user = await User.findOne({ email, isDeleted: false });
      if (!user) {
          return res.status(404).json({
              success: false,
              message: "No account found with this email"
          });
      }

      // Generate reset code
      const resetCode = random(10000, 99999).toString();

      // Create email content
      const emailContent = emailTemplate({
        firstName: user.firstName || "User",
        code: resetCode,
        type: "reset",
      });

      const content = `

        <p class="bodyText">You are receiving this email because you or someone else has requested
        the reset password for your account. To reset use the code below to verify your email</p>
        <p class="code">${resetCode}</p>
        <p class="bodySmallText">
          <span class="noteSpan">Note:</span>The Code expires upon page refreshing
        </p>

        `

      // Prepare email
      const msg = {
          to: email,
          from: "<EMAIL>",
          subject: "Password Reset Code - Black Panther",
          // html: emailContent,
          html: emailTemp(content),
      };

      // Send email
      await sgMail.send(msg);

      // Save reset code
      const newResetCode = new ResetCode({
          email,
          code: resetCode,
          expirationDate: new Date(Date.now() + 20 * 60 * 1000), // 20 minutes
          type: "reset"
      });

      await newResetCode.save();

      return res.status(200).json({
          success: true,
          message: "Password reset code sent successfully"
      });

  } catch (error) {
      console.error("Error in ForgotPassword: ", error);
      return res.status(500).json({
          success: false,
          message: "Failed to process password reset request",
          error: error.message
      });
  }
};

// Step 3: ChangePassword - sets the new password
exports.ChangePassword = async (req, res, next) => {
  const { email, newPassword } = req.body;

  try {
    // Find user to compare with old password
    const user = await User.findOne({ email: email });
    if (!user) {
      return res.status(404).json({
        message: "User not found",
      });
    }

    // Check if new password is same as old password
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      return res.status(400).json({
        message: "New password must be different from your current password",
      });
    }

    const hash = await bcrypt.hash(newPassword, 10);
    
    const updateResult = await User.updateOne(
      { email: email },
      { $set: { password: hash } }
    );

    if (updateResult.modifiedCount > 0) {
      res.status(200).json({
        message: "Password changed successfully. You can now login with your new password.",
      });
    } else {
      res.status(403).json({
        message: "Failed to change password",
      });
    }
  } catch (error) {
    console.error("Error changing password:", error);
    res.status(500).json({
      message: "Failed to change password",
      error: error,
    });
  }
};



exports.ResendResetCode = async (req, res, next) => {
  try {
    const user = await User.findOne({ email: req.body.email });

    if (!user) {
      return res.status(404).json({
        message: "No user found with this email. Please verify your email.",
      });
    }

    const existingResetCode = await ResetCode.findOne({ email: user.email,  type: "reset"});

    if (!existingResetCode) {
      return res.status(404).json({
        message: "No reset code found for this email. Please request a new reset code.",
      });
    }

    const newCode = random(); // Generate a new random code
    const emailContent = emailTemplate({
      firstName: user.firstName || "User",
      code: newCode,
      type: "reset",
    });

    
    const content = `

    <p class="bodyText">You are receiving this email because you or someone else has requested
    the reset password for your account. To reset use the code below to verify your email</p>
    <p class="code">${newCode}</p>
    <p class="bodySmallText">
      <span class="noteSpan">Note:</span>The Code expires upon page refreshing
    </p>

    `

    const msg = {
      to: user.email,
      from: "<EMAIL>",
      subject: "Password Reset Code from Black Panther",
          // html: emailContent,
          html: emailTemp(content),
    };

    await sgMail.send(msg);

    existingResetCode.code = newCode;
    existingResetCode.type = "reset";
    await existingResetCode.save();

    res.status(200).json({
      message: "A new reset code has been sent to your email.",
    });
  } catch (error) {
    console.error("Error in ResendResetCode: ", error);
    res.status(500).json({
      message: "Failed to resend reset code.",
      error: error,
    });
  }
};


exports.DeleteUserAccount = async (req, res, next) => {
  try {
    const userId = req.userEmailAndId.userId; // Extract user ID from authenticated request

    // Find the user and ensure they exist and are not already deleted
    const user = await User.findOne({ _id: userId, isDeleted: false });
    if (!user) {
      return res.status(404).json({
        message: "User not found or account already deleted.",
      });
    }

     // Perform a hard delete
    // await User.deleteOne({ _id: userId });
    // Perform a soft delete by setting isDeleted to true and anonymizing sensitive data
    await User.updateOne(
      { _id: userId },
      {
        $set: {
          isDeleted: true,
          email: `deleted_${userId}@example.com`, // Anonymize email to ensure uniqueness
          password: null, // Clear password
          walletAddress: null, // Clear wallet address
          country: null, // Clear country
          firstName: "Deleted", // Optional: Mark as deleted
          lastName: "User", // Optional: Mark as deleted
        },
      }
    );

    return res.status(200).json({
      message: "User account deleted successfully.",
    });
  } catch (error) {
    console.error("Error deleting user account:", error);
    return res.status(500).json({
      message: "Failed to delete user account.",
      error: error.message,
    });
  }
};
 
         
exports.ChangePasswordWithVerification = async (req, res) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const userId = req.userEmailAndId.userId;

    // Input validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "All fields are required"
      });
    }

    // Check if new password matches confirmation
    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "New password and confirmation do not match"
      });
    }

    // Password strength validation - Updated regex to be more permissive
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$/;
    
  
    
    if (!passwordRegex.test(newPassword)) {
      return res.status(400).json({
        success: false,
        message: "Password must be at least 8 characters long and contain letters, numbers, and symbols"
      });
    }

    // Find user and verify current password
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Current password is incorrect"
      });
    }

    // Check if new password is same as current password
    const isSamePassword = await bcrypt.compare(newPassword, user.password);
    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        message: "New password must be different from your current password"
      });
    }

    // Hash and update new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);
    const updateResult = await User.updateOne(
      { _id: userId },
      { $set: { password: hashedNewPassword } }
    );

    if (updateResult.modifiedCount > 0) {
      return res.status(200).json({
        success: true,
        message: "Password changed successfully"
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Failed to update password"
      });
    }

  } catch (error) {
    console.error("Error in ChangePasswordWithVerification:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to change password",
      error: error.message
    });
  }
};


exports.RefreshToken = (req, res, next) => {
  const oldToken = req.headers.authorization?.split(" ")[1]; 

  if (!oldToken) {
      return res.status(401).json({ message: "No token provided" });
  }

  try {
      const decodedToken = jwt.verify(oldToken, process.env.JWT_SECRET);

      // Generate a new token with a 30-minute expiration
      const newToken = jwt.sign(
          { email: decodedToken.email, userId: decodedToken.userId },
          process.env.JWT_SECRET,
          { expiresIn: "30m" } // Set expiration to 30 minutes
      );

      return res.status(200).json({
          token: newToken,
          expiresIn: 1800, // 30 minutes in seconds
      });
  } catch (error) {
      return res.status(401).json({ message: "Invalid or expired token" });
  }
};


exports.GetAllUsers = async (req, res) => {
  try {
    const users = await User.find({ isDeleted: false });
    res.status(200).json({ users });
  } catch (error) {
    res.status(500).json({
      message: "Failed to fetch users",
      error: error.message,
    });
  }
};

exports.PromoteUserToAdmin = async (req, res) => {
  try {
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).json({ message: "User ID is required" });
    }

    const updatedUser = await User.findOneAndUpdate(
      { _id: userId, isDeleted: false },
      { $set: { role: "admin" } },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ message: "User not found or already deleted" });
    }

    res.status(200).json({ message: "User promoted to Admin successfully", user: updatedUser });
  } catch (error) {
    res.status(500).json({ message: "Failed to promote user", error: error.message });
  }
};

exports.RemoveUser = async (req, res) => {
  try {
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).json({ message: "User ID is required" });
    }

    // Demote Admin to Member
    const updatedUser = await User.findOneAndUpdate(
      { _id: userId, isDeleted: false },
      { $set: { role: "Member" } },
      { new: true }
    );

    if (!updatedUser) {
      return res.status(404).json({ message: "User not found or already deleted" });
    }

    res.status(200).json({ message: "User demoted to Member successfully", user: updatedUser });
  } catch (error) {
    res.status(500).json({ message: "Failed to demote user", error: error.message });
  }
};

// Utility to sanitize email
function sanitizeEmail(email) {
  return email ? email.trim().toLowerCase() : "";
}

exports.GetAllUserCount = async (req, res) => {
  try {
    const count = await User.countDocuments({ isDeleted: false });
    res.status(200).json({ count });
  } catch (error) {
    res.status(500).json({
      message: "Failed to fetch user count",
      error: error.message,
    });
  }
}