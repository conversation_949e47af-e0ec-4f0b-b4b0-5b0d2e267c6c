const Countdown = require("../models/countDown")

exports.GetCountDown = async (req, res, next) => {
    let countdown = await Countdown.findOne().sort({ createdAt: -1 });
    if (!countdown) {
        const endTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
        countdown = new Countdown({ endTime });
        await countdown.save();
    }

    const now = new Date();
    if (now >= countdown.endTime) {
        endTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // Reset to 7 days from now
        countdown = new Countdown({endTime})
        await countdown.save();
    }

    res.json({ endTime: countdown.endTime });
}