require("dotenv").config();
const { ethers } = require("ethers");
const { v4: uuidv4 } = require("uuid");
const mongoose = require("mongoose");
const Affiliate = require("../models/affiliate");
const AffiliateClaim = require("../models/affiliateClaim");
const Community = require("../models/community");
const CommunityAffiliate = require("../models/communityAffiliate");
const User = require("../models/user");
const AFFILIATE_CONFIG = require("../constants/affiliateConfig");

const ERC20_ABI = [
  "function decimals() view returns (uint8)",
  "event Transfer(address indexed from, address indexed to, uint256 value)"
];

const logErr = (ctx, err) => console.error(`[${ctx}]`, err);


/**
 * Validate affiliate link and track usage
 */
exports.validateAffiliateLink = async (req, res) => {
  try {
    const { ref, token } = req.query;
    
    if (!ref || !token) {
      return res.status(400).json({ 
        valid: false, 
        message: "Both referral code and token are required" 
      });
    }

    // Find affiliate by code and token (don't filter by isActive yet)
    const affiliate = await Affiliate.findOne({ 
      affiliateCode: ref.toUpperCase(),
      linkToken: token
    }).populate('userId', 'firstName lastName');

    console.log('Affiliate found:', affiliate ? {
      code: affiliate.affiliateCode,
      currentUsers: affiliate.currentUsers,
      maxUsers: affiliate.maxUsers,
      isActive: affiliate.isActive,
      expiresAt: affiliate.expiresAt,
      tokenExpires: affiliate.tokenExpiresAt,
      isExpired: affiliate.expiresAt && new Date() > affiliate.expiresAt,
      isMaxReached: affiliate.currentUsers >= affiliate.maxUsers
    } : 'Not found');

    if (!affiliate) {
      return res.status(404).json({ 
        valid: false, 
        message: "Referral link not found" 
      });
    }

    // Check if max users reached FIRST (highest priority)
    if (affiliate.currentUsers >= affiliate.maxUsers) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(200).json({ 
        valid: false, 
        message: `This referral link has reached its maximum number of users (${affiliate.currentUsers}/${affiliate.maxUsers})`,
        maxReached: true,
        canRegenerate: true
      });
    }

    // Check link expiration
    if (affiliate.expiresAt && new Date() > affiliate.expiresAt) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(200).json({ 
        valid: false, 
        message: "This referral link has expired",
        expired: true,
        canRegenerate: true
      });
    }

    // Check token expiration
    if (affiliate.tokenExpiresAt && new Date() > affiliate.tokenExpiresAt) {
      return res.status(200).json({ 
        valid: false, 
        message: "This referral token has expired",
        expired: true,
        canRegenerate: true
      });
    }

    // Check if inactive
    if (!affiliate.isActive) {
      return res.status(200).json({ 
        valid: false, 
        message: "This referral link is no longer active",
        inactive: true,
        canRegenerate: true
      });
    }

    const spotsRemaining = affiliate.maxUsers - affiliate.currentUsers;
    return res.status(200).json({
      valid: true,
      data: {
        affiliateCode: affiliate.affiliateCode,
        referrerName: `${affiliate.userId.firstName} ${affiliate.userId.lastName}`,
        usersSignedUp: affiliate.currentUsers,
        maxUsers: affiliate.maxUsers,
        spotsRemaining,
        expiresAt: affiliate.expiresAt,
        tokenExpiresAt: affiliate.tokenExpiresAt,
        rewards: {
          usdPerReferral: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
          tokensPerReferral: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL,
          bonusAtMax: {
            usd: AFFILIATE_CONFIG.BONUS_USD_AT_MAX,
            tokens: AFFILIATE_CONFIG.BONUS_TOKENS_AT_MAX
          }
        }
      }
    });

  } catch (error) {
    console.error("validateAffiliateLink error:", error);
    return res.status(500).json({ 
      valid: false, 
      message: "Internal server error" 
    });
  }
};

/**
 * Process affiliate signup - simplified to only create affiliate claim
 */
exports.processAffiliateSignup = async (req, res) => {
  
  try {
    const { ref, token, userId } = req.body;
    if (!ref || !token || !userId) {
      return res.status(400).json({ 
        success: false, 
        message: "Referral code, token, and user ID are required" 
      });
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: "User not found" 
      });
    }

    // Find affiliate (don't filter by isActive yet, so we can give specific error messages)
    const affiliate = await Affiliate.findOne({ 
      affiliateCode: ref.toUpperCase(),
      linkToken: token
    });

    if (!affiliate) {
      return res.status(404).json({ 
        success: false, 
        message: "Referral link not found" 
      });
    }

    // Check validations in priority order (same as validateAffiliateLink)
    
    // Check if max users reached FIRST (highest priority)
    if (affiliate.currentUsers >= affiliate.maxUsers) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(200).json({ 
        success: false, 
        message: `This referral link has reached its maximum number of users (${affiliate.currentUsers}/${affiliate.maxUsers})`,
        maxReached: true,
        canRegenerate: true
      });
    }

    // Check link expiration
    if (affiliate.expiresAt && new Date() > affiliate.expiresAt) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(200).json({ 
        success: false, 
        message: "This referral link has expired",
        expired: true,
        canRegenerate: true
      });
    }

    // Check token expiration
    if (affiliate.tokenExpiresAt && new Date() > affiliate.tokenExpiresAt) {
      return res.status(200).json({ 
        success: false, 
        message: "This referral token has expired",
        expired: true,
        canRegenerate: true
      });
    }

    // Check if inactive
    if (!affiliate.isActive) {
      return res.status(200).json({ 
        success: false, 
        message: "This referral link is no longer active",
        inactive: true,
        canRegenerate: true
      });
    }

    // Check if user already referred
    if (affiliate.referrals.includes(userId)) {
      return res.status(400).json({ 
        success: false, 
        message: "User already referred by this affiliate" 
      });
    }

    // Get affiliate user 
    const affiliateUser = await User.findById(affiliate.userId);
    if (!affiliateUser) {
      return res.status(400).json({ 
        success: false, 
        message: "Affiliate user not found" 
      });
    }

    // Create the affiliate claim without wallet requirement
    const claim = await AffiliateClaim.create({
      affiliate: affiliate.affiliateCode,
      claimant: affiliate.userId,
      referredUser: userId,
      walletAddress: affiliateUser.walletAddress?.toLowerCase() || "pending",
      amount: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
      tokenAmount: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL,
      status: 'pending',
      rewardType: 'immediate'
    });

    affiliate.referrals.push(userId);
    affiliate.currentUsers += 1;

    // Check if max users reached and deactivate if needed
    if (affiliate.currentUsers >= affiliate.maxUsers) {
      affiliate.isActive = false;
    }

    // Save the updated affiliate document
    await affiliate.save();

    return res.status(200).json({
      success: true,
      message: "Affiliate signup tracked successfully",
      data: {
        claimId: claim._id,
        affiliateCode: affiliate.affiliateCode,
        amount: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
        tokenAmount: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL,
        currentUsers: affiliate.currentUsers, // Include updated count
        maxUsers: affiliate.maxUsers,
        rewardsEarned: {
          usd: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
          tokens: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL,
          bonus: affiliate.currentUsers >= affiliate.maxUsers // Bonus if link is now full
        }
      }
    });

  } catch (error) {
    console.error("processAffiliateSignup error:", error);
    return res.status(500).json({ 
      success: false, 
      message: "Internal server error" 
    });
  }
};

/**
 * Get affiliate link statistics
 */
exports.getAffiliateLinkStats = async (req, res) => {
  try {
    const { ref } = req.params;
    
    const affiliate = await Affiliate.findOne({ 
      affiliateCode: ref.toUpperCase() 
    }).populate('userId', 'firstName lastName')
      .populate('referrals', 'firstName lastName email createdAt');

    if (!affiliate) {
      return res.status(404).json({ 
        message: "Affiliate not found" 
      });
    }

    const pendingClaims = await AffiliateClaim.find({
      affiliate: affiliate.affiliateCode,
      status: 'pending'
    }).populate('referredUser', 'firstName lastName email');

    return res.status(200).json({
      affiliateCode: affiliate.affiliateCode,
      referrerName: `${affiliate.userId.firstName} ${affiliate.userId.lastName}`,
      stats: {
        currentUsers: affiliate.currentUsers,
        maxUsers: affiliate.maxUsers,
        isActive: affiliate.isActive,
        expiresAt: affiliate.expiresAt,
        tokenExpiresAt: affiliate.tokenExpiresAt,
        totalEarnings: {
          usd: affiliate.rewardAmount,
          tokens: affiliate.tokenRewardAmount
        },
        referrals: affiliate.referrals.map(user => ({
          id: user._id,
          name: `${user.firstName} ${user.lastName}`,
          email: user.email,
          joinedAt: user.createdAt
        }))
      },
      pendingClaims: pendingClaims.length,
      linkUrl: affiliate.linkUrl
    });

  } catch (error) {
    console.error("getAffiliateLinkStats error:", error);
    return res.status(500).json({ 
      message: "Internal server error" 
    });
  }
};

/**
 * Regenerate affiliate code for users with expired/maxed out links
 */
exports.regenerateAffiliateCode = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });
    
    // Find existing affiliate
    const existingAffiliate = await Affiliate.findOne({ userId });
    
    if (!existingAffiliate) {
      return res.status(404).json({
        success: false,
        message: "No existing affiliate link found to regenerate"
      });
    }

    console.log('🔍 Found existing affiliate:', {
      code: existingAffiliate.affiliateCode,
      isActive: existingAffiliate.isActive,
      currentUsers: existingAffiliate.currentUsers,
      maxUsers: existingAffiliate.maxUsers,
      expiresAt: existingAffiliate.expiresAt
    });

    // Get configuration from request or use defaults
    const { 
      expirationInHours = AFFILIATE_CONFIG.DEFAULT_EXPIRATION_HOURS,
      maxUsers = AFFILIATE_CONFIG.DEFAULT_MAX_USERS,
      rewardPerUser = AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
      tokensPerUser = AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL
    } = req.body;

    // Generate unique affiliate code
    let uniqueCode;
    do {
      uniqueCode = uuidv4().slice(0, 8).toUpperCase();
    } while (await Affiliate.findOne({ affiliateCode: uniqueCode }));

    // Calculate expiration date
    const expiresAt = new Date(Date.now() + (expirationInHours * 60 * 60 * 1000));
    
    // Generate expiring token for the link
    const linkToken = uuidv4().replace(/-/g, '').slice(0, 16); // 16 character token
    const tokenExpiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // Token expires in 24 hours
    
    // Generate affiliate link with both referral code and expiring token
    const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/black_panther_dao?ref=${uniqueCode}&token=${linkToken}`;

    // Update existing affiliate with new values (reset everything)
    existingAffiliate.affiliateCode = uniqueCode;
    existingAffiliate.rewardAmount = 0; // Reset rewards
    existingAffiliate.tokenRewardAmount = 0; // Reset token rewards
    existingAffiliate.referrals = []; // Reset referrals
    existingAffiliate.earningsHistory = []; // Reset earnings history
    existingAffiliate.expirationInHours = expirationInHours;
    existingAffiliate.expiresAt = expiresAt;
    existingAffiliate.maxUsers = maxUsers;
    existingAffiliate.currentUsers = 0; // Reset user count
    existingAffiliate.linkUrl = affiliateLink;
    existingAffiliate.linkToken = linkToken;
    existingAffiliate.tokenExpiresAt = tokenExpiresAt;
    existingAffiliate.isActive = true;
    
    await existingAffiliate.save();

    return res.status(200).json({
      success: true,
      message: "Affiliate link regenerated successfully!",
      affiliateCode: uniqueCode,
      affiliateLink,
      expiresAt,
      tokenExpiresAt,
      maxUsers,
      currentUsers: 0,
      settings: {
        rewardPerUser,
        tokensPerUser,
        bonusAtMax: {
          usd: AFFILIATE_CONFIG.BONUS_USD_AT_MAX,
          tokens: AFFILIATE_CONFIG.BONUS_TOKENS_AT_MAX
        }
      }
    });

  } catch (error) {
    console.error("regenerateAffiliateCode error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

/**
 * Generate affiliate code for individual users
 */
exports.generateAffiliateCode = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });

    // Check if user already has an affiliate code
    const existingAffiliate = await Affiliate.findOne({ userId });
    if (existingAffiliate) {
      return res.status(200).json({ 
        message: "User already has an affiliate code! Use the regenerate button if you need a new one.",
        affiliateCode: existingAffiliate.affiliateCode,
        affiliateLink: existingAffiliate.linkUrl,
        expiresAt: existingAffiliate.expiresAt,
        tokenExpiresAt: existingAffiliate.tokenExpiresAt,
        currentUsers: existingAffiliate.currentUsers,
        maxUsers: existingAffiliate.maxUsers,
        isActive: existingAffiliate.isActive,
        hasExisting: true // Flag to indicate this is existing data
      });
    }

    // Get configuration from request or use defaults
    const { 
      expirationInHours = AFFILIATE_CONFIG.DEFAULT_EXPIRATION_HOURS,
      maxUsers = AFFILIATE_CONFIG.DEFAULT_MAX_USERS,
      rewardPerUser = AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
      tokensPerUser = AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL
    } = req.body;

    // Generate unique affiliate code
    let uniqueCode;
    do {
      uniqueCode = uuidv4().slice(0, 8).toUpperCase();
    } while (await Affiliate.findOne({ affiliateCode: uniqueCode }));

    // Calculate expiration date
    const expiresAt = new Date(Date.now() + (expirationInHours * 60 * 60 * 1000));
    
    // Generate expiring token for the link
    const linkToken = uuidv4().replace(/-/g, '').slice(0, 16); // 16 character token
    const tokenExpiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // Token expires in 24 hours
    
    // Generate affiliate link with both referral code and expiring token
    const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/black_panther_dao?ref=${uniqueCode}&token=${linkToken}`;
    
    // Create new affiliate (since we already checked existingAffiliate above)
    const newAffiliate = new Affiliate({
      userId,
      affiliateCode: uniqueCode,
      rewardAmount: 0,
      tokenRewardAmount: 0,
      referrals: [],
      earningsHistory: [],
      expirationInHours,
      expiresAt,
      maxUsers,
      currentUsers: 0,
      linkUrl: affiliateLink,
      linkToken,
      tokenExpiresAt,
      isActive: true
    });

    const affiliate = await newAffiliate.save();

    return res.status(201).json({
      message: "Affiliate code generated successfully!",
      affiliateCode: uniqueCode,
      affiliateLink,
      expiresAt,
      tokenExpiresAt,
      maxUsers,
      currentUsers: 0,
      settings: {
        rewardPerUser,
        tokensPerUser,
        bonusAtMax: {
          usd: AFFILIATE_CONFIG.BONUS_USD_AT_MAX,
          tokens: AFFILIATE_CONFIG.BONUS_TOKENS_AT_MAX
        }
      }
    });

  } catch (error) {
    logErr("generateAffiliateCode", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Record referral when user signs up with affiliate code
 */
exports.recordReferral = async (newUser, affiliateCode) => {
  try {
    // Find the affiliate record
    const affiliate = await Affiliate.findOne({ 
      affiliateCode: affiliateCode.toUpperCase(),
      isActive: true 
    });
    
    if (!affiliate) {
      return { success: false, reason: "Invalid affiliate code" };
    }

    // Check if link has expired
    if (affiliate.expiresAt && new Date() > affiliate.expiresAt) {
      affiliate.isActive = false;
      await affiliate.save();
      return { success: false, reason: "Affiliate link has expired" };
    }

    // Check if maximum users reached
    if (affiliate.currentUsers >= affiliate.maxUsers) {
      affiliate.isActive = false;
      await affiliate.save();
      return { success: false, reason: "Affiliate link has reached maximum users" };
    }

    // Check if user already referred by this affiliate
    if (affiliate.referrals.includes(newUser._id)) {
      return { success: false, reason: "User already referred" };
    }

    // Start transaction for atomic operations
    const session = await mongoose.startSession();
    await session.withTransaction(async () => {
      // Update affiliate record
      affiliate.referrals.push(newUser._id);
      affiliate.currentUsers += 1;

      // Check if max users reached after incrementing
      if (affiliate.currentUsers >= affiliate.maxUsers) {
        affiliate.isActive = false; 
      }

      await affiliate.save();

      // Update the new user
      newUser.referredBy = affiliateCode.toUpperCase();
      newUser.joinedViaRef = true;
      await newUser.save();

      // Get affiliate user details for the claim
      const affiliateUser = await User.findById(affiliate.userId);
      if (!affiliateUser || !affiliateUser.walletAddress) {
        throw new Error("Affiliate user not found or missing wallet address");
      }

      // Create immediate reward claim
      await AffiliateClaim.create({
        affiliate: affiliate.affiliateCode,
        claimant: affiliate.userId,
        referredUser: newUser._id,
        walletAddress: affiliateUser.walletAddress.toLowerCase(),
        amount: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
        tokenAmount: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL,
        status: 'pending',
        rewardType: 'immediate'
      });

      // Check if max users reached for bonus
      if (affiliate.currentUsers >= affiliate.maxUsers) {
        await AffiliateClaim.create({
          affiliate: affiliate.affiliateCode,
          claimant: affiliate.userId,
          referredUser: newUser._id,
          walletAddress: affiliateUser.walletAddress.toLowerCase(),
          amount: AFFILIATE_CONFIG.BONUS_USD_AT_MAX,
          tokenAmount: AFFILIATE_CONFIG.BONUS_TOKENS_AT_MAX,
          status: 'pending',
          rewardType: 'max_reached'
        });
      }
    });

    await session.endSession();

    return {
      success: true,
      data: {
        affiliateCode: affiliate.affiliateCode,
        currentUsers: affiliate.currentUsers,
        maxUsers: affiliate.maxUsers,
        isMaxReached: affiliate.currentUsers >= affiliate.maxUsers,
        totalEarned: {
          usd: affiliate.rewardAmount,
          tokens: affiliate.tokenRewardAmount
        }
      }
    };

  } catch (error) {
    logErr("recordReferral", error);
    return { success: false, reason: "Internal error processing referral" };
  }
};

/**
 * Get affiliate statistics for a user
 */
exports.getAffiliateStats = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });

    const affiliate = await Affiliate.findOne({ userId })
      .populate('referrals', 'firstName lastName email createdAt')
      .populate('earningsHistory.referredUser', 'firstName lastName email');

    if (!affiliate) {
      return res.status(404).json({ message: "No affiliate record found." });
    }

    // Get pending claims
    const pendingClaims = await AffiliateClaim.find({
      claimant: userId,
      status: 'pending'
    }).populate('referredUser', 'firstName lastName email');

    return res.status(200).json({
      affiliateCode: affiliate.affiliateCode,
      affiliateLink: affiliate.linkUrl,
      stats: {
        totalReferrals: affiliate.referrals.length,
        currentUsers: affiliate.currentUsers,
        maxUsers: affiliate.maxUsers,
        isActive: affiliate.isActive,
        expiresAt: affiliate.expiresAt,
        totalEarnings: {
          usd: affiliate.rewardAmount,
          tokens: affiliate.tokenRewardAmount
        }
      },
      referrals: affiliate.referrals,
      earningsHistory: affiliate.earningsHistory,
      pendingClaims,
      rewardStructure: {
        perReferral: {
          usd: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
          tokens: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL
        },
        maxBonus: {
          usd: AFFILIATE_CONFIG.BONUS_USD_AT_MAX,
          tokens: AFFILIATE_CONFIG.BONUS_TOKENS_AT_MAX
        }
      }
    });

  } catch (error) {
    logErr("getAffiliateStats", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Validate affiliate code (for frontend to check before signup)
 */
exports.validateAffiliateCode = async (req, res) => {
  try {
    const { code } = req.params;
    
    if (!code) {
      return res.status(400).json({ message: "Affiliate code is required." });
    }

    const affiliate = await Affiliate.findOne({ 
      affiliateCode: code.toUpperCase(),
      isActive: true 
    }).populate('userId', 'firstName lastName');

    if (!affiliate) {
      return res.status(404).json({ 
        valid: false, 
        message: "Invalid or inactive affiliate code." 
      });
    }

    // Check expiration
    if (affiliate.expiresAt && new Date() > affiliate.expiresAt) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(400).json({ 
        valid: false, 
        message: "Affiliate link has expired." 
      });
    }

    // Check max users
    if (affiliate.currentUsers >= affiliate.maxUsers) {
      affiliate.isActive = false;
      await affiliate.save();
      return res.status(400).json({ 
        valid: false, 
        message: "Affiliate link has reached maximum users." 
      });
    }

    return res.status(200).json({
      valid: true,
      data: {
        affiliateCode: affiliate.affiliateCode,
        referrerName: `${affiliate.userId.firstName} ${affiliate.userId.lastName}`,
        spotsRemaining: affiliate.maxUsers - affiliate.currentUsers,
        expiresAt: affiliate.expiresAt,
        rewards: {
          usdPerReferral: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL,
          tokensPerReferral: AFFILIATE_CONFIG.DEFAULT_TOKENS_PER_REFERRAL
        }
      }
    });

  } catch (error) {
    logErr("validateAffiliateCode", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Generate affiliate code for community
 */
exports.generateAffiliateCodeComunity = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    const { 
      communityId,
      expirationInHours = AFFILIATE_CONFIG.DEFAULT_EXPIRATION_HOURS,
      maxUsers = AFFILIATE_CONFIG.DEFAULT_MAX_USERS,
      rewardPerUser = AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL
    } = req.body;

    if (!userId || !communityId) {
      return res.status(400).json({ message: "Missing user or community ID." });
    }

    const community = await Community.findById(communityId);
    if (!community) {
      return res.status(404).json({ message: "Community not found." });
    }

    const isAdmin = community.communityAdmin.toString() === userId;

    let communityAffiliate = await CommunityAffiliate.findOne({ communityId });

    if (communityAffiliate) {
      if (
        !isAdmin &&
        communityAffiliate.generationPolicy !== "members_allowed"
      ) {
        return res.status(403).json({
          message: "Members are not allowed to generate affiliate links for this community.",
        });
      }

      const existingCode = await CommunityAffiliate.findOne({
        communityId,
        userId,
      });
      if (existingCode) {
        return res.status(400).json({
          message: "You already have a code for this community.",
          affiliateCode: existingCode.affiliateCode,
          affiliateLink: `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/dao/${communityId}/join?ref=${existingCode.affiliateCode}`
        });
      }
    } else {
      if (!isAdmin) {
        return res.status(403).json({
          message: "Only the community admin can create the initial affiliate policy.",
        });
      }
    }

    let uniqueCode;
    do {
      uniqueCode = uuidv4().slice(0, 8).toUpperCase();
    } while (await CommunityAffiliate.findOne({ affiliateCode: uniqueCode }));

    // Calculate expiration date
    const expiresAt = new Date(Date.now() + (expirationInHours * 60 * 60 * 1000));

    const newAffiliate = new CommunityAffiliate({
      userId,
      communityId,
      affiliateCode: uniqueCode,
      rewardAmount: 0,
      referrals: [],
      earningsHistory: [],
      isPolicyController: isAdmin,
      generationPolicy: isAdmin ? "admin_only" : undefined,
      expirationInHours,
      expiresAt,
      maxUsers,
      currentUsers: 0,
      isActive: true
    });

    await newAffiliate.save();

    const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/dao/${communityId}/join?ref=${uniqueCode}`;

    return res.status(201).json({
      message: "Community affiliate code generated successfully!",
      affiliateCode: uniqueCode,
      affiliateLink,
      expiresAt,
      maxUsers,
      currentUsers: 0,
      generationPolicy: newAffiliate.generationPolicy,
      communityId: communityId,
      communityName: community.communityName,
      settings: {
        rewardPerUser,
        expirationInHours,
        maxUsers
      }
    });

  } catch (error) {
    logErr("generateAffiliateCodeComunity", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Record community referral when user joins via community link
 */
exports.recordCommunityReferral = async (user, communityRefCode) => {
  try {
    const communityAffiliate = await CommunityAffiliate.findOne({ 
      affiliateCode: communityRefCode.toUpperCase(),
      isActive: true 
    });
    
    if (!communityAffiliate) {
      console.log(`Community affiliate code ${communityRefCode} not found or inactive`);
      return { success: false, reason: "Invalid community affiliate code" };
    }

    // Check if the community exists and get its details
    const community = await Community.findById(communityAffiliate.communityId);
    if (!community) {
      console.log(`Community ${communityAffiliate.communityId} not found`);
      return { success: false, reason: "Community not found" };
    }

    // Check if user is already a member of the community
    const isAlreadyMember = community.registeredMembers.includes(user._id);
    if (isAlreadyMember) {
      console.log(`User ${user._id} is already a member of community ${community.communityName}`);
      return { success: false, reason: "User is already a member of this community" };
    }

    // Check if link has expired
    if (communityAffiliate.expiresAt && new Date() > communityAffiliate.expiresAt) {
      communityAffiliate.isActive = false;
      await communityAffiliate.save();
      console.log(`Community affiliate code ${communityRefCode} has expired`);
      return { success: false, reason: "Community affiliate link has expired" };
    }

    // Check if maximum users reached
    if (communityAffiliate.currentUsers >= communityAffiliate.maxUsers) {
      communityAffiliate.isActive = false;
      await communityAffiliate.save();
      console.log(`Community affiliate code ${communityRefCode} has reached maximum users`);
      return { success: false, reason: "Community affiliate link has reached maximum users" };
    }

    // Check if user already referred by this community affiliate
    const alreadyReferred = communityAffiliate.referrals.includes(user._id);
    if (alreadyReferred) {
      console.log(`User ${user._id} already referred by community ${communityRefCode}`);
      return { success: false, reason: "User already referred by this community" };
    }

    // Check if community has reached its member limit
    if (community.registeredMembers.length >= community.communityMembers) {
      console.log(`Community ${community.communityName} has reached its member limit of ${community.communityMembers}`);
      return { success: false, reason: "Community has reached its maximum member limit" };
    }

    // Start transaction for atomic operations
    const session = await mongoose.startSession();
    await session.withTransaction(async () => {
      // Update community affiliate record
      communityAffiliate.referrals.push(user._id);
      communityAffiliate.currentUsers += 1;
      
      // Calculate rewards
      const rewardAmount = AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL;
      communityAffiliate.rewardAmount += rewardAmount;
      
      // Add to earnings history
      communityAffiliate.earningsHistory.push({
        amount: rewardAmount,
        date: new Date(),
        referredUser: user._id
      });

      // Check if max users reached - deactivate link
      if (communityAffiliate.currentUsers >= communityAffiliate.maxUsers) {
        communityAffiliate.isActive = false;
      }

      await communityAffiliate.save({ session });

      // Add user to community's registered members
      community.registeredMembers.push(user._id);
      await community.save({ session });

      // Update the user
      user.communityJoinRewardClaimed = true;
      user.joinedViaCommunityRef = communityRefCode.toUpperCase();
      user.communityJoined = communityAffiliate.communityId;
      await user.save({ session });

      // Get affiliate user details for the claim
      const affiliateUser = await User.findById(communityAffiliate.userId).session(session);
      if (affiliateUser && affiliateUser.walletAddress) {
        await AffiliateClaim.create([{
          affiliate: communityAffiliate.affiliateCode,
          claimant: communityAffiliate.userId,
          referredUser: user._id,
          walletAddress: affiliateUser.walletAddress.toLowerCase(),
          amount: rewardAmount,
          status: 'pending',
          rewardType: 'community_referral'
        }], { session });
      }
    });

    await session.endSession();

    return {
      success: true,
      data: {
        affiliateCode: communityAffiliate.affiliateCode,
        currentUsers: communityAffiliate.currentUsers,
        maxUsers: communityAffiliate.maxUsers,
        isMaxReached: communityAffiliate.currentUsers >= communityAffiliate.maxUsers,
        totalEarned: communityAffiliate.rewardAmount,
        community: {
          id: community._id,
          name: community.communityName,
          totalMembers: community.registeredMembers.length
        }
      }
    };

  } catch (err) {
    logErr("recordCommunityReferral", err);
    return { success: false, reason: "Internal error processing community referral" };
  }
};

/**
 * Update community affiliate policy
 */
exports.updateCommunityAffiliatePolicy = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    const { communityId, newPolicy } = req.body;

    if (!userId || !communityId) {
      return res.status(400).json({ message: "Missing user or community ID." });
    }

    const community = await Community.findById(communityId);
    if (!community || community.communityAdmin.toString() !== userId) {
      return res.status(403).json({ message: "Unauthorized." });
    }

    const affiliate = await CommunityAffiliate.findOne({ communityId });
    if (!affiliate) {
      return res.status(404).json({ message: "Affiliate policy not found." });
    }

    affiliate.generationPolicy = newPolicy;
    await affiliate.save();

    return res.status(200).json({
      message: "Policy updated successfully.",
      generationPolicy: affiliate.generationPolicy,
    });

  } catch (err) {
    logErr("updateCommunityAffiliatePolicy", err);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Update community affiliate settings
 */
exports.updateCommunityAffiliateSettings = async (req, res) => {
  try {
    const adminUserId = req.userEmailAndId?.userId;
    const { communityId, rewardAmount, expirationInHours, maxUsers } = req.body;

    if (!adminUserId || !communityId) {
      return res.status(400).json({ message: "Missing user or community ID." });
    }

    const community = await Community.findById(communityId);
    if (!community || community.communityAdmin.toString() !== adminUserId) {
      return res.status(403).json({ message: "Unauthorized: Only the community admin can update settings." });
    }

    const affiliate = await CommunityAffiliate.findOne({ communityId });
    if (!affiliate) {
      return res.status(404).json({ message: "Community affiliate not found." });
    }

    let updated = {};

    if (typeof rewardAmount === 'number') {
      affiliate.rewardAmount = rewardAmount;
      updated.rewardAmount = rewardAmount;
    }

    if (typeof expirationInHours === 'number') {
      affiliate.expirationInHours = expirationInHours;
      affiliate.expiresAt = new Date(Date.now() + expirationInHours * 60 * 60 * 1000);
      updated.expirationInHours = expirationInHours;
      updated.expiresAt = affiliate.expiresAt;
    }

    if (typeof maxUsers === 'number') {
      // Only allow increasing maxUsers, not decreasing below current users
      if (maxUsers >= affiliate.currentUsers) {
        affiliate.maxUsers = maxUsers;
        updated.maxUsers = maxUsers;
        
        // Reactivate if was inactive due to max users and now has room
        if (!affiliate.isActive && affiliate.currentUsers < maxUsers && 
            (!affiliate.expiresAt || new Date() <= affiliate.expiresAt)) {
          affiliate.isActive = true;
          updated.isActive = true;
        }
      } else {
        return res.status(400).json({ 
          message: `Cannot set maxUsers (${maxUsers}) below current users (${affiliate.currentUsers}).` 
        });
      }
    }

    await affiliate.save();

    return res.status(200).json({
      message: "Community affiliate settings updated successfully.",
      updated
    });

  } catch (error) {
    logErr("updateCommunityAffiliateSettings", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Verify and claim affiliate reward on blockchain
 */
exports.verifyClaimAffiliateReward = async (req, res) => {
  const session = await mongoose.startSession();
  try {
    const affiliateUserId = req.userEmailAndId?.userId;
    const { txHash, expectedAmount, recipientWallet, claimId, tokenAddress } = req.body;

    if (
      !affiliateUserId ||
      !ethers.utils.isHexString(txHash) ||
      !recipientWallet ||
      isNaN(expectedAmount) ||
      !claimId ||
      !tokenAddress
    ) {
      return res.status(400).json({ message: "Missing or invalid input fields." });
    }

    // Find the specific claim
    const claim = await AffiliateClaim.findById(claimId);
    if (!claim) {
      return res.status(404).json({ message: "Claim not found." });
    }

    if (claim.claimant.toString() !== affiliateUserId) {
      return res.status(403).json({ message: "Unauthorized to verify this claim." });
    }

    if (claim.status === 'completed') {
      return res.status(400).json({ message: "Claim already completed." });
    }

    // Verify transaction on blockchain
    const provider = new ethers.providers.JsonRpcProvider(process.env.RPC_URL);
    const receipt = await provider.getTransactionReceipt(txHash);
    if (!receipt || receipt.status !== 1) {
      return res.status(400).json({ message: "Transaction failed or not found." });
    }

    const iface = new ethers.utils.Interface(ERC20_ABI);
    const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
    const decimals = await tokenContract.decimals();
    const expectedValue = ethers.utils.parseUnits(expectedAmount.toString(), decimals);

    const transferEvents = receipt.logs
      .filter(log => log.address.toLowerCase() === tokenAddress.toLowerCase())
      .map(log => {
        try {
          return iface.parseLog(log);
        } catch (_) {
          return null;
        }
      })
      .filter(event => event && event.name === "Transfer");

    const matchedEvent = transferEvents.find(event =>
      event.args.to.toLowerCase() === recipientWallet.toLowerCase() &&
      event.args.value.eq(expectedValue)
    );

    if (!matchedEvent) {
      return res.status(400).json({ message: "No matching transfer event found." });
    }

    await session.withTransaction(async () => {
      // Update claim status
      claim.status = 'completed';
      claim.txHash = txHash;
      await claim.save({ session });

      // Update affiliate earnings
      const affiliate = await Affiliate.findOne({ userId: affiliateUserId }).session(session);
      if (affiliate) {
        const numericAmount = parseFloat(expectedAmount);
        if (affiliate.rewardAmount >= numericAmount) {
          affiliate.rewardAmount -= numericAmount;
          await affiliate.save({ session });
        }
      }
    });

    return res.status(200).json({
      message: "Reward verified and recorded successfully.",
      txHash,
      claimId: claim._id
    });

  } catch (error) {
    logErr("verifyClaimAffiliateReward", error);
    return res.status(500).json({ message: "Internal server error." });
  } finally {
    await session.endSession();
  }
};

/**
 * Get all pending claims for a user
 */
exports.getPendingClaims = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });

    const pendingClaims = await AffiliateClaim.find({
      claimant: userId,
      status: 'pending'
    }).populate('referredUser', 'firstName lastName email createdAt');

    return res.status(200).json({
      pendingClaims,
      totalPendingUSD: pendingClaims.reduce((sum, claim) => sum + claim.amount, 0),
      totalPendingTokens: pendingClaims.reduce((sum, claim) => sum + claim.tokenAmount, 0)
    });

  } catch (error) {
    logErr("getPendingClaims", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Validate community affiliate code (for frontend to check before joining)
 */
exports.validateCommunityAffiliateCode = async (req, res) => {
  try {
    const { code, communityId } = req.params;
    const userId = req.userEmailAndId?.userId; // Optional - if user is logged in
    
    if (!code || !communityId) {
      return res.status(400).json({ message: "Affiliate code and community ID are required." });
    }

    const communityAffiliate = await CommunityAffiliate.findOne({ 
      affiliateCode: code.toUpperCase(),
      communityId: communityId,
      isActive: true 
    }).populate('userId', 'firstName lastName')
      .populate('communityId', 'communityName');

    if (!communityAffiliate) {
      return res.status(404).json({ 
        valid: false, 
        message: "Invalid or inactive community affiliate code." 
      });
    }

    // Get community details to check membership
    const community = await Community.findById(communityId);
    if (!community) {
      return res.status(404).json({ 
        valid: false, 
        message: "Community not found." 
      });
    }

    // If user is logged in, check if they're already a member
    if (userId && community.registeredMembers.includes(userId)) {
      return res.status(400).json({ 
        valid: false, 
        message: "You are already a member of this community." 
      });
    }

    // Check if community has reached its member limit
    if (community.registeredMembers.length >= community.communityMembers) {
      return res.status(400).json({ 
        valid: false, 
        message: "Community has reached its maximum member limit." 
      });
    }

    // Check expiration
    if (communityAffiliate.expiresAt && new Date() > communityAffiliate.expiresAt) {
      communityAffiliate.isActive = false;
      await communityAffiliate.save();
      return res.status(400).json({ 
        valid: false, 
        message: "Community affiliate link has expired." 
      });
    }

    // Check max users
    if (communityAffiliate.currentUsers >= communityAffiliate.maxUsers) {
      communityAffiliate.isActive = false;
      await communityAffiliate.save();
      return res.status(400).json({ 
        valid: false, 
        message: "Community affiliate link has reached maximum users." 
      });
    }

    return res.status(200).json({
      valid: true,
      data: {
        affiliateCode: communityAffiliate.affiliateCode,
        referrerName: `${communityAffiliate.userId.firstName} ${communityAffiliate.userId.lastName}`,
        communityName: communityAffiliate.communityId.communityName,
        spotsRemaining: communityAffiliate.maxUsers - communityAffiliate.currentUsers,
        expiresAt: communityAffiliate.expiresAt,
        rewards: {
          usdPerReferral: AFFILIATE_CONFIG.DEFAULT_USD_PER_REFERRAL
        },
        community: {
          totalMembers: community.registeredMembers.length,
          memberLimit: community.communityMembers
        }
      }
    });

  } catch (error) {
    logErr("validateCommunityAffiliateCode", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Get community affiliate statistics for a user
 */
exports.getCommunityAffiliateStats = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    const { communityId } = req.params;
    
    if (!userId) return res.status(401).json({ message: "Unauthorized." });
    if (!communityId) return res.status(400).json({ message: "Community ID is required." });

    const communityAffiliate = await CommunityAffiliate.findOne({ 
      userId, 
      communityId 
    })
      .populate('referrals', 'firstName lastName email createdAt')
      .populate('earningsHistory.referredUser', 'firstName lastName email')
      .populate('communityId', 'communityName');

    if (!communityAffiliate) {
      return res.status(404).json({ message: "No community affiliate record found." });
    }

    // Get pending claims for this community affiliate
    const pendingClaims = await AffiliateClaim.find({
      claimant: userId,
      affiliate: communityAffiliate.affiliateCode,
      status: 'pending'
    }).populate('referredUser', 'firstName lastName email');

    const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/dao/${communityId}/join?ref=${communityAffiliate.affiliateCode}`;

    return res.status(200).json({
      affiliateCode: communityAffiliate.affiliateCode,
      affiliateLink,
      communityName: communityAffiliate.communityId.communityName,
      stats: {
        totalReferrals: communityAffiliate.referrals.length,
        currentUsers: communityAffiliate.currentUsers,
        maxUsers: communityAffiliate.maxUsers,
        isActive: communityAffiliate.isActive,
        expiresAt: communityAffiliate.expiresAt,
        totalEarnings: communityAffiliate.rewardAmount
      },
      referrals: communityAffiliate.referrals,
      earningsHistory: communityAffiliate.earningsHistory,
      pendingClaims,
      generationPolicy: communityAffiliate.generationPolicy,
      isPolicyController: communityAffiliate.isPolicyController
    });

  } catch (error) {
    logErr("getCommunityAffiliateStats", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Get all community affiliate links for a user
 */
exports.getAllCommunityAffiliateLinks = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });

    const communityAffiliates = await CommunityAffiliate.find({ userId })
      .populate('communityId', 'communityName')
      .sort({ createdAt: -1 });

    const affiliateLinks = communityAffiliates.map(affiliate => {
      const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/dao/${affiliate.communityId._id}/join?ref=${affiliate.affiliateCode}`;
      
      return {
        affiliateCode: affiliate.affiliateCode,
        affiliateLink,
        communityId: affiliate.communityId._id,
        communityName: affiliate.communityId.communityName,
        stats: {
          currentUsers: affiliate.currentUsers,
          maxUsers: affiliate.maxUsers,
          isActive: affiliate.isActive,
          expiresAt: affiliate.expiresAt,
          totalEarnings: affiliate.rewardAmount,
          totalReferrals: affiliate.referrals.length
        },
        generationPolicy: affiliate.generationPolicy,
        isPolicyController: affiliate.isPolicyController
      };
    });

    return res.status(200).json({
      totalLinks: affiliateLinks.length,
      affiliateLinks
    });

  } catch (error) {
    logErr("getAllCommunityAffiliateLinks", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Regenerate token for affiliate link
 */
exports.regenerateAffiliateToken = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    if (!userId) return res.status(401).json({ message: "Unauthorized." });

    const affiliate = await Affiliate.findOne({ userId });
    if (!affiliate) {
      return res.status(404).json({ message: "No affiliate record found." });
    }

    // Generate new token
    const newToken = uuidv4().replace(/-/g, '').slice(0, 16);
    const newTokenExpiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // 24 hours

    // Update affiliate record
    affiliate.linkToken = newToken;
    affiliate.tokenExpiresAt = newTokenExpiresAt;
    
    // Update the link URL
    affiliate.linkUrl = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/black_panther_dao?ref=${affiliate.affiliateCode}&token=${newToken}`;
    
    await affiliate.save();

    return res.status(200).json({
      message: "Affiliate token regenerated successfully!",
      affiliateCode: affiliate.affiliateCode,
      affiliateLink: affiliate.linkUrl,
      newToken,
      tokenExpiresAt: newTokenExpiresAt
    });

  } catch (error) {
    logErr("regenerateAffiliateToken", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

/**
 * Regenerate token for community affiliate link
 */
exports.regenerateCommunityAffiliateToken = async (req, res) => {
  try {
    const userId = req.userEmailAndId?.userId;
    const { communityId } = req.params;
    
    if (!userId) return res.status(401).json({ message: "Unauthorized." });
    if (!communityId) return res.status(400).json({ message: "Community ID is required." });

    const communityAffiliate = await CommunityAffiliate.findOne({ 
      userId, 
      communityId 
    });

    if (!communityAffiliate) {
      return res.status(404).json({ message: "No community affiliate record found." });
    }

    // Generate new token
    const newToken = uuidv4().replace(/-/g, '').slice(0, 16);
    const newTokenExpiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // 24 hours

    // Update community affiliate record
    communityAffiliate.linkToken = newToken;
    communityAffiliate.tokenExpiresAt = newTokenExpiresAt;
    
    await communityAffiliate.save();

    const affiliateLink = `${AFFILIATE_CONFIG.FRONTEND_BASE_URL}/dao/${communityId}/join?ref=${communityAffiliate.affiliateCode}&token=${newToken}`;

    return res.status(200).json({
      message: "Community affiliate token regenerated successfully!",
      affiliateCode: communityAffiliate.affiliateCode,
      affiliateLink,
      newToken,
      tokenExpiresAt: newTokenExpiresAt
    });

  } catch (error) {
    logErr("regenerateCommunityAffiliateToken", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};
