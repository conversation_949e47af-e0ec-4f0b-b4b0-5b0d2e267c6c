require('dotenv').config();
const axios = require('axios');
const moment = require('moment');

const MpesaSTK = require("../models/payments");
const { bnbPurchaseContract } = require('../config/constracts');
const ConvertFromBNBToUSD = require('../utils/convertFromBNBToUSD');
const { ethers } = require('ethers');


const consumerKey = process.env.CONSUMER_KEY;
const consumerSecret = process.env.CONSUMER_SECRET;
const shortcode = process.env.MPESA_SHORTCODE;
const passkey = process.env.MPESA_PASSKEY;
const callbackURL = process.env.MPESA_CALLBACK_URL;




exports.stkPush = async (req, res) => {
  //let callbackURL = `${process.env.MPESA_CALLBACK_URL}`;
  const { phone, amount, bnbAmount, currency } = req.body;
  // console.log("Mpesa body: ", req.body)

  if (!phone || !amount) {
    return res.status(400).json({ message: 'Phone and amount are required' });
  }

  if (bnbAmount != currency.original.value) {
    return res.status(400).json({ message: 'BNB amount does not match the original value' });
  }

  const oneBNBValueInUsd = await ConvertFromBNBToUSD(1)
  const _bnbAmount = bnbAmount / oneBNBValueInUsd;
  // Limit to 18 decimal places to avoid ethers precision errors
  const bnbAmountFormatted = _bnbAmount.toFixed(18);
  const bnbAmountToBuy = ethers.parseEther(bnbAmountFormatted);
  const contractBalance = await bnbPurchaseContract.getContractBalance()
  const formattedContractBalance = ethers.formatEther(contractBalance);
  if(Number(contractBalance) < Number(bnbAmountToBuy)) {
    return res.status(400).json({ message: `Please enter an amount below ${formattedContractBalance} BNB` });
  }


  let accessToken;

  //Generate Access Token
  try {
    const auth = Buffer.from(`${consumerKey}:${consumerSecret}`).toString('base64');
    const tokenRes = await axios.get(
      'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
      {
        headers: {
          Authorization: `Basic ${auth}`,
        },
      }
    );
    accessToken = tokenRes.data.access_token;

  } catch (tokenError) {
    console.error('❌ TOKEN ERROR:', tokenError.response?.data || tokenError.message);
    return res.status(500).json({
      message: 'Failed to generate access token',
      errorMessage: tokenError.response?.data || tokenError.message,
    });
  }


  //Make STK Push
  try {
    const timestamp = moment().format('YYYYMMDDHHmmss');
    const password = Buffer.from(shortcode + passkey + timestamp).toString('base64');

    const payload = {
      BusinessShortCode: shortcode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: Math.round(amount),
      PartyA: phone,
      PartyB: shortcode,
      PhoneNumber: phone,
      CallBackURL: callbackURL,
      AccountReference: 'INV001',
      TransactionDesc: 'Payment for goods',
    };

    const stkRes = await axios.post(
      'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
      payload,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
    try {
      const { phone, walletAddress, provider, bnbAmount } = req.body;
      const oneBNBValueInUsd = await ConvertFromBNBToUSD(1)
      const _bnbAmount = bnbAmount / oneBNBValueInUsd;
      // Limit to 8 decimal places for storage
      const bnbAmountForStorage = _bnbAmount.toFixed(8);
      const stkRequest = new MpesaSTK({
        phoneNumber: phone,
        sessionId: stkRes.data.CheckoutRequestID,
        walletAddress,
        amount: amount,
        bnbAmount: bnbAmountForStorage,
        status: "pending",
        provider
      });

      const savedRequest = await stkRequest.save();

      res.status(200).json({
        message: 'succeded',
        sessionID: stkRes.data.CheckoutRequestID
      });
      try {
        const response = await axios.post('https://mpesa-callback-proxy.onrender.com/stk-push', {
          callbackUrl: callbackURL, // 🔁 your real backend callback URL
          CheckoutRequestID: stkRes.data.CheckoutRequestID,                    // 🔁 from the real STK Push response
        });

        console.log('✅ STK Simulation Request Sent:', response.data);
      } catch (error) {
        console.error('❌ Error sending simulation:', error.message);
      }

    } catch (err) {
      res.status(500).json({ error: 'Failed to create STK request' });
    }
  } catch (stkError) {
    console.error('STK PUSH ERROR:', stkError.response?.data || stkError.message);
    return res.status(500).json({
      message: 'STK push failed',
      error: stkError.response?.data.errorMessage
    });
  }

};




exports.callBackMpesa = async (req, res, next) => {
  const callbackData = req.body;

  const checkoutRequestID = callbackData.Body.stkCallback.CheckoutRequestID;


  const resultCode = callbackData.Body.stkCallback.ResultCode;


  if (resultCode === 0) {
    // ✅ Success
    // return {
    //   status: 'success',
    //   message: 'Payment completed successfully.',
    //   checkoutRequestID: checkoutRequestID
    // };
    const status = 'success';

    try {


      await MpesaSTK.updateOne(
        { sessionId: checkoutRequestID },
        { $set: { status } }
      );

      const transaction = await MpesaSTK.findOne({ sessionId: checkoutRequestID });

      const recipientAddress = transaction.walletAddress;
      // Ensure proper formatting for ethers.parseEther
      const bnbAmountFormatted = parseFloat(transaction.bnbAmount).toFixed(18);
      const bnbAmount = ethers.parseEther(bnbAmountFormatted);

      if (recipientAddress != "" && bnbAmount > 0) {
        try {
          const transferTXn = await bnbPurchaseContract.transferBNB(recipientAddress, bnbAmount)
          await transferTXn.wait();

          await MpesaSTK.updateOne(
            { sessionId: checkoutRequestID },
            {
              $set: {
                bnbStatus: "success",
                transactionHash: transferTXn.hash
              }
            }
          );
        } catch (err) {
          console.error("Transfer error:", err);
          res.status(500).json({ error: "BNB transfer failed", details: err.message });
        }
      }


      res.status(200).json({ message: 'Callback received successfully' })
      console.log(`✅ Updated status to Sucess`);
    } catch (err) {
      console.error('Error updating transaction status:', err.message);
    }

  } else {
    // Handle known failures
    let reason;

    switch (resultCode) {
      case 1:
        reason = 'Insufficient funds. Please top up and try again.';
        break;
      case 1032:
        reason = 'Payment was cancelled by the user.';
        break;
      case 1006:
        reason = 'Unable to process request. Please try again later.';
        break;
      case 17:
        reason = 'Transaction declined. Please contact support.';
        break;
      case 20:
        reason = 'Phone number not registered on M-Pesa.';
        break;
      default:
        reason = `Failed `;
    }

    // return {
    //   status: 'failed',
    //   reason,
    //   resultCode: resultCode,
    //   checkoutRequestID: checkoutRequestID
    // };



    const status = `failed:${reason}`;

    try {
      await MpesaSTK.updateOne(
        { checkoutRequestID },
        { $set: { status } }
      );


      res.status(200).json({ message: 'Callback received successfully' })
      console.log(`✅ Updated status to failed`);
    } catch (err) {
      console.error('Error updating transaction status:', err.message);
    }
    res.status(200).json({ message: 'Callback received successfully' })
  }
};



exports.mpesaStatus = async (req, res, next) => {
  const sessionID = req.query.sessionID;


  if (!sessionID) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing sessionID'
    });
  }

  try {
    const txn = await MpesaSTK.findOne({ sessionId: sessionID });

    if (!txn) {
      return res.status(404).json({
        status: 'error',
        message: 'Transaction not found'
      });
    }
    console.log("This the current status: ", txn.status)

    // Return just the status
    return res.status(200).json({
      sessionID,
      status: txn.status, // e.g., 'pending', 'success', 'failed'
      bnbStatus: txn.bnbStatus, 
      transactionHash: txn.transactionHash,
      message: 'Status fetched successfully'
    });

  } catch (err) {
    console.error('Error fetching transaction status:', err.message);
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
};
