const MiniGame = require('../models/miniGame'); // Path to your model file

// Get all records by userId
exports.GetMyMiniGameInfoByUserId = async (req, res, next) => {
  try {
    const records = await MiniGame.find({ userId: req.userEmailAndId.userId });
    if (!records) {
      return res.status(404).json({ message: "No records found for this userId" });
    }

    res.status(200).json({
        message: "Fetched successfully",
        records
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update number of coins using userId
exports.UpdateCoinsByUserId = async (req, res) => {
    try {
      const userId  = req.userEmailAndId.userId
      const { coins } = req.body;
  
      const updatedRecord = await MiniGame.findOneAndUpdate(
        { userId },
        { $set: { coins } },
        { new: true }
      );
  
      if (!updatedRecord) {
        return res.status(404).json({ message: "User not found" });
      }
  
      res.status(200).json(updatedRecord);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  };

  // Update earnPerTap using userId
  exports.UpdateEarnPerTapByUserId = async (req, res) => {
    try {
      const userId = req.userEmailAndId.userId;
      const { earnPerTap, numberOfCoinsToPay } = req.body;
  
      // Validate required fields
      if (!earnPerTap || !numberOfCoinsToPay) {
        return res.status(400).json({
          message: "Both earnPerTap and numberOfCoinsToPay are required.",
        });
      }
  
      // Find the user's current record
      const miniGame = await MiniGame.findOne({ userId });
      if (!miniGame) {
        return res.status(404).json({ message: "User not found" });
      }
  
      // Check if user has enough coins to pay
      if (miniGame.coins < numberOfCoinsToPay) {
        return res.status(400).json({
          message: "Insufficient coins to perform this action.",
        });
      }
  
      // Deduct coins and update earnPerTap
      miniGame.coins -= numberOfCoinsToPay;
      miniGame.earnPerTap = earnPerTap;
  
      // Save updated record
      const updatedRecord = await miniGame.save();
  
      res.status(200).json({
        message: "EarnPerTap updated successfully, and coins deducted.",
        updatedRecord,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  };
  

  // Check if coins >= coinsToLevelUp and update level and coinsToLevelUp
exports.CheckAndUpdateLevel = async (req, res) => {
    try {
      const userId = req.userEmailAndId.userId
  
      const user = await MiniGame.findOne({ userId });
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
  
      let { coins, level } = user;
      let nextLevel = level + 1;
  
      // Define coin thresholds for levels
      const levelThresholds = {
        2: 15000, // 15K
        3: 50000, // 50K
        4: 100000, // 100K
        5: 200000, // 200K
      };
  
      if (coins >= levelThresholds[nextLevel]) {
        user.level = nextLevel;
        user.coinsToLevelUp = `${levelThresholds[nextLevel] / 1000}K`; // e.g., "50K"
        await user.save();
  
        return res.status(200).json({
          message: `Level updated to ${nextLevel}`,
          user,
        });
      }
  
      res.status(200).json({ message: "Coins not sufficient for level up", user });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  };
  
  // Update profitPerDay using userId
exports.UpdateProfitPerDayByUserId = async (req, res) => {
    try {
      const userId = req.userEmailAndId.userId
      const { profitPerDay } = req.body;
  
      const updatedRecord = await MiniGame.findOneAndUpdate(
        { userId },
        { $set: { profitPerDay } },
        { new: true }
      );
  
      if (!updatedRecord) {
        return res.status(404).json({ message: "User not found" });
      }
  
      res.status(200).json(updatedRecord);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  };

  // Update energyLimit using userId
  exports.UpdateEnergyLimitByUserId = async (req, res) => {
    try {
      const userId = req.userEmailAndId.userId;
      const { energyLimit, numberOfCoinsToPay } = req.body;
  
      // Validate required fields
      if (!energyLimit || !numberOfCoinsToPay) {
        return res.status(400).json({
          message: "Both energyLimit and numberOfCoinsToPay are required.",
        });
      }
  
      // Find the user's current record
      const miniGame = await MiniGame.findOne({ userId });
      if (!miniGame) {
        return res.status(404).json({ message: "User not found" });
      }
  
      // Check if user has enough coins to pay
      if (miniGame.coins < numberOfCoinsToPay) {
        return res.status(400).json({
          message: "Insufficient coins to perform this action.",
        });
      }
  
      // Deduct coins and update energyLimit
      miniGame.coins -= numberOfCoinsToPay;
      miniGame.energyLimit = energyLimit;
  
      // Save updated record
      const updatedRecord = await miniGame.save();
  
      res.status(200).json({
        message: "Energy limit updated successfully, and coins deducted.",
        updatedRecord,
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  };
  
  
  
  
  
