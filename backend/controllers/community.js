require("dotenv").config();
const sgMail = require("@sendgrid/mail");
sgMail.setApiKey(process.env.SENDGRID_API);
const Community = require('../models/community');
const User = require('../models/user');
const blackPantherLogo = "https://i.pinimg.com/736x/89/48/7e/89487e40ea960b6283a5f28d20ca9821.jpg";
const { emailTemp } = require("../public/html files/emailTemp");
const CommunityDisbursementTrx = require("../models/communityDisbursementTrx");

// Creating a community
exports.createCommunity = async (req, res) => {
  try {
    const {
      communityName,
      communityDescription,
      communityDetailedDescription,
      category,
      visibility,
      logo,
      contributionType,
      contributionAmounts,
      contributionWallet,
      ownerWallet
    } = req.body;

    // Use uploaded file URL if present, otherwise use logo from body or empty string
    const fileUrl = req.body.fileUrl?.[0]?.url || logo || "";

    const newCommunity = new Community({
      communityName,
      communityDescription,
      communityDetailedDescription,
      category,
      visibility,
      logo: fileUrl,
      contributionType,
      contributionAmounts,
      contributionWallet,
      ownerWallet,
      communityAdmin: req.userEmailAndId.userId,
    });

    await newCommunity.save();

    return res.status(201).json({
      message: 'Community Successfully Created!!',
      community: newCommunity,
    });

  } catch (error) {
    // Duplicate community name error
    if (error.code === 11000 && error.keyPattern && error.keyPattern.communityName) {
      return res.status(400).json({ error: "A community with this name already exists. Please choose a different name." });
    }
    // Validation error
    if (error.name === "ValidationError") {
      return res.status(400).json({ error: "Some required fields are missing or invalid. Please check your input." });
    }
    // General fallback
    return res.status(400).json({ error: error.message });
  }
};


// Request to join community
exports.requestToJoin = async (req, res) => {
  try {
     const userId = req.user?._id || req.userEmailAndId?.userId;
    const { communityId, acceptedTerms } = req.body;

    if (!acceptedTerms) return res.status(400).json({ error: "Terms must be accepted to join" });

    const user = await User.findById(userId);
    const community = await Community
      .findById(communityId)
      .populate("communityAdmin");

    if (!user || !community) return res.status(404).json({ error: "User or Community not found!!" });
    

    if (community.registeredMembers.length >= community.communityMembers) return res.status(400).json({ error: "The community capacity has been reached!!" });


    const alreadyRequested = community.pendingRequests.some(r =>
      r.user.equals(userId)
    );


    if (alreadyRequested) return res.status(400).json({ error: "You have already requested to join!!" });
    

    community.pendingRequests.push({ user: userId });
    await community.save();


    const admin = community.communityAdmin;
    const content = `
      <div style="max-width: 600px; margin: auto; font-family: Arial, sans-serif; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
        <div style="display: flex; flex-wrap: wrap;">

          <div style="flex: 0 0 45%; padding: 20px; box-sizing: border-box;">
            <h2 style="margin-top: 0; color: #333;">Request To Join ${community.communityName}</h2>
            <p style="color: #555;">Hello ${admin.firstName},</p>
            <p style="color: #555;">
              ${user.firstName} ${user.lastName} is requesting to join your community.
              Please log in to approve or decline the request.
            </p>
            <div style="text-align: center; margin-top: 20px;">
              <a href="https://yourdomain.com/communities/${community._id}/requests" 
                 style="background-color: rgb(8, 8, 8); color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Check Request
              </a>
            </div>
          </div>

          <div style="flex: 0 0 10%;"></div>

          <div style="flex: 0 0 45%;">
            <img src="${blackPantherLogo}" alt="Black Panther Logo" 
                 style="width: 100%; height: 100%; object-fit: cover; display: block;" />
          </div>

        </div>
      </div>
    `;

    const message = {
      to: admin.email,
      from: "<EMAIL>",
      subject: `Join Request: ${user.firstName} wants to join ${community.communityName}`,
      html: emailTemp(content),
    };
    await sgMail.send(message);

    return res
      .status(200)
      .json({ message: "Join request submitted and email sent to admin." });

  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};


// view request
exports.viewJoinRequests = async (req, res) => {
  try {
    const communityId = req.params.communityId;
    const community = await Community
      .findById(communityId)
      .populate('pendingRequests.user', 'firstName lastName email');

    if (!community) return res.status(404).json({ error: "Community not found" });

    // Support both req.user and req.userEmailAndId
    const adminId = req.user?._id || req.userEmailAndId?.userId;
    if (!adminId) return res.status(401).json({ error: "Unauthorized: user not found in request." });

    if (!community.communityAdmin.equals(adminId)) return res.status(403).json({ error: "Access denied" });
    

    return res.status(200).json({
      communityName: community.communityName,
      pendingRequests: community.pendingRequests.map(r => ({
        userId:   r.user?._id,
        name:     r.user ? `${r.user.firstName} ${r.user.lastName}` : '',
        email:    r.user?.email,
        requestedAt: r.requestedAt
      }))
    });
  } catch (err) {
    return res.status(500).json({ error: err.message });
  }
};

// Accept or decline request
exports.acceptOrDeclineRequests = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { userId, action } = req.body; 

    if (!["accept", "decline"].includes(action)) {
      return res.status(400).json({ error: "Invalid action. Must be 'accept' or 'decline'." });
    }

    const community = await Community.findById(communityId).populate("communityAdmin");
    if (!community) return res.status(404).json({ error: "Community not found" });

    // Support both req.user and req.userEmailAndId
    const adminId = req.user?._id || req.userEmailAndId?.userId;
    if (!adminId) return res.status(401).json({ error: "Unauthorized: user not found in request." });

    if (!community.communityAdmin._id.equals(adminId)) {
      return res.status(403).json({ error: "Access denied. Only the community admin can perform this action." });
    }

    const requestIndex = community.pendingRequests.findIndex(r => r.user.equals(userId));
    if (requestIndex === -1) {
      return res.status(404).json({ error: "Join request not found" });
    }

    if (action === "accept") {
      if (community.registeredMembers.length >= community.communityMembers) return res.status(400).json({ error: "Community is at full capacity." });
      
      if (!community.registeredMembers.includes(userId)) {
        community.registeredMembers.push(userId);
      }
    }

    community.pendingRequests.splice(requestIndex, 1);
    await community.save();

    const user = await User.findById(userId);
    if (user) {
      const statusText = action === "accept" ? "accepted" : "declined";

      const emailHeading = action === "accept"
        ? `Welcome to ${community.communityName}!`
        : `Request to Join ${community.communityName} Declined`;
      const emailMessage = action === "accept"
        ? `Congratulations, ${user.firstName}! Your request to join the community <strong>${community.communityName}</strong> has been <strong>accepted</strong>. You can now log in and start participating.`
        : `Hello ${user.firstName}, we’re sorry to inform you that your request to join the community <strong>${community.communityName}</strong> has been <strong>declined</strong>.`;

      const content = `
        <div style="max-width: 600px; margin: auto; font-family: Arial, sans-serif; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
          <div style="display: flex; flex-wrap: wrap;">
            <div style="flex: 0 0 45%; padding: 20px; box-sizing: border-box;">
              <h2 style="margin-top: 0; color: #333;">${emailHeading}</h2>
              <p style="color: #555;">${emailMessage}</p>
              ${action === "accept" ? `
                <div style="text-align: center; margin-top: 20px;">
                  <a href="https://yourdomain.com/communities/${community._id}" 
                     style="background-color: rgb(8, 8, 8); color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
                    Go to Community
                  </a>
                </div>
              ` : ``}
            </div>

            <div style="flex: 0 0 10%;"></div>

            <div style="flex: 0 0 45%;">
              <img src="${blackPantherLogo}" alt="Community Logo" 
                   style="width: 100%; height: 100%; object-fit: cover; display: block;" />
            </div>
          </div>
        </div>
      `;

      const msg = {
        to: user.email,
        from: "<EMAIL>",
        subject: action === "accept"
          ? `Your request to join ${community.communityName} was accepted`
          : `Your request to join ${community.communityName} was declined`,
        html: emailTemp(content),
      };

      sgMail.send(msg).catch(err => console.error("User notification email error:", err));
    }

    return res.status(200).json({
      message: `User has been ${action === "accept" ? "accepted" : "declined"} successfully and notified.`,
    });

  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};



// Edit Community Name and Description
exports.editCommunity = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { communityName, communityDescription } = req.body;

    const community = await Community.findById(communityId);
    if (!community) return res.status(404).json({ error: "Community not found" });

    if (!community.communityAdmin.equals(req.user._id)) {
      return res.status(403).json({ error: "Only the community admin can edit this community." });
    }

    if (communityName) community.communityName = communityName;
    if (communityDescription) community.communityDescription = communityDescription;

    await community.save();

    return res.status(200).json({
      message: "Community updated successfully.",
      community
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

// Get all communities
exports.getAllCommunities = async (req, res) => {
  try {
    // Populate full communityAdmin object (remove field selection)
    const communities = await Community.find().populate('communityAdmin');
    return res.status(200).json({ communities });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};










exports.getAllCommunityInfoToApprove = async (req, res) => {
  try {
    const communities = await Community.find().populate('communityAdmin', '_id firstName lastName country email');
    return res.status(200).json({ communities });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

exports.approveCommunity = async (req, res) => {
  try {
    const { communityId } = req.params;

    const updated = await Community.findByIdAndUpdate(
      communityId,
      { communityAcceptanceStatus: 'approved', rejectionReason: '' },
      { new: true }
    );

    if (!updated) {
      return res.status(404).json({ message: 'Community not found' });
    }

    res.status(200).json({ message: 'Community approved', community: updated });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
};

exports.rejectCommunity = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { rejectionReason } = req.body;

    if (!rejectionReason) {
      return res.status(400).json({ message: 'Rejection reason is required' });
    }

    const updated = await Community.findByIdAndUpdate(
      communityId,
      {
        communityAcceptanceStatus: 'rejected',
        rejectionReason
      },
      { new: true }
    );

    if (!updated) {
      return res.status(404).json({ message: 'Community not found' });
    }

    res.status(200).json({ message: 'Community rejected', community: updated });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
};


exports.logCommunityDisbursement = async (req, res) => {
  try {
    const { communityId } = req.params;
    const { amount, method, trxHash, receipt, notes } = req.body;

    const newDisbursement = new CommunityDisbursementTrx({
      communityId,
      amount,
      method,
      trxHash,
      receipt,
      notes,
      createdBy: req.userEmailAndId.userId,
    });

    await newDisbursement.save();

    res.status(200).json({ message: 'Disbursement logged successfully', disbursement: newDisbursement });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error });
  }
};
