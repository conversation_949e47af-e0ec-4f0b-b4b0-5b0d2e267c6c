const Invite = require("../models/invite");
const sgMail = require("@sendgrid/mail");
const crypto = require("crypto");
require("dotenv").config();
sgMail.setApiKey(process.env.SENDGRID_API);
const { emailInvite } = require("../public/html files/emailInvite");

const websiteUrl = "https://www.staging.blackpanthertkn.com/dashboard";



exports.InviteFriend = async (req, res) => {
  const { email, message } = req.body;
  if (!email) return res.status(400).json({ message: "Email is required" });

  try {
    const existingInvite = await Invite.findOne({ 
      email, 
      inviteType: 'email' 
    });

    if (existingInvite) {
      return res.status(409).json({ 
        message: "This email has already been invited",
        invitedAt: existingInvite.invitedAt,
        accepted: existingInvite.accepted
      });
    }

    const invite = await Invite.create({ 
      email, 
      message, 
      inviteType: 'email' 
    });

    const msg = {
      to: email,
      from: {
        email: "<EMAIL>",
        name: "Black Panther Team"
      },
      subject: "You've been invited to join Black Panther DAO",
      html: emailInvite(websiteUrl, message),
      replyTo: "<EMAIL>",
      headers: {
        "List-Unsubscribe": "<mailto:<EMAIL>>"
      },
      mailSettings: {
        sandboxMode: { enable: false }
      },
      trackingSettings: {
        clickTracking: { enable: false },
        openTracking: { enable: false }
      }
    };

    // Add logging
    console.log("Sending email to:", email);
    
    const [response] = await sgMail.send(msg);
    console.log("SendGrid Response:", response?.statusCode);

    if (response?.statusCode === 202) {
      await Invite.findByIdAndUpdate(invite._id, {
        status: 'sent',
        sentAt: new Date()
      });
      
      return res.status(200).json({ 
        message: "Invitation sent successfully",
        inviteId: invite._id
      });
    }

  } catch (error) {
    console.error("Invite error:", error);
   
    if (error.code === 11000) {
      if (error.message.includes('email')) {
        return res.status(409).json({ 
          message: "This email has already been invited",
          error: "Duplicate email invitation"
        });
      }
      if (error.message.includes('linkToken')) {
        return res.status(409).json({ 
          message: "Invite link already exists",
          error: "Duplicate link token"
        });
      }
      return res.status(409).json({ 
        message: "Duplicate invitation detected",
        error: "Duplicate key error"
      });
    }
    res
      .status(500)
      .json({ message: "Failed to send invitation", error: error.message });
  }
};




// You can generate a unique link/token here if needed
function generateInviteLink(baseUrl = "https://communityapp.com/join/black-panther-dao", token = "") {
  return token ? `${baseUrl}?token=${token}` : baseUrl;
}

exports.GenerateShareLink = async (req, res) => {
  const { expiration = "1 day", maxUses = 100, baseUrl } = req.body;

  try {
    // Generate a unique token and save to DB for tracking
    const token = crypto.randomBytes(16).toString("hex");
    
    // Calculate expiration date
    const expirationDate = new Date();
    if (expiration === "1 day") {
      expirationDate.setDate(expirationDate.getDate() + 1);
    } else if (expiration === "1 week") {
      expirationDate.setDate(expirationDate.getDate() + 7);
    } else if (expiration === "1 month") {
      expirationDate.setMonth(expirationDate.getMonth() + 1);
    } else {
      // Default to 1 day
      expirationDate.setDate(expirationDate.getDate() + 1);
    }

    // Create invite record with linkToken and proper inviteType
    await Invite.create({ 
      linkToken: token, 
      expiration: expirationDate, 
      maxUsers: maxUses,
      baseUrl: baseUrl || "https://www.staging.blackpanthertkn.com/dashboard",
      inviteType: 'link'
    });

    const inviteLink = generateInviteLink(
      baseUrl || "https://www.staging.blackpanthertkn.com/dashboard", 
      token
    );

    res.status(200).json({
      inviteLink,
      token,
      expiration: expirationDate,
      maxUses,
      message: "Shareable invite link generated successfully"
    });
  } catch (error) {
    console.error("Share link error:", error);
    
    // Handle MongoDB duplicate key error specifically
    if (error.code === 11000) {
      if (error.message.includes('linkToken')) {
        return res.status(409).json({ 
          message: "Invite link token already exists, please try again",
          error: "Duplicate link token"
        });
      }
      return res.status(409).json({ 
        message: "Duplicate link detected",
        error: "Duplicate key error"
      });
    }
    
    res.status(500).json({ message: "Failed to generate invite link", error: error.message });
  }
};