const Event = require("../models/events");
const mongoose = require("mongoose");
const sgMail = require("@sendgrid/mail");
sgMail.setApiKey(process.env.SENDGRID_API);
const { emailEvent } = require("../public/html files/emailEvent");


/**
 * Combine a date string (ISO) and a time string (e.g. "11:30am") into a UTC Date object.
 * Assumes time is in EAT (East Africa Time, UTC+3) unless you store timezone.
 */function getEventDateTimeUTC(date, time, tzOffsetMinutes = 180) {
  // date: "2025-06-29T00:00:00.000Z" or "2025-06-29"
  // time: "12:00pm"
  let [hours, minutes] = [0, 0];
  let t = (time || '').trim().toLowerCase();
  if (t.includes('am') || t.includes('pm')) {
    let match = t.match(/(\d{1,2}):(\d{2})\s*(am|pm)/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
      if (match[3] === 'pm' && hours !== 12) hours += 12;
      if (match[3] === 'am' && hours === 12) hours = 0;
    }
  } else {
    let match = t.match(/(\d{1,2}):(\d{2})/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
    }
  }
  // Create a date in EAT (UTC+3)
  let d = new Date(date);
  d.setUTCHours(hours - tzOffsetMinutes / 60, minutes, 0, 0);
  return d;
}

function getEventEndDateTimeUTC(date, time, duration, tzOffsetMinutes = 180) {
  // Use your existing getEventDateTimeUTC to get the start
  const start = getEventDateTimeUTC(date, time, tzOffsetMinutes);
  // Add duration (in minutes) to get end time
  return new Date(start.getTime() + (duration || 0) * 60000);
}

// Create Event
exports.createEvent = async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    const { title, description, date, time, duration, meetingLink } = req.body;

    if (!title || !description || !date || !time || !duration || !meetingLink) {
      await session.abortTransaction();
      session.endSession();
      return res.status(400).json({ message: "Missing required fields" });
    }

    const event = await Event.create([{
      title,
      description,
      date,
      time,
      duration,
      meetingLink,
      attendees: [],
      questions: [],
      isPast: false
    }], { session });

    await session.commitTransaction();
    session.endSession();

    res.status(201).json({ message: "Event created", event: event[0] });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Update Event
exports.updateEvent = async (req, res) => {
  try {
    const event = await Event.findByIdAndUpdate(
      req.params.eventId,
      req.body,
      { new: true }
    );
    if (!event) return res.status(404).json({ message: "Event not found" });
    res.json({ message: "Event updated", event });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Delete Event
exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findByIdAndDelete(req.params.eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });
    res.json({ message: "Event deleted" });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// List Events (all, upcoming, past)
exports.listEvents = async (req, res) => {
  try {
    const now = new Date();
    const tzOffsetMinutes = 180; // EAT (UTC+3)
    const allEvents = await Event.find({}).sort({ date: 1 });

    let filtered;
    if (req.query.type === "upcoming") {
      filtered = allEvents.filter(ev => {
        const eventEndDateTime = getEventEndDateTimeUTC(ev.date, ev.time, ev.duration, tzOffsetMinutes);
        return eventEndDateTime > now;
      });
    } else if (req.query.type === "past") {
      filtered = allEvents.filter(ev => {
        const eventEndDateTime = getEventEndDateTimeUTC(ev.date, ev.time, ev.duration, tzOffsetMinutes);
        return eventEndDateTime <= now;
      });
    } else {
      filtered = allEvents;
    }
    res.json(filtered);
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Get Single Event
exports.getEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) return res.status(404).json({ message: "Event not found" });

    const now = new Date();
    const tzOffsetMinutes = 180; // EAT
    const eventDateTime = getEventDateTimeUTC(event.date, event.time, tzOffsetMinutes);
    const isPast = eventDateTime < now;

    res.json({ ...event.toObject(), isPast });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Attend Event
exports.attendEvent = async (req, res) => {
  try {
    const { name, email } = req.body;
    if (!name || !email) return res.status(400).json({ message: "Name and email required" });

    const event = await Event.findById(req.params.eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (event.attendees.some(a => a.email === email)) {
      return res.status(400).json({ message: "Already attending" });
    }

    event.attendees.push({ name, email });
    await event.save();

    // Send email with meeting link
    const msg = {
      to: email,
      from: "<EMAIL>",
      subject: `You're attending: ${event.title}`,
      html: emailEvent(event.meetingLink, event.title),
      replyTo: "<EMAIL>",
      trackingSettings: {
        clickTracking: { enable: false, enableText: false }
      }
    };
    await sgMail.send(msg);

    res.json({ message: "Attendance confirmed. Email sent with meeting link.", attendees: event.attendees });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Submit Question
exports.submitQuestion = async (req, res) => {
  try {
    const { text, submittedBy } = req.body;
    if (!text || !submittedBy) return res.status(400).json({ message: "Question and submitter required" });

    const event = await Event.findById(req.params.eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    event.questions.push({ text, submittedBy });
    await event.save();
    res.json({ message: "Question submitted", questions: event.questions });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Duplicate an event
exports.duplicateEvent = async (req, res) => {
  try {
    const original = await Event.findById(req.params.eventId);
    if (!original) return res.status(404).json({ message: "Event not found" });

    // Exclude attendees and questions from duplication
    const { _id, createdAt, updatedAt, attendees, questions, ...data } = original.toObject();
    const duplicated = new Event({
      ...data,
      title: data.title,
      attendees: [],
      questions: [],
    });
    await duplicated.save();
    res.status(201).json({ message: "Event duplicated", event: duplicated });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};

// Subscribe
exports.subscribeReminder = async (req, res) => {
  const { eventId } = req.params;
  const { email } = req.body;
  try {
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ error: 'Event not found' });
    if (!event.reminders.includes(email)) {
      event.reminders.push(email);
      await event.save();
     
    }
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: 'Could not subscribe for reminder' });
  }
};

// Unsubscribe
exports.unsubscribeReminder = async (req, res) => {
  const { eventId } = req.params;
  const { email } = req.body;
  try {
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ error: 'Event not found' });
    event.reminders = event.reminders.filter(e => e !== email);
    await event.save();
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ error: 'Could not unsubscribe from reminder' });
  }
};



exports.addRecordingLink = async (req, res) => {
  try {
    const { link } = req.body;
    if (!link) return res.status(400).json({ message: "Recording link required" });

    const event = await Event.findById(req.params.eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    event.recordingLinks = event.recordingLinks || [];
    event.recordingLinks.push(link);
    await event.save();

    res.json({ message: "Recording link added", recordingLinks: event.recordingLinks });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};



exports.editRecordingLink = async (req, res) => {
  try {
    const { index, link } = req.body;
    if (typeof index !== "number" || !link) {
      return res.status(400).json({ message: "Index and new link required" });
    }

    const event = await Event.findById(req.params.eventId);
    if (!event) return res.status(404).json({ message: "Event not found" });

    if (!event.recordingLinks || !event.recordingLinks[index]) {
      return res.status(404).json({ message: "Recording link not found at given index" });
    }

    event.recordingLinks[index] = link;
    await event.save();

    res.json({ success: true, message: "Recording link updated", recordingLinks: event.recordingLinks });
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error", error: error.message });
  }
};
