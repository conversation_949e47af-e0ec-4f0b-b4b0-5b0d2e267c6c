const { v4: uuidv4 } = require('uuid');
const jwt = require('jsonwebtoken');
const sgMail = require('@sendgrid/mail');
require('dotenv').config();

const InviteLink = require('../models/InviteLink');
const { emailTemp } = require('../public/html files/emailTemp'); 

sgMail.setApiKey(process.env.SENDGRID_API);

//  Converts things like "1 day" to "1d", "7 days" to "7d"
function normalizeExpiration(str) {
  const map = {
    '1 day': '1d',
    '7 days': '7d',
    '30 days': '30d',
  };
  return map[str.toLowerCase()] || str;
}

// Converts "1d", "2h", etc. to milliseconds
function ms(duration) {
  const match = /^(\d+)([smhd])$/.exec(duration);
  if (!match) {
    throw new Error(`Invalid expiration format: ${duration}`);
  }
  const map = { s: 1000, m: 60000, h: 3600000, d: 86400000 };
  return parseInt(match[1]) * map[match[2]];
}

exports.generateLink = async (req, res) => {
  let { expiration = '1 day', maxUsers = 1 } = req.body;

  try {
    // Convert "1 day", "7 days", etc. to short format like "1d"
    const normalized = normalizeExpiration(expiration);
    const durationMs = ms(normalized);
    const expiresAt = new Date(Date.now() + durationMs);

    // Generate unique token
    const linkId = uuidv4();
    const token = jwt.sign({ linkId }, process.env.JWT_SECRET, { expiresIn: normalized });

    // Save link to DB
    await InviteLink.create({
      linkId,
      token,
      expiresAt,
      maxUsers,
      usedCount: 0,
      isBlacklisted: false
    });

    // Send full invite URL back to frontend
    const inviteLink = `${process.env.BASE_URL}/dashboard/manage-community?token=${token}`;

    return res.status(200).json({
      inviteLink,
      baseUrl: process.env.BASE_URL,
      linkId,
      expiresAt
    });
  } catch (err) {
    console.error('generateLink error:', err);
    res.status(500).json({ error: 'Failed to generate invite link.' });
  }
};

function ms(duration) {
  const match = /^(\d+)([smhd])$/.exec(duration);
  if (!match) {
    throw new Error(`Invalid expiration format: ${duration}`);
  }
  const map = { s: 1000, m: 60000, h: 3600000, d: 86400000 };
  return parseInt(match[1]) * map[match[2]];
}



exports.sendInviteEmails = async (req, res) => {
  const { emails, message = '', linkId } = req.body;

  if (!emails || !emails.length || !linkId) {
    return res.status(400).json({ message: "Emails and linkId are required" });
  }

  const emailList = Array.isArray(emails)
    ? emails
    : emails.split(",").map((email) => email.trim()).filter(Boolean);

  try {
    const link = await InviteLink.findOne({ linkId });

    if (!link) {
      return res.status(404).json({ message: "Invalid invitation link" });
    }

    if (link.isBlacklisted || new Date() > link.expiresAt) {
      return res.status(410).json({ message: "Link is expired or blacklisted" });
    }

    if (emailList.length > link.maxUsers) {
      return res.status(400).json({
        message: `Max allowed users is ${link.maxUsers}, but got ${emailList.length}`,
      });
    }

    
    const expiresOn = new Date(link.expiresAt).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Africa/Nairobi', 
    });

    const results = [];
    const errors = [];

    for (const email of emailList) {
      try {
        const websiteUrl = `${process.env.BASE_URL}/dashboard/manage-community?token=${link.token}&email=${encodeURIComponent()}`;

        const content = `
  <div style="font-family: Arial, sans-serif; color: #111827; line-height: 1.6;">
    <p style="font-size: 1.1em; margin-bottom: 20px;">
      You've been invited to join the <strong>Black Panther DAO</strong> community!
    </p>

    <div style="margin: 24px 0;">
      <a 
        href="${websiteUrl}" 
        style="
          display: inline-block;
          padding: 18px 36px;
          background: #111828;
          color: #ffffff !important;
          border-radius: 18px;
          text-decoration: none;
          font-weight: bold;
          font-size: 1.3em;
        " 
        target="_blank"
      >
        Join Black Panther DAO
      </a>
    </div>

    <p style="margin: 24px 0; font-size: 1em;">
      Click the button above to join and enjoy exclusive community benefits.
    </p>

    ${
      message
        ? `<p style="margin-top: 32px; font-size: 1em;">
             <b style="font-weight:600;">Message from the inviter:</b><br/>
             <span style="display: inline-block; margin-top: 8px;">${message}</span>
           </p>`
        : ""
    }

    <p style="margin-top: 32px; font-size: 1em; color: #fbbf24;">
      ⚠️ This link will expire on <strong>${expiresOn}</strong>. Please join before it expires.
    </p>
  </div>
`;


        const msg = {
          to: email,
          from: '<EMAIL>',
          subject: "You're Invited to Join Black Panther DAO!",
          html: emailTemp(content),
          trackingSettings: {
            clickTracking: { enable: false, enableText: false },
          },
        };

        await sgMail.send(msg);
        results.push(email);
      } catch (err) {
        console.error(`Error sending to ${email}:`, err.message);
        errors.push({ email, error: err.message });
      }
    }

    return res.status(200).json({
      successful: results,
      failed: errors.length ? errors : undefined,
    });

  } catch (error) {
    console.error("sendInviteEmails error:", error);
    return res.status(500).json({ message: "Failed to send invites", error: error.message });
  }
};




// exports.consumeInvite = async (req, res) => {
//   const { token, email } = req.query;

//   if (!token || !email) {
//     return res.status(400).json({ message: "Token and email are required" });
//   }

//   try {
//     const link = await InviteLink.findOne({ token });

//     if (!link) {
//       return res.status(404).json({ message: "Invalid or expired link" });
//     }

//     if (link.isBlacklisted || new Date() > link.expiresAt) {
//       return res.status(410).json({ message: "Link is expired or blacklisted" });
//     }

    
//     link.isBlacklisted = true;
//     await link.save();

    
//     return res.status(200).json({ message: "Link consumed and blacklisted" });
//     // Or: res.redirect(`/dashboard/manage-community?token=${token}&email=${encodeURIComponent(email)}`);

//   } catch (error) {
//     console.error("consumeInvite error:", error);
//     return res.status(500).json({ message: "Failed to consume invite", error: error.message });
//   }
// };





// Helper: convert '1d', '2h', etc. to ms
function ms(duration) {
  const match = /^(\d+)([smhd])$/.exec(duration);
  const map = { s: 1000, m: 60000, h: 3600000, d: 86400000 };
  return parseInt(match[1]) * map[match[2]];
}

exports.verifyLink = async (req, res) => {
  const { linkId } = req.params;
  const { token } = req.query;

  try {
    const linkData = await InviteLink.findOne({ linkId });

    if (!linkData) {
      return res.status(404).send('This invitation link does not exist.');
    }

    if (linkData.isBlacklisted) {
      return res.status(403).send('This invitation link has been blacklisted.');
    }

    if (new Date() > linkData.expiresAt) {
      return res.status(410).send('This invitation link has expired.');
    }

    try {
      jwt.verify(token, process.env.JWT_SECRET);
    } catch (err) {
      return res.status(403).send('Invalid or expired token.');
    }

    if (linkData.usedCount >= linkData.maxUsers) {
      return res.status(429).send('This invitation link has reached its usage limit.');
    }

    // All good: increment usage
    linkData.usedCount += 1;

    if (linkData.usedCount >= linkData.maxUsers) {
      linkData.isBlacklisted = true;
    }

    await linkData.save();

    
    return res.status(200).send('Invitation verified. Welcome to the Black Panther DAO!');
  } catch (error) {
    console.error('verifyLink error:', error);
    return res.status(500).send('Server error during verification.');
  }
};

exports.validateTokenAccess = async (req, res) => {
  const { token, email } = req.query;
  console.log('📥 validateTokenAccess called with:', { token, email });

  if (!token) {
    return res.status(400).json({ message: 'Token is required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const { linkId } = decoded;

    console.log('✅ Token decoded. Link ID:', linkId);

    const link = await InviteLink.findOne({ linkId });

    if (!link) {
      return res.status(404).json({ message: 'Invalid invitation link' });
    }

    // ... same checks ...

    link.usedCount += 1;
    if (link.usedCount >= link.maxUsers || new Date() > link.expiresAt) {
      link.isBlacklisted = true;
    }

    await link.save();
    console.log('📦 Link updated:', link);

    return res.status(200).json({
      message: 'Link is valid',
      expiresAt: link.expiresAt,
      usedCount: link.usedCount,
      isBlacklisted: link.isBlacklisted,
      emailUsed: email || null,
    });
  } catch (err) {
    console.error('❌ validateTokenAccess error:', err);
    return res.status(403).json({ message: 'Invalid or expired token' });
  }
};

