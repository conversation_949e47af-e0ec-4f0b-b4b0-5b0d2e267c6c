const mongoose = require('mongoose');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2023-10-16' // Use the latest API version
  });
const { ethers } = require('ethers');
const { Contribution, ContributionHistory } = require('../models/contribution');
const User = require('../models/user');
const { rpcProvider } = require('../config/constracts');
const {abortSession,validateAmount,calculateNextPaymentDate, generateSystemReference, getContributionTier, isTransactionHashExists,isActiveContributor  } = require('../utils/contribution')


// Helper function to verify SHLN token payment
const verifySHLNPayment = async (txHash, expectedTo, expectedFrom, expectedAmount, tokenContract) => {
    const tx = await rpcProvider.getTransaction(txHash);
    const receipt = await rpcProvider.getTransactionReceipt(txHash);
    if (!tx || !receipt || receipt.status !== 1) return false;
    if (tx.to !== tokenContract) return false; 
    const iface = new ethers.Interface(["function transfer(address to, uint amount)"]);
    const decoded = iface.parseTransaction({ data: tx.data });
    return (
        decoded.args[0].toLowerCase() === expectedTo.toLowerCase() &&
        tx.from.toLowerCase() === expectedFrom.toLowerCase() &&
        Number(ethers.formatUnits(decoded.args[1], 6)) === Number(expectedAmount)
    );
};

// Helper function to check if payment reference exists
const isPaymentReferenceExists = async (paymentReference, session = null) => {

    if (!paymentReference) {
        return false;
    }
    
    // Use the working approach: Direct dot notation with session support
    const queryOptions = session ? { session } : {};
    const existingContribution = await Contribution.findOne({
        'paymentHistory.paymentReference': paymentReference
    }, null, queryOptions);
    
    return !!existingContribution;
};

// Helper function to handle Web3 subscription cancellation
const handleWeb3SubscriptionCancellation = async (contribution) => {
    try {
        // Here we would implement the logic to cancel the Web3 subscription
        // This could involve:
        // 1. Checking if the subscription is active on the blockchain
        // 2. Calling the smart contract to cancel the subscription
        // 3. Updating any off-chain subscription status

        // For now, we'll just mark it as cancelled in our database
        contribution.status = 'cancelled';
        contribution.endDate = new Date();
        await contribution.save();

        return true;
    } catch (error) {
        // console.error('Error cancelling Web3 subscription:', error);
        return false;
    }
};

// Generate payment intent and subscription for Stripe (USD fallback)
const generatePaymentIntent = async (req, res) => {
    try {
        const { amount, userId, paymentMethodId, action } = req.body;
        if(action === 'activate'){
            const contribution = await isActiveContributor(userId);
            if(contribution){
                return res.status(400).json({
                    error: 'Contribution already exists',
                    message: 'An active contribution already exists for this user'
                });
            }
        }
        if (!amount || amount < 5) {
            return res.status(400).json({
                error: 'Invalid amount',
                message: 'Amount must be at least 5 SHLN (or $5 USD)'
            });
        }

        if (!userId) {
            return res.status(400).json({
                error: 'Missing userId',
                message: 'userId is required'
            });
        }

        if (!paymentMethodId) {
            return res.status(400).json({
                error: 'Missing paymentMethodId',
                message: 'paymentMethodId is required from Stripe.js client'
            });
        }

        // Fetch user from database to get email
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                message: 'No user found with the provided userId'
            });
        }

        // Create a customer if they don't exist
        let customer;
        const existingCustomer = await stripe.customers.list({
            email: user.email,
            limit: 1
        });

        if (existingCustomer.data.length > 0) {
            customer = existingCustomer.data[0];
        } else {
            customer = await stripe.customers.create({
                email: user.email,
                metadata: {
                    userId: userId,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });
        }

        // Attach the payment method to the customer
        await stripe.paymentMethods.attach(paymentMethodId, { customer: customer.id });
        // Set as default payment method for invoices
        await stripe.customers.update(customer.id, {
            invoice_settings: { default_payment_method: paymentMethodId }
        });

        // Fetch the customer again to ensure the default is set
        const updatedCustomer = await stripe.customers.retrieve(customer.id);
        if (
            !updatedCustomer.invoice_settings ||
            updatedCustomer.invoice_settings.default_payment_method !== paymentMethodId
        ) {
            return res.status(400).json({
                error: 'Failed to set default payment method for customer. Please try again.',
                details: updatedCustomer,
            });
        }

        // Create a product
        const product = await stripe.products.create({
            name: 'Black Panther Community Contribution',
            description: 'Monthly community contribution subscription',
            metadata: {
                userId: userId,
                shlnAmount: amount
            }
        });

        // Create a price for the product
        const price = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(amount * 100), // Convert to cents
            currency: 'usd',
            recurring: {
                interval: 'month'
            }
        });

        // Create the subscription with auto-collection and immediate payment
        const subscription = await stripe.subscriptions.create({
            customer: customer.id,
            items: [{ price: price.id, quantity: 1 }],
            default_payment_method: paymentMethodId,
            payment_behavior: 'default_incomplete',
            // collection_method: 'charge_automatically',
            payment_settings: {
                payment_method_types: ['card'],
                save_default_payment_method: 'on_subscription'
            },
            expand: ['latest_invoice.payment_intent'],
            metadata: {
                userId: userId,
                shlnAmount: amount,
                productId: product.id,
                priceId: price.id
            }
        });


        // Get the client secret for the payment intent
        let paymentIntent = subscription.latest_invoice && subscription.latest_invoice.payment_intent;
        let clientSecret = paymentIntent ? paymentIntent.client_secret : null;
      

        if (!paymentIntent) {
        throw new Error('No PaymentIntent was created with the subscription');
        }
        return res.status(200).json({
            subscriptionId: subscription.id,     
            clientSecret,
            customerId: customer.id,
            productId: product.id,
            priceId: price.id
        });
    } catch (error) {
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to generate payment intent',
            details: error.message
        });
    }
};


// Activate contribution (called by blockchain webhook)
const activateContribution = async (req, res) => {
    const MAX_RETRIES = 5;
    for (let attempt = 0; attempt < MAX_RETRIES; ++attempt) {
        const session = await mongoose.startSession();
        session.startTransaction();
        try {
            const { userId, amount, currency, paymentMethod, paymentReference, subscriptionId, paymentIntentId } = req.body;

            // Validate required fields
            if (!userId || !amount || !currency || !paymentMethod) {
                await session.abortTransaction();
                session.endSession();
                return res.status(400).json({
                    error: 'Missing required fields',
                    message: 'Please provide userId, amount, currency, paymentMethod, and paymentReference'
                });
            }

            // Check if payment reference already exists within the transaction
            const refToCheck = paymentReference || paymentIntentId;

            const referenceExists = await isPaymentReferenceExists(refToCheck, session);
            if (referenceExists) {
                // console.log('found duplicate');
                await abortSession(session)
                return res.status(400).json({
                    error: 'Duplicate payment reference',
                    message: 'This payment reference has already been used',
                    code: 'DUPLICATE_PAYMENT'
                });
            }

            // Validate amount
            if (!validateAmount(amount, currency)) {
                await abortSession(session);
                return res.status(400).json({
                    error: 'Invalid amount',
                    message: 'Amount must be at least 5 SHLN',
                    code: 'INVALID_AMOUNT'
                });
            }

            // Verify payment based on method
            let isPaymentValid = false;
            let tokenAmount = amount;
            let newPaymentReference = refToCheck;

            if (paymentMethod === 'stripe') {
                if (!subscriptionId || !paymentIntentId) {
                    await abortSession(session);
                    return res.status(400).json({
                        error: 'Missing Stripe subscriptionId or paymentIntentId',
                        message: 'Both subscriptionId and paymentIntentId are required for Stripe payments.'
                    });
                }
                // Retrieve the subscription and its latest invoice/payment intent
                const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
                    expand: ['latest_invoice.payment_intent']
                });
                const paymentIntent = subscription.latest_invoice && subscription.latest_invoice.payment_intent;
                if (!paymentIntent || paymentIntent.id !== paymentIntentId) {
                    await abortSession(session);
                    return res.status(400).json({
                        error: 'Payment intent mismatch',
                        message: 'The provided paymentIntentId does not match the subscription.'
                    });
                }
                if (paymentIntent.status === 'succeeded') {
                    isPaymentValid = true;
                    newPaymentReference = paymentIntent.id;
                } else {
                   await abortSession(session);
                    return res.status(400).json({
                        error: 'Invalid payment',
                        message: 'Payment intent is not succeeded',
                        code: 'INVALID_PAYMENT'
                    });
                }
            } else if (paymentMethod === 'crypto') {
                try {
                    isPaymentValid = await verifySHLNPayment(paymentReference, amount);
                } catch (verificationError) {
                    await abortSession(session);
                    return res.status(400).json({
                        error: 'Payment verification failed',
                        message: verificationError.message,
                        code: 'VERIFICATION_FAILED',
                        details: {
                            transactionHash: paymentReference,
                            amount: amount,
                            timestamp: new Date()
                        }
                    });
                }
            }

            if (!isPaymentValid) {
                await abortSession(session);
                return res.status(400).json({
                    error: 'Invalid payment',
                    message: 'Payment verification failed',
                    code: 'INVALID_PAYMENT'
                });
            }

            // Check for existing contribution for the user
            let contribution = await Contribution.findOne({ userId }).session(session);
            let reason = 'new';

            if (contribution) {
                if (await isActiveContributor(userId)) {
                    await abortSession(session);
                    const nextPaymentDate = new Date(contribution.nextPaymentDate).toLocaleDateString('en-US', {
                        year: 'numeric', month: 'long', day: 'numeric'
                    });
                    return res.status(409).json({
                        error: 'Conflict',
                        message: `An active contribution already exists for this user. The next payment is on ${nextPaymentDate}.`,
                        nextPaymentDate: contribution.nextPaymentDate
                    });
                }
                
                // Reactivating an inactive or cancelled contribution
                reason = 'reactivate';
                contribution.status = 'active';
                contribution.amount = amount;
                contribution.currency = currency;
                contribution.paymentMethod = paymentMethod;
                contribution.subscriptionId = subscriptionId;
                contribution.lastPaymentDate = new Date();
                contribution.nextPaymentDate = calculateNextPaymentDate(new Date());
                contribution.endDate = null; // Clear end date on reactivation
                contribution.paymentHistory.push({
                    amount,
                    date: new Date(),
                    paymentReference: newPaymentReference,
                    status: 'success',
                    currency,
                    tokenAmount
                });

            } else {
                // Create a new contribution
                contribution = new Contribution({
                    userId,
                    amount,
                    currency,
                    paymentMethod,
                    status: 'active',
                    subscriptionId,
                    lastPaymentDate: new Date(),
                    nextPaymentDate: calculateNextPaymentDate(new Date()),
                    paymentHistory: [{
                        amount,
                        date: new Date(),
                        paymentReference: newPaymentReference,
                        status: 'success',
                        currency,
                        tokenAmount
                    }]
                });
            }

            await contribution.save({ session });

            await ContributionHistory.create([{
                userId: contribution.userId,
                amount: contribution.amount,
                currency: contribution.currency,
                paymentMethod: contribution.paymentMethod,
                status: contribution.status,
                subscriptionId: contribution.subscriptionId,
                lastPaymentDate: contribution.lastPaymentDate,
                nextPaymentDate: contribution.nextPaymentDate,
                date: new Date(),
                reason: reason
            }], { session });

            await session.commitTransaction();
            session.endSession();

            return res.status(200).json({
                message: 'Contribution activated successfully',
                contributionId: contribution._id,
                status: contribution.status,
                nextPaymentDate: contribution.nextPaymentDate,
                tokenAmount,
                subscriptionId: contribution.subscriptionId
            });
        } catch (error) {
            await abortSession(session);
            if (error.codeName === 'WriteConflict' && attempt < MAX_RETRIES - 1) {
                // Wait a bit before retrying
                await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
                continue; // retry
            }
            console.error('Error in activateContribution:', error);
            return res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to activate contribution',
                code: 'INTERNAL_ERROR',
                details: error.message
            });
        }
    }
};

// Update contribution controller
const updateContribution = async (req, res) => {
    /*
      Handles updating a user's contribution.
      Supports Stripe and crypto payment methods.
      Allows immediate or scheduled updates via updateType.
    */
    const session = await mongoose.startSession();
    session.startTransaction();
    try {
        const { userId, amount, currency, paymentMethod, updateType, subscriptionId, scheduledDate,paymentIntentId } = req.body;
        if (!userId || !amount || !currency || !paymentMethod || !updateType) {
            await abortSession(session);
            return res.status(400).json({ error: 'Missing required fields', message: 'userId, amount, currency, paymentMethod, and updateType are required.' });
        }
        const contribution = await Contribution.findOne({ userId }).session(session);
        if (!contribution) {
            await abortSession(session);
            return res.status(404).json({ error: 'Contribution not found', message: 'No active contribution to update.' });
        }
        // Only allow updates if contribution is active
        if (!(await isActiveContributor(userId))) {
            await abortSession(session);
            return res.status(400).json({ error: 'Inactive contribution', message: 'Only active contributions can be updated.' });
        }
        // Validate amount
        if (!validateAmount(amount, currency)) {
            await abortSession(session);
            return res.status(400).json({ error: 'Invalid amount', message: 'Amount must be at least 5 SHLN.' });
        }
        let stripeUpdateResult = null;
        if (paymentMethod === 'stripe') {
            // Stripe subscription update logic
            if (!subscriptionId && !contribution.subscriptionId) {
                await abortSession(session);
                return res.status(400).json({ error: 'Missing subscriptionId', message: 'Stripe subscriptionId is required.' });
            }

            const subId = contribution.subscriptionId;
            // Fetch current subscription
            const existingSubscription = await stripe.subscriptions.retrieve(subId);
            if (!existingSubscription) {
                await abortSession(session);
                return res.status(404).json({ error: 'Stripe subscription not found', message: 'Could not find Stripe subscription.' });
            }
            if (updateType === 'immediate') {
                // For immediate change,  cancel the old one
                // User gets Debited immediately

                if (!subscriptionId || !paymentIntentId) {
                    await abortSession(session);
                    return res.status(400).json({
                        error: 'Missing Stripe subscriptionId or paymentIntentId',
                        message: 'Both subscriptionId and paymentIntentId are required for Stripe payments.'
                    });
                }
                // Retrieve the subscription and its latest invoice/payment intent
                const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
                    expand: ['latest_invoice.payment_intent']
                });
                const paymentIntent = subscription.latest_invoice && subscription.latest_invoice.payment_intent;
                if (!paymentIntent || paymentIntent.id !== paymentIntentId) {
                    await abortSession(session);
                    return res.status(400).json({
                        error: 'Payment intent mismatch',
                        message: 'The provided paymentIntentId does not match the subscription.'
                    });
                }
                if (!(paymentIntent.status === 'succeeded')) {
                   await abortSession(session);
                    return res.status(400).json({
                        error: 'Invalid payment',
                        message: 'Payment intent is not succeeded',
                        code: 'INVALID_PAYMENT'
                    });
                }


                await stripe.subscriptions.cancel(subId);

                contribution.subscriptionId = subscription.id;
                contribution.amount = amount;
                contribution.currency = currency;
                contribution.nextPaymentDate = calculateNextPaymentDate(new Date());
                contribution.lastPaymentDate = new Date();
                contribution.paymentHistory.push({
                    amount,
                    date: new Date(),
                    paymentReference: paymentIntent.id,
                    status: 'success',
                    currency,
                    tokenAmount: amount,
                    reason: 'Stripe subscription updated (immediate)'
                });

            } else if (updateType === 'scheduled') {
                // Create new price for the new amount
                const price = await stripe.prices.create({
                    unit_amount: Math.round(amount * 100),
                    currency: 'usd',
                    recurring: { interval: 'month' },
                    product: existingSubscription.items.data[0].price.product,
                });

                // 2. Create the schedule to start at the scheduled date, with the new price
                const schedule = await stripe.subscriptionSchedules.create({
                    customer: existingSubscription.customer,
                    start_date: Math.floor(new Date(scheduledDate).getTime() / 1000),
                    phases: [
                        {
                            items: [{ price: price.id }],
                            // No iterations: this phase will continue indefinitely
                        },
                    ],
                    // end_behavior: 'none', // optional, default is 'none'
                });

                stripeUpdateResult = schedule;
                contribution.paymentHistory.push({
                    amount,
                    date: new Date(scheduledDate),
                    paymentReference: schedule.id, // Use schedule.id as reference
                    status: 'scheduled',
                    currency,
                    tokenAmount: amount,
                    reason: 'Stripe subscription update scheduled'
                });
            } else {
                await abortSession(session);
                return res.status(400).json({ error: 'Invalid updateType', message: 'updateType must be immediate or scheduled.' });
            }
        } else if (paymentMethod === 'crypto') {
            // Crypto (SHLN) update logic
            contribution.amount = amount;
            contribution.currency = currency;
            contribution.nextPaymentDate = updateType === 'immediate' ? calculateNextPaymentDate(new Date()) : contribution.nextPaymentDate;
            contribution.paymentHistory.push({
                amount,
                date: new Date(),
                paymentReference: `crypto_update_${Date.now()}`,
                status: updateType === 'immediate' ? 'success' : 'scheduled',
                currency,
                tokenAmount: amount,
                reason: updateType === 'immediate' ? 'Crypto contribution updated' : 'Crypto update scheduled'
            });
        } else {
            await abortSession(session)
            return res.status(400).json({ error: 'Unsupported payment method', message: 'Only stripe and crypto are supported.' });
        }
        await contribution.save({ session });
        await ContributionHistory.create([{
            userId: contribution.userId,
            amount: contribution.amount,
            currency: contribution.currency,
            paymentMethod: contribution.paymentMethod,
            status: contribution.status,
            subscriptionId: contribution.subscriptionId,
            lastPaymentDate: contribution.lastPaymentDate,
            nextPaymentDate: contribution.nextPaymentDate,
            date: new Date(),
            reason: 'update'
        }], { session });
        await session.commitTransaction();
        session.endSession();
        return res.status(200).json({
            message: 'Contribution updated successfully',
            contribution,

            stripeUpdateResult
        });
    } catch (error) {
        await abortSession(session);
        console.error('Error in updateContribution:', error);
        return res.status(500).json({ error: 'Internal server error', message: 'Failed to update contribution', details: error.message });
    }
};

// Cancel contribution
const cancelContribution = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({
                error: 'Missing userId',
                message: 'Please provide userId'
            });
        }

        // Find active contribution
        const contribution = await Contribution.findOne({
            userId,
            status: 'active'
        });

        if (!contribution) {
            return res.status(404).json({
                error: 'No active contribution found',
                message: 'User has no active contribution to cancel'
            });
        }

        let cancellationSuccess = false;

        // Handle cancellation based on payment method
        if (contribution.paymentMethod === 'stripe') {
            try {
                // Cancel Stripe subscription if exists
                if (contribution.subscriptionId) {
                    await stripe.subscriptions.cancel(contribution.subscriptionId);
                }
                cancellationSuccess = true;
            } catch (stripeError) {
                // console.error('Stripe cancellation error:', stripeError);
                return res.status(500).json({
                    error: 'Stripe cancellation failed',
                    message: 'Failed to cancel Stripe subscription'
                });
            }
        } else if (contribution.paymentMethod === 'crypto') {
            // Handle Web3 subscription cancellation
            cancellationSuccess = await handleWeb3SubscriptionCancellation(contribution);
            if (!cancellationSuccess) {
                return res.status(500).json({
                    error: 'Web3 cancellation failed',
                    message: 'Failed to cancel Web3 subscription'
                });
            }
        }

        // Update contribution status
        contribution.status = 'cancelled';
        contribution.endDate = new Date();
        await contribution.save();

        return res.status(200).json({
            message: 'Contribution cancelled successfully',
            contributionId: contribution._id,
            cancellationDate: contribution.endDate
        });

    } catch (error) {
        // console.error('Error in cancelContribution:', error);
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to cancel contribution'
        });
    }
};

// Get contribution status
const getContributionStatus = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({
                error: 'Missing userId',
                message: 'Please provide userId'
            });
        }

        // Find contribution
        const contribution = await Contribution.findOne({ userId });

        if (!contribution) {
            return res.status(200).json({
                status: 'inactive',
                message: 'No contribution found'
            });
        }

        // Check if contribution is active
        const now = new Date();
        const isActive = await isActiveContributor(userId);

       

        // Calculate time until next payment
        const timeUntilNextPayment = contribution.nextPaymentDate - now;
        const daysUntilNextPayment = Math.ceil(timeUntilNextPayment / (1000 * 60 * 60 * 24));

        return res.status(200).json({
            status: contribution.status,
            contribution: {
                amount: contribution.amount,
                currency: contribution.currency,
                paymentMethod: contribution.paymentMethod,
                lastPaymentDate: contribution.lastPaymentDate,
                nextPaymentDate: contribution.nextPaymentDate,
                tier: getContributionTier(contribution.amount),
                daysUntilNextPayment,
                paymentHistory: contribution.paymentHistory.map(payment => ({
                    ...payment,
                    tokenAmount: payment.tokenAmount || payment.amount,
                    reason: payment.reason || null
                }))
            }
        });

    } catch (error) {
        // console.error('Error in getContributionStatus:', error);
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to get contribution status'
        });
    }
};

// Handle Stripe webhook events
const handleStripeWebhook = async (req, res) => {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
        event = stripe.webhooks.constructEvent(
            req.body,
            sig,
            process.env.STRIPE_WEBHOOK_SECRET
        );
    } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        switch (event.type) {
            case 'invoice.payment_succeeded':
                const invoice = event.data.object;
                const subscriptionId = invoice.subscription;

                let contribution = await Contribution.findOne({ subscriptionId });

                if (!contribution && subscriptionId) {
                    // Try to retrieve the subscription and get its schedule
                    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
                    if (subscription && subscription.schedule) {
                        contribution = await Contribution.findOne({
                            'paymentHistory.status': 'scheduled',
                            'paymentHistory.paymentReference': subscription.schedule
                        });
                        if (contribution) {
                            // Cancel the old subscription 
                            if (contribution.subscriptionId) {
                                try { await stripe.subscriptions.cancel(contribution.subscriptionId); } catch (e) {}
                            }
                            // Update the contribution with the new subscriptionId
                            contribution.subscriptionId = subscriptionId;
                        }
                    }
                }

                if (contribution) {
                    const scheduledPayment = contribution.paymentHistory
                        .filter(p => p.status === 'scheduled')
                        .sort((a, b) => b.date - a.date)[0];

                    if (scheduledPayment) {
                        scheduledPayment.status = 'succeeded';
                        scheduledPayment.paymentReference = invoice.payment_intent || invoice.id;
                    } else {
                        contribution.paymentHistory.push({
                            amount: invoice.amount_paid / 100,
                            date: new Date(),
                            paymentReference: invoice.payment_intent || invoice.id,
                            status: 'succeeded',
                            currency: invoice.currency.toUpperCase(),
                            tokenAmount: invoice.amount_paid / 100,
                            reason: 'Recurring payment'
                        });
                    }

                    contribution.status = 'active';
                    contribution.lastPaymentDate = new Date();
                    contribution.nextPaymentDate = calculateNextPaymentDate(new Date());
                    await contribution.save();
                }
                break;

            case 'customer.subscription.deleted':
                const subscription = event.data.object;
                const contributionBySubscription = await Contribution.findOne({
                    subscriptionId: subscription.id
                });

                if (contributionBySubscription) {
                    contributionBySubscription.status = 'inactive';
                    contributionBySubscription.endDate = new Date();
                    await contributionBySubscription.save();
                }
                console.log('deleted done--',event,contributionBySubscription)

                break;

            case 'customer.subscription.canceled':
                const subscriptionObj = event.data.object;
                const contributionBySubscriptionObj = await Contribution.findOne({
                    subscriptionId: subscriptionObj.id
                });

                if (contributionBySubscriptionObj) {
                    contributionBySubscriptionObj.status = 'inactive';
                    contributionBySubscriptionObj.endDate = new Date();
                    await contributionBySubscriptionObj.save();
                }
                console.log('cancelled done--',event,contributionBySubscriptionObj)

                break;

            case 'invoice.payment_failed':
                const failedInvoice = event.data.object;
                const failedSubscriptionId = failedInvoice.subscription;

                if (failedSubscriptionId) {
                    const contribution = await Contribution.findOne({ subscriptionId: failedSubscriptionId });

                    if (contribution) {
                        const scheduledPayment = contribution.paymentHistory
                            .filter(p => p.status === 'scheduled')
                            .sort((a, b) => b.date - a.date)[0];

                        if (scheduledPayment) {
                            scheduledPayment.status = 'failed';
                            scheduledPayment.paymentReference = failedInvoice.payment_intent || failedInvoice.id;
                        } else {
                            contribution.paymentHistory.push({
                                amount: failedInvoice.amount_due / 100,
                                date: new Date(),
                                paymentReference: failedInvoice.payment_intent || failedInvoice.id,
                                status: 'failed',
                                currency: failedInvoice.currency.toUpperCase(),
                                tokenAmount: failedInvoice.amount_due / 100,
                                reason: 'Payment failure'
                            });
                        }

                        contribution.status = 'inactive';
                        await contribution.save();
                    }
                }
                break;
        }

        res.json({ received: true });
    } catch (error) {
        // console.error('Error processing webhook:', error);
        res.status(500).json({ error: 'Webhook processing failed ', ms:error?.toString() });
    }
};

// Verify crypto payment
const verifyCryptoPayment = async (req, res) => {
    try {
        const { userId, transactionHash, from, to, amount, tokenContract } = req.body;
        if (userId != req.userEmailAndId.userId) {
            return res.status(401).json({
                error: 'Unauthorized',
                message: 'You are not authorized to verify this payment'
            });
        }

        if (!userId || !transactionHash) {
            return res.status(400).json({
                error: 'Missing required fields',
                message: 'Please provide userId and transactionHash'
            });
        }
        // Check if transaction hash already exists
        const hashExists = await isTransactionHashExists(transactionHash);
        if (hashExists) {
            return res.status(400).json({
                error: 'Duplicate transaction hash',
                message: 'This transaction hash has already been processed',
                code: 'DUPLICATE_TRANSACTION_HASH'
            });
        }
        let contribution = new Contribution();

        const contributionExist = await Contribution.findOne({ userId: userId, paymentMethod: 'crypto' });
        let isExisitng = false

        if (contributionExist) {
            contribution = contributionExist;
            isExisitng = true;
        } else {
            contribution.userId = userId;
            contribution.amount = amount;
            contribution.currency = 'SHLN';
            contribution.paymentMethod = 'crypto';
            contribution.status = 'pending';
            contribution.nextPaymentDate = calculateNextPaymentDate(new Date());
        }

        try {
            // Verify the blockchain transaction
            const isValid = await verifySHLNPayment(transactionHash, to, from, amount, tokenContract);
            if (isValid) {
                contribution.status = 'active';
                contribution.paymentHistory.push({
                    amount: amount,
                    date: new Date(),
                    paymentReference: transactionHash,
                    status: 'success',
                    currency: 'SHLN',
                    tokenAmount: amount
                });
                await contribution.save();
                return res.status(200).json({
                    message: 'Payment verified successfully',
                    contributionId: contribution._id,
                    status: 'active',
                    success: true,
                });
            } else {
                throw new Error('Payment verification failed');
            }
        } catch (verificationError) {
            // Handle specific verification errors
            const errorResponse = {
                error: 'Payment verification failed',
                message: verificationError.message,
                code: 'VERIFICATION_FAILED',
                details: {
                    transactionHash,
                    amount: amount,
                    timestamp: new Date()
                }
            };

            // update the next payment date to be now
            if(!isExisitng) {
                contribution.nextPaymentDate = new Date();
            }
            // Add the failed payment to history
            contribution.paymentHistory.push({
                amount: amount,
                date: new Date(),
                paymentReference: transactionHash,
                status: 'failed',
                currency: 'SHLN',
                tokenAmount: amount,
                reason: verificationError.message
            });
            await contribution.save();

            return res.status(400).json(errorResponse);
        }
    } catch (error) {
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to verify payment',
            code: 'INTERNAL_ERROR'
        });
    }
};


// Get contribution history using ContributionHistory model (snapshot of all contributions)
const getContributionHistory = async (req, res) => {
    try {
        const { userId } = req.params;
        const { page = 1, limit = 10 } = req.query;

        if (!userId) {
            return res.status(400).json({
                error: 'Missing userId',
                message: 'Please provide userId'
            });
        }
        const skip = (parseInt(page) - 1) * parseInt(limit);

        const [total, histories] = await Promise.all([
            ContributionHistory.countDocuments({ userId }),
            ContributionHistory.find({ userId })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(parseInt(limit))
        ]);

        return res.status(200).json({
            histories,
            pagination: {
                total,
                page: parseInt(page),
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        // console.error('Error in getContributionHistory:', error);
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to get contribution history'
        });
    }
};

// Get user's contribution payment history
const getUserContributionPaymentHistory = async (req, res) => {
    try {
        const userId = req.user.id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Get the contribution with paginated payment history
        const contribution = await Contribution.findOne({ userId })
            .select('paymentHistory')
            .slice('paymentHistory', [skip, limit])
            .sort({ 'paymentHistory.date': -1 });

        if (!contribution) {
            return res.status(404).json({
                success: false,
                message: 'No contribution history found for this user'
            });
        }

        // Get total count for pagination
        const totalCount = contribution.paymentHistory.length;
        const totalPages = Math.ceil(totalCount / limit);

        res.status(200).json({
            success: true,
            data: {
                payments: contribution.paymentHistory,
                pagination: {
                    currentPage: page,
                    totalPages,
                    totalItems: totalCount,
                    itemsPerPage: limit
                }
            }
        });
    } catch (error) {
        // console.error('Error fetching contribution history:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching contribution history',
            error: error.message
        });
    }
};

const getUserContributionScheduledPayment = async (req, res) => {
    try {
        const userId = req.userEmailAndId?.userId || req.params?.userId;
        const contribution = await Contribution.findOne({ userId });
        if (!contribution) {
            return res.status(404).json({
                success: false,
                message: 'No contribution history found for this user'
            });
        }
        const scheduledPayment = contribution.paymentHistory
            .filter(p => p.status === 'scheduled')
            .sort((a, b) => b.date - a.date)[0];
        if (!scheduledPayment) {
            return res.status(404).json({
                success: false,
                message: 'No scheduled payment found for this user'
            });
        }
        return res.status(200).json({
            success: true,
            data: scheduledPayment
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: 'Error fetching scheduled payment',
            error: error.message
        });
    }
}

// Dashboard summary for all contributions and expenditures
const getDashboardSummary = async (req, res) => {
  try {
    const now = new Date();
    const thisMonth = now.getMonth();
    const thisYear = now.getFullYear();

    const allContributions = await Contribution.find({ status: 'active' });

    let totalContributions = 0;
    let monthlyContributions = 0;
    let cashDeployed = 0;
    let monthlyOverheads = 0;
    let membersSet = new Set();

    for (const c of allContributions) {
      for (const p of c.paymentHistory) {
        // Contributions
        if (p.status === 'success' || p.status === 'succeeded') {
          totalContributions += Number(p.amount);
          membersSet.add(c.userId);
          const d = new Date(p.date);
          if (d.getMonth() === thisMonth && d.getFullYear() === thisYear) {
            monthlyContributions += Number(p.amount);
          }
        }
        // Expenditures (cash deployed)
        if (p.status === 'deployed' || p.status === 'overhead') {
          cashDeployed += Number(p.amount);
          const d = new Date(p.date);
          if (d.getMonth() === thisMonth && d.getFullYear() === thisYear) {
            monthlyOverheads += Number(p.amount);
          }
        }
      }
    }

    // Cash on hand = totalContributions - cashDeployed
    const cashOnHand = totalContributions - cashDeployed;

    res.json({
      totalContributions,
      cashOnHand,
      cashDeployed,
      monthlyContributions,
      monthlyOverheads,
      activeMembers: membersSet.size
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to get dashboard summary', details: error.message });
  }
};
module.exports = {
    generatePaymentIntent,
    activateContribution,
    cancelContribution,
    getContributionStatus,
    handleStripeWebhook,
    verifyCryptoPayment,
    getContributionHistory,
    getUserContributionPaymentHistory,
    updateContribution,
    getUserContributionScheduledPayment,
    getDashboardSummary,
};