const Project = require("../models/project");

// Get all projects
exports.GetAllProjects = async (req, res) => {
  try {
    const projects = await Project.find({ isDeleted: false });

    if (!projects || projects.length === 0) {
      return res.status(404).json({ message: "No projects found." });
    }

    res.status(200).json({
      message: "Projects retrieved successfully.",
      projects
    });
  } catch (error) {
    console.error("Error retrieving projects:", error);
    res.status(500).json({
      message: "Failed to retrieve projects.",
      error: error.message
    });
  }
};



// Get completed projects
exports.GetCompletedProjects = async (req, res) => {
  try {
    const projects = await Project.find({ 
      status: "completed", 
      isDeleted: false 
    });

    if (!projects || projects.length === 0) {
      return res.status(404).json({ message: "No completed projects found." });
    }

    res.status(200).json({
      message: "Completed projects retrieved successfully.",
      projects
    });
  } catch (error) {
    console.error("Error retrieving completed projects:", error);
    res.status(500).json({
      message: "Failed to retrieve completed projects.",
      error: error.message
    });
  }
};

// Get ongoing projects
exports.GetOngoingProjects = async (req, res) => {
  try {
    const projects = await Project.find({ 
      status: "ongoing", 
      isDeleted: false 
    });

    if (!projects || projects.length === 0) {
      return res.status(404).json({ message: "No ongoing projects found." });
    }

    res.status(200).json({
      message: "Ongoing projects retrieved successfully.",
      projects
    });
  } catch (error) {
    console.error("Error retrieving ongoing projects:", error);
    res.status(500).json({
      message: "Failed to retrieve ongoing projects.",
      error: error.message
    });
  }
};

// Get a single ongoing project by ID
exports.GetOngoingProjectById = async (req, res) => {
  try {
    const { id } = req.params;
    const project = await Project.findOne({ 
      _id: id, 
      status: "ongoing", 
      isDeleted: false 
    });

    if (!project) {
      return res.status(404).json({ message: "Ongoing project not found." });
    }

    res.status(200).json({
      message: "Ongoing project retrieved successfully.",
      project
    });
  } catch (error) {
    console.error("Error retrieving ongoing project:", error);
    res.status(500).json({
      message: "Failed to retrieve ongoing project.",
      error: error.message
    });
  }
};

// Mark a project as completed
exports.CompleteProject = async (req, res) => {
  try {
    const { projectId } = req.body;
    
    if (!projectId) {
      return res.status(400).json({ message: "Project ID is required." });
    }

    const project = await Project.findById(projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found." });
    }

    if (project.status === "completed") {
      return res.status(400).json({ message: "Project is already completed." });
    }

    project.status = "completed";
    project.completionDate = new Date();
    await project.save();

    res.status(200).json({
      message: "Project marked as completed.",
      project
    });
  } catch (error) {
    console.error("Error completing project:", error);
    res.status(500).json({
      message: "Failed to complete project.",
      error: error.message
    });
  }
};

exports.addUpdate = async (req, res) => {
  try {
    const { text } = req.body;
    const fileUrls = req.body.fileUrl || [];
    const image = fileUrls[0]?.url || null;

    const update = { text, image };

    const project = await Project.findByIdAndUpdate(
      req.params.id,
      { $push: { updates: { $each: [update], $position: 0 } } },
      { new: true }
    );
    if (!project) {
      return res.status(404).json({ error: "Project not found" });
    }
    res.json(project);
  } catch (err) {
    console.error("addUpdate error:", err);
    res.status(500).json({ error: err.message });
  }
};

// Update a specific update in a project
exports.updateUpdate = async (req, res) => {
  try {
    const { text, image } = req.body;
    const { id, updateId } = req.params;
    const project = await Project.findOneAndUpdate(
      { _id: id, "updates._id": updateId },
      {
        $set: {
          "updates.$.text": text,
          "updates.$.image": image
        }
      },
      { new: true }
    );
    if (!project) {
      return res.status(404).json({ error: "Project or update not found" });
    }
    res.json(project);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// Delete a specific update from a project
exports.deleteUpdate = async (req, res) => {
  try {
    const { id, updateId } = req.params;
    const project = await Project.findByIdAndUpdate(
      id,
      { $pull: { updates: { _id: updateId } } },
      { new: true }
    );
    if (!project) {
      return res.status(404).json({ error: "Project or update not found" });
    }
    res.json(project);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

exports.updateProjectUpdateImageAndText = async (req, res) => {
  try {
    const { projectId, updateId } = req.params;
    const { text } = req.body;
    const fileUrls = req.body.fileUrl || [];
    const newImage = fileUrls[0]?.url || null;

    // Build the update object dynamically
    const updateFields = {};
    if (newImage) updateFields["updates.$.image"] = newImage;
    if (text !== undefined) updateFields["updates.$.text"] = text;

    if (Object.keys(updateFields).length === 0) {
      return res.status(400).json({ message: "No data to update." });
    }

    const project = await Project.findOneAndUpdate(
      { _id: projectId, "updates._id": updateId },
      { $set: updateFields },
      { new: true }
    );

    if (!project) {
      return res.status(404).json({ message: "Project or update not found." });
    }

    res.status(200).json({
      message: "Update edited successfully.",
      project
    });
  } catch (error) {
    console.error("Error updating update:", error);
    res.status(500).json({
      message: "Failed to update update.",
      error: error.message
    });
  }
};
