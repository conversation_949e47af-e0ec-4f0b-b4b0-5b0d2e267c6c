const { ethers } = require('ethers');
const { bnbPurchaseContract } = require('../config/constracts');
const Supply = require('../models/supply');
const ConvertFromBNBToUSD = require('../utils/convertFromBNBToUSD');

exports.CreateSupply = async (req, res) => {
    const { month, year, totalSupply } = req.body;

    const exists = await Supply.findOne({ month, year });
    if (!exists) {
        const entry = new Supply({ month, year, totalSupply });
        await entry.save();
        return res.json({ message: 'Saved' });
    }
    res.json({ message: 'Already exists' });
}

exports.GetSupply = async (req, res) => {
    const monthOrder = {
        January: 1, February: 2, March: 3, April: 4, May: 5, June: 6,
        July: 7, August: 8, September: 9, October: 10, November: 11, December: 12,
    };

    let data = await Supply.find();

    data.sort((a, b) => {
        if (a.year === b.year) {
            return monthOrder[a.month] - monthOrder[b.month];
        }
        return a.year - b.year;
    });

    res.json(data);
}

exports.GetValueOfOneBNB = async (req, res) => {
    try {
        const bnbValue = await ConvertFromBNBToUSD(1);
        res.json({ value: bnbValue });
    } catch (error) {
        console.error("Error fetching BNB value:", error);
        res.status(500).json({ error: 'Failed to fetch BNB value' });
    }
}

exports.GetListOfTransactionsBNB = async (req, res) => {
  try {
    const transactionCount = await bnbPurchaseContract.getTransferCount();
    const count = Number(transactionCount);

    if (count < 1) {
      return res.json({ message: "No transactions found", list: [] });
    }

    const transactions = await Promise.all(
      [...Array(count).keys()].map(async (i) => {
        const result = await bnbPurchaseContract.getTransferDetails(i);

        return {
          index: i,
          sender: result[0],
          initiator: result[1],
          recipient: result[2],
          amount: ethers.formatEther(result[3]), // convert wei to BNB
          timestamp: Number(result[4]),
          date: new Date(Number(result[4]) * 1000).toLocaleString(), // human-readable
        };
      })
    );

    res.status(200).json({
      message: "Transactions retrieved successfully",
      list: transactions.reverse(), // optional: newest first
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    res.status(500).json({ error: "Failed to fetch transactions" });
  }
};