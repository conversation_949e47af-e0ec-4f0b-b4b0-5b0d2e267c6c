const cron = require('node-cron');
const { saveTotalSupply } = require('../utils/saveTotalSupply');

const scheduleSupplyJob = () => {
  cron.schedule('1 0 * * *', async () => {
    const today = new Date();
    if (today.getDate() === 1) {
      try {
        await saveTotalSupply();
        console.log('✅ Total supply saved on the 1st.');
      } catch (err) {
        console.error('❌ Error saving total supply:', err);
      }
    }
  });
};

module.exports = scheduleSupplyJob;