const { ethers } = require("ethers");
const cron = require("node-cron");
const { lotteryContract } = require("../config/constracts");
require("dotenv").config();

// Run every Sunday at 00:00
async function initializeLotteryIfNeeded() {
  const environment = process.env.Environment
  if (environment == "test" || environment == "Production") {
    try {
      const initialized = await lotteryContract.lotteryInitialized();
      if (!initialized) {
        console.log("Initializing lottery...");

        const tx = await lotteryContract.initializeLottery();
        await tx.wait();

        console.log("Lottery initialized successfully");
      } else {
        console.log("Lottery already initialized");
      }
    } catch (err) {
      console.error("Failed to check or initialize:", err);
    }
  }
}

cron.schedule("* */5 * * *", async () => {
  const environment = process.env.Environment
  if (environment == "test" || environment == "Production") {
    console.log("Starting weekly lottery draw...");

    try {
      //close the lottery at this period
      const txc = await lotteryContract.closeLotter();
      await txc.wait();
      console.log("Lottery Closed");

      // Step 1: drawNumbers
      const tx2 = await lotteryContract.drawNumbers();
      await tx2.wait();
      console.log("drawNumbers requested");

      // Step 2: allocateRewards
      const tx3 = await lotteryContract.allocateRewards();
      await tx3.wait();
      console.log("allocateRewards completed");
    } catch (err) {
      console.error("Error executing lottery functions:", err);
    }
  }
});


// Run every Monday at 01:00
cron.schedule("* */6 * * *", async () => {
  const environment = process.env.Environment
  if (environment == "test" || environment == "Production") {
    console.log("Starting weekly lottery rollover...");

    try {
      //reopen the lottery
      const txr = await lotteryContract.openLotter();
      await txr.wait();
      console.log("Lottery Opened");

      // Step 2: roundTurnover
      const tx1 = await lotteryContract.roundTurnOver();
      await tx1.wait();
      console.log("roundTurnOver executed");
    } catch (err) {
      console.error("Error executing lottery functions:", err);
    }
  }
});

module.exports = {
  initializeLotteryIfNeeded
}

