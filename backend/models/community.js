const mongoose = require('mongoose');
const requestSchema = require('./request');

const communitySchema = new mongoose.Schema({
  communityName: { type: String, unique: true, required: true },
  communityDescription: { type: String, required: true },
  communityDetailedDescription: { type: String, required: true },
  category: { type: String, required: true },
  visibility: { type: String, enum: ['public', 'private'], required: true },
  logo: { type: String }, // store image URL or filename
  contributionType: { type: String, enum: ['fixed', 'minimum', 'custom'], required: true },
  contributionAmounts: [{ type: Number, required: true }],
  ownerWallet: { type: String, required: true },
  contributionWallet: { type: String, required: true },
  communityAcceptanceStatus: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
  rejectionReason: { type: String },
  communityAdmin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  communityMembers: { type: Number, default: 100 },
  registeredMembers: [
    { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  ],
  pendingRequests: [requestSchema]
}, { timestamps: true });

module.exports = mongoose.model('Community', communitySchema);