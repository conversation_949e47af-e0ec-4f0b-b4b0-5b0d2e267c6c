const mongoose = require('mongoose');

const contributionSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true,
        index: true
    },
    amount: {
        type: Number,
        required: true,
        min: 5 // Minimum 5 SHLN contribution
    },
    currency: {
        type: String,
        enum: ['SHLN', 'USD'], // SHLN is primary, USD through Stripe as fallback
        required: true
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'pending'],
        default: 'pending'
    },
    paymentMethod: {
        type: String,
        enum: ['crypto', 'stripe'], // crypto for SHLN token, stripe for USD
        required: true
    },
    subscriptionId: {
        type: String,
    },
    lastPaymentDate: {
        type: Date,
        default: Date.now
    },
    nextPaymentDate: {
        type: Date,
        required: true
    },
    paymentHistory: [{
        amount: Number,
        date: Date,
        paymentReference: {
            type: String,
            required: true,
            unique: true
        },
        status: String,
        currency: String,
        tokenAmount: Number, // Amount in SHLN tokens
        reason: String // Optional reason for failed payments
    }]
}, {
    timestamps: true
});

// Index for efficient queries
contributionSchema.index({ userId: 1, status: 1 });
contributionSchema.index({ 'paymentHistory.paymentReference': 1 }, { unique: true });

const Contribution = mongoose.model('Contribution', contributionSchema);

// ContributionHistory schema and model
const contributionHistorySchema = new mongoose.Schema({
    userId: { type: String, required: true, index: true },
    amount: Number,
    currency: String,
    paymentMethod: String,
    status: String,
    setupIntentId: String,
    subscriptionId: String,
    lastPaymentDate: Date,
    nextPaymentDate: Date,
    date: { type: Date, default: Date.now },
    reason: String // Optional reason for the snapshot (e.g., 'upgrade', 'new', etc.)
}, { timestamps: true });

const ContributionHistory = mongoose.model('ContributionHistory', contributionHistorySchema);

module.exports = { Contribution, ContributionHistory }; 