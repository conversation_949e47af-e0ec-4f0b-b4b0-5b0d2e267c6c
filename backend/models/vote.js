const mongoose = require('mongoose');
const { Schema } = mongoose;
const Project = require('./project');

const DEFAULT_POINT_SYSTEM = { "1": 5, "2": 3, "3": 2, "4": 1, "5": 1 };

const VoteSchema = new Schema({
  votingType: {
    type: String,
    required: true,
    enum: ['multi', 'ranked'],
    index: true
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required'],
    validate: {
      validator: (v) => v instanceof Date && !isNaN(v),
      message: 'Invalid start date'
    }
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required'],
    validate: [
      {
        validator: (v) => v instanceof Date && !isNaN(v),
        message: 'Invalid end date'
      },
      {
        validator: function(v) {
          return v > this.startDate;
        },
        message: 'End date must be after start date'
      },
      {
        validator: function(v) {
          const maxDurationMs = 14 * 24 * 60 * 60 * 1000;
          return (v - this.startDate) <= maxDurationMs;
        },
        message: 'Voting period cannot exceed 14 days'
      }
    ]
  },
  totalFund: {
    type: Number,
    required: [true, 'Total fund amount is required'],
  },
  pointSystem: {
    type: Map,
    of: Number,
    default: DEFAULT_POINT_SYSTEM
  },
  projects: [{
    type: Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  }],
  status: {
    type: String,
    enum: ['pending', 'active', 'funded', 'completed'],
    default: 'active',
    index: true
  },
  isDeleted: {
    type: Boolean,
    default: false,
  },
  voteTitle: {
    type: String,
    required: [true, 'Vote title is required'],
    trim: true,

  },
  projectStarted: {
    type: Boolean,
    default: false,
  },
  fundingDistribution: {
    type: String,
    enum: ['total', 'per_project'],
    required: function() {
      return this.votingType === 'multi';
    }
  },
  numberOfWinningProjects: {
    type: Number,
    required: function() { return this.votingType === 'multi'; },
    min: [1, 'At least one winning project required']
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      ret.id = ret._id;
      if (ret.votingType !== 'ranked') {
        delete ret.pointSystem;
      } else {
        if (ret.pointSystem instanceof Map) {
          ret.pointSystem = Object.fromEntries(ret.pointSystem);
        } else if (typeof ret.pointSystem === 'object' && ret.pointSystem !== null && Object.keys(ret.pointSystem).length > 0) {
          // Already an object, do nothing
        } else {
          ret.pointSystem = { "1": 5, "2": 3, "3": 2, "4": 1, "5": 1 };
        }
      }
      if (ret.votingType !== 'multi') {
        delete ret.fundingDistribution;
        delete ret.numberOfWinningProjects;
      }
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

VoteSchema.virtual('remainingDays').get(function() {
  const now = new Date();
  const end = new Date(this.endDate);
  return Math.max(0, Math.ceil((end - now) / (1000 * 60 * 60 * 24)));
});

VoteSchema.query.active = function() {
  const now = new Date();
  return this.where({
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now }
  });
};

VoteSchema.methods.addRankedVote = async function(userId, rankings) {
  if (this.votingType !== 'ranked') {
    throw new Error('This vote does not accept ranked choices.');
  }
  const rankSet = new Set();
  rankings.forEach(r => {
    if (rankSet.has(r.rank)) {
      throw new Error(`Duplicate rank ${r.rank}`);
    }
    rankSet.add(r.rank);
  });
  await Promise.all(rankings.map(async ({ projectId, rank }) => {
    await Project.findByIdAndUpdate(projectId, {
      $push: { rankings: { userId, rank } }
    });
  }));
  await this.calculateResults();
};

VoteSchema.methods.calculateResults = async function(session) {
  if (this.votingType !== 'ranked') return;
  let pointSystemObj = this.pointSystem;
  if (pointSystemObj instanceof Map) {
    pointSystemObj = Object.fromEntries(pointSystemObj);
  }
  const pointSystemMap = new Map(
    Object.entries(pointSystemObj).map(([k, v]) => [parseInt(k), v])
  );
  const projects = await Project.find({ _id: { $in: this.projects } }).session(session);
  await Promise.all(projects.map(async (project) => {
    project.points = project.calculatePoints(pointSystemMap);
    await project.save({ session });
  }));
};

const Vote = mongoose.model('Vote', VoteSchema);

module.exports = Vote;