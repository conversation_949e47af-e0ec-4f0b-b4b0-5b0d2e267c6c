const mongoose = require('mongoose');

const affiliateSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  affiliateCode: { type: String, unique: true, required: true },
  rewardAmount: { type: Number, default: 0 }, 
  tokenRewardAmount: { type: Number, default: 0 }, 
  referrals: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  earningsHistory: [{
    amount: Number,
    tokenAmount: Number,
    date: { type: Date, default: Date.now },
    referredUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],
  isPolicyController: { type: Boolean, default: false }, 
  generationPolicy: {
    type: String,
    enum: ['admin_only', 'members_allowed'],
    default: 'admin_only'
  },
  expirationInHours: { type: Number, default: 168 }, 
  expiresAt: { type: Date },
  maxUsers: { type: Number, default: 5 }, 
  currentUsers: { type: Number, default: 0 }, 
  isActive: { type: Boolean, default: true },
  linkUrl: { type: String },
  linkToken: { type: String }, // Token for link expiration
  tokenExpiresAt: { type: Date }, // Token expiration date 
}, {
  timestamps: true
});

module.exports = mongoose.model('Affiliate', affiliateSchema);
