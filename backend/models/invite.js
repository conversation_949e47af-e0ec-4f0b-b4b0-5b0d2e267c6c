const mongoose = require('mongoose');
const crypto = require('crypto');

const inviteSchema = new mongoose.Schema({
  email: { type: String, required: false },
  message: { type: String },
  invitedAt: { type: Date, default: Date.now },
  accepted: { type: Boolean, default: false },
  linkToken: { type: String, sparse: true }, // For link-based invites
  expiration: { type: Date },
  maxUsers: { type: Number },
  uses: { type: Number, default: 0 },
  baseUrl: { type: String },
  inviteType: { type: String, enum: ['email', 'link'], default: 'email' }
});

// Create a unique index on linkToken but only when it's not null
inviteSchema.index({ linkToken: 1 }, { unique: true, sparse: true });

// For email invites, ensure one invite per email
inviteSchema.index({ email: 1 }, { 
  unique: true, 
  sparse: true,
  partialFilterExpression: { 
    inviteType: 'email', 
    email: { $exists: true, $ne: null } 
  }
});

module.exports = mongoose.model('Invite', inviteSchema);