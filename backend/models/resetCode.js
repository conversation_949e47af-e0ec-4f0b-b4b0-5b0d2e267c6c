const mongoose = require('mongoose');

const resetCodeSchema = mongoose.Schema({
    email: { 
        type: String, 
        required: true,
        lowercase: true 
    },
    code: { 
        type: String, 
        required: true 
    },
    expirationDate: { 
        type: Date, 
        required: true,
        default: () => new Date(Date.now() + 20 * 60 * 1000) // 20 minutes from now
    },
    verified: { type: Boolean, default: false },
    // The type field will allow you to distinguish between signup, signin, and reset codes.
    type: { type: String, enum: ["signup", "signin", "reset"], default: "signup" } 
}, { timestamps: true });

// Add TTL index to automatically remove expired documents
resetCodeSchema.index({ expirationDate: 1 }, { expireAfterSeconds: 0 });

const ResetCode = mongoose.model('ResetCode', resetCodeSchema);
module.exports = ResetCode;