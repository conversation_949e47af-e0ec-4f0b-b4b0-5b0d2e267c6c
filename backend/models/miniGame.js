const mongoose = require('mongoose');

const miniGameSchema = mongoose.Schema({
    userId: {type: String, required: true},
    email: {type: String, required: true},
    walletAddress: {type: String, required: false},
    coins: {type: Number, default: 0},
    earnPerTap: {type: Number, default: 1},
    level: {type: Number, default: 1},
    coinsToLevelUp: {type: String, default: "5K"},
    profitPerDay: {type: Number, default: 0},
    energyLimit: {type: Number, default: 100}   //JICKS DON'T FORGET, WE HAVE ENERGY AND ENERGY LIMIT, THE ENERGY WILL BE HANDLED INTHE FRONTEND, THE COUNTER WILL ADD EVERY INTERVAL AND STOPS WHEN IT REACHES THE ENERGY LIMIT
},
{timestamps: true, strictQuery: false},
    {
    writeConcern: {
        j: true,
        wtimeout: 1000
      }
})

module.exports = mongoose.model('MiniGame', miniGameSchema)