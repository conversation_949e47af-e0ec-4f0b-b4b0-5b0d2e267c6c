const mongoose = require('mongoose');

const inviteLinkSchema = new mongoose.Schema({
  linkId: { type: String, required: true, unique: true },
  token: { type: String, required: true },
  expiresAt: { type: Date, required: true },
  maxUsers: { type: Number, required: true },
  usedCount: { type: Number, default: 0 },
  isBlacklisted: { type: Boolean, default: false }
}, { timestamps: true });

module.exports = mongoose.model('InviteLink', inviteLinkSchema);
