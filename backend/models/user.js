const mongoose = require('mongoose');

const userSchema = mongoose.Schema(
  {
    firstName: { type: String,  required: true},
    lastName: { type: String, required: true },
    email: { type: String, required: true, unique: true, lowercase: true },
    password: { type: String, required: true}, 
    walletAddress: { type: String, lowercase: true },
    country: { type: String, lowercase: true }, 
    points: {type: Number, default: 0},
    role: {type: String, enum: ['admin', 'user'], default: 'user'},
    isDeleted: { type: Boolean, default: false }, 
  },
  // Schema configuration
  { timestamps: true, strictQuery: false },
  {
    writeConcern: {
      j: true,
      wtimeout: 1000,
    },
  }
);

const User = mongoose.model('User', userSchema);

module.exports = User;