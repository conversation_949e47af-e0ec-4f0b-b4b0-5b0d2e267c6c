const mongoose = require('mongoose');

const affiliateClaimSchema = new mongoose.Schema({
  affiliate: { type: String, required: true }, // affiliateCode
  claimant: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  referredUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  walletAddress: { type: String, required: true },
  claimedAt: { type: Date, default: Date.now },
  txHash: { type: String, default: null },
  amount: { type: Number, default: 0 }, // USD amount
  tokenAmount: { type: Number, default: 0 }, // Token amount
  status: { 
    type: String, 
    enum: ['pending', 'completed', 'failed'], 
    default: 'pending' 
  },
  rewardType: {
    type: String,
    enum: ['immediate', 'max_reached'], // immediate per joining, max_reached when limit hit
    default: 'immediate'
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('AffiliateClaim', affiliateClaimSchema);
