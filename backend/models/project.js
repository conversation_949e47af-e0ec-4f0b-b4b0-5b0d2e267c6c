const mongoose = require('mongoose');
const { Schema } = mongoose;

// Update subdocument schema
const UpdateSchema = new Schema({
  text: { type: String, required: true },
  image: { type: String },
  createdAt: { type: Date, default: Date.now },
});

// Main Project schema
const ProjectSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Project title is required'],
    trim: true,
    maxlength: [120, 'Title cannot exceed 120 characters'],
    minlength: [5, 'Title must be at least 5 characters']
  },
  description: {
    type: String,
    required: [true, 'Project description is required'],
    trim: true,
    minlength: [10, 'Description must be at least 10 characters']
  },
  image: {
    type: String,
    default: "",
    validate: {
      validator: v => !v || /^((https?:\/\/)|\/).+/.test(v),
      message: 'Image must be a valid URL or local path'
    }
  },
  voteId: {
    type: Schema.Types.ObjectId,
    ref: "Vote",
    required: true,
  },
  fundingAllocation: {
    type: Number,
    required: true,
    min: [0, 'Funding allocation cannot be negative']
  },
  status: {
    type: String,
    // enum: ["ongoing", "completed", "active", "funded", "started"],
    enum: ["ongoing", "completed"],
    default: "ongoing"
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  completionDate: {
    type: Date,
  },
  isDeleted: {
    type: Boolean,
    default: false,
  },
  updates: [UpdateSchema],
  // Multi-Voting fields
  voteCount: {
    yes: {
      type: Number,
      default: 0,
      min: 0
    },
    no: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  // Ranked Choice fields
  rankings: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rank: {
      type: Number,
      required: true,
      min: 1
    },
    _id: false
  }],
  voters: [{
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    vote: {
      type: String,
      enum: ['yes', 'no'],
      required: true
    },
    _id: false
  }],
  // Calculated fields
  points: {
    type: Number,
    default: 0
  }
}, { 
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// Virtual for vote percentages
ProjectSchema.virtual('percentages').get(function() {
  const total = this.voteCount.yes + this.voteCount.no;
  if (total === 0) return { yes: 0, no: 0 };
  return {
    yes: Math.round((this.voteCount.yes / total) * 100),
    no: Math.round((this.voteCount.no / total) * 100)
  };
});

// Method for calculating points from rankings
ProjectSchema.methods.calculatePoints = function(pointSystem) {
  return this.rankings.reduce((total, ranking) => {
    return total + (pointSystem.get(Number(ranking.rank)) || 0);
  }, 0);
};

const Project = mongoose.model('Project', ProjectSchema);

module.exports = Project;