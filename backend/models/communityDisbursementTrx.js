const mongoose = require("mongoose");


const CommunityDisbursementTrxSchema = new mongoose.Schema({
    communityId: { type: String, required: true,ref: "Community" },
    amount: { type: Number, required: true },
    status: { type: String, required: true, default: "completed", enum: ["pending", "completed", "canceled"] },
    method: { type: String, required: true, default: "crypto", enum: ["offline","crypto"] },
    trxHash: { type: String, required: false },
    receipt: { type: String, required: false },
    createdBy: { type: String, required: true,ref: "User" },
    completedBy: { type: String, required: false,ref: "User" },
    notes: { type: String, required: false },
}, { timestamps: true });

const CommunityDisbursementTrx = mongoose.model("CommunityDisbursementTrx", CommunityDisbursementTrxSchema);

module.exports = CommunityDisbursementTrx;



