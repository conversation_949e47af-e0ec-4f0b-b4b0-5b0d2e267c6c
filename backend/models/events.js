const mongoose = require('mongoose');

const attendeeSchema = new mongoose.Schema({
  name: String,
  email: String,
}, { _id: false });

const questionSchema = new mongoose.Schema({
  text: String,
  submittedBy: String, // Name or email
}, { _id: false });

const eventSchema = mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String, required: true },
    date: { type: Date, required: true },
    time: { type: String, required: true },
    duration: { type: Number, required: true },
    meetingLink: { type: String, required: true },
    attendees: [attendeeSchema],
    questions: [questionSchema],
    isPast: { type: Boolean, default: false },
    reminders: [{ type: String }], 
    recordingLinks: [{ type: String }],
  },
  { timestamps: true, strictQuery: false }
);

const Event = mongoose.model('Event', eventSchema);

module.exports = Event;