const mongoose = require('mongoose');

const communityAffiliateSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  communityId: { type: mongoose.Schema.Types.ObjectId, ref: 'Community' },
  affiliateCode: { type: String, unique: true },
  rewardAmount: { type: Number, default: 0 },
  referrals: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  earningsHistory: [{
    amount: Number,
    date: Date,
    referredUser: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
  }],

  isPolicyController: { type: Boolean, default: false }, 
  generationPolicy: {
    type: String,
    enum: ['admin_only', 'members_allowed'],
  },
  
  expirationInHours: Number, 
  expiresAt: Date,
  maxUsers: { type: Number, default: 5 },
  currentUsers: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true },
  linkToken: { type: String }, // Token for link expiration
  tokenExpiresAt: { type: Date }, // Token expiration date
}, {
  timestamps: true
});

module.exports = mongoose.model('CommunityAffiliate', communityAffiliateSchema);
