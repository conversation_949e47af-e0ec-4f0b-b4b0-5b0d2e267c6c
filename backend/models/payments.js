const mongoose = require('mongoose');

const mpesaStkSchema = mongoose.Schema({
  phoneNumber: { type: String, required: true },
  sessionId: { type: String, required: true, unique: true },
  walletAddress: { type: String, required: true },
  amount: { type: Number, required: true },
  bnbAmount: { type: Number, required: true },
  status: {
    type: String, required: true
    
  },
  bnbStatus: {
    type: String, required: true, default: "pending"
    
  },
  transactionHash: {type: String},
  provider: { type: String, required: true }
}, {
  timestamps: true
});

module.exports = mongoose.model('MpesaSTK', mpesaStkSchema);