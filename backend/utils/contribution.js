const { Contribution } = require('../models/contribution');


async function abortSession(session) {
    await session.abortTransaction();
    session.endSession();
    return;
}
const isActiveContributor = async (userId) => {
    const contribution = await Contribution.findOne({ userId });
    if (!contribution) return false;
    let active= contribution.nextPaymentDate > new Date();
    contribution.active = active;
    await contribution.save();
    return active;
}

// // SHLN token contract address and ABI
// const SHLN_TOKEN_ADDRESS = process.env.SHLN_TOKEN_ADDRESS;
// const SHLN_TOKEN_ABI = [
//     "function transfer(address to, uint256 amount) returns (bool)",
//     "function balanceOf(address account) view returns (uint256)",
//     "function decimals() view returns (uint8)"
// ];


const isTransactionHashExists = async (transactionHash) => {
    try {
        const existing = await Contribution.findOne({
            'paymentHistory.paymentReference': transactionHash,
        });

        return !!existing; // true if found, false if not
    } catch (error) {
        console.error('Error checking transaction hash existence:', error);
        throw new Error('Internal error during hash check');
    }
};

// Contribution tiers and benefits
const CONTRIBUTION_TIERS = {
    BRONZE: {
        minAmount: 1,
        benefits: ['Basic access', 'Community chat']
    },
    SILVER: {
        minAmount: 10,
        benefits: ['Basic access', 'Community chat', 'Early access to features']
    },
    GOLD: {
        minAmount: 50,
        benefits: ['Basic access', 'Community chat', 'Early access to features', 'Priority support']
    },
    PLATINUM: {
        minAmount: 100,
        benefits: ['Basic access', 'Community chat', 'Early access to features', 'Priority support', 'Exclusive events']
    }
};

// Helper function to get contribution tier
const getContributionTier = (amount) => {
    if (amount >= CONTRIBUTION_TIERS.PLATINUM.minAmount) return 'PLATINUM';
    if (amount >= CONTRIBUTION_TIERS.GOLD.minAmount) return 'GOLD';
    if (amount >= CONTRIBUTION_TIERS.SILVER.minAmount) return 'SILVER';
    return 'BRONZE';
};

// Helper function to validate amount
const validateAmount = (amount, currency) => {
    if (amount < 5) return false; // Minimum 5 SHLN contribution
    return true;
};

// Helper function to calculate next payment date
const calculateNextPaymentDate = (startDate) => {
    const nextDate = new Date(startDate);
    nextDate.setMonth(nextDate.getMonth() + 1);
    return nextDate;
};

// Helper function to generate unique system reference
const generateSystemReference = async () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    return `system_${timestamp}_${random}`;
};


module.exports ={ abortSession, isTransactionHashExists, CONTRIBUTION_TIERS,getContributionTier, generateSystemReference, calculateNextPaymentDate,validateAmount,isActiveContributor };