const CommunityAffiliate = require("../models/communityAffiliate");
const User = require("../models/user");
const { ethers } = require("ethers");

const ERC20_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)"
];

const MIN_REQUIRED_BALANCE = parseFloat(process.env.MIN_REQUIRED_BALANCE || "100000");
const TOKEN_ADDRESS = "******************************************";



/**
 * Verifies an Ethereum-compatible wallet address.
*/

const verifyWalletAddress = (address) => {
  if (typeof address !== "string") {
    return { isValid: false, message: "Address must be a string" };
  }
  if (!ethers.isAddress(address)) {
    return { isValid: false, message: "Invalid wallet address format" };
  }
  return {
    isValid: true,
    checksummed: ethers.getAddress(address),
  };
};



/**
 * Gets the native or ERC20 token balance of a wallet.
*/

const getWalletBalance = async (walletAddress, tokenAddress, provider) => {
  try {
    const validation = verifyWalletAddress(walletAddress);
    if (!validation.isValid) throw new Error(validation.message);

    const checksummedAddress = validation.checksummed;

    if (!tokenAddress || tokenAddress === ethers.ZeroAddress) {
      const balanceWei = await provider.getBalance(checksummedAddress);
      return parseFloat(ethers.formatEther(balanceWei));
    }

    const token = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
    const [rawBalance, decimals] = await Promise.all([
      token.balanceOf(checksummedAddress),
      token.decimals()
    ]);

    return parseFloat(ethers.formatUnits(rawBalance, decimals));
  } catch (error) {
    console.error(`Failed to fetch balance for ${walletAddress}:`, error);
    throw new Error("Unable to fetch wallet balance");
  }
};


/**
 * Checks if user has been referred by any affiliate.
*/

const checkForAffiliate = async (userId) => {
  try {
    const affiliateRecord = await CommunityAffiliate.findOne({ referrals: userId });
    if (!affiliateRecord) {
      return { found: false, message: "User is not referred by any affiliate" };
    }

    return {
      found: true,
      affiliateCode: affiliateRecord.affiliateCode,
      rewardAmount: affiliateRecord.rewardAmount,
      communityId: affiliateRecord.communityId,
      referredBy: affiliateRecord.userId,
    };
  } catch (err) {
    console.error("checkForAffiliate error:", err.message);
    return {
      found: false,
      error: err.message
    };
  }
};


/**
 * Calculates the discounted contribution amount.
*/

const calculateDiscountedAmount = (reward, original) => {
  if (reward > original) throw new Error("Reward exceeds contribution amount");
  return reward === original ? 1 : original - reward;
};


/**
 * Main logic for handling community contribution with affiliate reward.
*/

export const communityContribution = async (rewardAmount, originalAmount, userId) => {
  try {
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

    const user = await User.findById(userId);
    if (!user || !user.walletAddress) {
      throw new Error("User or wallet address not found.");
    }

    const { isValid, checksummed, message } = verifyWalletAddress(user.walletAddress);
    if (!isValid) {
      throw new Error(message);
    }

    // Get affiliate reward if exists
    const affiliateData = await checkForAffiliate(userId);
    rewardAmount = affiliateData?.rewardAmount || 0;

    // Calculate discounted amount
    const discountedAmount = calculateDiscountedAmount(rewardAmount, originalAmount);

    // Fetch wallet balance
    const walletBalance = await getWalletBalance(checksummed, TOKEN_ADDRESS, provider);
    console.log(`[INFO] Wallet balance for ${checksummed}: ${walletBalance}, Required: ${MIN_REQUIRED_BALANCE}`);

    // If reward fully covers contribution
    if (discountedAmount === 1) {
      return {
        status: "reward_applied",
        message: "User's reward covers full contribution.",
        discountedAmount,
        walletBalance
      };
    }

    // If balance too low
    if (walletBalance < MIN_REQUIRED_BALANCE) {
      console.warn(`[WARN] Wallet balance too low for user ${userId}`);
      return {
        status: "low_balance",
        shouldFundUser: true,
        userWallet: checksummed,
        walletBalance,
        requiredBalance: MIN_REQUIRED_BALANCE,
        message: "Insufficient token balance. Please fund the user's wallet from the frontend."
      };

      // Frontend should handle transfer of 100000 tokens here
      


    }

    // Contribution can proceed
    return {
      status: "valid",
      discountedAmount,
      walletBalance,
      message: "Contribution ready"
    };

  } catch (err) {
    console.error("communityContribution error:", err.message);
    return {
      status: "error",
      message: err.message
    };
  }
};