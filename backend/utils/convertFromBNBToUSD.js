const { ethers, formatUnits } = require('ethers');
const GetChainlinkAggregatorV3InterfaceABI = require('./getChainlinkAggregatorV3InterfaceABI');

const ConvertFromBNBToUSD = async (bnb) => {
    const environment = process.env.Environment
    const v3InterfaceABI = GetChainlinkAggregatorV3InterfaceABI

    const urlRPC = environment === "Production" ? "https://bnb-mainnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT" : "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"
    
    const provider = new ethers.JsonRpcProvider(
        urlRPC
    );

    const priceFeedAddress = environment === "Production"
        ? "******************************************" // Mainnet BNB/USD
        : "******************************************"; // Testnet BNB/USD on BSC Testnet

    const priceFeed = new ethers.Contract(
        priceFeedAddress,
        v3InterfaceABI,
        provider    
    )

    const latestRoundData = await priceFeed.latestRoundData()
    const decimals = await priceFeed.decimals()

    const valueOfOneBNB = Number(formatUnits(latestRoundData.answer, decimals)).toFixed(2);
    
    //JICKS: Look into adjusting fee wrt gas fees
    
    return Number((bnb * valueOfOneBNB).toFixed(2))
}

module.exports = ConvertFromBNBToUSD;