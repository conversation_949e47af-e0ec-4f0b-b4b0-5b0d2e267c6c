const { ethers } = require('ethers');
const { sahelionContract } = require('../config/constracts');
const Supply = require('../models/supply');

const saveTotalSupply = async () => {
  const rawSupply = await sahelionContract.totalSupply();
  const decimals = await sahelionContract.decimals();
  const formattedSupply = parseFloat(ethers.formatUnits(rawSupply, decimals));
  const totalSupply = formattedSupply;

  const now = new Date();
  const month = now.toLocaleString('default', { month: 'long' });
  const year = now.getFullYear();

  const exists = await Supply.findOne({ month, year });
  if (!exists) {
    const entry = new Supply({ month, year, totalSupply });
    await entry.save();
    console.log(`Saved supply for ${month} ${year}: ${totalSupply}`);
  } else {
    console.log(`Supply for ${month} ${year} already exists`);
  }
};

module.exports = { saveTotalSupply };