const sgMail = require("@sendgrid/mail");
const crypto = require("crypto");
require("dotenv").config();

sgMail.setApiKey(process.env.SENDGRID_API);

/**
 * Enhanced email configuration for better deliverability
 * Includes proper headers, authentication, and anti-spam measures
 */
const getBaseEmailConfig = () => ({
  from: {
    email: "<EMAIL>",
    name: "Black Panther DAO"
  },
  replyTo: "<EMAIL>",
  headers: {
    "X-Entity-Ref-ID": crypto.randomBytes(32).toString("hex"),
    "List-Unsubscribe": "<mailto:<EMAIL>>",
    "List-Unsubscribe-Post": "List-Unsubscribe=One-Click",
    "X-Priority": "3", // Normal priority (1=High, 3=Normal, 5=Low)
    "X-MSMail-Priority": "Normal",
    "Importance": "Normal",
    "X-Mailer": "Black Panther DAO Platform v1.0",
    "Organization": "Black Panther DAO",
    "X-Auto-Response-Suppress": "OOF, DR, RN, NRN, AutoReply",
    "Authentication-Results": "spf=pass smtp.mailfrom=blackpanthertkn.org",
    "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed",
    "Message-ID": `<${crypto.randomBytes(16).toString("hex")}@blackpanthertkn.org>`
  },
  mailSettings: {
    sandboxMode: { enable: false },
    bypassListManagement: { enable: false }, // Changed to false for better reputation
    footer: { enable: false },
    spamCheck: { enable: true, threshold: 1 }
  },
  trackingSettings: {
    clickTracking: { enable: false, enableText: false },
    openTracking: { enable: false },
    subscriptionTracking: { enable: false },
    ganalytics: { enable: false }
  },
  asm: {
    groupId: 0 // Unsubscribe group
  }
});

/**
 * Send invitation email with enhanced deliverability
 */
const sendInvitationEmail = async (recipientEmail, websiteUrl, personalMessage = '') => {
  const baseConfig = getBaseEmailConfig();
  
  const msg = {
    ...baseConfig,
    to: {
      email: recipientEmail,
      name: recipientEmail.split('@')[0]
    },
    subject: "Personal Invitation to Join Black Panther DAO",
    html: generateInviteEmailHTML(websiteUrl, personalMessage),
    // Add text version for better deliverability
    text: generateInviteEmailText(websiteUrl, personalMessage),
    categories: ["invitation", "dao", "community"],
    customArgs: {
      type: "invitation",
      source: "dao_platform"
    }
  };

  try {
    console.log(`Sending invitation email to: ${recipientEmail}`);
    const [response] = await sgMail.send(msg);
    console.log(`Email sent successfully. Status: ${response?.statusCode}`);
    return { success: true, statusCode: response?.statusCode };
  } catch (error) {
    console.error("Email sending error:", error);
    throw error;
  }
};

/**
 * Send community transfer email
 */
const sendTransferEmail = async (newOwnerId, communityName, transferToken) => {
  const User = require('../models/user');
  const user = await User.findById(newOwnerId);
  
  if (!user) {
    throw new Error('User not found');
  }

  const baseConfig = getBaseEmailConfig();
  const acceptUrl = `${process.env.BP_FrontEndURL || 'https://www.staging.blackpanthertkn.com'}/community/transfer/accept?token=${transferToken}`;
  
  const msg = {
    ...baseConfig,
    to: {
      email: user.email,
      name: `${user.firstName} ${user.lastName}`
    },
    subject: `Community Ownership Transfer - ${communityName}`,
    html: generateTransferEmailHTML(user.firstName, communityName, acceptUrl),
    text: generateTransferEmailText(user.firstName, communityName, acceptUrl),
    categories: ["transfer", "community", "ownership"],
    customArgs: {
      type: "community_transfer",
      communityName: communityName
    }
  };

  try {
    console.log(`Sending transfer email to: ${user.email}`);
    const [response] = await sgMail.send(msg);
    console.log(`Transfer email sent successfully. Status: ${response?.statusCode}`);
    return { success: true, statusCode: response?.statusCode };
  } catch (error) {
    console.error("Transfer email sending error:", error);
    throw error;
  }
};

/**
 * Generate HTML content for invitation email
 */
const generateInviteEmailHTML = (websiteUrl, personalMessage) => {
  const { emailTemp } = require("../public/html files/emailTemp");
  
  const content = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.6; color: #2d3748;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c5282; font-size: 28px; font-weight: 600; margin: 0;">
          Welcome to Black Panther DAO
        </h1>
        <p style="color: #4a5568; font-size: 18px; margin: 10px 0 0 0;">
          You've been personally invited to join our community
        </p>
      </div>
      
      ${personalMessage ? `
        <div style="margin: 30px 0; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #2c5282; border-radius: 8px;">
          <h3 style="color: #2c5282; margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">Personal Message:</h3>
          <p style="color: #4a5568; margin: 0; font-style: italic; line-height: 1.5;">"${personalMessage}"</p>
        </div>
      ` : ''}
      
      <div style="margin: 40px 0; text-align: center;">
        <a href="${websiteUrl}" 
           style="display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%); color: #ffffff; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(44, 82, 130, 0.3); transition: all 0.3s ease;">
          Join Our Community →
        </a>
      </div>

      <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
        <h3 style="color: #2c5282; margin: 0 0 15px 0; font-size: 18px;">What awaits you:</h3>
        <ul style="margin: 0; padding-left: 20px; color: #4a5568;">
          <li style="margin-bottom: 8px;">Access to exclusive DAO governance</li>
          <li style="margin-bottom: 8px;">Participate in community decisions</li>
          <li style="margin-bottom: 8px;">Connect with like-minded individuals</li>
          <li style="margin-bottom: 8px;">Early access to new features and opportunities</li>
        </ul>
      </div>

      <div style="margin: 30px 0; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
        <p style="margin: 0; color: #856404; font-size: 14px;">
          <strong>Can't click the button?</strong> Copy and paste this link into your browser:<br/>
          <span style="word-break: break-all; color: #2c5282; font-family: monospace;">${websiteUrl}</span>
        </p>
      </div>
      
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 40px 0;" />
      
      <div style="text-align: center;">
        <p style="color: #718096; font-size: 14px; margin: 0;">
          Questions? Reply to this email - we're here to help!<br/>
          <strong>Looking forward to seeing you in the community!</strong>
        </p>
        <p style="color: #a0aec0; font-size: 12px; margin: 20px 0 0 0;">
          This invitation was sent by Black Panther DAO. If you didn't expect this email, you can safely ignore it.
        </p>
      </div>
    </div>
  `;
  
  return emailTemp(content);
};

/**
 * Generate plain text content for invitation email
 */
const generateInviteEmailText = (websiteUrl, personalMessage) => {
  return `
Welcome to Black Panther DAO!

You've been personally invited to join our community.

${personalMessage ? `Personal Message: "${personalMessage}"\n\n` : ''}

Join our community by visiting: ${websiteUrl}

What awaits you:
- Access to exclusive DAO governance
- Participate in community decisions  
- Connect with like-minded individuals
- Early access to new features and opportunities

Questions? Just reply to this email - we're here to help!

Looking forward to seeing you in the community!

---
This invitation was sent by Black Panther DAO. If you didn't expect this email, you can safely ignore it.
  `.trim();
};

/**
 * Generate HTML content for transfer email
 */
const generateTransferEmailHTML = (firstName, communityName, acceptUrl) => {
  const { emailTemp } = require("../public/html files/emailTemp");

  const content = `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.6; color: #2d3748;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c5282; font-size: 28px; font-weight: 600; margin: 0;">
          Community Ownership Transfer
        </h1>
        <p style="color: #4a5568; font-size: 18px; margin: 10px 0 0 0;">
          You've been selected as the new community owner
        </p>
      </div>

      <div style="margin: 30px 0; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #2c5282; border-radius: 8px;">
        <p style="color: #2d3748; margin: 0; font-size: 16px; line-height: 1.5;">
          Hello <strong>${firstName}</strong>,<br/><br/>
          You have been selected to become the new owner of the community <strong>"${communityName}"</strong>.
          This is an important responsibility that includes managing community members, settings, and overall governance.
        </p>
      </div>

      <div style="margin: 40px 0; text-align: center;">
        <a href="${acceptUrl}"
           style="display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: #ffffff; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);">
          Accept Ownership →
        </a>
      </div>

      <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
        <h3 style="color: #2c5282; margin: 0 0 15px 0; font-size: 18px;">As the new owner, you'll be able to:</h3>
        <ul style="margin: 0; padding-left: 20px; color: #4a5568;">
          <li style="margin-bottom: 8px;">Manage community members and permissions</li>
          <li style="margin-bottom: 8px;">Update community settings and description</li>
          <li style="margin-bottom: 8px;">Transfer ownership to other members</li>
          <li style="margin-bottom: 8px;">Delete the community if necessary</li>
        </ul>
      </div>

      <div style="margin: 30px 0; padding: 15px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px;">
        <p style="margin: 0; color: #92400e; font-size: 14px;">
          <strong>⚠️ Important:</strong> This transfer link will expire in 24 hours. Please accept or decline promptly.<br/>
          If you can't click the button, copy this link: <span style="word-break: break-all; font-family: monospace;">${acceptUrl}</span>
        </p>
      </div>

      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 40px 0;" />

      <div style="text-align: center;">
        <p style="color: #718096; font-size: 14px; margin: 0;">
          Questions about this transfer? Reply to this email for assistance.<br/>
          <strong>Thank you for being part of our community!</strong>
        </p>
      </div>
    </div>
  `;

  return emailTemp(content);
};

/**
 * Generate plain text content for transfer email
 */
const generateTransferEmailText = (firstName, communityName, acceptUrl) => {
  return `
Community Ownership Transfer

Hello ${firstName},

You have been selected to become the new owner of the community "${communityName}".
This is an important responsibility that includes managing community members, settings, and overall governance.

As the new owner, you'll be able to:
- Manage community members and permissions
- Update community settings and description
- Transfer ownership to other members
- Delete the community if necessary

To accept this transfer, visit: ${acceptUrl}

⚠️ Important: This transfer link will expire in 24 hours. Please accept or decline promptly.

Questions about this transfer? Reply to this email for assistance.

Thank you for being part of our community!
  `.trim();
};

module.exports = {
  sendInvitationEmail,
  sendTransferEmail,
  getBaseEmailConfig,
  generateInviteEmailHTML,
  generateInviteEmailText,
  generateTransferEmailHTML,
  generateTransferEmailText
};
