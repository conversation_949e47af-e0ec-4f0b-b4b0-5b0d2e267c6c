{"name": "backend", "version": "1.0.0", "description": "node js", "main": "index.js", "scripts": {"test": "jest", "start": "node index.js", "start:nodemon": "nodemon index.js"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-bedrock-agent-runtime": "^3.815.0", "@aws-sdk/client-bedrock-runtime": "^3.0.0", "@chaingpt/generalchat": "^0.0.17", "@sendgrid/mail": "^8.1.4", "@stripe/stripe-js": "^7.3.1", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.2.2", "dotenv": "^16.4.7", "ethers": "^6.14.3", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-slow-down": "^2.0.1", "hcaptcha": "^0.2.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "memcached": "^2.2.2", "moment": "^2.30.1", "mongoose": "^8.4.4", "multer": "^1.4.5-lts.2", "nanoid": "^3.3.9", "node-cron": "^4.1.0", "node-mocks-http": "^1.17.2", "rate-limiter-flexible": "^4.0.1", "stripe": "^18.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.12.0", "web3": "^4.16.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "nodemon": "^3.1.4", "supertest": "^7.1.3", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}}