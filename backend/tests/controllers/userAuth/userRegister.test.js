jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
}));
const bcrypt = require('bcryptjs');

let lastUserData;
const findOneMock = jest.fn();
const saveMock = jest.fn();

jest.mock('../../../models/user', () => {
  return function MockUser(data) {
    lastUserData = data;
    return {
      ...data,
      save: saveMock,
    };
  };
});

const User = require('../../../models/user');
User.findOne = findOneMock;

const { SignUp } = require('../../../controllers/user');

describe('SignUp Controller', () => {
  let req, res, statusMock, jsonMock;

  beforeEach(() => {
    jest.clearAllMocks();
    lastUserData = null;

    jsonMock = jest.fn();
    statusMock = jest.fn(() => ({ json: jsonMock }));

    req = {
      body: {
        firstName: 'Max',
        lastName: 'Genius',
        email: '<EMAIL>',
        password: 'password123',
        country: 'Kenya',
        walletAddress: '0x123',
        isSubscribed: true,
        role: 'user',
      },
    };

    res = { status: statusMock };
  });

  it('should return 203 if user already exists', (done) => {
    findOneMock.mockResolvedValue({ email: '<EMAIL>' });

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(findOneMock).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(statusMock).toHaveBeenCalledWith(203);
      expect(jsonMock).toHaveBeenCalledWith({
        message: expect.stringContaining('already exists'),
      });
      done();
    });
  });

  it('should create a new user if email is not taken', (done) => {
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('hashed_password');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 10);
      expect(saveMock).toHaveBeenCalled();
      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith({
        message: 'User created successfully',
      });
      expect(lastUserData.password).toBe('hashed_password');
      done();
    });
  });

  it('should default optional fields and role when omitted', (done) => {
    req.body = {
      firstName: 'Alice',
      lastName: 'Smith',
      email: '<EMAIL>',
      password: 'pass',
    };
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(saveMock).toHaveBeenCalled();
      expect(statusMock).toHaveBeenCalledWith(201);
      expect(lastUserData.country).toBeNull();
      expect(lastUserData.walletAddress).toBeNull();
      expect(lastUserData.isSubscribed).toBe(false);
      expect(lastUserData.role).toBe('user');
      done();
    });
  });

  it('should handle missing password (hash called with empty)', (done) => {
    req.body.password = '';
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(bcrypt.hash).toHaveBeenCalledWith('', 10);
      expect(saveMock).toHaveBeenCalled();
      done();
    });
  });

  it('should handle long email strings', (done) => {
    const longEmail = 'a'.repeat(1000) + '@test.com';
    req.body.email = longEmail;
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(findOneMock).toHaveBeenCalledWith({ email: longEmail });
      expect(saveMock).toHaveBeenCalled();
      done();
    });
  });

  it('should handle unicode characters in names', (done) => {
    req.body.firstName = '你好';
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(lastUserData.firstName).toBe('你好');
      expect(saveMock).toHaveBeenCalled();
      done();
    });
  });

  it('should treat uppercase and lowercase emails as the same', (done) => {
    req.body.email = '<EMAIL>';
    findOneMock.mockImplementation(({ email }) =>
      Promise.resolve(email.toLowerCase() === '<EMAIL>' ? { email } : null)
    );

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(findOneMock).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(statusMock).toHaveBeenCalledWith(203);
      done();
    });
  });

  it('should treat trimmed and untrimmed emails as the same', (done) => {
    req.body.email = '  <EMAIL>  ';
    findOneMock.mockImplementation(({ email }) =>
      Promise.resolve(email.trim() === '<EMAIL>' ? { email } : null)
    );

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(findOneMock).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(statusMock).toHaveBeenCalledWith(203);
      done();
    });
  });

  it('should attempt lookup on injection-like email but not crash', (done) => {
    const malicious = "{'$gt': ''}";
    req.body.email = malicious;
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(findOneMock).toHaveBeenCalledWith({ email: malicious });
      expect(saveMock).toHaveBeenCalled();
      done();
    });
  });

  it('should handle whitespace-only names', (done) => {
    req.body.firstName = '   ';
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('h');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(lastUserData.firstName).toBe('   ');
      expect(saveMock).toHaveBeenCalled();
      done();
    });
  });

  it('should not allow a user to register themselves as an admin', (done) => {
    req.body.role = 'admin';
    findOneMock.mockResolvedValue(null);
    bcrypt.hash.mockResolvedValue('hashed_password');
    saveMock.mockResolvedValue(true);

    SignUp(req, res, () => {});

    setImmediate(() => {
      expect(saveMock).toHaveBeenCalled();
      expect(lastUserData.role).not.toBe('admin');
      expect(lastUserData.role).toBe('user');
      done(); 
    });
  });

});
