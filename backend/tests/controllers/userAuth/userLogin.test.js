// Mocks
jest.mock('bcrypt', () => ({
  compare: jest.fn(),
  hash: jest.fn(),
}));

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
}));

jest.mock('../../../models/user', () => ({
  findOne: jest.fn(),
}));

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../../../models/user');
const { SignIn } = require('../../../controllers/user');

describe('SignIn Controller - Security Tests', () => {
  let req, res, statusMock, jsonMock;

  beforeEach(() => {
    statusMock = jest.fn().mockReturnThis();
    jsonMock = jest.fn();

    req = {
      body: {
        email: '<EMAIL>',
        password: 'password123',
      },
    };

    res = {
      status: statusMock,
      json: jsonMock,
    };

    jest.clearAllMocks();
  });

  it('should return 401 if email is missing', async () => {
    req.body.email = '';
    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should return 401 if password is missing', async () => {
    req.body.password = '';
    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should reject SQL injection attempt in email', async () => {
    req.body.email = "' OR 1=1 --";
    User.findOne.mockResolvedValue(null);

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
    expect(jsonMock).toHaveBeenCalledWith({
      message: 'User not found',
    });
  });

  it('should reject invalid email format', async () => {
    req.body.email = 'invalid-email';
    User.findOne.mockResolvedValue(null);

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should return 401 for very short password', async () => {
    req.body.password = '1';
    User.findOne.mockResolvedValue({
      _id: 'id1',
      email: '<EMAIL>',
      password: 'hashed',
      isDeleted: false,
    });
    bcrypt.compare.mockResolvedValue(false);

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should handle long passwords safely', async () => {
    req.body.password = 'a'.repeat(1000);
    User.findOne.mockResolvedValue({
      _id: 'id1',
      email: '<EMAIL>',
      password: 'hashed',
      isDeleted: false,
    });
    bcrypt.compare.mockResolvedValue(false);

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should sanitize XSS-like email input', async () => {
    req.body.email = `<script>alert('xss')</script>`;
    User.findOne.mockResolvedValue(null);

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
  });

  it('should handle JWT token signing error', async () => {
    const mockUser = {
      _id: 'userId123',
      email: '<EMAIL>',
      password: 'hashedPassword',
      role: 'user',
      isSubscribed: false,
      isDeleted: false,
    };
    User.findOne.mockResolvedValue(mockUser);
    bcrypt.compare.mockResolvedValue(true);
    jwt.sign.mockImplementation(() => {
      throw new Error('JWT signing failed');
    });

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
    expect(jsonMock).toHaveBeenCalledWith({
      message: 'Invalid password or email',
      err: [expect.any(Error)],
    });
  });

  it('should return 401 for deleted user', async () => {
    User.findOne.mockResolvedValue(null);
    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(401);
    expect(jsonMock).toHaveBeenCalledWith({
      message: 'User not found',
    });
  });

  it('should handle multiple failed attempts gracefully', async () => {
    User.findOne.mockResolvedValue({
      password: 'hashedPassword',
      isDeleted: false,
    });
    bcrypt.compare.mockResolvedValue(false);

    for (let i = 0; i < 5; i++) {
      await SignIn(req, res, () => {});
    }

    expect(statusMock).toHaveBeenCalledTimes(5);
    expect(jsonMock).toHaveBeenCalledTimes(5);
  });

  it('should return valid token and user data on success', async () => {
    const mockUser = {
      _id: 'userId123',
      email: '<EMAIL>',
      password: 'hashedPassword',
      isDeleted: false,
      role: 'user',
      isSubscribed: true,
    };
    User.findOne.mockResolvedValue(mockUser);
    bcrypt.compare.mockResolvedValue(true);
    jwt.sign.mockReturnValue('secure_token');

    await SignIn(req, res, () => {});
    expect(statusMock).toHaveBeenCalledWith(200);
    expect(jsonMock).toHaveBeenCalledWith({
      token: 'secure_token',
      expiresIn: 1800,
      userId: 'userId123',
      message: 'Signed in successfully',
    });
  });
});
