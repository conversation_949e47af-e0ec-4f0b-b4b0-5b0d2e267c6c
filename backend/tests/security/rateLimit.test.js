jest.mock('../../config/securityConfig', () => ({
  isFeatureEnabled: () => true,
  getConfigValue: (key, defaultValue) => defaultValue
}));
const request = require('supertest');
const express = require('express');
const { rateLimiters, speedLimiter, applyAdvancedRateLimit, botDetection, requestFingerprint } = require('../../config/rateLimit');


describe('Rate Limiting Tests', () => {
    let app;

    beforeEach(() => {
        app = express();
        app.use(express.json());
        app.enable('trust proxy');
    });

    describe('Basic Rate Limiting', () => {
        it('should allow requests within rate limit', async () => {
            app.use('/test', rateLimiters.api, (req, res) => {
                res.json({ message: 'success' });
            });

            // Make requests within limit
            for (let i = 0; i < 5; i++) {
                const response = await request(app)
                    .get('/test')
                    .set('X-Forwarded-For', '***********');
                
                expect(response.status).toBe(200);
            }
        });

        it('should block requests exceeding rate limit', async () => {
            app.use('/test', rateLimiters.auth, (req, res) => {
                res.json({ message: 'success' });
            });

            // Make requests exceeding limit
            for (let i = 0; i < 5; i++) {
                await request(app)
                    .post('/test')
                    .set('X-Forwarded-For', '***********');
            }

            // Next request should be blocked
            const response = await request(app)
                .post('/test')
                .set('X-Forwarded-For', '***********');

            expect(response.status).toBe(429);
            expect(response.body.code).toBe('AUTH_RATE_LIMIT_EXCEEDED');
        });

        it('should use different keys for different IPs', async () => {
            app.use('/test', rateLimiters.auth, (req, res) => {
                res.json({ message: 'success' });
            });

            // Exceed limit for one IP
            for (let i = 0; i < 6; i++) {
                await request(app)
                    .post('/test')
                    .set('X-Forwarded-For', '***********');
            }

            // Different IP should still work
            const response = await request(app)
                .post('/test')
                .set('X-Forwarded-For', '***********');

            expect(response.status).toBe(200);
        });
    });

    describe('Speed Limiting', () => {
        it('should apply delays after threshold', async () => {
            app.use('/test', speedLimiter, (req, res) => {
                res.json({ message: 'success' });
            });

            const startTime = Date.now();

            // Make requests to trigger speed limiting
            for (let i = 0; i < 60; i++) {
                await request(app)
                    .get('/test')
                    .set('X-Forwarded-For', '***********');
            }

            const endTime = Date.now();
            const duration = endTime - startTime;

            // Should have some delay applied
            expect(duration).toBeGreaterThan(1000);
        });
    });

    describe('Bot Detection', () => {
        it('should detect bot user agents', async () => {
            app.use('/test', botDetection, (req, res) => {
                res.json({ message: 'success' });
            });

            const botUserAgents = [
                'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                'curl/7.68.0',
                'python-requests/2.25.1',
                'Java/1.8.0_291',
                'Go-http-client/1.1'
            ];

            for (const userAgent of botUserAgents) {
                const response = await request(app)
                    .get('/test')
                    .set('User-Agent', userAgent);

                expect(response.status).toBe(403);
                expect(response.body.code).toBe('BOT_ACCESS_DENIED');
            }
        });

        it('should allow legitimate user agents', async () => {
            app.use('/test', botDetection, (req, res) => {
                res.json({ message: 'success' });
            });

            const legitimateUserAgents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
            ];

            for (const userAgent of legitimateUserAgents) {
                const response = await request(app)
                    .get('/test')
                    .set('User-Agent', userAgent);

                expect(response.status).toBe(200);
            }
        });
    });

    describe('Request Fingerprinting', () => {
        it('should generate consistent fingerprints', async () => {
            app.use('/test', requestFingerprint, (req, res) => {
                res.json({ fingerprint: req.fingerprint });
            });

            const response1 = await request(app)
                .get('/test')
                .set('User-Agent', 'test-agent')
                .set('Accept-Language', 'en-US')
                .set('X-Forwarded-For', '***********');

            const response2 = await request(app)
                .get('/test')
                .set('User-Agent', 'test-agent')
                .set('Accept-Language', 'en-US')
                .set('X-Forwarded-For', '***********');

            expect(response1.body.fingerprint).toBe(response2.body.fingerprint);
        });

        it('should generate different fingerprints for different requests', async () => {
            app.use('/test', requestFingerprint, (req, res) => {
                res.json({ fingerprint: req.fingerprint });
            });

            const response1 = await request(app)
                .get('/test')
                .set('User-Agent', 'agent1')
                .set('X-Forwarded-For', '***********');

            const response2 = await request(app)
                .get('/test')
                .set('User-Agent', 'agent2')
                .set('X-Forwarded-For', '***********');

            expect(response1.body.fingerprint).not.toBe(response2.body.fingerprint);
        });
    });

    describe('Payment Rate Limiting', () => {
        it('should apply stricter limits to payment endpoints', async () => {
            app.use('/payment', rateLimiters.payment, (req, res) => {
                res.json({ message: 'payment processed' });
            });

            // Payment endpoints have lower limits
            for (let i = 0; i < 10; i++) {
                await request(app)
                    .post('/payment')
                    .set('X-Forwarded-For', '***********');
            }

            // Next request should be blocked
            const response = await request(app)
                .post('/payment')
                .set('X-Forwarded-For', '***********');

            expect(response.status).toBe(429);
            expect(response.body.code).toBe('PAYMENT_RATE_LIMIT_EXCEEDED');
        });
    });

    describe('Email Rate Limiting', () => {
        it('should limit email requests by email address', async () => {
            app.use('/email', rateLimiters.email, (req, res) => {
                res.json({ message: 'email sent' });
            });

            // Send emails to same address
            for (let i = 0; i < 5; i++) {
                await request(app)
                    .post('/email')
                    .send({ email: '<EMAIL>' });
            }

            // Next request should be blocked
            const response = await request(app)
                .post('/email')
                .send({ email: '<EMAIL>' });

            expect(response.status).toBe(429);
            expect(response.body.code).toBe('EMAIL_RATE_LIMIT_EXCEEDED');
        });

        it('should allow different email addresses', async () => {
            app.use('/email', rateLimiters.email, (req, res) => {
                res.json({ message: 'email sent' });
            });

            // Exceed limit for one email
            for (let i = 0; i < 6; i++) {
                await request(app)
                    .post('/email')
                    .send({ email: '<EMAIL>' });
            }

            // Different email should still work
            const response = await request(app)
                .post('/email')
                .send({ email: '<EMAIL>' });

            expect(response.status).toBe(200);
        });
    });
}); 