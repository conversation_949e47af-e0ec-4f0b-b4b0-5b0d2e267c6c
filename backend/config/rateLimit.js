const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const { RateLimiterRedis } = require('rate-limiter-flexible');
const Redis = require('ioredis');
const { securityConfig, isFeatureEnabled, getConfigValue } = require('./securityConfig');

// Redis client for distributed rate limiting
const redisClient = isFeatureEnabled('rateLimiting.redis.enabled') ? new Redis({
    host: getConfigValue('rateLimiting.redis.host', 'localhost'),
    port: getConfigValue('rateLimiting.redis.port', 6379),
    password: getConfigValue('rateLimiting.redis.password'),
    retryDelayOnFailover: getConfigValue('rateLimiting.redis.retryDelayOnFailover', 100),
    maxRetriesPerRequest: getConfigValue('rateLimiting.redis.maxRetriesPerRequest', 3),
    lazyConnect: getConfigValue('rateLimiting.redis.lazyConnect', true),
    connectTimeout: getConfigValue('rateLimiting.redis.connectTimeout', 17000),
    retryStrategy: getConfigValue('rateLimiting.redis.retryStrategy', (times) => Math.min(times * 30, 1000)),
    reconnectOnError: getConfigValue('rateLimiting.redis.reconnectOnError', (error) => {
        const targetErrors = [/READONLY/, /ETIMEDOUT/];
        return targetErrors.some(targetError => targetError.test(error.message));
    })
}) : null;

// Log Redis connection issues
if (redisClient) {
    redisClient.on('error', (error) => {
        console.error('[SECURITY] Redis connection error:', error.message);
    });
}

// Rate limiters for different endpoint types
const rateLimiters = {
    // Authentication endpoints - strict limits
    auth: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.auth.windowMs', 15 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.auth.max', 5),
        message: {
            error: 'Too many authentication attempts',
            message: 'Please try again in 15 minutes',
            code: 'AUTH_RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => `${req.ip}-${req.headers['user-agent']}`,
        skip: (req) => req.path.includes('/webhook')
    }) : (req, res, next) => next(),

    // General API endpoints - moderate limits
    api: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.api.windowMs', 15 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.api.max', 100),
        message: {
            error: 'Too many API requests',
            message: 'Please try again in 15 minutes',
            code: 'API_RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.userEmailAndId?.userId || req.ip
    }) : (req, res, next) => next(),

    // Payment endpoints - very strict limits
    payment: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.payment.windowMs', 60 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.payment.max', 10),
        message: {
            error: 'Too many payment attempts',
            message: 'Please try again in 1 hour',
            code: 'PAYMENT_RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.userEmailAndId?.userId || req.ip
    }) : (req, res, next) => next(),

    // Crypto verification - extremely strict
    cryptoVerification: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.crypto.windowMs', 60 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.crypto.max', 3),
        message: {
            error: 'Too many crypto verification attempts',
            message: 'Please try again in 1 hour',
            code: 'CRYPTO_VERIFICATION_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.userEmailAndId?.userId || req.ip
    }) : (req, res, next) => next(),

    // Email sending - strict limits
    email: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.email.windowMs', 60 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.email.max', 5),
        message: {
            error: 'Too many email requests',
            message: 'Please try again in 1 hour',
            code: 'EMAIL_RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.body.email || req.ip
    }) : (req, res, next) => next(),

    // File upload limits
    fileUpload: isFeatureEnabled('rateLimiting.enabled') ? rateLimit({
        windowMs: getConfigValue('rateLimiting.limits.fileUpload.windowMs', 60 * 60 * 1000),
        max: getConfigValue('rateLimiting.limits.fileUpload.max', 20),
        message: {
            error: 'Too many file uploads',
            message: 'Please try again in 1 hour',
            code: 'FILE_UPLOAD_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => req.userEmailAndId?.userId || req.ip
    }) : (req, res, next) => next()
};

// Progressive speed limiting (slows down requests gradually)
const speedLimiter = isFeatureEnabled('rateLimiting.speedLimit.enabled') ? slowDown({
    windowMs: getConfigValue('rateLimiting.speedLimit.windowMs', 15 * 60 * 1000),
    delayAfter: getConfigValue('rateLimiting.speedLimit.delayAfter', 50),
    delayMs: getConfigValue('rateLimiting.speedLimit.delayMs', 500),
    maxDelayMs: getConfigValue('rateLimiting.speedLimit.maxDelayMs', 20000),
    keyGenerator: (req) => req.userEmailAndId?.userId || req.ip
}) : (req, res, next) => next();

// Advanced Redis-based rate limiter factory
const createAdvancedRateLimiter = (keyPrefix, points, duration) => {
    if (!isFeatureEnabled('rateLimiting.redis.enabled') || !redisClient) {
        return (req, res, next) => next();
    }
    
    return new RateLimiterRedis({
        storeClient: redisClient,
        keyPrefix,
        points, // Number of allowed requests
        duration, // Time window in seconds
        blockDuration: 60 * 15, // Block duration in seconds
    });
};

// Advanced rate limiters for specific scenarios
const advancedLimiters = {
    bruteForce: createAdvancedRateLimiter('brute_force', 5, 60 * 15), // 5 attempts per 15 minutes
    userActions: createAdvancedRateLimiter('user_actions', 100, 60 * 15), // 100 actions per 15 minutes
    paymentAttempts: createAdvancedRateLimiter('payment_attempts', 10, 60 * 60), // 10 attempts per hour
    emailVerification: createAdvancedRateLimiter('email_verification', 3, 60 * 60), // 3 attempts per hour
};

// Middleware to apply advanced rate limiting
const applyAdvancedRateLimit = (limiterType) => {
    return async (req, res, next) => {
        try {
            const key = req.userEmailAndId?.userId || req.ip;
            const limiter = advancedLimiters[limiterType];
            
            if (!limiter) {
                return next();
            }

            await limiter.consume(key);
            next();
        } catch (rejRes) {
            const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
            
            console.warn('[SECURITY] Advanced rate limit exceeded:', {
                type: limiterType,
                key: req.userEmailAndId?.userId || req.ip,
                retryAfter: secs
            });
            
            res.set('Retry-After', String(secs));
            res.status(429).json({
                error: 'Rate limit exceeded',
                message: `Too many requests. Try again in ${secs} seconds.`,
                code: `${limiterType.toUpperCase()}_RATE_LIMIT_EXCEEDED`,
                retryAfter: secs
            });
        }
    };
};

// Bot detection based on user agent patterns
const botDetection = (req, res, next) => {
    const userAgent = req.headers['user-agent'] || '';
    const suspiciousPatterns = [
        /bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i,
        /python/i, /java/i, /perl/i, /ruby/i, /go-http-client/i,
        /httpclient/i, /okhttp/i, /axios/i, /fetch/i
    ];

    const isBot = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    
    if (isBot && !req.path.includes('/webhook')) {
        console.warn('[SECURITY] Bot detected:', {
            ip: req.ip,
            userAgent: userAgent.substring(0, 50), // Truncate for safety
            path: req.path
        });
        
        return res.status(403).json({
            error: 'Bot access denied',
            message: 'Automated access is not allowed',
            code: 'BOT_ACCESS_DENIED'
        });
    }

    next();
};

// Generate request fingerprint for tracking
const requestFingerprint = (req, res, next) => {
    const fingerprint = {
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        acceptLanguage: req.headers['accept-language'],
        acceptEncoding: req.headers['accept-encoding'],
        method: req.method,
        path: req.path
    };

    req.fingerprint = JSON.stringify(fingerprint);
    next();
};

module.exports = {
    rateLimiters,
    speedLimiter,
    advancedLimiters,
    applyAdvancedRateLimit,
    botDetection,
    requestFingerprint,
    redisClient
}; 