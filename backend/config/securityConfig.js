// Security configuration driven by environment variables
const securityConfig = {
    captchaType: process.env.CAPTCHA_TYPE || 'recaptcha',
    // Master security toggle
    enabled: process.env.SECURITY_ENABLED !== 'false' && (process.env.NODE_ENV === 'production' || process.env.SECURITY_ENABLED === 'true'),
    
    // Rate limiting configuration
    rateLimiting: {
        enabled: process.env.SECURITY_RATE_LIMITING_ENABLED !== 'false' && (process.env.NODE_ENV === 'production' || process.env.SECURITY_RATE_LIMITING_ENABLED === 'true'),
        redis: {
            enabled: process.env.SECURITY_REDIS_ENABLED === 'true',
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD || null
        },
        limits: {
            auth: {
                windowMs: 15 * 60 * 1000, // 15 minutes
                max: process.env.NODE_ENV === 'development' ? 20 : 5
            },
            api: {
                windowMs: 15 * 60 * 1000, // 15 minutes
                max: process.env.NODE_ENV === 'development' ? 500 : 100
            },
            payment: {
                windowMs: 60 * 60 * 1000, // 1 hour
                max: process.env.NODE_ENV === 'development' ? 50 : 10
            },
            email: {
                windowMs: 60 * 60 * 1000, // 1 hour
                max: 5
            },
            crypto: {
                windowMs: 60 * 60 * 1000, // 1 hour
                max: 3
            }
        }
    },

    // Anti-bot protection configuration
    antiBot: {
        enabled: process.env.SECURITY_ANTI_BOT_ENABLED !== 'false' && (process.env.NODE_ENV === 'production' || process.env.SECURITY_ANTI_BOT_ENABLED === 'true'),
        captcha: {
            // Auto-enable if keys are present
            enabled: process.env.SECURITY_CAPTCHA_ENABLED === 'true' || 
                     (process.env.RECAPTCHA_SECRET_KEY && process.env.RECAPTCHA_SITE_KEY) ||
                     (process.env.HCAPTCHA_SECRET_KEY && process.env.HCAPTCHA_SITE_KEY),
            recaptcha: {
                enabled: !!(process.env.RECAPTCHA_SECRET_KEY && process.env.RECAPTCHA_SITE_KEY),
                siteKey: process.env.RECAPTCHA_SITE_KEY,
                secretKey: process.env.RECAPTCHA_SECRET_KEY,
                minScore: parseFloat(process.env.RECAPTCHA_MIN_SCORE) || 0.5
            },
            hcaptcha: {
                enabled: !!(process.env.HCAPTCHA_SECRET_KEY && process.env.HCAPTCHA_SITE_KEY),
                siteKey: process.env.HCAPTCHA_SITE_KEY,
                secretKey: process.env.HCAPTCHA_SECRET_KEY
            }
        },
        botDetection: {
            enabled: process.env.SECURITY_BOT_DETECTION_ENABLED !== 'false',
            headerAnalysis: process.env.SECURITY_HEADER_ANALYSIS_ENABLED !== 'false',
            patternAnalysis: process.env.SECURITY_PATTERN_ANALYSIS_ENABLED !== 'false',
            rapidRequestDetection: process.env.SECURITY_RAPID_REQUEST_DETECTION_ENABLED !== 'false'
        }
    },

    // Request size limiting
    requestSizeLimit: {
        enabled: process.env.SECURITY_REQUEST_SIZE_LIMIT_ENABLED !== 'false',
        maxSize: process.env.SECURITY_MAX_REQUEST_SIZE || '10mb'
    },

    // Security monitoring and logging
    monitoring: {
        enabled: process.env.SECURITY_MONITORING_ENABLED !== 'false' && (process.env.NODE_ENV === 'production' || process.env.SECURITY_MONITORING_ENABLED === 'true'),
        logLevel: process.env.SECURITY_LOG_LEVEL || 'info'
    }
};

// Check if a feature is enabled by dot notation path
const isFeatureEnabled = (featurePath) => {
    const path = featurePath.split('.');
    let config = securityConfig;
    
    for (const key of path) {
        if (config && typeof config === 'object' && key in config) {
            config = config[key];
        } else {
            return false;
        }
    }
    
    return config === true || config === 'true';
};

// Get configuration value by dot notation path
const getConfigValue = (featurePath, defaultValue = null) => {
    const path = featurePath.split('.');
    let config = securityConfig;
    
    for (const key of path) {
        if (config && typeof config === 'object' && key in config) {
            config = config[key];
        } else {
            return defaultValue;
        }
    }
    
    return config !== undefined ? config : defaultValue;
};

// Validate security configuration
const validateConfig = () => {
    const errors = [];
    
    // Check CAPTCHA keys if enabled
    if (securityConfig.antiBot.captcha.enabled) {
        if (securityConfig.antiBot.captcha.recaptcha.enabled && 
            (!securityConfig.antiBot.captcha.recaptcha.siteKey || !securityConfig.antiBot.captcha.recaptcha.secretKey)) {
            errors.push('reCAPTCHA keys required when enabled');
        }
        
        if (securityConfig.antiBot.captcha.hcaptcha.enabled && 
            (!securityConfig.antiBot.captcha.hcaptcha.siteKey || !securityConfig.antiBot.captcha.hcaptcha.secretKey)) {
            errors.push('hCaptcha keys required when enabled');
        }
    }
    
    return errors;
};

// Log security configuration status
const logConfigStatus = () => {
    const env = process.env.NODE_ENV || 'development';
    const logLevel = getConfigValue('monitoring.logLevel', 'info');
    
    // Only log configuration in development or debug mode
    if (logLevel === 'debug' || env === 'development') {
        console.log(`🔐 Security Configuration (${env}):`);
        console.log(`   Security: ${securityConfig.enabled ? 'ON' : 'OFF'}`);
        console.log(`   Rate Limiting: ${securityConfig.rateLimiting.enabled ? 'ON' : 'OFF'}`);
        console.log(`   Anti-Bot: ${securityConfig.antiBot.enabled ? 'ON' : 'OFF'}`);
        console.log(`   CAPTCHA: ${securityConfig.antiBot.captcha.enabled ? 'ON' : 'OFF'}`);
        console.log(`   reCAPTCHA: ${securityConfig.antiBot.captcha.recaptcha.enabled ? 'ON' : 'OFF'}`);
        console.log(`   hCAPTCHA: ${securityConfig.antiBot.captcha.hcaptcha.enabled ? 'ON' : 'OFF'}`);
        
        const errors = validateConfig();
        if (errors.length > 0) {
            console.warn('⚠️  Security config warnings:', errors.join(', '));
        }
    }
};

module.exports = {
    securityConfig,
    isFeatureEnabled,
    getConfigValue,
    validateConfig,
    logConfigStatus
}; 