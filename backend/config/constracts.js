const { ethers } = require("ethers");
const { sahelionAddress, sahelionABI, sahelionReserveAddress, sahelionReserveABI, lotteryABI, lotteryAddress, bnbPurchaseAddress, bnbPurchaseABI } = require("../constants/constants");
require("dotenv").config();
const environment = process.env.Environment

const urlRPC = environment === "Production" ? "https://bnb-mainnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT" : "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

const provider = new ethers.JsonRpcProvider(
    urlRPC
);

let sahelionContract;
let sahelionReserveContract;
let lotteryContract;
let bnbPurchaseContract;

sahelionContract = new ethers.Contract(
    sahelionAddress,
    sahelionABI,
    provider
);

sahelionReserveContract = new ethers.Contract(
    sahelionReserveAddress,
    sahelionReserveABI,
    provider
);

const signer = new ethers.Wallet(process.env.PRIVATE_KEY, provider);

lotteryContract = new ethers.Contract(
    lotteryAddress,
    lotteryABI,
    signer
)

bnbPurchaseContract = new ethers.Contract(
    bnbPurchaseAddress,
    bnbPurchaseABI,
    signer
)

module.exports = {
    sahelionContract,
    sahelionReserveContract,
    lotteryContract,
    rpcProvider: provider,
    bnbPurchaseContract
};