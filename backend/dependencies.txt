backend@1.0.0 /home/<USER>/blackpanther/backend
├─┬ @sendgrid/mail@8.1.4
│ ├─┬ @sendgrid/client@8.1.4
│ │ ├── @sendgrid/helpers@8.0.0 deduped
│ │ └─┬ axios@1.8.3
│ │   ├── follow-redirects@1.15.9
│ │   ├─┬ form-data@4.0.2
│ │   │ ├── asynckit@0.4.0
│ │   │ ├─┬ combined-stream@1.0.8
│ │   │ │ └── delayed-stream@1.0.0
│ │   │ ├─┬ es-set-tostringtag@2.1.0
│ │   │ │ ├── es-errors@1.3.0 deduped
│ │   │ │ ├── get-intrinsic@1.3.0 deduped
│ │   │ │ ├─┬ has-tostringtag@1.0.2
│ │   │ │ │ └── has-symbols@1.1.0 deduped
│ │   │ │ └─┬ hasown@2.0.2
│ │   │ │   └── function-bind@1.1.2 deduped
│ │   │ └── mime-types@2.1.35 deduped
│ │   └── proxy-from-env@1.1.0
│ └─┬ @sendgrid/helpers@8.0.0
│   └── deepmerge@4.3.1
├── bcryptjs@2.4.3
├─┬ cors@2.8.5
│ ├── object-assign@4.1.1
│ └── vary@1.1.2
├── dotenv@16.4.7
├─┬ express@4.21.2
│ ├─┬ accepts@1.3.8
│ │ ├─┬ mime-types@2.1.35
│ │ │ └── mime-db@1.52.0
│ │ └── negotiator@0.6.3
│ ├── array-flatten@1.1.1
│ ├─┬ body-parser@1.20.3
│ │ ├── bytes@3.1.2
│ │ ├── content-type@1.0.5 deduped
│ │ ├── debug@2.6.9 deduped
│ │ ├── depd@2.0.0 deduped
│ │ ├── destroy@1.2.0
│ │ ├── http-errors@2.0.0 deduped
│ │ ├─┬ iconv-lite@0.4.24
│ │ │ └── safer-buffer@2.1.2
│ │ ├── on-finished@2.4.1 deduped
│ │ ├── qs@6.13.0 deduped
│ │ ├─┬ raw-body@2.5.2
│ │ │ ├── bytes@3.1.2 deduped
│ │ │ ├── http-errors@2.0.0 deduped
│ │ │ ├── iconv-lite@0.4.24 deduped
│ │ │ └── unpipe@1.0.0 deduped
│ │ ├── type-is@1.6.18 deduped
│ │ └── unpipe@1.0.0
│ ├─┬ content-disposition@0.5.4
│ │ └── safe-buffer@5.2.1 deduped
│ ├── content-type@1.0.5
│ ├── cookie-signature@1.0.6
│ ├── cookie@0.7.1
│ ├─┬ debug@2.6.9
│ │ └── ms@2.0.0
│ ├── depd@2.0.0
│ ├── encodeurl@2.0.0
│ ├── escape-html@1.0.3
│ ├── etag@1.8.1
│ ├─┬ finalhandler@1.3.1
│ │ ├── debug@2.6.9 deduped
│ │ ├── encodeurl@2.0.0 deduped
│ │ ├── escape-html@1.0.3 deduped
│ │ ├── on-finished@2.4.1 deduped
│ │ ├── parseurl@1.3.3 deduped
│ │ ├── statuses@2.0.1 deduped
│ │ └── unpipe@1.0.0 deduped
│ ├── fresh@0.5.2
│ ├─┬ http-errors@2.0.0
│ │ ├── depd@2.0.0 deduped
│ │ ├── inherits@2.0.4
│ │ ├── setprototypeof@1.2.0 deduped
│ │ ├── statuses@2.0.1 deduped
│ │ └── toidentifier@1.0.1
│ ├── merge-descriptors@1.0.3
│ ├── methods@1.1.2
│ ├─┬ on-finished@2.4.1
│ │ └── ee-first@1.1.1
│ ├── parseurl@1.3.3
│ ├── path-to-regexp@0.1.12
│ ├─┬ proxy-addr@2.0.7
│ │ ├── forwarded@0.2.0
│ │ └── ipaddr.js@1.9.1
│ ├─┬ qs@6.13.0
│ │ └─┬ side-channel@1.1.0
│ │   ├── es-errors@1.3.0
│ │   ├── object-inspect@1.13.4
│ │   ├─┬ side-channel-list@1.0.0
│ │   │ ├── es-errors@1.3.0 deduped
│ │   │ └── object-inspect@1.13.4 deduped
│ │   ├─┬ side-channel-map@1.0.1
│ │   │ ├─┬ call-bound@1.0.4
│ │   │ │ ├─┬ call-bind-apply-helpers@1.0.2
│ │   │ │ │ ├── es-errors@1.3.0 deduped
│ │   │ │ │ └── function-bind@1.1.2 deduped
│ │   │ │ └── get-intrinsic@1.3.0 deduped
│ │   │ ├── es-errors@1.3.0 deduped
│ │   │ ├─┬ get-intrinsic@1.3.0
│ │   │ │ ├── call-bind-apply-helpers@1.0.2 deduped
│ │   │ │ ├── es-define-property@1.0.1
│ │   │ │ ├── es-errors@1.3.0 deduped
│ │   │ │ ├─┬ es-object-atoms@1.1.1
│ │   │ │ │ └── es-errors@1.3.0 deduped
│ │   │ │ ├── function-bind@1.1.2
│ │   │ │ ├─┬ get-proto@1.0.1
│ │   │ │ │ ├─┬ dunder-proto@1.0.1
│ │   │ │ │ │ ├── call-bind-apply-helpers@1.0.2 deduped
│ │   │ │ │ │ ├── es-errors@1.3.0 deduped
│ │   │ │ │ │ └── gopd@1.2.0 deduped
│ │   │ │ │ └── es-object-atoms@1.1.1 deduped
│ │   │ │ ├── gopd@1.2.0
│ │   │ │ ├── has-symbols@1.1.0
│ │   │ │ ├── hasown@2.0.2 deduped
│ │   │ │ └── math-intrinsics@1.1.0
│ │   │ └── object-inspect@1.13.4 deduped
│ │   └─┬ side-channel-weakmap@1.0.2
│ │     ├── call-bound@1.0.4 deduped
│ │     ├── es-errors@1.3.0 deduped
│ │     ├── get-intrinsic@1.3.0 deduped
│ │     ├── object-inspect@1.13.4 deduped
│ │     └── side-channel-map@1.0.1 deduped
│ ├── range-parser@1.2.1
│ ├── safe-buffer@5.2.1
│ ├─┬ send@0.19.0
│ │ ├── debug@2.6.9 deduped
│ │ ├── depd@2.0.0 deduped
│ │ ├── destroy@1.2.0 deduped
│ │ ├── encodeurl@1.0.2
│ │ ├── escape-html@1.0.3 deduped
│ │ ├── etag@1.8.1 deduped
│ │ ├── fresh@0.5.2 deduped
│ │ ├── http-errors@2.0.0 deduped
│ │ ├── mime@1.6.0
│ │ ├── ms@2.1.3
│ │ ├── on-finished@2.4.1 deduped
│ │ ├── range-parser@1.2.1 deduped
│ │ └── statuses@2.0.1 deduped
│ ├─┬ serve-static@1.16.2
│ │ ├── encodeurl@2.0.0 deduped
│ │ ├── escape-html@1.0.3 deduped
│ │ ├── parseurl@1.3.3 deduped
│ │ └── send@0.19.0 deduped
│ ├── setprototypeof@1.2.0
│ ├── statuses@2.0.1
│ ├─┬ type-is@1.6.18
│ │ ├── media-typer@0.3.0
│ │ └── mime-types@2.1.35 deduped
│ ├── utils-merge@1.0.1
│ └── vary@1.1.2 deduped
├─┬ jsonwebtoken@9.0.2
│ ├─┬ jws@3.2.2
│ │ ├─┬ jwa@1.4.1
│ │ │ ├── buffer-equal-constant-time@1.0.1
│ │ │ ├─┬ ecdsa-sig-formatter@1.0.11
│ │ │ │ └── safe-buffer@5.2.1 deduped
│ │ │ └── safe-buffer@5.2.1 deduped
│ │ └── safe-buffer@5.2.1 deduped
│ ├── lodash.includes@4.3.0
│ ├── lodash.isboolean@3.0.3
│ ├── lodash.isinteger@4.0.4
│ ├── lodash.isnumber@3.0.3
│ ├── lodash.isplainobject@4.0.6
│ ├── lodash.isstring@4.0.1
│ ├── lodash.once@4.1.1
│ ├── ms@2.1.3
│ └── semver@7.6.2
├─┬ mongoose@8.13.2
│ ├── bson@6.10.3
│ ├── kareem@2.6.3
│ ├─┬ mongodb@6.15.0
│ │ ├── UNMET OPTIONAL DEPENDENCY @aws-sdk/credential-providers@^3.188.0
│ │ ├─┬ @mongodb-js/saslprep@1.2.2
│ │ │ └─┬ sparse-bitfield@3.0.3
│ │ │   └── memory-pager@1.5.0
│ │ ├── UNMET OPTIONAL DEPENDENCY @mongodb-js/zstd@^1.1.0 || ^2.0.0
│ │ ├── bson@6.10.3 deduped
│ │ ├── UNMET OPTIONAL DEPENDENCY gcp-metadata@^5.2.0
│ │ ├── UNMET OPTIONAL DEPENDENCY kerberos@^2.0.1
│ │ ├── UNMET OPTIONAL DEPENDENCY mongodb-client-encryption@>=6.0.0 <7
│ │ ├─┬ mongodb-connection-string-url@3.0.2
│ │ │ ├─┬ @types/whatwg-url@11.0.5
│ │ │ │ └── @types/webidl-conversions@7.0.3
│ │ │ └─┬ whatwg-url@14.2.0
│ │ │   ├─┬ tr46@5.1.0
│ │ │   │ └── punycode@2.3.1
│ │ │   └── webidl-conversions@7.0.0
│ │ ├── UNMET OPTIONAL DEPENDENCY snappy@^7.2.2
│ │ └── UNMET OPTIONAL DEPENDENCY socks@^2.7.1
│ ├── mpath@0.9.0
│ ├─┬ mquery@5.0.0
│ │ └─┬ debug@4.3.5
│ │   └── ms@2.1.2
│ ├── ms@2.1.3
│ └── sift@17.1.3
├── nanoid@3.3.9
├─┬ nodemon@3.1.4
│ ├─┬ chokidar@3.6.0
│ │ ├─┬ anymatch@3.1.3
│ │ │ ├── normalize-path@3.0.0 deduped
│ │ │ └── picomatch@2.3.1
│ │ ├─┬ braces@3.0.3
│ │ │ └─┬ fill-range@7.1.1
│ │ │   └─┬ to-regex-range@5.0.1
│ │ │     └── is-number@7.0.0
│ │ ├── UNMET OPTIONAL DEPENDENCY fsevents@~2.3.2
│ │ ├─┬ glob-parent@5.1.2
│ │ │ └── is-glob@4.0.3 deduped
│ │ ├─┬ is-binary-path@2.1.0
│ │ │ └── binary-extensions@2.3.0
│ │ ├─┬ is-glob@4.0.3
│ │ │ └── is-extglob@2.1.1
│ │ ├── normalize-path@3.0.0
│ │ └─┬ readdirp@3.6.0
│ │   └── picomatch@2.3.1 deduped
│ ├─┬ debug@4.3.5
│ │ └── ms@2.1.2
│ ├── ignore-by-default@1.0.1
│ ├─┬ minimatch@3.1.2
│ │ └─┬ brace-expansion@1.1.11
│ │   ├── balanced-match@1.0.2
│ │   └── concat-map@0.0.1
│ ├── pstree.remy@1.1.8
│ ├── semver@7.6.2 deduped
│ ├─┬ simple-update-notifier@2.0.0
│ │ └── semver@7.6.2 deduped
│ ├─┬ supports-color@5.5.0
│ │ └── has-flag@3.0.0
│ ├── touch@3.1.1
│ └── undefsafe@2.0.5
└── validator@13.12.0

