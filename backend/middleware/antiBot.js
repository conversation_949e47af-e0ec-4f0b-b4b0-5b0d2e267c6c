const hcaptcha = require('hcaptcha');
const axios = require('axios');
const { isFeatureEnabled, getConfigValue } = require('../config/securityConfig');

// hCaptcha configuration
const hcaptchaConfig = isFeatureEnabled('antiBot.captcha.hcaptcha.enabled') ? {
    secret: getConfigValue('antiBot.captcha.hcaptcha.secretKey'),
    sitekey: getConfigValue('antiBot.captcha.hcaptcha.siteKey')
} : null;

// reCAPTCHA v3 verification via Google API
const verifyRecaptchaV3 = async (token, expectedAction = 'submit') => {
    try {
        const secretKey = getConfigValue('antiBot.captcha.recaptcha.secretKey');
        const minScore = getConfigValue('antiBot.captcha.recaptcha.minScore') || 0.5;
        
        const response = await axios.post('https://www.google.com/recaptcha/api/siteverify', null, {
            params: {
                secret: secretKey,
                response: token,
                remoteip: undefined
            }
        });

        const result = response.data;

        if (!result.success) {
            if (process.env.NODE_ENV === 'development') {
                console.warn('[SECURITY] reCAPTCHA verification failed:', result['error-codes']);
            }
            return { success: false, score: 0, action: null };
        }

        // Check score threshold
        if (result.score < minScore) {
            return { success: false, score: result.score, action: result.action };
        }

        return { success: true, score: result.score, action: result.action };
    } catch (error) {
        console.error('[SECURITY] reCAPTCHA verification error:', error.message);
        return { success: false, score: 0, action: null };
    }
};

// Behavioral analysis for bot detection
const suspiciousBehaviors = {
    // Detect rapid successive requests from same IP
    rapidRequests: (req, res, next) => {
        const now = Date.now();
        const clientKey = req.ip;
        
        if (!req.app.locals.requestTimestamps) {
            req.app.locals.requestTimestamps = new Map();
        }
        
        const timestamps = req.app.locals.requestTimestamps.get(clientKey) || [];
        const recentRequests = timestamps.filter(time => now - time < 1000);
        
        if (recentRequests.length > 10) {
            console.warn('[SECURITY] Rapid requests detected from IP:', req.ip);
            return res.status(429).json({
                error: 'Suspicious behavior detected',
                message: 'Too many rapid requests',
                code: 'RAPID_REQUESTS_DETECTED'
            });
        }
        
        timestamps.push(now);
        if (timestamps.length > 100) {
            timestamps.splice(0, timestamps.length - 100);
        }
        
        req.app.locals.requestTimestamps.set(clientKey, timestamps);
        next();
    },

    // Analyze request headers for bot patterns
    headerAnalysis: (req, res, next) => {
        const requiredHeaders = ['user-agent', 'accept'];
        const suspiciousHeaders = {
            'user-agent': [
                /bot/i, /crawler/i, /spider/i, /scraper/i, /curl/i, /wget/i,
                /python/i, /java/i, /perl/i, /ruby/i, /go-http-client/i,
                /httpclient/i, /okhttp/i, /axios/i, /fetch/i, /postman/i, /insomnia/i
            ]
        };

        for (const header of requiredHeaders) {
            if (!req.headers[header]) {
                return res.status(400).json({
                    error: 'Missing required header',
                    message: `${header} header is required`,
                    code: 'MISSING_HEADER'
                });
            }
        }

        const userAgent = req.headers['user-agent'] || '';
        for (const pattern of suspiciousHeaders['user-agent']) {
            if (pattern.test(userAgent)) {
                console.warn('[SECURITY] Suspicious user agent detected:', userAgent.substring(0, 50));
                return res.status(403).json({
                    error: 'Suspicious user agent detected',
                    message: 'Automated access is not allowed',
                    code: 'SUSPICIOUS_USER_AGENT'
                });
            }
        }

        next();
    },

    // Analyze request patterns for bot behavior
    patternAnalysis: (req, res, next) => {
        const clientKey = req.ip;
        const now = Date.now();
        
        if (!req.app.locals.requestPatterns) {
            req.app.locals.requestPatterns = new Map();
        }
        
        const patterns = req.app.locals.requestPatterns.get(clientKey) || {
            requests: [],
            suspiciousCount: 0
        };

        patterns.requests.push({
            timestamp: now,
            method: req.method,
            path: req.path,
            userAgent: req.headers['user-agent']
        });

        if (patterns.requests.length > 50) {
            patterns.requests.splice(0, patterns.requests.length - 50);
        }

        const recentRequests = patterns.requests.filter(req => now - req.timestamp < 60000);
        
        // Check for identical requests (potential bot)
        const identicalRequests = recentRequests.filter(r => 
            r.method === req.method && 
            r.path === req.path &&
            r.userAgent === req.headers['user-agent']
        );

        if (identicalRequests.length > 5) {
            patterns.suspiciousCount++;
        }

        if (recentRequests.length > 30) {
            patterns.suspiciousCount++;
        }

        if (patterns.suspiciousCount > 3) {
            console.warn('[SECURITY] Suspicious pattern detected from IP:', req.ip);
            return res.status(403).json({
                error: 'Suspicious request pattern detected',
                message: 'Access temporarily blocked due to suspicious behavior',
                code: 'SUSPICIOUS_PATTERN_DETECTED'
            });
        }

        req.app.locals.requestPatterns.set(clientKey, patterns);
        next();
    }
};

// CAPTCHA verification middleware
const verifyCaptcha = (captchaType = 'recaptcha', expectedAction = 'submit') => {
    return async (req, res, next) => {
        if (!isFeatureEnabled('antiBot.captcha.enabled')) {
            return next();
        }

        try {
            const token = (req.body && req.body.captchaToken) || req.headers['x-captcha-token'];
            const action = (req.body && req.body.captchaAction) || req.headers['x-captcha-action'] || expectedAction;
            
            if (!token) {
                return res.status(400).json({
                    error: 'CAPTCHA token required',
                    message: 'Please complete the CAPTCHA verification',
                    code: 'CAPTCHA_TOKEN_MISSING'
                });
            }

            let isValid = false;
            let verificationResult = null;

            if (captchaType === 'recaptcha' && isFeatureEnabled('antiBot.captcha.recaptcha.enabled')) {
                verificationResult = await verifyRecaptchaV3(token, action);
                isValid = verificationResult.success;
            } else if (captchaType === 'hcaptcha' && isFeatureEnabled('antiBot.captcha.hcaptcha.enabled')) {
                const response = await hcaptcha.verify(hcaptchaConfig.secret, token);
                isValid = response.success;
            }

            if (!isValid) {
                const errorMessage = verificationResult && verificationResult.score !== undefined 
                    ? `CAPTCHA verification failed. Score: ${verificationResult.score}`
                    : 'CAPTCHA verification failed';
                
                console.warn('[SECURITY] CAPTCHA verification failed for IP:', req.ip);
                
                return res.status(400).json({
                    error: 'Invalid CAPTCHA',
                    message: errorMessage,
                    code: 'CAPTCHA_VERIFICATION_FAILED',
                    ...(verificationResult && verificationResult.score !== undefined ? { score: verificationResult.score } : {})
                });
            }

            req.captchaVerification = verificationResult;
            next();
        } catch (error) {
            console.error('[SECURITY] CAPTCHA verification error:', error.message);
            return res.status(500).json({
                error: 'CAPTCHA verification error',
                message: 'Failed to verify CAPTCHA',
                code: 'CAPTCHA_VERIFICATION_ERROR'
            });
        }
    };
};

// Main anti-bot middleware factory
const antiBotMiddleware = (options = {}) => {
    if (!isFeatureEnabled('antiBot.enabled')) {
        return [(req, res, next) => next()];
    }

    const {
        enableCaptcha = isFeatureEnabled('antiBot.captcha.enabled'),
        captchaType = getConfigValue('captchaType'),
        captchaAction = 'submit',
        enableBehavioralAnalysis = isFeatureEnabled('antiBot.botDetection.enabled'),
        enableHeaderAnalysis = isFeatureEnabled('antiBot.botDetection.headerAnalysis'),
        enablePatternAnalysis = isFeatureEnabled('antiBot.botDetection.patternAnalysis'),
        enableRapidRequestDetection = isFeatureEnabled('antiBot.botDetection.rapidRequestDetection')
    } = options;

    return [
        ...(enableHeaderAnalysis ? [suspiciousBehaviors.headerAnalysis] : []),
        ...(enableRapidRequestDetection ? [suspiciousBehaviors.rapidRequests] : []),
        ...(enablePatternAnalysis ? [suspiciousBehaviors.patternAnalysis] : []),
        ...(enableCaptcha ? [verifyCaptcha(captchaType, captchaAction)] : [])
    ];
};

// Pre-configured anti-bot settings for different endpoint types
const antiBotConfigs = {
    auth: {
        enableCaptcha: true,
        captchaType: getConfigValue('captchaType'),
        captchaAction: 'login',
        enableBehavioralAnalysis: true,
        enableHeaderAnalysis: true,
        enablePatternAnalysis: true,
        enableRapidRequestDetection: true
    },
    payment: {
        enableCaptcha: true,
        captchaType: getConfigValue('captchaType'),
        captchaAction: 'payment',
        enableBehavioralAnalysis: true,
        enableHeaderAnalysis: true,
        enablePatternAnalysis: true,
        enableRapidRequestDetection: true
    },
    api: {
        enableCaptcha: false,
        captchaAction: 'submit',
        enableBehavioralAnalysis: true,
        enableHeaderAnalysis: true,
        enablePatternAnalysis: false,
        enableRapidRequestDetection: true
    },
    webhook: {
        enableCaptcha: false,
        captchaAction: 'webhook',
        enableBehavioralAnalysis: false,
        enableHeaderAnalysis: false,
        enablePatternAnalysis: false,
        enableRapidRequestDetection: false
    }
};

// Get anti-bot middleware for specific endpoint type
const getAntiBotMiddleware = (endpointType) => {
    const config = antiBotConfigs[endpointType] || antiBotConfigs.api;
    return antiBotMiddleware(config);
};

module.exports = {
    antiBotMiddleware,
    getAntiBotMiddleware,
    verifyCaptcha,
    suspiciousBehaviors,
    antiBotConfigs
}; 