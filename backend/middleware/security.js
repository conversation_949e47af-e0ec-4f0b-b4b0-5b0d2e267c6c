const { rateLimiters, speedLimiter, applyAdvancedRateLimit, botDetection, requestFingerprint } = require('../config/rateLimit');
const { getAntiBotMiddleware } = require('./antiBot');
const { isFeatureEnabled, getConfigValue } = require('../config/securityConfig');

// Security middleware configurations for different protection levels
const securityMiddleware = {
    // Basic protection for general routes
    basic: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...(isFeatureEnabled('rateLimiting.speedLimit.enabled') ? [speedLimiter] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.api] : [])
    ],

    // Enhanced protection for authentication routes
    auth: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('auth'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [applyAdvancedRateLimit('bruteForce')] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.auth] : [])
    ],

    // High security for payment routes
    payment: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('payment'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [applyAdvancedRateLimit('paymentAttempts')] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.payment] : [])
    ],

    // Maximum security for crypto operations
    crypto: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('payment'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [applyAdvancedRateLimit('paymentAttempts')] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.cryptoVerification] : [])
    ],

    // Email-specific protection
    email: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('auth'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [applyAdvancedRateLimit('emailVerification')] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.email] : [])
    ],

    // File upload protection
    fileUpload: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('api'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.fileUpload] : [])
    ],

    // Minimal protection for webhooks
    webhook: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : [])
    ],

    // Maximum protection for admin routes
    admin: [
        ...(isFeatureEnabled('fingerprinting.enabled') ? [requestFingerprint] : []),
        ...(isFeatureEnabled('antiBot.botDetection.enabled') ? [botDetection] : []),
        ...getAntiBotMiddleware('auth'),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [applyAdvancedRateLimit('userActions')] : []),
        ...(isFeatureEnabled('rateLimiting.enabled') ? [rateLimiters.api] : [])
    ]
};

// Create security middleware array for given protection level
const createSecurityMiddleware = (securityLevel) => {
    return securityMiddleware[securityLevel] || securityMiddleware.basic;
};

// Route-to-security level mapping
const routeSecurityConfig = {
    // Authentication endpoints
    '/api/user/signup': 'auth',
    '/api/user/signin': 'auth',
    '/api/user/send-signup-code': 'email',
    '/api/user/send-signin-code': 'email',
    '/api/user/verify-code': 'auth',
    '/api/user/resend-code': 'email',
    '/api/user/check-password': 'auth',
    '/api/user/refresh-token': 'auth',
    '/api/user/change-password': 'auth',
    // Payment endpoints
    '/api/contribution/generate-intent': 'payment',
    // '/api/contribution/activate': 'payment',
    // '/api/contribution/update': 'payment',
    '/api/contribution/cancel': 'payment',
    '/api/contribution/verify-crypto': 'crypto',
    // File upload endpoints
    '/api/upload': 'fileUpload',
    // Webhook endpoints
    '/api/contribution/webhook': 'webhook',
    // General API endpoints
    '/api/user/profile': 'basic',
    '/api/user/update': 'basic',
    '/api/contribution/status': 'basic',
    '/api/contribution/history': 'basic'
};

// Dynamic security middleware that applies appropriate protection based on route
const dynamicSecurityMiddleware = (req, res, next) => {
    const route = req.path;
    const securityLevel = routeSecurityConfig[route] || 'basic';
    const middleware = createSecurityMiddleware(securityLevel);
    
    let index = 0;
    
    const applyNext = () => {
        if (index >= middleware.length) {
            return next();
        }
        
        const currentMiddleware = middleware[index];
        index++;
        
        try {
            currentMiddleware(req, res, applyNext);
        } catch (error) {
            console.error('[SECURITY] Middleware error:', error.message);
            return res.status(500).json({
                error: 'Security middleware error',
                message: 'An error occurred while processing security checks',
                code: 'SECURITY_MIDDLEWARE_ERROR'
            });
        }
    };
    
    applyNext();
};

// Security event monitoring and logging
const securityMonitoring = (req, res, next) => {
    if (!isFeatureEnabled('monitoring.enabled')) {
        return next();
    }

    const logSecurityEvent = (event, details) => {
        const logLevel = getConfigValue('monitoring.logLevel', 'info');
        const logData = {
            timestamp: new Date().toISOString(),
            ip: req.ip,
            path: req.path,
            method: req.method,
            userId: req.userEmailAndId?.userId,
            ...details
        };

        // Log important security events
        if (logLevel === 'debug' || process.env.NODE_ENV === 'development') {
            console.log(`[SECURITY] ${event}:`, logData);
        } else if (logLevel === 'info' && (event === 'RATE_LIMIT_VIOLATION' || event === 'BOT_DETECTED')) {
            console.log(`[SECURITY] ${event}:`, {
                ip: logData.ip,
                path: logData.path,
                method: logData.method,
                userId: logData.userId
            });
        }
    };

    // Override response to capture security events
    const originalJson = res.json;
    res.json = function(data) {
        // Log rate limit violations
        if (res.statusCode === 429) {
            logSecurityEvent('RATE_LIMIT_VIOLATION', {
                statusCode: res.statusCode,
                response: data
            });
        }
        
        // Log bot detection events
        if (res.statusCode === 403 && data.code?.includes('BOT')) {
            logSecurityEvent('BOT_DETECTED', {
                statusCode: res.statusCode,
                response: data
            });
        }
        
        // Log suspicious behavior
        if (res.statusCode === 403 && data.code?.includes('SUSPICIOUS')) {
            logSecurityEvent('SUSPICIOUS_BEHAVIOR', {
                statusCode: res.statusCode,
                response: data
            });
        }
        
        return originalJson.call(this, data);
    };

    next();
};

// Request size limiting middleware
const requestSizeLimit = (req, res, next) => {
    if (!isFeatureEnabled('requestSizeLimit.enabled')) {
        return next();
    }

    // Safety check for request headers
    if (!req || !req.headers) {
        return next();
    }

    const maxSize = getConfigValue('requestSizeLimit.maxSize', '10mb');
    const sizeInBytes = parseInt(maxSize.replace(/[^\d]/g, '')) * (maxSize.includes('mb') ? 1024 * 1024 : 1024);
    
    if (req.headers['content-length'] && parseInt(req.headers['content-length']) > sizeInBytes) {
        console.warn('[SECURITY] Request size limit exceeded:', {
            ip: req.ip,
            size: req.headers['content-length'],
            limit: maxSize
        });
        
        return res.status(413).json({
            error: 'Request size too large',
            message: `Request size exceeds ${maxSize} limit`,
            code: 'REQUEST_SIZE_LIMIT_EXCEEDED'
        });
    }

    next();
};

// IP whitelist middleware factory
const ipWhitelist = (allowedIPs = []) => {
    return (req, res, next) => {
        if (!isFeatureEnabled('ipWhitelist.enabled')) {
            return next();
        }

        const clientIP = req.ip;
        const configuredIPs = allowedIPs.length > 0 ? allowedIPs : getConfigValue('ipWhitelist.allowedIPs', []);
        
        if (configuredIPs.length > 0 && !configuredIPs.includes(clientIP)) {
            console.warn('[SECURITY] IP not whitelisted:', clientIP);
            return res.status(403).json({
                error: 'IP not whitelisted',
                message: 'Access denied from this IP address',
                code: 'IP_NOT_WHITELISTED'
            });
        }
        
        next();
    };
};

module.exports = {
    securityMiddleware,
    createSecurityMiddleware,
    dynamicSecurityMiddleware,
    securityMonitoring,
    ipWhitelist,
    requestSizeLimit,
    routeSecurityConfig
}; 