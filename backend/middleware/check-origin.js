require("dotenv").config();

// Configuration: allowed domains and webhook paths
const allowedDomains = process.env['ALLOWED_DOMAINS'] ? process.env['ALLOWED_DOMAINS'].split(',') : [];
const allowedWebhookPaths = [
    '/api/contribution/webhook',
    "/api/payments/stkcallback"
];

// Origin validation middleware
const checkOrigin = (req, res, next) => {
    // Skip validation for webhook endpoints
    if (allowedWebhookPaths.includes(req.path)) {
        return next();
    }

    const origin = req.headers.origin;
    const referer = req.headers.referer;

    // Extract domain from URL string
    const getDomain = (url) => {
        if (!url) return null;
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch (e) {
            return null;
        }
    };

    const requestDomain = getDomain(origin) || getDomain(referer);

    console.log("Request From ",requestDomain, "| ", req.method)
    // Allow requests from configured domains
    if (requestDomain && allowedDomains.includes(requestDomain)) {
        return next();
    }

    // Allow localhost in development
    if (process.env.Environment === 'dev' && 
        (requestDomain === 'localhost' || requestDomain === '127.0.0.1')) {
        return next();
    }

    // Allow requests with no origin in development/test
    if ((process.env.Environment === 'dev' || process.env.Environment === 'test') && !requestDomain) {
        console.log("Allowing request with no origin in development mode");

        return next();
    }

    // Log blocked origin attempts
    console.warn('[SECURITY] Origin blocked:', {
        requestDomain,
        origin,
        referer: referer?.substring(0, 50), // Truncate for safety
        path: req.path
    });

    return res.status(403).json({
        error: 'Forbidden',
        message: 'Access denied: Invalid origin',
        code: 'INVALID_ORIGIN'
    });
};

module.exports = checkOrigin;
