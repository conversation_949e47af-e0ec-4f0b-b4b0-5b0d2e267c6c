const multer = require("multer");
require("dotenv").config();
const crypto = require("crypto");
const path = require("path");
const fs = require("fs");
const AWS = require("aws-sdk");

const parentDirectory = path.resolve(__dirname, "..");
const localDirectory = path.join(parentDirectory, "public/uploads");

if (!fs.existsSync(localDirectory)) {
  try {
    fs.mkdirSync(localDirectory, { recursive: true });
  } catch (error) {
    console.error("Error creating directory:", error);
  }
}

let upload;
let uploadToS3;

if (process.env.Environment === "dev") {
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, localDirectory);
    },
    filename: function (req, file, cb) {
      cb(null, crypto.randomUUID() + "" + Date.now() + "" + file.originalname);
    },
  });
  upload = multer({ storage: storage });
} else {
  const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  const storage = multer.memoryStorage();
  upload = multer({ storage: storage });

  uploadToS3 = (file) => {
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: crypto.randomUUID() + Date.now() + path.extname(file.originalname),
      Body: file.buffer,
      ContentType: file.mimetype,
      // ACL: 'public-read'
    };
    return s3.upload(params).promise();
  };
}

const setFileUrl = (req, res, next) => {
  const uploadHandler = upload.array("uploaded_file", 10);

  uploadHandler(req, res, async (err) => {
    if (err) {
      return res.status(500).json({ message: err.message });
    }
    if (process.env.Environment === "dev") {
      if (req.files && req.files.length > 0) {
        req.body.fileUrl = req.files.map((file) => ({
          url: `${req.protocol}://${req.get("host")}/uploads/${file.filename}`,
          originalname: file.originalname,
          size: file.size,
          fieldname: file.fieldname, // <-- Add this line
        }));
      } else {
        req.body.fileUrl = [];
      }
    } else {
      if (req.files && req.files.length > 0) {
        try {
          const uploadPromises = req.files.map((file) => uploadToS3(file));
          const uploadResults = await Promise.all(uploadPromises);

          req.body.fileUrl = uploadResults.map((result, index) => ({
            url: result.Location,
            originalname: req.files[index].originalname,
            size: req.files[index].size,
            fieldname: req.files[index].fieldname, // <-- Add this line
          }));
        } catch (uploadError) {
          return res.status(500).json({ message: uploadError.message });
        }
      } else {
        req.body.fileUrl = [];
      }
    }
    next();
  });
};

module.exports = setFileUrl;
