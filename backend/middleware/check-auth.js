const jwt = require('jsonwebtoken');

module.exports = (req, res, next) => {
    try {
        const token = req.headers.authorization.split(" ")[1];
        const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
        req.userEmailAndId = { email: decodedToken.email, userId: decodedToken.userId};
        req.userRole = decodedToken.role;
        next();
    } catch (error) {
        res.status(401).json({ message: "Not authorize" });
    }
}
