import fs from 'fs';
import path from 'path';

// Route paths
const AppRoutesPaths = {
  LANDING: '/',
  ABOUT: '/about',
  PRESALE_AUTH: '/presale-auth',
  LOTTERY: '/lottery',
  LOTTERY_ACTIVITY: '/lottery-activity',
  GAME: '/game',
  DAO: '/dao',
  PRESALE_OLD: '/presale_old',
  MYPOINTS: '/mypoints',
  FAILED_PAYMENT: '/failed_payment',
  SAHELION: '/sahelion',
  VERIFY_EMAIL: '/verifyemail',
  FORGOT_PASSWORD: '/forgot-password',
  CHANGE_PASSWORD: '/changepassword',
  LOGIN: '/login',
  SIGNUP: '/signup',
  PRESALE: '/pre-sale',
  RESET_PASSWORD: '/resetpassword',
  DAO_TRANSACTIONS: '/daotransactions',
  PRIVACY_POLICY: '/privacy-policy'
};

const BASE_URL = 'https://www.blackpanthertkn.com';
const timestamp = new Date().toISOString();

const urls = Object.values(AppRoutesPaths).map(route => `
  <url>
    <loc>${BASE_URL}${route}</loc>
    <lastmod>${timestamp}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('\n');

// Full XML
const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset 
  xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 
                      http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${urls}
</urlset>`;

// Save to public/sitemap.xml
const outputPath = path.join(process.cwd(), 'public', 'sitemap.xml');
fs.writeFileSync(outputPath, sitemap.trim());

console.log('✅ sitemap.xml has been generated at:', outputPath);
