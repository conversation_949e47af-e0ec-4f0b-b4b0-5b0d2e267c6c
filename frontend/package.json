{"name": "panther-frontend", "homepage": "blackpanthertkn.com", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@ant-design/icons": "^5.6.1", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@google/generative-ai": "^0.13.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.65.1", "@walletconnect/web3wallet": "^1.10.2", "@web3modal/ethereum": "^2.7.1", "@web3modal/wagmi": "^4.0.8", "antd": "^5.24.3", "aws-amplify": "^6.14.2", "axios": "^1.7.2", "chart.js": "^4.4.3", "chartjs-plugin-datalabels": "^2.2.0", "constate": "^3.3.2", "countries-list": "^3.1.1", "dotenv": "^16.4.5", "ethers": "^5.7.0", "framer-motion": "^11.18.2", "lucide-react": "^0.483.0", "parallax-js": "^3.1.0", "pdfjs-dist": "^4.8.69", "prop-types": "^15.8.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-google-recaptcha-v3": "^1.11.0", "react-icons": "^5.2.1", "react-idle-timer": "^5.7.2", "react-pdf": "^9.2.1", "react-qr-code": "^2.0.15", "react-responsive": "^10.0.0", "react-router-dom": "^6.29.0", "react-share-kit": "^1.1.0", "react-slick": "^0.30.2", "react-toastify": "^10.0.5", "react-transition-group": "^4.4.5", "recharts": "^2.15.1", "slick-carousel": "^1.8.1", "sweetalert2": "^11.19.1", "sweetalert2-react-content": "^5.1.0", "viem": "^2.22.17", "wagmi": "^2.14.9", "web3modal": "^1.9.9"}, "devDependencies": {"@aws-amplify/backend": "^1.15.0", "@aws-amplify/backend-cli": "^1.5.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "aws-cdk": "^2.1003.0", "aws-cdk-lib": "^2.180.0", "constructs": "^10.4.2", "esbuild": "^0.25.2", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jest": "^29.7.0", "postcss": "^8.4.38", "prettier": "^3.3.0", "tailwindcss": "^3.4.3", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^5.2.0"}}