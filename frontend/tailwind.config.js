/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{js,jsx,ts,tsx}", "./public/index.html"],
  theme: {
    extend: {
      backgroundImage: {
        'bg2': 'url("/src/assets/images/bg2.jpg")'
      },
      colors: {
        "BP-black": "#111111",
        "BP-purple": "#571C86",
        "BP-hovered-purple": "#6A2D9B",
        "BP-light-purple": "#E5D8F0",
        "BP-gold": "#E1A80D",
        "BP-lightbaige": "#F8F5ED",
        "BP-opacited-white": "rgba(255, 255, 255, 0.8)", // White with 80% opacity
        "BP-nav-gray": "#7A7A7A", // 50% opacity (hex with alpha)
        "BP-gray-20-opc": "#42424033", // 20% opacity (hex with alpha)
        "BP-gray-50-opc": "#42424080", // 50% opacity (hex with alpha)
        "BP-gray-100-opc": "#424240", // 100% opacity
        "BP-gold-gradient-start": "#F9F2A3",
        "BP-gold-gradient-end": "#EBAA37",
        "BP-dark-grayish-blue": "#111828",
        "BP-gold-gradient-end": "#EBAA37",
        "BP-yellow" : "#E1AB0D",
        "BP-hovered-yellow": "#F5C21A",
        "BP-flatstate-gray": "#D1D5DB",
        "BP-hovered-gray": "#9CA3AF",
        "BP-blue": "#2C5282",
      },
      fontFamily: {
        title: ['Karla', 'sans-serif'],
        body: ['Poppins', 'sans-serif'],
      },
      keyframes: {
        slideInLeft: {
          '0%': { opacity: '0', transform: 'translateX(-50px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        slideInRight: {
          '0%': { opacity: '0', transform: 'translateX(50px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        rotate: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        bounceshort: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        pulseradiation: {
          '0%, 100%': { 
            transform: 'scale(1)',
            boxShadow: '0 0 0 0 rgba(242, 183, 5, 0.7)'
          },
          '50%': { 
            transform: 'scale(1.5)',
            boxShadow: '0 0 0 20px rgba(242, 183, 5, 0)'
          }
        },
        pulseglow: {
          '0%, 100%': { 
            transform: 'scale(1)',
            boxShadow: '0 0 0 0 rgba(242, 183, 5, 0.4)'
          },
          '50%': { 
            transform: 'scale(1.2)',
            boxShadow: '0 0 0 10px rgba(242, 183, 5, 0)'
          }
        },
      },
      animation: {
        slideInLeft: 'slideInLeft 0.7s ease-out',
        slideInRight: 'slideInRight 0.7s ease-out',
        rotate: 'rotate 2s linear infinite',
        bounceshort: 'bounce-short 0.5s ease-in-out',
        pulseradiation: 'pulseradiation 1.5s infinite',
        pulseglow: 'pulseglow 2s infinite',
  
      },
    },
  },
  variants: {
    extend: {
      screens: {
        md: '768px',
      },
    },
  },
  plugins: [
    function({ addUtilities }) {
      const newUtilities = {
        '.scrollbar-hide': {
          /* Firefox */
          'scrollbar-width': 'none',
          /* Chrome, Safari and Opera */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      }
      addUtilities(newUtilities);
    }
  ],
};
