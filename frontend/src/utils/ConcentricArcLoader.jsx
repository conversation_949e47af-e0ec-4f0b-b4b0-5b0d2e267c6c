import React from "react";

const ConcentricArcLoader = ({ size = 40, colors = ["#571C86", "rgba(255, 255, 255, 0.8)", "#E1A80D"] }) => {
  const strokeWidth = size * 0.08;
  const baseRadius = size / 2 - strokeWidth / 2;

  // ⬅️ Increased gap multiplier from 1.2 → 1.8 for better spacing
  const gap = strokeWidth * 1.8;

  const radii = [
    baseRadius,
    baseRadius - gap,
    baseRadius - 2 * gap,
  ];

  const durations = ["1.2s", "1.5s", "1.8s"];

  const describeArc = (x, y, radius, startAngle, endAngle) => {
    const start = polarToCartesian(x, y, radius, endAngle);
    const end = polarToCartesian(x, y, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return [
      "M", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
    ].join(" ");
  };

  const polarToCartesian = (centerX, centerY, radius, angleInDegrees) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
    return {
      x: centerX + radius * Math.cos(angleInRadians),
      y: centerY + radius * Math.sin(angleInRadians),
    };
  };

  return (
    <div
      className="relative flex items-center justify-center"
      style={{ width: size, height: size }}
    >
      <style>
        {`
          @keyframes spinReverse {
            from { transform: rotate(360deg); }
            to { transform: rotate(0deg); }
          }
        `}
      </style>

      {radii.map((r, index) => {
        const isReverse = index % 2 === 1;
        return (
          <svg
            key={index}
            className="absolute"
            style={{
              width: size,
              height: size,
              animation: `${isReverse ? "spinReverse" : "spin"} ${durations[index]} linear infinite`,
            }}
            viewBox="0 0 100 100"
          >
            <path
              d={describeArc(50, 50, (r / size) * 100, 0, 180)}
              stroke={colors[index]}
              strokeWidth={strokeWidth}
              fill="none"
              strokeLinecap="round"
            />
          </svg>
        );
      })}
    </div>
  );
};

export default ConcentricArcLoader;
