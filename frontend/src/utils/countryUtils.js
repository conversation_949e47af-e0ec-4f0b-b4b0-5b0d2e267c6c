import { countries } from 'countries-list';

export const getCountryOptions = () => {
  const countryOptions = Object.keys(countries).map((code) => ({
    value: countries[code].name,
    valueSec: `+${countries[code].phone}`,
    label: `${countries[code].name}`
  }));

  // Separate out "United States" and sort the rest alphabetically
  const unitedStates = countryOptions.find(country => country.value === 'United States');
  const sortedCountries = countryOptions
    .filter(country => country.value !== 'United States')
    .sort((a, b) => a.value.localeCompare(b.value));

  return [unitedStates, ...sortedCountries];
};