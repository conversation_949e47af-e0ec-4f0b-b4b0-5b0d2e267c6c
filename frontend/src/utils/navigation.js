import { AppRoutesPaths } from "../route/app_route";

export const getPostLoginRoute = (origin) => {
  if (!origin) return AppRoutesPaths.home;

  // If coming from presale-auth
  if (origin === AppRoutesPaths.presaleAuth) {  
    return AppRoutesPaths.presale;
  }

  // If coming from DAO pages
  if (origin === AppRoutesPaths.dao || origin.startsWith('/dao')) {
    return AppRoutesPaths.dashboard.root;
  }

  // Default to home
  return AppRoutesPaths.home;
};