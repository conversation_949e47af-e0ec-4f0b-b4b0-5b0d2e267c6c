// Currency conversion utilities
import { countries } from 'countries-list';

// Currency mapping for countries (ISO codes to currency codes)
const CURRENCY_MAP = {
  'US': 'USD', // United States
  'KE': 'KES', // Kenya
  'UG': 'UGX', // Uganda
  'TZ': 'TZS', // Tanzania
  'NG': 'NGN', // Nigeria
  'GH': 'GHS', // Ghana
  'ZA': 'ZAR', // South Africa
  'EG': 'EGP', // Egypt
  'MA': 'MAD', // Morocco
  'DZ': 'DZD', // Algeria
  'TN': 'TND', // Tunisia
  'ET': 'ETB', // Ethiopia
  'ZW': 'ZWL', // Zimbabwe
  'ZM': 'ZMW', // Zambia
  'BW': 'BWP', // Botswana
  'MW': 'MWK', // Malawi
  'RW': 'RWF', // Rwanda
  'CM': 'XAF', // Cameroon
  'CF': 'XAF', // Central African Republic
  'TD': 'XAF', // Chad
  'CG': 'XAF', // Republic of the Congo
  'GA': 'XAF', // Gabon
  'GQ': 'XAF', // Equatorial Guinea
  'AO': 'AOA', // Angola
  'CD': 'CDF', // Democratic Republic of the Congo
  'BF': 'XOF', // Burkina Faso
  'CI': 'XOF', // Côte d'Ivoire
  'ML': 'XOF', // Mali
  'NE': 'XOF', // Niger
  'SN': 'XOF', // Senegal
  'TG': 'XOF', // Togo
  'BJ': 'XOF', // Benin
  'GN': 'GNF', // Guinea
  'LR': 'LRD', // Liberia
  'SL': 'SLL', // Sierra Leone
  'MR': 'MRU', // Mauritania
  'GM': 'GMD', // The Gambia
  'CV': 'CVE', // Cape Verde
  'ST': 'STN', // São Tomé and Príncipe
  'DJ': 'DJF', // Djibouti
  'ER': 'ERN', // Eritrea
  'SO': 'SOS', // Somalia
  'SS': 'SSP', // South Sudan
  'SD': 'SDG', // Sudan
  'LY': 'LYD', // Libya
  'MZ': 'MZN', // Mozambique
  'MG': 'MGA', // Madagascar
  'MU': 'MUR', // Mauritius
  'SC': 'SCR', // Seychelles
  'KM': 'KMF', // Comoros
  'LS': 'LSL', // Lesotho
  'SZ': 'SZL', // Eswatini (Swaziland)
  'NA': 'NAD', // Namibia
  'GB': 'GBP', // United Kingdom
  'CA': 'CAD', // Canada
  'AU': 'AUD', // Australia
  'EU': 'EUR', // Eurozone countries
  'DE': 'EUR', // Germany
  'FR': 'EUR', // France
  'IT': 'EUR', // Italy
  'ES': 'EUR', // Spain
  'NL': 'EUR', // Netherlands
  'BE': 'EUR', // Belgium
  'AT': 'EUR', // Austria
  'IE': 'EUR', // Ireland
  'PT': 'EUR', // Portugal
  'FI': 'EUR', // Finland
  'GR': 'EUR', // Greece
  'LU': 'EUR', // Luxembourg
  'MT': 'EUR', // Malta
  'CY': 'CY', // Cyprus
  'SK': 'EUR', // Slovakia
  'SI': 'EUR', // Slovenia
  'EE': 'EUR', // Estonia
  'LV': 'EUR', // Latvia
  'LT': 'EUR', // Lithuania
  'JP': 'JPY', // Japan
  'CN': 'CNY', // China
  'IN': 'INR', // India
  'KR': 'KRW', // South Korea
  'SG': 'SGD', // Singapore
  'MY': 'MYR', // Malaysia
  'TH': 'THB', // Thailand
  'PH': 'PHP', // Philippines
  'ID': 'IDR', // Indonesia
  'VN': 'VND', // Vietnam
  'BD': 'BDT', // Bangladesh
  'PK': 'PKR', // Pakistan
  'LK': 'LKR', // Sri Lanka
  'MM': 'MMK', // Myanmar
  'KH': 'KHR', // Cambodia
  'LA': 'LAK', // Laos
  'BN': 'BND', // Brunei
  'MN': 'MNT', // Mongolia
  'KZ': 'KZT', // Kazakhstan
  'UZ': 'UZS', // Uzbekistan
  'KG': 'KGS', // Kyrgyzstan
  'TJ': 'TJS', // Tajikistan
  'TM': 'TMT', // Turkmenistan
  'AF': 'AFN', // Afghanistan
  'IR': 'IRR', // Iran
  'IQ': 'IQD', // Iraq
  'SA': 'SAR', // Saudi Arabia
  'AE': 'AED', // UAE
  'QA': 'QAR', // Qatar
  'KW': 'KWD', // Kuwait
  'BH': 'BHD', // Bahrain
  'OM': 'OMR', // Oman
  'JO': 'JOD', // Jordan
  'LB': 'LBP', // Lebanon
  'SY': 'SYP', // Syria
  'IL': 'ILS', // Israel
  'PS': 'ILS', // Palestine (uses Israeli Shekel)
  'TR': 'TRY', // Turkey
  'RU': 'RUB', // Russia
  'UA': 'UAH', // Ukraine
  'BY': 'BYN', // Belarus
  'MD': 'MDL', // Moldova
  'GE': 'GEL', // Georgia
  'AM': 'AMD', // Armenia
  'AZ': 'AZN', // Azerbaijan
  'BR': 'BRL', // Brazil
  'AR': 'ARS', // Argentina
  'CL': 'CLP', // Chile
  'CO': 'COP', // Colombia
  'PE': 'PEN', // Peru
  'VE': 'VES', // Venezuela
  'UY': 'UYU', // Uruguay
  'PY': 'PYG', // Paraguay
  'BO': 'BOB', // Bolivia
  'EC': 'USD', // Ecuador (uses USD)
  'GY': 'GYD', // Guyana
  'SR': 'SRD', // Suriname
  'MX': 'MXN', // Mexico
  'GT': 'GTQ', // Guatemala
  'BZ': 'BZD', // Belize
  'SV': 'USD', // El Salvador (uses USD)
  'HN': 'HNL', // Honduras
  'NI': 'NIO', // Nicaragua
  'CR': 'CRC', // Costa Rica
  'PA': 'PAB', // Panama
  'CU': 'CUP', // Cuba
  'JM': 'JMD', // Jamaica
  'HT': 'HTG', // Haiti
  'DO': 'DOP', // Dominican Republic
  'TT': 'TTD', // Trinidad and Tobago
  'BB': 'BBD', // Barbados
  'LC': 'XCD', // Saint Lucia
  'GD': 'XCD', // Grenada
  'VC': 'XCD', // Saint Vincent and the Grenadines
  'AG': 'XCD', // Antigua and Barbuda
  'DM': 'XCD', // Dominica
  'KN': 'XCD', // Saint Kitts and Nevis
  'BS': 'BSD', // Bahamas
  'NO': 'NOK', // Norway
  'SE': 'SEK', // Sweden
  'DK': 'DKK', // Denmark
  'IS': 'ISK', // Iceland
  'CH': 'CHF', // Switzerland
  'PL': 'PLN', // Poland
  'CZ': 'CZK', // Czech Republic
  'HU': 'HUF', // Hungary
  'HR': 'HRK', // Croatia
  'RO': 'RON', // Romania
  'BG': 'BGN', // Bulgaria
  'RS': 'RSD', // Serbia
  'BA': 'BAM', // Bosnia and Herzegovina
  'ME': 'EUR', // Montenegro
  'MK': 'MKD', // North Macedonia
  'AL': 'ALL', // Albania
  'XK': 'EUR', // Kosovo
  'NZ': 'NZD', // New Zealand
  'FJ': 'FJD', // Fiji
  'PG': 'PGK', // Papua New Guinea
  'SB': 'SBD', // Solomon Islands
  'VU': 'VUV', // Vanuatu
  'NC': 'XPF', // New Caledonia
  'PF': 'XPF', // French Polynesia
  'WS': 'WST', // Samoa
  'TO': 'TOP', // Tonga
  'KI': 'AUD', // Kiribati (uses AUD)
  'TV': 'AUD', // Tuvalu (uses AUD)
  'NR': 'AUD', // Nauru (uses AUD)
  'FM': 'USD', // Micronesia (uses USD)
  'MH': 'USD', // Marshall Islands (uses USD)
  'PW': 'USD', // Palau (uses USD)
};

// Get currency symbol for display
const CURRENCY_SYMBOLS = {
  'USD': '$',
  'KES': 'KSh',
  'UGX': 'USh',
  'TZS': 'TSh',
  'NGN': '₦',
  'GHS': '₵',
  'ZAR': 'R',
  'EGP': '£',
  'MAD': 'DH',
  'DZD': 'DA',
  'TND': 'DT',
  'ETB': 'Br',
  'ZWL': 'Z$',
  'ZMW': 'ZK',
  'BWP': 'P',
  'MWK': 'MK',
  'RWF': 'RF',
  'XAF': 'FCFA', // Central African CFA franc (Cameroon, etc.)
  'XOF': 'CFA', // West African CFA franc
  'AOA': 'Kz', // Angola Kwanza
  'CDF': 'FC', // Congolese franc
  'GNF': 'FG', // Guinean franc
  'LRD': 'L$', // Liberian dollar
  'SLL': 'Le', // Sierra Leonean leone
  'MRU': 'UM', // Mauritanian ouguiya
  'GMD': 'D', // Gambian dalasi
  'CVE': '$', // Cape Verdean escudo
  'STN': 'Db', // São Tomé and Príncipe dobra
  'DJF': 'Fdj', // Djiboutian franc
  'ERN': 'Nfk', // Eritrean nakfa
  'SOS': 'S', // Somali shilling
  'SSP': '£', // South Sudanese pound
  'SDG': 'ج.س.', // Sudanese pound
  'LYD': 'ل.د', // Libyan dinar
  'MZN': 'MT', // Mozambican metical
  'MGA': 'Ar', // Malagasy ariary
  'MUR': '₨', // Mauritian rupee
  'SCR': '₨', // Seychellois rupee
  'KMF': 'CF', // Comorian franc
  'LSL': 'L', // Lesotho loti
  'SZL': 'L', // Swazi lilangeni
  'NAD': 'N$', // Namibian dollar
  'GBP': '£',
  'EUR': '€',
  'CAD': 'C$',
  'AUD': 'A$',
  'JPY': '¥',
  'CNY': '¥',
  'INR': '₹',
  'KRW': '₩',
  'SGD': 'S$',
  'MYR': 'RM',
  'THB': '฿',
  'PHP': '₱',
  'IDR': 'Rp',
  'VND': '₫',
  'BDT': '৳',
  'PKR': '₨',
  'LKR': '₨',
  'MMK': 'K',
  'KHR': '៛',
  'LAK': '₭',
  'BND': 'B$',
  'MNT': '₮',
  'KZT': '₸',
  'UZS': 'soʻm',
  'KGS': 'лв',
  'TJS': 'SM',
  'TMT': 'T',
  'AFN': '؋',
  'IRR': '﷼',
  'IQD': 'ع.د',
  'SAR': '﷼',
  'AED': 'د.إ',
  'QAR': '﷼',
  'KWD': 'د.ك',
  'BHD': '.د.ب',
  'OMR': '﷼',
  'JOD': 'د.ا',
  'LBP': '£',
  'SYP': '£',
  'ILS': '₪',
  'TRY': '₺',
  'RUB': '₽',
  'UAH': '₴',
  'BYN': 'Br',
  'MDL': 'L',
  'GEL': '₾',
  'AMD': '֏',
  'AZN': '₼',
  'BRL': 'R$',
  'ARS': '$',
  'CLP': '$',
  'COP': '$',
  'PEN': 'S/',
  'VES': 'Bs',
  'UYU': '$U',
  'PYG': '₲',
  'BOB': '$b',
  'GYD': '$',
  'SRD': '$',
  'MXN': '$',
  'GTQ': 'Q',
  'BZD': 'BZ$',
  'HNL': 'L',
  'NIO': 'C$',
  'CRC': '₡',
  'PAB': 'B/.',
  'CUP': '₱',
  'JMD': 'J$',
  'HTG': 'G',
  'DOP': 'RD$',
  'TTD': 'TT$',
  'BBD': 'Bds$',
  'XCD': 'EC$',
  'BSD': 'B$',
  'NOK': 'kr',
  'SEK': 'kr',
  'DKK': 'kr',
  'ISK': 'kr',
  'CHF': 'CHF',
  'PLN': 'zł',
  'CZK': 'Kč',
  'HUF': 'Ft',
  'HRK': 'kn',
  'RON': 'lei',
  'BGN': 'лв',
  'RSD': 'Дин.',
  'BAM': 'KM',
  'MKD': 'ден',
  'ALL': 'L',
  'NZD': 'NZ$',
  'FJD': 'FJ$',
  'PGK': 'K',
  'SBD': 'SI$',
  'VUV': 'VT',
  'XPF': '₣',
  'WST': 'WS$',
  'TOP': 'T$',
};

// Function to get country code from country name
export const getCountryCode = (countryName) => {
  const countryEntries = Object.entries(countries);
  const foundCountry = countryEntries.find(([code, country]) => 
    country.name.toLowerCase() === countryName.toLowerCase()
  );
  return foundCountry ? foundCountry[0] : null;
};

// Function to get currency code for a country
export const getCurrencyCode = (countryName) => {
  const countryCode = getCountryCode(countryName);
  return countryCode ? (CURRENCY_MAP[countryCode] || 'USD') : 'USD';
};

// Function to get currency symbol
export const getCurrencySymbol = (currencyCode) => {
  return CURRENCY_SYMBOLS[currencyCode] || currencyCode;
};

// Function to get currency info for a country
export const getCurrencyInfo = (countryName) => {
  const currencyCode = getCurrencyCode(countryName);
  const symbol = getCurrencySymbol(currencyCode);
  return {
    code: currencyCode,
    symbol: symbol,
    name: currencyCode
  };
};

// Real-time currency conversion using exchangerate-api.com (free tier)
export const convertCurrency = async (amount, fromCurrency = 'USD', toCurrency) => {
  try {
    // If converting to the same currency, return the amount as is
    if (fromCurrency === toCurrency) {
      return {
        success: true,
        convertedAmount: parseFloat(amount),
        rate: 1,
        fromCurrency,
        toCurrency
      };
    }

    // Free API endpoint (no API key required for basic usage)
    const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${fromCurrency}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch exchange rates');
    }

    const data = await response.json();
    
    if (!data.rates || !data.rates[toCurrency]) {
      throw new Error(`Exchange rate not found for ${toCurrency}`);
    }

    const rate = data.rates[toCurrency];
    const convertedAmount = parseFloat(amount) * rate;

    return {
      success: true,
      convertedAmount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
      rate,
      fromCurrency,
      toCurrency,
      timestamp: data.date
    };
  } catch (error) {
    console.error('Currency conversion error:', error);
    return {
      success: false,
      error: error.message,
      convertedAmount: parseFloat(amount), // Fallback to original amount
      rate: 1,
      fromCurrency,
      toCurrency
    };
  }
};

// Function to convert USD to country's local currency
export const convertUSDToLocalCurrency = async (usdAmount, countryName) => {
  const currencyInfo = getCurrencyInfo(countryName);
  const conversion = await convertCurrency(usdAmount, 'USD', currencyInfo.code);
  
  return {
    ...conversion,
    currencyInfo,
    formattedAmount: `${currencyInfo.symbol}${conversion.convertedAmount.toLocaleString()}`
  };
};

// Function to format currency amount with proper symbol and formatting
export const formatCurrencyAmount = (amount, currencyCode, countryName = null) => {
  const symbol = getCurrencySymbol(currencyCode);
  const numericAmount = parseFloat(amount);
  
  // Handle special formatting for different currencies
  if (currencyCode === 'JPY' || currencyCode === 'KRW' || currencyCode === 'VND') {
    // No decimal places for these currencies
    return `${symbol}${Math.round(numericAmount).toLocaleString()}`;
  }
  
  return `${symbol}${numericAmount.toLocaleString(undefined, { 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  })}`;
};

// Function to get updated country options with currency info
export const getCountryOptionsWithCurrency = () => {
  const countryOptions = Object.keys(countries).map((code) => {
    const country = countries[code];
    const currencyCode = CURRENCY_MAP[code] || 'USD';
    const currencySymbol = getCurrencySymbol(currencyCode);
    
    return {
      value: country.name,
      valueSec: `+${country.phone}`,
      label: `${country.name}`,
      currencyCode,
      currencySymbol,
      countryCode: code
    };
  });

  // Separate out "United States" and sort the rest alphabetically
  const unitedStates = countryOptions.find(country => country.value === 'United States');
  const sortedCountries = countryOptions
    .filter(country => country.value !== 'United States')
    .sort((a, b) => a.value.localeCompare(b.value));

  return [unitedStates, ...sortedCountries];
};

export default {
  getCountryCode,
  getCurrencyCode,
  getCurrencySymbol,
  getCurrencyInfo,
  convertCurrency,
  convertUSDToLocalCurrency,
  formatCurrencyAmount,
  getCountryOptionsWithCurrency
};
