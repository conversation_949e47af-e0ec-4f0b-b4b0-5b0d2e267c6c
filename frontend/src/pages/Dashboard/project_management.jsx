import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import admin from '../../assets/admin/admin.png';
import briefing from '../../assets/admin/briefing.png';
import upload from '../../assets/admin/upload-big-arrow.png';
import attach from '../../assets/admin/attach-paperclip-symbol.png';
import checked from '../../assets/admin/checked.png';
import { FiEye } from 'react-icons/fi';
import { FaPlus } from 'react-icons/fa6';
import { ProjectService } from '../../services/projectService';
import { toast } from 'react-toastify';

export const ProjectManagement = () => {
  const [projects, setProjects] = useState([]);
  const [updates, setUpdates] = useState({});
  const [updateInputs, setUpdateInputs] = useState({});
  const [selectedProjectId, setSelectedProjectId] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalUpdates, setModalUpdates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingUpdate, setEditingUpdate] = useState({});
  const navigate = useNavigate();

  // Fetch projects on mount
  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        const data = await ProjectService.getOngoingProjects();
        const sorted = [...data].sort(
          (a, b) => new Date(b.startDate) - new Date(a.startDate)
        );
        setProjects(sorted);
        const updatesObj = {};
        sorted.forEach((project) => {
          updatesObj[project.id] = project.updates || [];
        });
        setUpdates(updatesObj);
      } catch (err) {
        toast.error('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };
    fetchProjects();
  }, []);

  // Mark project as completed
  const handleCompleteProject = async (projectId) => {
    if (!projectId) return;
    setLoading(true);
    try {
      const result = await ProjectService.completeProject(projectId);
      if (result) {
        // toast.success('Project marked as completed');
        navigate('/dashboard/project-history');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle update input change
  const handleUpdateInput = (projectId, value) => {
    setUpdateInputs((prev) => ({
      ...prev,
      [projectId]: {
        ...prev[projectId],
        text: value,
      },
    }));
  };

  // Submit a new update
  const handleSubmitUpdate = async (projectId) => {
    if (!projectId) {
      toast.error('Project ID is missing.');
      return;
    }
    const input = updateInputs[projectId];
    if (!input || !input.text) return;
    setLoading(true);
    try {
      const updatedProject = await ProjectService.addUpdate(
        projectId,
        input.text,
        input.image
      );
      setUpdates((prev) => ({
        ...prev,
        [projectId]: updatedProject.updates,
      }));
      setUpdateInputs((prev) => ({
        ...prev,
        [projectId]: { text: '', image: null },
      }));
      toast.success('Update submitted successfully');
    } catch (err) {
      toast.error('Failed to submit update');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="text-gray-700 mx-auto px-0 w-full min-h-screen py-12">
      <h1 className="text-3xl sm:text-4xl font-bold text-black text-center mt-[-60px] lg:ml-24 sm:ml-0">
        Project Management
      </h1>
      <div className="flex items-center text-left space-x-2 mt-6 ml-0 sm:ml-10">
        <img src={briefing} alt="Ongoing Projects Icon" className="w-8 h-8" />
        <h3 className="text-2xl font-Poppins text-gray-700">
          Ongoing Projects
        </h3>
      </div>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      ) : projects.length === 0 ? (
        <div className="flex justify-center w-full mt-8">
          <div className="bg-white shadow-md rounded-lg md:p-48 p-24 font-karla md:text-4xl text-lg text-center">
            <p className="text-gray-600">No ongoing projects at the moment.</p>
          </div>
        </div>
      ) : (
        projects.map((project) => (
          <div
            key={project.id || Math.random()}
            className="flex justify-center items-center w-full mt-8"
          >
            <div className="relative rounded-2xl overflow-hidden w-full">
              {/* Project Image */}
              <img
                src={project.image || admin}
                alt={project.title}
                crossOrigin="anonymous"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = admin;
                }}
                className="absolute inset-0 w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/50" />
              <div className="relative z-10 p-6 space-y-4 text-white">
                <h3 className="font-bold text-xl sm:text-2xl md:text-3xl leading-tight">{project.title}</h3>
                <h3 className="mt-2 text-sm sm:text-base text-gray-200 line-clamp-3">{project.description}</h3>
                {/* Upload Main Project Image */}
                <div>
                  <p className="text-sm">Upload Main Project Image</p>
                  <div
                    className="flex items-center space-x-2 text-yellow-500 cursor-pointer p-4"
                    onClick={() =>
                      document
                        .getElementById(`main-image-input-${project.id}`)
                        .click()
                    }
                  >
                    <img src={upload} alt="Upload Icon" className="w-6 h-6" />
                    <span>Upload Image</span>
                    <input
                      id={`main-image-input-${project.id}`}
                      type="file"
                      accept="image/*"
                      style={{ display: 'none' }}
                      onChange={async (e) => {
                        const file = e.target.files[0];
                        if (!file) return;
                        setLoading(true);
                        try {
                          const updatedProject =
                            await ProjectService.updateProjectImage(
                              project.id,
                              file
                            );
                          setProjects((prev) =>
                            prev.map((p) =>
                              p.id === project.id
                                ? { ...p, image: updatedProject.image }
                                : p
                            )
                          );
                        } catch (err) {
                          toast.error('Failed to update project image');
                        } finally {
                          setLoading(false);
                        }
                      }}
                    />
                  </div>
                  <p className="text-sm">Project Update</p>
                </div>
                {/* Update Form */}
                <div className="bg-white p-4 rounded-lg shadow text-black">
                  <textarea
                    className="w-full md:h-[150px] rounded bg-white p-2 border border-gray-200 focus:border-2 focus:border-gray-300 outline-none ring-0"
                    placeholder="Write your update here..."
                    rows="3"
                    value={updateInputs[project.id]?.text || ''}
                    onChange={(e) =>
                      handleUpdateInput(project.id, e.target.value)
                    }
                  />
                  <div className="flex justify-end mt-2 text-purple-500 cursor-pointer items-center">
                    <label className="flex items-center cursor-pointer">
                      <img src={attach} alt="Attach Icon" className="w-6 h-6" />
                      <span className="ml-1 text-sm">Attach update image</span>
                      <input
                        type="file"
                        accept="image/*"
                        style={{ display: 'none' }}
                        crossOrigin="anonymous"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            setUpdateInputs((prev) => ({
                              ...prev,
                              [project.id]: {
                                ...prev[project.id],
                                image: file,
                                text: prev[project.id]?.text || '',
                              },
                            }));
                          }
                        }}
                      />
                    </label>
                    {updateInputs[project.id]?.image && (
                      <span className="ml-2 text-xs text-green-600">
                        {updateInputs[project.id].image.name}
                      </span>
                    )}
                  </div>
                  {/* Edit Mode */}
                  {editingUpdate[project.id] && (
                    <div className="flex gap-2 mt-2">
                      <button
                        className="bg-green-500 text-white px-3 py-1 rounded"
                        onClick={async () => {
                          const input = updateInputs[project.id];
                          if (!input || !input.text) return;
                          setLoading(true);
                          try {
                            // Call the correct service
                            const updatedProject =
                              await ProjectService.updateProjectUpdateImageAndText(
                                project.id,
                                input.updateId,
                                input.text,
                                input.image
                              );
                            // Find the updated update from the returned project
                            const updatedUpdate = updatedProject.updates.find(
                              (u) => (u._id || u.id) === input.updateId
                            );
                            setUpdates((prev) => ({
                              ...prev,
                              [project.id]: prev[project.id].map((u) =>
                                (u._id || u.id) === input.updateId
                                  ? {
                                      ...u,
                                      text: updatedUpdate.text,
                                      image: updatedUpdate.image,
                                    }
                                  : u
                              ),
                            }));
                            setEditingUpdate((prev) => ({
                              ...prev,
                              [project.id]: false,
                            }));
                            setUpdateInputs((prev) => ({
                              ...prev,
                              [project.id]: { text: '', image: null },
                            }));
                          } catch (err) {
                            // toast.error('Failed to edit update');
                          } finally {
                            setLoading(false);
                          }
                        }}
                        disabled={loading}
                      >
                        {loading ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        className="bg-gray-300 text-black px-3 py-1 rounded"
                        onClick={() => {
                          setEditingUpdate((prev) => ({
                            ...prev,
                            [project.id]: false,
                          }));
                          setUpdateInputs((prev) => ({
                            ...prev,
                            [project.id]: { text: '', image: null },
                          }));
                        }}
                        disabled={loading}
                      >
                        Cancel
                      </button>
                    </div>
                  )}
                </div>
                {/* Submit Update Button */}
                {!editingUpdate[project.id] && (
                  <button
                    className="bg-[#B266FF] px-6 py-3 rounded-lg text-white flex items-center gap-2 w-auto mx-auto sm:mx-0 shadow-md hover:bg-[#A255F2] transition-all duration-200"
                    onClick={() => handleSubmitUpdate(project.id)}
                  >
                    <span className="w-6 h-6 flex items-center justify-center rounded-full border border-white">
                      <FaPlus size={14} className="text-white" />
                    </span>
                    Submit update
                  </button>
                )}
                {/* Latest Update Card */}
                {updates[project.id] &&
                  updates[project.id].length > 0 &&
                  (() => {
                    // Always get the latest by date, not just last in array
                    const latest = [...updates[project.id]].sort(
                      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
                    )[0];
                    return (
                      <div className="bg-[#172036] text-white rounded-2xl p-4 space-y-4 mt-6">
                        <div className="flex items-center justify-between">
                          <h3 className="text-xl font-Karla">Latest update</h3>
                          <button
                            className="bg-yellow-500 text-black px-3 py-1 rounded text-sm ml-2"
                            onClick={() => {
                              setEditingUpdate((prev) => ({
                                ...prev,
                                [project.id]: true,
                              }));
                              setUpdateInputs((prev) => ({
                                ...prev,
                                [project.id]: {
                                  text: latest.text,
                                  image: null,
                                  updateId: latest._id || latest.id,
                                },
                              }));
                            }}
                          >
                            Edit update
                          </button>
                        </div>
                        <p className="text-lg font-Poppins">
                          {latest.text && latest.text.length > 250
                            ? `${latest.text.slice(0, 250)}...`
                            : latest.text}
                        </p>
                        {latest.image && (
                          <img
                            src={latest.image}
                            crossOrigin="anonymous"
                            alt="Latest update"
                            className="rounded-xl w-full sm:w-40"
                          />
                        )}
                        <div
                          className="flex items-center text-purple-500 cursor-pointer space-x-1"
                          onClick={() => {
                            setModalUpdates(updates[project.id] || []);
                            setSelectedProjectId(project.id);
                            setModalOpen(true);
                          }}
                        >
                          <FiEye className="text-lg" />
                          <span className="text-lg">View all updates</span>
                        </div>
                      </div>
                    );
                  })()}
                {/* Mark as completed button */}
                <div className="flex justify-end">
                  <button
                    className="bg-green-500 px-6 py-3 rounded-lg text-white flex items-center gap-2 w-auto mx-auto sm:mx-0"
                    onClick={() => handleCompleteProject(project.id)}
                  >
                    <img src={checked} alt="Checked Icon" className="w-6 h-6" />
                    <span>Mark as completed</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))
      )}

      {/* Updates Modal */}
      <UpdatesModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        updates={modalUpdates}
        projectId={selectedProjectId}
        refreshUpdates={async (newUpdates) => {
          if (newUpdates) {
            setUpdates((prev) => ({
              ...prev,
              [selectedProjectId]: newUpdates,
            }));
            return;
          }
          setLoading(true);
          const data = await ProjectService.getFundedProjects();
          setProjects(data);
          const updatesObj = {};
          data.forEach((project) => {
            updatesObj[project.id] = project.updates || [];
          });
          setUpdates(updatesObj);
          setLoading(false);
        }}
        setLoading={setLoading}
        loading={loading}
      />
    </div>
  );
};

// Modal for showing all updates
const UpdatesModal = ({
  open,
  onClose,
  updates,
  projectId,
  refreshUpdates,
  setLoading,
  loading,
}) => {
  const [editIndex, setEditIndex] = useState(null);
  const [editText, setEditText] = useState('');
  const [deleteIndex, setDeleteIndex] = useState(null);
  const [editImage, setEditImage] = useState(null);

  useEffect(() => {
    setEditIndex(null);
    setEditText('');
    setEditImage(null);
  }, [open, updates]);

  if (!open) return null;

  const handleEdit = (idx, text) => {
    setEditIndex(idx);
    setEditText(text);
    setEditImage(null);
  };

  const handleEditSave = async (idx) => {
    const update = updates[idx];
    const updateId = update.id || update._id;
    if (!update || !updateId) {
      toast.error('Invalid update or missing id');
      return;
    }
    setLoading(true);
    try {
      const updatedProject =
        await ProjectService.updateProjectUpdateImageAndText(
          projectId,
          updateId,
          editText,
          editImage
        );
      const updatedUpdate = updatedProject.updates.find(
        (u) => (u._id || u.id) === updateId
      );
      const newUpdates = updates.map((u, i) =>
        i === idx
          ? { ...u, text: updatedUpdate.text, image: updatedUpdate.image }
          : u
      );
      await refreshUpdates(newUpdates);
      setEditIndex(null);
      setEditText('');
      setEditImage(null);
      onClose(); // This closes the modal
    } catch (err) {
      toast.error('Failed to edit update');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (idx) => {
    const update = updates[idx];
    const updateId = update.id || update._id;
    setLoading(true);
    try {
      await ProjectService.deleteProjectUpdate(projectId, updateId);
      await refreshUpdates();
      setDeleteIndex(null);
      // No need to call onClose() here, window.location.reload() will handle it
    } catch (err) {
      // toast.error('Failed to delete update');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
      <div className="bg-white rounded-2xl p-8 max-w-4xl w-full h-[85vh] shadow-2xl relative flex flex-col">
        <button
          className="absolute top-3 right-5 text-2xl text-gray-400 hover:text-black"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>
        <h2 className="text-2xl font-bold mb-6 text-black text-center">
          All Updates
        </h2>
        <div className="space-y-6 max-h-[60vh] bg-white overflow-y-auto pr-2">
          {updates && updates.length > 0 ? (
            updates.map((u, i) => (
              <div key={u.id || i}>
                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-400 mb-1">
                    {u.createdAt ? new Date(u.createdAt).toLocaleString() : ''}
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="text-blue-500 text-sm"
                      onClick={() => handleEdit(i, u.text)}
                    >
                      Edit
                    </button>
                    <button
                      className="text-red-500 text-xs"
                      onClick={() => setDeleteIndex(i)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
                {editIndex === i ? (
                  <div className="mt-2">
                    <textarea
                      className="w-full border bg-white rounded p-2"
                      value={editText}
                      onChange={(e) => setEditText(e.target.value)}
                    />
                    <div className="flex items-center mt-2">
                      <label className="flex items-center cursor-pointer text-purple-500">
                        <img
                          src={attach}
                          alt="Attach Icon"
                          className="w-6 h-6"
                        />
                        <span className="ml-1 text-sm">
                          Attach update image
                        </span>
                        <input
                          type="file"
                          accept="image/*"
                          style={{ display: 'none' }}
                          crossOrigin="anonymous"
                          onChange={(e) => setEditImage(e.target.files[0])}
                        />
                      </label>
                      {editImage && (
                        <span className="ml-2 text-xs text-green-600">
                          {editImage.name}
                          <button
                            className="ml-2 text-red-500"
                            onClick={() => setEditImage(null)}
                            title="Remove image"
                          >
                            &times;
                          </button>
                        </span>
                      )}
                      {!editImage && u.image && (
                        <img
                          src={u.image}
                          crossOrigin="anonymous"
                          alt="Current"
                          className="ml-2 w-10 h-10 rounded object-cover border"
                        />
                      )}
                    </div>
                    <div className="flex gap-2 mt-2">
                      <button
                        className="bg-green-500 text-white px-3 py-1 rounded"
                        onClick={() => handleEditSave(i)}
                        disabled={loading}
                      >
                        {loading ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        className="bg-gray-300 text-black px-3 py-1 rounded"
                        onClick={() => {
                          setEditIndex(null);
                          setEditImage(null);
                        }}
                        disabled={loading}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-800 whitespace-pre-line">{u.text}</p>
                )}
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center">No updates yet.</p>
          )}
        </div>
      </div>
      {/* Delete Confirmation Modal */}
      {deleteIndex !== null && (
        <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/40">
          <div className="bg-white rounded-xl p-6 shadow-xl flex flex-col items-center">
            <p className="mb-4 text-lg text-gray-800">
              Are you sure you want to Delete this update?
            </p>
            <div className="flex gap-4">
              <button
                className="bg-red-500 text-white px-4 py-2 rounded"
                onClick={async () => {
                  await handleDelete(deleteIndex);
                  window.location.reload(); // This will close both modals and refresh the page
                }}
                disabled={loading}
              >
                Delete
              </button>
              <button
                className="bg-gray-300 text-black px-4 py-2 rounded"
                onClick={() => setDeleteIndex(null)}
                disabled={loading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

UpdatesModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  updates: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string, // <-- fixes "updates[].id' is missing in props validation"
      text: PropTypes.string,
      image: PropTypes.string,
      createdAt: PropTypes.string,
    })
  ).isRequired,
  projectId: PropTypes.string,
  refreshUpdates: PropTypes.func.isRequired,
  setLoading: PropTypes.func.isRequired,
  loading: PropTypes.bool.isRequired,
};
