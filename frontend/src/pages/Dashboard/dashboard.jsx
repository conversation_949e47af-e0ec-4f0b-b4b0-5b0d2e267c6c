import  { useState, useEffect, useRef } from 'react';
import { Routes, Route, Link, useNavigate, useLocation, Outlet } from 'react-router-dom';
import newBPlogo from '../../assets/newBPlogo.png';


import * as io from 'react-icons/io';
import {
  FaUserCog,
  FaBars,
  FaVoteYea
} from 'react-icons/fa';
import { GiArmoredBoomerang } from "react-icons/gi";
import { IoLogOut } from "react-icons/io5";
import home from '../../assets/dashboard/home.png';
import mainuser from '../../assets/dashboard/mainuser.png';
import appointment from '../../assets/dashboard/appointment.png';
import start from '../../assets/dashboard/start-up.png';
import user from '../../assets/dashboard/user.png';
import { AppRoutesPaths } from '../../route/app_route';
import { useAuthentication } from '../../components/utils/provider';
import { RiArrowDropDownLine } from "react-icons/ri";
import { toast } from 'react-toastify';
import { RiAdminFill } from "react-icons/ri";

export const Dashboard = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAdminOpen, setIsAdminOpen] = useState(false);
  const [isVotingOpen, setIsVotingOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuthentication();
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const userDropdownRef = useRef(null);
  const { onLogout } = useAuthentication();

  const [isSubscriber, setIsSubscriber] = useState(currentUser.isSubscribed);

  const handleNavigation = (path) => {
    navigate(path);
    setIsMenuOpen(false);
    setIsOpen(false);
    setIsAdminOpen(false);
  };

  useEffect(() => {
  function handleClickOutside(event) {
    if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
      setUserDropdownOpen(false);
    }
  }
  document.addEventListener("mousedown", handleClickOutside);
  return () => document.removeEventListener("mousedown", handleClickOutside);
}, []);

 const handleLogout = async () => {
        try {
            onLogout();
            window.location.href = "/login"
            toast.success('Logged out successfully');
        } catch (error) {
            console.error('Error logging out:', error);
            toast.error('Failed to logout');
        }
    };
 
  return (
    <div className="bg-[#F8F5ED] min-h-screen flex flex-col">
      <div className="w-full">
        {/* Navbar */}
        <div className="hidden md:flex bg-white shadow-md shadow-[#0000001A] px-10 py-6 items-center justify-between w-full absolute top-0 left-0">
          <div className="hidden md:flex items-center gap-6 text-black font-Karla text-xl">
          <img onClick={()=>{navigate(AppRoutesPaths.home)}} src={newBPlogo} alt="" className=" w-12 h-12 hover:cursor-pointer" />
            <img
              src={home}
              alt=""
              className="w-8 h-8 cursor-pointer"
              onClick={() => handleNavigation(AppRoutesPaths.dashboard.root)}
            />
            <span
              className="font-bold text-[24px] cursor-pointer"
              onClick={() => handleNavigation(AppRoutesPaths.dashboard.root)}
            >
              Dashboard
            </span>
            <div className="hidden md:flex  text-gray-600 items-center">
              <Link
                to="/dashboard/events"
                className="hidden  lg:flex items-center gap-2 cursor-pointer px-2 py-1 md:px-3 md:py-2 rounded hover:bg-gray-100 transition"
              >
                <img src={appointment} alt="Sahelion Logo" className="w-6 h-6" />
                <span className="text-gray-700">Events</span>
              </Link>
              {isSubscriber &&
                <Link
                to={AppRoutesPaths.dashboard.projectRankMultiVotes}
                className="hidden lg:flex items-center gap-2 cursor-pointer px-2 py-1 md:px-3 md:py-2 rounded hover:bg-gray-100 transition"
              >
                 <FaVoteYea  className='text-gray-500 w-6 h-6'/>
                <span className="text-gray-700">Project Votes</span>
              </Link>}


              {/* 
                  More button that will be displayed on medium screens
               */}
                <div className="relative md:block lg:hidden">
                <div
                  className="flex items-center gap-2 border px-4 py-2 rounded-full cursor-pointer shadow-sm bg-white"
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <GiArmoredBoomerang className="w-6 h-6" />
                  <span className="text-gray-700 font-medium">More</span>
                  <io.IoMdArrowDropdown className="text-gray-600" />
                </div>

                {isOpen && (
                  <div className="absolute left-0 mt-2 w-56 bg-white border rounded-lg shadow-lg py-2">
                    <ul className="text-gray-700">
                      <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.events)}
                      >
                        Events
                      </li>
                      { isSubscriber && (
                      <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.projectRankMultiVotes)}
                      >
                        Projects Vote
                      </li>
                      )}
                       <li
                          className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                          onClick={() => setIsVotingOpen(!isVotingOpen)}
                        >
                          <div className="flex justify-between items-center">
                            Projects
                            <io.IoMdArrowDropdown
                              className={`text-gray-600 transition-transform ${
                                isVotingOpen ? 'rotate-180' : 'rotate-0'
                              }`}
                      />
                    </div>
                     </li>
                      {isVotingOpen && (
                      <ul className="pl-1">
                        {isSubscriber &&
                        <li
                          className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.ongoingVote);
                            setIsAdminOpen(false);
                            setIsVotingOpen(false);
                          }}
                        >
                          Ongoing Votes
                        </li>}
                        <li
                          className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.ongoingProjects);
                            setIsAdminOpen(false);
                            setIsVotingOpen(false);
                          }}
                        >
                          Ongoing Projects
                        </li>
                        {isSubscriber &&
                          <li
                          className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.projectHistory);
                            setIsAdminOpen(false);
                            setIsVotingOpen(false);
                          }}
                        >
                          Projects History
                        </li>}
                      </ul>
                  )}
                    </ul>
                  </div>
                )}
              </div>



              {/* Projects Button */}
              <div className="relative hidden  md:hidden lg:block  ">
                <div
                  className="flex items-center gap-2 border px-4 py-2 rounded-full cursor-pointer shadow-sm bg-white"
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <img src={start} alt="Projects Icon" className="w-6 h-6" />
                  <span className="text-gray-700 font-medium">Projects</span>
                  <io.IoMdArrowDropdown className="text-gray-600" />
                </div>

                {isOpen && (
                  <div className="absolute left-0 mt-2 w-56 bg-white border rounded-lg shadow-lg py-2 z-50">
                    <ul className="text-gray-700">
                      {isSubscriber &&
                       <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.ongoingVote)}
                      >
                        Ongoing votes
                      </li>}
                      <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.ongoingProjects)}
                      >
                        Ongoing projects
                      </li>
                      {isSubscriber &&
                      <li
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.projectHistory)}
                      >
                        Projects history
                      </li>}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Admin Button */}
          {currentUser?.role === "admin" ? (
          <div className="relative">
            {/* Admin Button */}
            <div
              className="flex items-center gap-2 px-14 py-2 cursor-pointer bg-white"
              onClick={() => setIsAdminOpen(!isAdminOpen)}
            >
              <RiAdminFill className="text-gray-600" />
              <span className="text-gray-700 font-medium">
                {currentUser?.firstName || "Admin"}
              </span>
              <io.IoMdArrowDropdown className="text-gray-600" />
            </div>

            {/* Admin Dropdown Menu */}
            {isAdminOpen && (
              <div className="absolute left-0 mt-1 w-56 bg-white border rounded-lg shadow-lg py-3 z-50">
                <ul className="text-gray-700">
                  <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.eventManagement);
                      setIsAdminOpen(false);
                    }}
                  >
                    Event Management
                  </li>
                  <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.projectManagement);
                      setIsAdminOpen(false);
                    }}
                  >
                    Project Management
                  </li>

                  <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.managecommunity);
                      setIsAdminOpen(false);
                    }}
                  >
                    Manage Community
                  </li>
                    <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.managecontribution);
                      setIsAdminOpen(false);
                    }}
                  >
                    Manage Contribution
                  </li>

                  <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.communityRequests);
                      setIsAdminOpen(false);
                    }}
                  >
                    Community Requests
                  </li>

                  <li
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={() => setIsVotingOpen(!isVotingOpen)}
                  >
                    <div className="flex justify-between items-center">
                      Voting Management
                      <io.IoMdArrowDropdown
                        className={`text-gray-600 transition-transform ${
                          isVotingOpen ? 'rotate-180' : 'rotate-0'
                        }`}
                      />
                    </div>
                  </li>
                  {/* Nested Voting Options */}
                  {isVotingOpen && (
                    <ul className="pl-1">
                      <li
                        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          handleNavigation(AppRoutesPaths.dashboard.initiateVote);
                          setIsAdminOpen(false);
                          setIsVotingOpen(false);
                        }}
                      >
                        Initiate vote
                      </li>
                      <li
                        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          handleNavigation(AppRoutesPaths.dashboard.ongoingVote);
                          setIsAdminOpen(false);
                          setIsVotingOpen(false);
                        }}
                      >
                        Ongoing vote
                      </li>
                      <li
                        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          handleNavigation(AppRoutesPaths.dashboard.voteResults);
                          setIsAdminOpen(false);
                          setIsVotingOpen(false);
                        }}
                      >
                        Vote results
                      </li>
                    </ul>
                  )}
                </ul>

                <hr className='my-2 border-gray-600'/>

                {currentUser?.role === "admin" ? (
                    <ul>
                      <li
                        className="flex items-center gap-2 px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNavigation(AppRoutesPaths.dashboard.account)}
                      >
                        <img src={mainuser} alt="Sahelion Logo" className="w-5 h-5" />
                        <span className='text-gray-700'>Account</span>
                      </li>
                      <li
                        className="flex items-center gap-2 px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                        onClick={handleLogout}
                      >
                        <IoLogOut className="w-5 h-5 text-gray-700" />
                        <span className='text-gray-700'>Logout</span>
                      </li>
                    </ul>
                  ) : null}
              </div>
            )}
          </div>
          ) : currentUser?.role === "user" ? (
             <div className="flex items-center justify-between p-4  gap-4 ">
                  <div className='flex flex-row items-center gap-2'>
                    <img src={user} alt="User" className="w-8 h-8 rounded-full border border-gray-400 object-cover  p-2" />
                     <span className="text-gray-700 font-medium">
                       {currentUser?.firstName || "User"}
                      </span>

                  </div>
                  <div ref={userDropdownRef} className="relative">
                      <RiArrowDropDownLine
                        className="w-6 h-6 text-gray-700 cursor-pointer"
                        onClick={() => setUserDropdownOpen((open) => !open)}
                      />
                    {userDropdownOpen && (
                      <div className="absolute right-0 mt-2 w-24 bg-white border rounded-lg shadow-lg py-2 z-50">
                        <button
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 text-gray-700"
                          onClick={() => {
                            // Navigate to Account page
                            handleNavigation(AppRoutesPaths.dashboard.account);
                            setUserDropdownOpen(false);
                          }}
                        >
                          Account
                        </button>
                        <button
                          className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                          onClick={handleLogout}
                        >
                          Logout
                        </button>
                      </div>
                    )}
                </div>
              </div>

          ) : null }
       </div>
      

        {/* Mobile Navbar */}
        <div className="relative md:hidden w-full flex justify-end z-50 shadow-lg p-4">
          <div className="relative w-full flex justify-between items-center space-x-2">
            {/* Mobile Menu Button */}

            <img onClick={()=>{navigate(AppRoutesPaths.home)}} src={newBPlogo} alt="" className="w-10 h-10" />

            <div
              className="md:hidden text-[24px] cursor-pointer font-semibold text-gray-900"
              onClick={() => {
                handleNavigation(AppRoutesPaths.dashboard.root);
              }}
            >
              Dashboard
              
            </div>

            <div
              className="md:hidden text-gray-700"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <FaBars className="text-2xl" />
            </div>
            

            {/* Dropdown Menu */}
            {isMenuOpen && (
              <div className="absolute right-0 top-full mt-2  w-64 bg-white shadow-lg rounded-lg p-4 min-h-[420px] z-50">
                  {currentUser?.role === "admin" ? (
                  <div className="relative w-full">
                    <div
                      className="flex items-center gap-4 px-4 py-2 cursor-pointer bg-white sm:pl-4 pl-4"
                      onClick={() => setIsAdminOpen(!isAdminOpen)}
                    >
                      <RiAdminFill className="text-gray-600" />
                       <span className="text-gray-700 font-medium">
                          {currentUser?.firstName || "Admin"}
                        </span>
                      <io.IoMdArrowDropdown
                        className={`text-gray-600 transition-transform ${
                          isAdminOpen ? 'rotate-180' : 'rotate-0'
                        }`}
                      />
                    </div>
                    {/* Admin Dropdown Menu */}
                    {isAdminOpen && (
                      <div className="absolute left-4 sm:left-0 mt-2 w-52 bg-white border rounded-lg shadow-lg py-4 z-50">
                        <ul className="text-gray-700">
                          <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              handleNavigation(AppRoutesPaths.dashboard.eventManagement);
                              setIsMenuOpen(false);
                              setIsAdminOpen(false);
                            }}
                          >
                            Event Management
                          </li>
                          <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              handleNavigation(AppRoutesPaths.dashboard.projectManagement);
                              setIsMenuOpen(false);
                              setIsAdminOpen(false);
                            }}
                          >
                            Project Management
                          </li>
                          <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              handleNavigation(AppRoutesPaths.dashboard.managecommunity);
                              setIsMenuOpen(false);
                              setIsAdminOpen(false);
                            }}
                          >
                            Manage Community
                          </li>
                           <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              handleNavigation(AppRoutesPaths.dashboard.managecontribution);
                              setIsMenuOpen(false);
                              setIsAdminOpen(false);
                            }}
                          >
                            Manage Community
                          </li>
                          <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              handleNavigation(AppRoutesPaths.dashboard.communityRequests);
                              setIsMenuOpen(false);
                              setIsAdminOpen(false);
                            }}
                          >
                            Community Requests
                          </li>
                          <li
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => setIsVotingOpen(!isVotingOpen)}
                          >
                            <div className="flex justify-between items-center">
                              Voting Management
                              <io.IoMdArrowDropdown
                                className={`text-gray-600 transition-transform ${
                                  isVotingOpen ? 'rotate-180' : 'rotate-0'
                                }`}
                              />
                            </div>
                          </li>
                          {/* Nested Voting Options */}
                          {isVotingOpen && (
                            <ul className="pl-4">
                              <li
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                  handleNavigation(AppRoutesPaths.dashboard.initiateVote);
                                  setIsMenuOpen(false);
                                  setIsAdminOpen(false);
                                  setIsVotingOpen(false);
                                }}
                              >
                                Initiate vote
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                  handleNavigation(AppRoutesPaths.dashboard.ongoingVote);
                                  setIsMenuOpen(false);
                                  setIsAdminOpen(false);
                                  setIsVotingOpen(false);
                                }}
                              >
                                Ongoing vote
                              </li>
                              <li
                                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                  handleNavigation(AppRoutesPaths.dashboard.voteResults);
                                  setIsMenuOpen(false);
                                  setIsAdminOpen(false);
                                  setIsVotingOpen(false);
                                }}
                              >
                                Vote results
                              </li>
                            </ul>
                          )}

                          <hr className="my-2 border-gray-600" />
                        {/* Add the Account and logout here */}
                         <li
                            className="flex items-center gap-2 px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                               onClick={() => handleNavigation(AppRoutesPaths.dashboard.account)}
                              >
                              <img src={mainuser} alt="Sahelion Logo" className="w-5 h-5" />
                                  <span className="text-gray-700">Account</span>
                              </li>
                              <li
                                className="flex items-center gap-2 px-3 py-1.5 hover:bg-gray-100  cursor-pointer"
                                 onClick={handleLogout}
                              >
                               <IoLogOut className="w-5 h-5" />
                                 <span  className='text-gray-700'>Logout</span>
                            </li>
                        </ul>
                      </div>
                    )}
                  </div>
                ) : currentUser?.role === "user" ? (
                <div className="flex items-center ml-8 p-4 gap-2">
                    <div className="flex flex-row md:flex-col lg:flex-row items-center gap-4">
                      <img
                          src={user}
                          alt="User"
                          className="w-8 h-8 rounded-full border border-gray-400 object-cover -ml-9 p-2"
                        />
                        <span className="text-gray-700 font-medium">
                          {currentUser?.firstName || "User"}
                        </span>
                      </div>
                      <div ref={userDropdownRef} className="relative">
                          <RiArrowDropDownLine
                            className="w-6 h-6 text-gray-700 cursor-pointer"
                            onClick={() => setUserDropdownOpen((open) => !open)}
                          />
                          {userDropdownOpen && (
                            <div className="absolute right-0 mt-2 w-24 bg-white border rounded-lg shadow-lg py-2 z-50">
                                  <button
                                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-gray-700"
                                    onClick={() => {
                                      handleNavigation(AppRoutesPaths.dashboard.account);
                                      setUserDropdownOpen(false);
                                      setIsMenuOpen(false);
                                    }}
                                  >
                                    Account
                                  </button>
                                  <button
                                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                                    onClick={handleLogout}
                                  >
                                    Logout
                                  </button>
                                </div>
                          )

                          }
                      </div>
                  </div>
                ) : null}

                <div className="flex items-center gap-4 cursor-pointer p-3">
                  <img
                    src={appointment}
                    alt="Sahelion Logo"
                    className="w-6 h-6"
                  />
                  <span
                    className="text-gray-700"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.events);
                      setIsMenuOpen(!isMenuOpen);
                    }}
                  >
                    Events
                  </span>
                </div>
                {isSubscriber && (
                 <div className="flex items-center gap-4 cursor-pointer p-3">
                  <span
                    className="text-gray-700 flex gap-4"
                    onClick={() => {
                      handleNavigation(AppRoutesPaths.dashboard.projectRankMultiVotes);
                      setIsMenuOpen(!isMenuOpen);
                    }}
                  >
                     <FaVoteYea  className='w-6 h-6'/>
                     Projects Vote
                  </span>
                </div>
                )}
                {/* Projects Button */}
                <div className="relative p-4">
                  <div
                    className="flex items-center gap-4 border px-4 py-2 rounded-full cursor-pointer shadow-sm bg-white"
                    onClick={() => setIsOpen(!isOpen)}
                  >
                    <img src={start} alt="Projects Icon" className="w-6 h-6" />
                    <span className="text-gray-700 font-medium">Projects</span>
                    <io.IoMdArrowDropdown className="text-gray-600" />
                  </div>

                  {/* Nested Dropdown Menu */}
                  {isOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg py-2 z-50">
                      <ul className="text-gray-700">
                        {isSubscriber &&
                         <li
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.ongoingVote);
                            setIsMenuOpen(!isMenuOpen);
                          }}
                        >
                          Ongoing votes
                        </li>}
                        <li
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.ongoingProjects);
                            setIsMenuOpen(!isMenuOpen);
                          }}
                        >
                          Ongoing projects
                        </li>
                        {isSubscriber &&
                        <li
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleNavigation(AppRoutesPaths.dashboard.projectHistory);
                            setIsMenuOpen(!isMenuOpen);
                          }}
                        >
                          Projects history
                        </li>}
                      </ul>
                    </div>
                  )}
                </div>

                
              </div>
            )}
          </div>
        </div>
        

        {/* Replace the conditional rendering with Outlet */}
        <div className="mt-8 p-8 md:p-24 sm:mt-12 md:mt-24">
          <Outlet />
        </div>
      </div>
    </div>
  );
};



