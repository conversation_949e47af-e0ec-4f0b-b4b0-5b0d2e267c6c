import React, { useState, useEffect } from 'react';
import user from '../../assets/dashboard/user.png';
import share from '../../assets/dashboard/share.png';
import whatsapp from '../../assets/dashboard/whatsapp.png';
import { SocialService } from '../../services/SocialService'; 
import { ContributionService } from '../../services/ContributionService'; 
import { ChooseMembership } from './chooseMembership';
import donation from "../../assets/images/donation.png"
import cashondelivery from "../../assets/images/cash-on-delivery.png"
import operation from "../../assets/images/operation.png"
import deployment from "../../assets/images/deployment.png"
import { Link } from 'react-router-dom';
import { UserService } from '../../services/userService';
import { ImCross } from "react-icons/im";
import { FaRocket } from 'react-icons/fa';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { AppRoutesPaths } from '../../route/app_route';
import { useAuthentication } from '../../components/utils/provider';
import ShareLinksModal from '../modals/ShareLinksModal';
import { CiLock } from "react-icons/ci";
import { FiX } from "react-icons/fi";


export const DashboardHome = () => {

  const navigate = useNavigate();
  const { currentUser } = useAuthentication();
  const [inviteOpen, setInviteOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteEmails, setInviteEmails] = useState(['']);
  const [inviteStatus, setInviteStatus] = useState('');
  const [isSubscriber, setIsSubscriber] = useState(currentUser.isSubscribed);
  // const [isSubscriber, setIsSubscriber] = useState(true); 
  const [loading, setLoading] = useState(false);
  const [currentContribution, setCurrentContribution] = useState('0');
  const [contributionCurrency, setContributionCurrency] = useState('USD');
  const [contributionLoading, setContributionLoading] = useState(false);
  const [amountUpgrade, setAmountUpgrade] = useState(null);
  // const [hasContribution, setHasContribution] = useState(true);

  const [membersCount, setMembersCount] = useState(0);
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [summary, setSummary] = useState(null);

  const [subscriptionExpiredDiv, showSubscriptionExpiredDiv] = useState(false);
  const [subscriptionExpiredModel, showSubscriptionExpiredModel] = useState(false);

  useEffect(() => {
        if (subscriptionExpiredModel) {
          document.body.style.overflow = 'hidden';
        } else {
          document.body.style.overflow = 'auto';
        }
      }, [subscriptionExpiredModel]);

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const count = await UserService.getAllUserCount();
        setMembersCount(count || 0);
      } catch (error) {
        setMembersCount(0);
      }
    };

    const fetchCurrentContribution = async () => {
      setContributionLoading(true);
      try {
        console.log('Fetching current contribution...');
        const result = await ContributionService.getCurrentContribution();
        console.log('Contribution result:', result);
        
        if (result.success && result.data) {
          console.log('Setting contribution amount:', result.data.amount);
          setCurrentContribution(result.data.amount || '0');
          setContributionCurrency(result.data.currency || 'USD');
          setAmountUpgrade(result.data.amountUpgrade || null);
          // setHasContribution(true);
        } else {
          console.log('No contribution found or failed:', result.message);
          setCurrentContribution('0');
          setContributionCurrency('USD');
          setAmountUpgrade(null);
        }
      } catch (error) {
        console.error('Error fetching current contribution:', error);
        setCurrentContribution('0');
        setContributionCurrency('USD');
        setAmountUpgrade(null);
      } finally {
        setContributionLoading(false);
      }
    };

    fetchMembers();
    if (isSubscriber) {
      fetchCurrentContribution();
    }
  }, [isSubscriber]);

  const handleInvite = async () => {
    setLoading(true);
    setInviteStatus('');
    try {
      // Filter out empty emails
      const validEmails = inviteEmails.filter(email => email.trim() !== '');
      
      if (validEmails.length === 0) {
        setInviteStatus('Please enter at least one email address');
        setLoading(false);
        return;
      }

      // Send invites to all valid emails
      for (const email of validEmails) {
        await SocialService.inviteFriend(email.trim());
      }
      
      setInviteStatus(`Invitation${validEmails.length > 1 ? 's' : ''} sent successfully to ${validEmails.length} email${validEmails.length > 1 ? 's' : ''}!`);
      setInviteEmails(['']);
      setInviteEmail('');
      setInviteOpen(false);
    } catch (err) {
      setInviteStatus(err.message);
    }
    setLoading(false);
  };

  const addEmailField = () => {
    setInviteEmails([...inviteEmails, '']);
  };

  const removeEmailField = (index) => {
    if (inviteEmails.length > 1) {
      const newEmails = inviteEmails.filter((_, i) => i !== index);
      setInviteEmails(newEmails);
    }
  };

  const updateEmailField = (index, value) => {
    const newEmails = [...inviteEmails];
    newEmails[index] = value;
    setInviteEmails(newEmails);
  };

  useEffect(() => {
  ContributionService.getDashboardSummary().then(res => {
    if (res.success) setSummary(res.data);
  });
}, []);

  return (
    <>

      {subscriptionExpiredDiv && (
        <div className="md:w-[75vw] lg:w-[50vw] relative grid grid-cols-8 gap-x-5 border border-BP-yellow rounded-xl bg-[#FFFF000D] mx-auto mb-10 max-sm:py-6 max-sm:px-3 p-6">
          <button
              className="absolute top-1.5 md:top-4 right-1.5 md:right-4 text-BP-yellow hover:text-yellow-700 text-xl"
              aria-label="Close"
              onClick={() => showSubscriptionExpiredDiv(false)}
            >
              <FiX />
            </button>
          <div className="col-span-1 flex items-center justify-center w-9 h-9 md:w-14 md:h-14 rounded-full bg-[#E1AB0D40] mr-5">
                <CiLock className="text-xl md:text-3xl text-BP-yellow" />
              </div>
          <div className="col-span-7 text-BP-black text-xs md:text-xl">Your account has been limited due to a missed monthly contribution.</div>
          <div className="col-span-1"></div>
          <div className="col-span-7">
            <button
                  className="mt-4 bg-BP-yellow text-white px-8 py-2 rounded-lg shadow hover:bg-yellow-600 transition max-md:text-xs"
                >
                  Contribute Now
                </button>
          </div>
        </div>
      )}

      {subscriptionExpiredModel && (
        <div style={{ zIndex: 1000 }} className="h-[110vh] w-full bg-BP-black bg-opacity-50 fixed -top-10 left-0 flex justify-center items-center">
          <div className="md:w-[75vw] lg:w-[50vw] relative grid grid-cols-8 rounded-xl bg-white mx-auto mb-10 p-6">
            <button
              className="absolute top-4 right-4 text-BP-yellow hover:text-yellow-700 text-xl"
              aria-label="Close"
              onClick={() => showSubscriptionExpiredModel(false)}
            >
              <FiX />
            </button>
            <div className="col-span-1 flex items-center justify-center w-9 h-9 md:w-14 md:h-14 rounded-full bg-[#E1AB0D40] mr-5">
              <CiLock className="text-xl md:text-3xl text-BP-yellow" />
            </div>
            <div className="col-span-7 text-BP-black text-xs md:text-xl">Your account has been limited due to a missed monthly contribution.</div>
            <div className="col-span-1"></div>
            <div className="col-span-7">
              <button
                className="mt-4 bg-BP-yellow text-white px-8 py-2 rounded-lg shadow hover:bg-yellow-600 transition max-md:text-xs"
              >
                Contribute Now
              </button>
            </div>
          </div>
        </div>
      )}
    
      {/* Move all the dashboard home content here */}
      <div className="flex justify-center items-center sm:block">

        {!isSubscriber ? (

          <ChooseMembership />

        ) : (


          <div className="sm:w-full min-w-[90vw] md:min-w-[60vw] lg:min-w-[40vw] xl:min-w-[30vw] 2xl:min-w-[25vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h2 className="text-2xl sm:text-3xl font-semibold text-gray-800 text-left">
                  Monthly Contribution
                </h2>
                {amountUpgrade && amountUpgrade !== currentContribution ? (
                  <div className="mt-2 text-left">
                    <p className="text-green-600 font-medium text-sm">
                      Next Month: {contributionCurrency === 'USD' ? `$${amountUpgrade}` : `${amountUpgrade} SHLN`}/month
                    </p>
                    <p className="text-gray-500 text-sm">
                      Current Contribution
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500 mt-2 text-left">
                    Current Contribution
                  </p>
                )}
              </div>
            </div>

            {/* Adjusted layout for mobile & small screens */}
            <div className="flex flex-row justify-between items-center mt-4">
              {/* Price */}
              <div className="flex items-baseline space-x-2">

                <div className="font-bold text-lg sm:text-2xl text-gray-400">
                  {contributionCurrency === 'USD'
                    ? `$ ${currentContribution}`
                    : `${currentContribution} SHLN`}
                </div>

                <span className="text-gray-400 text-lg sm:text-xl">/month</span>


              </div>

              {/* Button - Positioned next to the price */}
              <button
                className="bg-purple-500 text-white px-14 py-3 rounded-[24px] shadow-md text-[14px] sm:text-[18px] font-medium hover:bg-purple-600 transition-colors"
                onClick={() => navigate(AppRoutesPaths.monthlyContributionChangeDate)}
              // disabled={contributionLoading}
              >
                change
              </button>
            </div>

            {/* Upgrade notification */}
            {amountUpgrade && amountUpgrade !== currentContribution && (
              <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-yellow-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-yellow-800">
                      Contribution Update Scheduled
                    </h4>
                    <p className="mt-1 text-sm text-yellow-700">
                      Starting from next month, your contribution will be updated to{' '}
                      <span className="font-semibold">
                        {contributionCurrency === 'USD' 
                          ? `$${amountUpgrade}` 
                          : `${amountUpgrade} SHLN`}
                      </span>
                      /month
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex justify-center items-center sm:block">
        {/* Community Section */}
        <div className="sm:w-full min-w-[90vw] md:min-w-[80vw] lg:min-w-[60vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
          <h2 className="text-lg font-bold text-gray-700 ml-1 sm:ml-1  mt-[-10px]">
            Community
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 mt-6">
            {/* Invite a Friend */}
            <div className="p-6 rounded-xl text-center border border-gray-300 max-w-[320px] mx-auto">
              <div className="text-3xl mb-4 flex justify-center">
                <img
                  src={user}
                  alt="Invite a Friend"
                  className="w-12 h-12 mx-auto"
                />
              </div>
              <h3 className="font-semibold text-gray-700 mb-2">
                Invite a Friend
              </h3>
              <p className="text-gray-500 text-sm">
                Invite friends to join the DAO and enjoy benefits.
              </p>
              <button
                className="bg-yellow-500 text-white px-12 py-2 rounded-[24px] mt-4"
                onClick={() => setInviteOpen(true)}
              >
                Invite
              </button>
              {/* Invite Modal */}
              {inviteOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
                  <div className="bg-white rounded-3xl p-8 shadow-2xl w-full max-w-md mx-4 relative">
                    {/* Close button */}
                    <button
                      onClick={() => {
                        setInviteOpen(false);
                        setInviteStatus('');
                        setInviteEmail('');
                        setInviteEmails(['']);
                      }}
                      className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
                      disabled={loading}
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>

                    <div className="text-center mb-8">
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">
                        Invite Friends to DAO
                      </h3>
                    </div>

                    <div className="mb-6">
                      <label className="block text-gray-700 text-sm font-medium mb-3">
                        Enter Email Address
                      </label>
                      
                      {/* Email inputs */}
                      {inviteEmails.map((email, index) => (
                        <div key={index} className="relative mb-4">
                          <input
                            type="email"
                            placeholder="<EMAIL>"
                            className="w-full border-2 border-green-400 rounded-lg px-4 py-3 bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:border-green-500 transition-colors"
                            value={email}
                            onChange={(e) => updateEmailField(index, e.target.value)}
                            disabled={loading}
                          />
                          {(email || inviteEmails.length > 1) && (
                            <button
                              onClick={() => {
                                if (inviteEmails.length > 1) {
                                  removeEmailField(index);
                                } else {
                                  updateEmailField(index, '');
                                }
                              }}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
                              disabled={loading}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          )}
                        </div>
                      ))}

                      {/* Add another email button */}
                      <button
                        className="w-full py-3 text-yellow-500 font-medium hover:text-yellow-600 transition-colors bg-gray-50 rounded-lg border-2 border-dashed border-gray-200 hover:border-yellow-300"
                        onClick={addEmailField}
                        disabled={loading}
                      >
                        + Add another email
                      </button>
                    </div>

                    {/* Send Invite button */}
                    <button
                      className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-4 rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleInvite}
                      disabled={loading || !inviteEmails.some(email => email.trim() !== '')}
                    >
                      {loading ? 'Sending...' : 'Send Invite'}
                    </button>

                    {inviteStatus && (
                      <div
                        className={`mt-4 p-3 rounded-lg text-sm text-center ${inviteStatus.includes('success') 
                          ? 'bg-green-50 text-green-700 border border-green-200' 
                          : 'bg-red-50 text-red-700 border border-red-200'
                        }`}
                      >
                        {inviteStatus}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Share */}
            {/* Ensure user can share the community to there social media platforms linkedin, instagram, twitter, facebook */}
            <div className="p-6 rounded-xl text-center border border-gray-300 max-w-[320px] mx-auto">
              <div className="text-3xl mb-4 flex justify-center">
                <img src={share} alt="Share" className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="font-semibold text-gray-700 mb-2">Share</h3>
              <p className="text-gray-500 text-sm">
                Let friends know you're a proud member of the community!
              </p>
              <button
                className="bg-purple-500 text-white px-12 py-2 rounded-[24px] mt-4"
                onClick={() => setShareModalOpen(true)}
              >
                Share
              </button>
            </div>

            {/* Join WhatsApp */}
            <div className="p-6 rounded-xl text-center border border-gray-300 max-w-[320px] mx-auto">
              <div className="text-3xl mb-4 flex justify-center">
                <img
                  src={whatsapp}
                  alt="Join WhatsApp"
                  className="w-12 h-12 mx-auto"
                />
              </div>
              <h3 className="font-semibold text-gray-700 mb-2">
                Join WhatsApp
              </h3>
              <p className="text-gray-500 text-sm">
                Connect with other members in our WhatsApp group.
              </p>
              <button onClick={isSubscriber ? () => { } : () => { setShowComingSoon(true) }} className="bg-green-500 text-white px-12 py-2 rounded-[24px] mt-4">
                Join
              </button>
            </div>
          </div>
        </div>
      </div>

      {showComingSoon && (

        <div style={{ zIndex: 1000 }} className="h-[110vh] w-full bg-BP-black bg-opacity-50 fixed -top-10 left-0 flex justify-center items-center">
          <div className="w-[90vw] max-w-[500px] h-96 sm:h-80 -mt-16 rounded-xl bg-gradient-to-br from-[#2C5282] to-[#58759D] relative">

            <ImCross onClick={() => { setShowComingSoon(false) }} className='absolute right-3 top-3 rounded-full hover:bg-gray-500 p-1.5 hover:cursor-pointer' size={28} />


            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="flex flex-col justify-center items-center h-full text-center px-4"
            >
              <FaRocket
                className="text-6xl mb-4 text-BP-hovered-gray animate-bounce drop-shadow-lg"
              />

              <h1 className="text-4xl font-bold mb-2 text-BP-lightbaige drop-shadow-sm">
                Become Contributor
              </h1>

              <div className="h-1 w-24 rounded-full mb-4 bg-BP-opacited-white opacity-70" />

              <p className="text-base text-BP-opacited-white max-w-md">
                You can use this feature only if you are a subscriber. Please subscribe to unlock this feature.
              </p>

              <button onClick={() => { navigate(AppRoutesPaths.monthlyContributionUpdate) }} className={`bg-BP-dark-grayish-blue hover:bg-BP-hovered-gray text-BP-opacited-white hover:text-BP-dark-grayish-blue font-semibold px-8 py-1 rounded-lg shadow transition flex items-center gap-2 max-sm:text-xs my-2`}>
                Start contributing monthly
              </button>
            </motion.div>

          </div>
        </div>

      )}

      <div className="flex justify-center items-center sm:block">

        {/* Transparency & Impact */}
        {/* <div className="sm:w-full min-w-[90vw] md:min-w-[80vw] lg:min-w-[60vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto"> */}
        {/* <h2 className="text-lg font-semibold text-gray-700">
            Transparency & Impact
          </h2> */}

        {/* <p className="text-gray-500 text-sm mt-2 sm:ml-6">
            This section will display transparency details from the landing
            page.
          </p> */}

        <div className="bg-[#ffffff] text-black py-16 px-6 md:px-16 lg:px-24 rounded-[16px]  mx-auto">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold font-title mb-12 text-left">Transparency & Impact</h2>
            <div className="flex flex-col">
              <div className="flex flex-col md:flex-row justify-between gap-6 mb-12">
                <div className="flex gap-4 items-center  pr-2">
                  <div className="w-0.5 h-16 bg-yellow-500"></div>
                  <p className="text-lg font-body">Live updates on fund distribution and project progress</p>
                </div>

                <div className="flex gap-4 items-center  pr-2">
                  <div className="w-0.5 h-16 bg-yellow-500"></div>
                  <p className="text-lg font-body">100% of funds go to research and healthcare projects</p>
                </div>

                <div className="flex gap-4 items-center  pr-2">
                  <div className="w-0.5 h-16 bg-yellow-500"></div>
                  <p className="text-lg font-body">Community-driven decision-making</p>
                </div>
              </div>

              <div className="space-y-4">
               <div className=" w-full max-w-6xl h-[124px] bg-[#F8F5ED80] rounded-[20px] border border-gray-200 flex items-center px-8 py-6 shadow-sm mx-auto">
                  <div className="flex items-center mr-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-transparent border-2 border-yellow-500 rounded-full relative">
                      <div className="w-6 h-6 bg-yellow-500 rounded-full absolute animate-pulseglow"></div>
                    </div>
                  </div>
                  <div className="text-3xl font-bold font-title mr-6 text-black">{membersCount}</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto text-lg">Active members</div>
                </div>

                 <div className=" w-full max-w-6xl h-[124px] bg-[#F8F5ED80] rounded-[20px] border border-gray-200 flex items-center px-8 py-6 shadow-sm mx-auto">
                  <div className="flex items-center mr-2">
                    <img src={donation} alt="Contributions icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4 text-black">$ {summary?.monthlyContributions?.toLocaleString() ?? 0}</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly contributions</div>
                </div>

                 <div className=" w-full max-w-6xl h-[124px] bg-[#F8F5ED80] rounded-[20px] border border-gray-200 flex items-center px-8 py-6 shadow-sm mx-auto">
                  <div className="flex items-center mr-2">
                    <img src={cashondelivery} alt="Cash icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4 text-black">${summary?.cashOnHand?.toLocaleString() ?? 0}</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash on hand</div>
                </div>

                <div className=" w-full max-w-6xl h-[124px] bg-[#F8F5ED80] rounded-[20px] border border-gray-200 flex items-center px-8 py-6 shadow-sm mx-auto">
                  <div className="flex items-center mr-2">
                    <img src={operation} alt="Overheads icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold mr-4 font-title text-back">$ {summary?.monthlyOverheads?.toLocaleString() ?? 0}</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly overheads</div>
                </div>

                 <div className=" w-full max-w-6xl h-[124px] bg-[#F8F5ED80] rounded-[20px] border border-gray-200 flex items-center px-8 py-6 shadow-sm mx-auto">
                  <div className="flex items-center mr-2">
                    <img src={deployment} alt="Deployed cash icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4 text-black">$ {summary?.cashDeployed?.toLocaleString() ?? 0}</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash deployed</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* </div> */}
      </div>

      {/* Share Links Modal */}
      <ShareLinksModal 
        isOpen={shareModalOpen} 
        onClose={() => setShareModalOpen(false)} 
      />
    </>
  );
};
