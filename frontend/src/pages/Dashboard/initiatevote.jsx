import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { LuUpload } from 'react-icons/lu';
import { FiEdit2 } from 'react-icons/fi';
import { RiDeleteBinLine } from 'react-icons/ri';
import { Calendar, DollarSign } from 'lucide-react';
import { FaPlus } from 'react-icons/fa';
import { IoIosArrowDown } from "react-icons/io";
import Swal from "sweetalert2";
import { VoteService } from '../../services/voteService';
import { AppRoutesPaths } from "../../route/app_route";
import {DeleteVoteModal} from "../modals/DeleteVoteModal.jsx";

export const Initiatevote = () => {
  const navigate = useNavigate();
  const [votingType, setVotingType] = useState("multi");
  const [fundingDist, setFundingDist] = useState('total-all');
  const [winningProjectsCount, setWinningProjectsCount] = useState(1);
  const [showWinningCountDropdown, setShowWinningCountDropdown] = useState(false);
  const [projects, setProjects] = useState([]);
  const [newProject, setNewProject] = useState({
    title: "",
    description: "",
    location: "",
    image: null,
    previewImage: null,
  });
  const [voteDetails, setVoteDetails] = useState({
    startDate: "",
    endDate: "",
    fundingAllocation: "",
     voteTitle: ""
  });
  const [editingProjectIndex, setEditingProjectIndex] = useState(null);
  // State to  control the delete modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteIndex, setDeleteIndex] = useState(null);
  const [confirmText, setConfirmText] = useState("");
  const startDateRef = useRef(null);
  const endDateRef = useRef(null);
  const [showFull, setShowFull] = useState({});

  const isRankedChoice = votingType === "ranked";
  const isMultiVoting = votingType === "multi";

  const toggleShowFull = (index) => {
    setShowFull((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  // --- Validation helpers ---
  const isValidDate = (date) => !!date && !isNaN(new Date(date).getTime());
  const isValidAmount = (amount) => !isNaN(Number(amount)) && Number(amount) >= 100;

  // --- Add Project Logic ---
  const handleAddProject = () => {
    if (!newProject.title || !newProject.description) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Please fill in project title and description!',
      });
      return;
    }
    if (newProject.description.length < 50) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Project description must be at least 50 characters!',
      });
      return;
    }

    if (editingProjectIndex !== null) {
      // Update existing project
      const updatedProjects = [...projects];
      updatedProjects[editingProjectIndex] = { ...newProject };
      setProjects(updatedProjects);
      setEditingProjectIndex(null);
    } else {
      // Add new project
      setProjects([...projects, { ...newProject }]);
    }

    setNewProject({
      title: "",
      description: "",
      location: "",
      image: null,
      previewImage: null,
    });
  };

  // Show modal instead of deleting immediately
  const handleAskRemoveProject = (index) => {
    setDeleteIndex(index);
    setShowDeleteModal(true);
    setConfirmText("");
  };

  // Called after confirmation in modal
  const handleConfirmRemoveProject = () => {
    if (deleteIndex !== null) {
      const updatedProjects = [...projects];
      updatedProjects.splice(deleteIndex, 1);
      setProjects(updatedProjects);
      setDeleteIndex(null);
      setShowDeleteModal(false);
      setConfirmText("");
    }
  };

  // --- Image Change Logic ---
  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setNewProject({
          ...newProject,
          image: file,
          previewImage: reader.result
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // --- Main Submit Logic ---
  const handleSubmitVote = async () => {
    // --- Validation ---
    if (!isValidDate(voteDetails.startDate) || !isValidDate(voteDetails.endDate)) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Please provide valid start and end dates!' });
      return;
    }
    if (!isValidAmount(voteDetails.fundingAllocation)) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Funding allocation must be at least $100!' });
      return;
    }
    if (isRankedChoice && projects.length < 2) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Ranked voting requires at least 2 projects!' });
      return;
    }
    if (projects.length === 0) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Please add at least one project!' });
      return;
    }
    const today = new Date();
    today.setHours(0,0,0,0);
    const start = new Date(voteDetails.startDate);
    const end = new Date(voteDetails.endDate);

    if (start < today) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Start date cannot be before today!' });
      return;
    }
    if (end <= start) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'End date must be after start date!' });
      return;
    }
    const maxDurationMs = 14 * 24 * 60 * 60 * 1000;
    if (end - start > maxDurationMs) {
      Swal.fire({ icon: 'error', title: 'Oops...', text: 'Voting duration cannot exceed 14 days!' });
      return;
    }

    try {
      const totalFunding = Number(voteDetails.fundingAllocation);

      // Create FormData
      const formData = new FormData();
      formData.append('votingType', votingType);
      formData.append('startDate', new Date(voteDetails.startDate).toISOString());
      formData.append('endDate', new Date(voteDetails.endDate).toISOString());
      formData.append('fundingAllocation', totalFunding);
      formData.append('voteTitle', voteDetails.voteTitle);

      // Append projects as JSON (without images)
      const projectsData = projects.map((project, idx) => ({
        title: project.title,
        description: project.description,
        // We'll attach images separately
        index: idx,
      }));
      formData.append('projects', JSON.stringify(projectsData));

      // Attach each image file with a unique key
      projects.forEach((project, idx) => {
        if (project.image) {
          formData.append('uploaded_file', project.image); // backend should expect array of files
        }
      });

      if (isMultiVoting) {
        let fundingDistributionValue = fundingDist === "per-winning" ? "per_project" : "total";
        formData.append('fundingDistribution', fundingDistributionValue);
        formData.append('numberOfWinningProjects', winningProjectsCount);
      }

      // Send FormData to backend
      const success = await VoteService.createVote(formData, true); // true = isFormData

      if (success) {
        Swal.fire({
          icon: 'success',
          title: 'Vote Created!',
          text: 'Your voting session has been created successfully!',
        }).then(() => {
          navigate(AppRoutesPaths.dashboard.projectRankMultiVotes);
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to create vote. Please try again.',
        });
      }
    } catch (error) {
      console.log(error?.response?.data);
      const backendMsg = error?.response?.data?.message;
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: backendMsg || error.message || 'Failed to create vote. Please try again.',
      });
    }
  };

  const handleEditProject = (index) => {
    const project = projects[index];
    setNewProject({
      title: project.title,
      description: project.description,
      location: project.location || "",
      image: project.image || null,
      previewImage: project.previewImage || null,
    });
    setEditingProjectIndex(index);
  };

  const getVotingDuration = () => {
    if (!voteDetails.startDate || !voteDetails.endDate) return 0;
    const start = new Date(voteDetails.startDate);
    const end = new Date(voteDetails.endDate);
    const diff = end - start;
    return diff > 0 ? Math.ceil(diff / (1000 * 60 * 60 * 24)) : 0;
  };

  return (
    <div className="flex flex-col pb-12 bg-[#fcfbf7] items-center rounded-xl shadow-lg w-full min-w-[70vw] sm:w-full md:w-[70vw] lg:max-w-[50vw] mx-auto">
      {/* Header */}
      <div className="bg-purple-600 h-32 flex flex-col space-y-2 justify-start rounded-t-xl shadow-lg p-4 sm:p-6 w-full">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-white">Initiate New Voting</h2>
        <p className="text-white text-sm sm:text-base">Create a new community voting session</p>
      </div>

      <div className="px-4 sm:px-6 md:px-8 py-3 mt-3 space-y-5 w-full max-w-4xl mx-auto">
        <div>
          <h1 className="text-xl sm:text-2xl font-thin text-gray-900">Voting Configuration</h1>
          <hr className="border-gray-300 my-2" />
        </div>

        <div className="space-y-5">
          {/* Voting Type and Duration */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* Voting Type */}
            <div className="flex-1 m-3">
              <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Voting type</h2>
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                    <input
                      type="radio"
                      id="multi-voting"
                      name="voting-type"
                      value="multi-voting"
                      checked={isMultiVoting}
                      onChange={() => setVotingType("multi")}
                      className="appearance-none w-3 h-3 sm:w-2 sm:h-2 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                    />
                  </div>
                  <label htmlFor="multi-voting" className="text-gray-600 text-sm sm:text-base">
                    Up-Down Voting (Yes/No)
                  </label>
                </div>
                <div className="flex items-center">
                  <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                    <input
                      type="radio"
                      id="ranked-choice"
                      name="voting-type"
                      value="ranked-choice"
                      checked={isRankedChoice}
                      onChange={() => setVotingType("ranked")}
                      className="appearance-none w-3 h-3 sm:w-2 sm:h-2 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                    />
                  </div>
                  <label htmlFor="ranked-choice" className="text-gray-600 text-sm sm:text-base">
                    Ranked Choice Voting
                  </label>
                </div>
              </div>
            </div>

            {/* Voting Duration */}
            <div className="flex-1 m-3">
              <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Voting Duration</h2>
              <div className="flex flex-col sm:flex-row gap-2">
                <div className="flex-1">
                  <label htmlFor="startDateInput" className="block text-xs sm:text-sm text-gray-600 mb-1">
                    Start date
                  </label>
                  <div className="flex items-center gap-2 border-2 p-2 rounded-lg overflow-hidden bg-white">
                    <span className="relative group">
                      <Calendar
                        className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 cursor-pointer"
                        onClick={() =>
                          startDateRef.current && startDateRef.current.showPicker
                            ? startDateRef.current.showPicker()
                            : startDateRef.current.focus()
                        }
                        tabIndex={0}
                        aria-label="Pick start date"
                        onFocus={e => e.target.parentElement.classList.add('show-tooltip')}
                        onBlur={e => e.target.parentElement.classList.remove('show-tooltip')}
                      />
                      {voteDetails.startDate && voteDetails.endDate && (
                        <span
                          className="absolute left-6 top-1/2 -translate-y-1/2 z-10 bg-yellow-100 text-gray-800 text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity whitespace-nowrap pointer-events-none"
                          style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.07)' }}
                        >
                          Voting duration: {getVotingDuration()} day{getVotingDuration() === 1 ? "" : "s"} (max 14)
                        </span>
                      )}
                    </span>
                    <input
                      ref={startDateRef}
                      id="startDateInput"
                      type="date"
                      min={new Date().toISOString().slice(0, 10)}
                      value={voteDetails.startDate}
                      onChange={(e) => setVoteDetails({ ...voteDetails, startDate: e.target.value })}
                      className="flex-1 outline-none text-black px-1 w-full text-xs sm:text-sm bg-white"
                      style={{ width: "120px" }}
                      onFocus={e => e.target.previousSibling.classList.add('show-tooltip')}
                      onBlur={e => e.target.previousSibling.classList.remove('show-tooltip')}
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <label htmlFor="endDateInput" className="block text-xs sm:text-sm text-gray-600 mb-1">
                    End date
                  </label>
                  <div className="flex items-center gap-2 border-2 p-2 rounded-lg overflow-hidden bg-white">
                    <span className="relative group">
                      <Calendar
                        className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 cursor-pointer"
                        onClick={() =>
                          endDateRef.current && endDateRef.current.showPicker
                            ? endDateRef.current.showPicker()
                            : endDateRef.current.focus()
                        }
                      />
                      {/* Tooltip for end date */}
                      {voteDetails.startDate && voteDetails.endDate && (
                          <span
                            className="absolute left-6 top-1/2 -translate-y-1/2 z-10 bg-yellow-100 text-gray-800 text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity whitespace-nowrap pointer-events-none"
                            style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.07)' }}
                          >
                            Voting duration: {getVotingDuration()} day{getVotingDuration() === 1 ? "" : "s"} (max 14)
                          </span>
                        )}
                    </span>
                    <input
                      ref={endDateRef}
                      id="endDateInput"
                      type="date"
                      min={voteDetails.startDate || new Date().toISOString().slice(0, 10)}
                      max={
                        voteDetails.startDate
                          ? new Date(new Date(voteDetails.startDate).getTime() + 13 * 24 * 60 * 60 * 1000)
                              .toISOString()
                              .slice(0, 10)
                          : new Date(new Date().getTime() + 13 * 24 * 60 * 60 * 1000)
                              .toISOString()
                              .slice(0, 10)
                      }
                      value={voteDetails.endDate}
                      onChange={(e) => setVoteDetails({ ...voteDetails, endDate: e.target.value })}
                      className="flex-1 outline-none text-black px-1 w-full text-xs sm:text-sm bg-white"
                      style={{ width: "120px" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Vote Title
              field 
             */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* Funding Allocation */}
            <div className="flex flex-col justify-end space-y-1 w-full max-w-xs m-3 flex-1">
              <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Funding Allocation</h2>
              <div className="flex items-center gap-2 border-2 p-2 rounded-lg overflow-hidden w-full">
                <DollarSign className="w-4 h-4 text-purple-600 flex-shrink-0" />
                <input
                  type="number"
                  min={100}
                  placeholder="Amount"
                  className="flex-1 outline-none text-black px-1 w-full text-xs sm:text-sm bg-transparent"
                  style={{ width: "120px" }}
                  value={voteDetails.fundingAllocation}
                  onChange={(e) => setVoteDetails({ ...voteDetails, fundingAllocation: e.target.value })}
                />
              </div>
            </div>
            {/* Main Vote Title */}
            <div className="flex flex-col justify-end space-y-1 w-full max-w-xs m-3 flex-1">
              <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Main vote title</h2>
              <input
                type="text"
                placeholder="Enter main vote title"
                className="w-full border-2 p-2 rounded-lg outline-none text-black text-xs sm:text-sm bg-transparent"
                value={voteDetails.voteTitle || ""}
                onChange={e => setVoteDetails({ ...voteDetails, voteTitle: e.target.value })}
              />
            </div>
          </div>

          {/* Conditional Sections */}
          {isMultiVoting && (
            <>
              {/* Funding Distribution */}
              <div className="m-3">
                <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Funding Distribution</h2>
                <div className="flex flex-col sm:flex-row sm:space-x-2 space-y-2 sm:space-y-0">
                  {/* Total for all projects */}
                  <div className="flex items-center">
                    <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                      <input
                        type="radio"
                        id="total-all"
                        name="funding-dist"
                        value="total-all"
                        checked={fundingDist === "total-all"}
                        onChange={() => setFundingDist("total-all")}
                        className="appearance-none w-3 h-3 sm:w-4 sm:h-4 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                        style={{
                          backgroundColor: fundingDist === "total-all" ? "" : "#fff",
                          backgroundImage: fundingDist === "total-all" ? "" : "none",
                        }}
                      />
                    </div>
                    <label htmlFor="total-all" className="text-gray-600 text-sm sm:text-base">
                      Total for all projects
                    </label>
                  </div>
                  {/* Per winning project */}
                  <div className="flex items-center">
                    <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                      <input
                        type="radio"
                        id="per-winning"
                        name="funding-dist"
                        value="per-winning"
                        checked={fundingDist === "per-winning"}
                        onChange={() => setFundingDist("per-winning")}
                        className="appearance-none w-3 h-3 sm:w-4 sm:h-4 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                        style={{
                          backgroundColor: fundingDist === "per-winning" ? "" : "#fff",
                          backgroundImage: fundingDist === "per-winning" ? "" : "none",
                        }}
                      />
                    </div>
                    <label htmlFor="per-winning" className="text-gray-600 text-sm sm:text-base">
                      Per winning project
                    </label>
                  </div>
                </div>
              </div>

              {/* Number of Winning Projects */}
              <div className="m-3 ">
                <h2 className="text-base sm:text-lg font-thin text-gray-700 mb-3">Number of Winning Projects</h2>
                <button onClick={() => setShowWinningCountDropdown(!showWinningCountDropdown)} className="flex items-center justify-between bg-white text-gray-700 border border-gray-300 px-3 py-2 rounded-lg shadow-sm w-full sm:w-[120px]">
                  <span className="text-sm sm:text-base">{winningProjectsCount}</span>
                  <IoIosArrowDown className="w-3 h-3 sm:w-4 sm:h-4 ml-2" />
                </button>
                {showWinningCountDropdown && (
                  <div className="absolute z-10 mt-1 w-full sm:w-[120px] text-black bg-white border border-gray-300 rounded-lg shadow-lg">
                    {[1, 2, 3, 4, 5].map(num => (
                      <div
                        key={num}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => {
                          setWinningProjectsCount(num);
                          setShowWinningCountDropdown(false);
                        }}
                      >
                        {num}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Add Project Section */}
          <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md w-full max-w-4xl mx-auto mt-6 sm:mt-8">
            <h2 className="text-gray-900 font-semibold text-base sm:text-lg mb-3 sm:mb-4">
              Add project
            </h2>
            <input
              type="text"
              className="w-full p-2 sm:p-3 border rounded-lg text-gray-600 bg-[#ffffff] mb-2 sm:mb-3 outline-none focus:ring-1 focus:ring-gray-500 text-sm sm:text-base"
              value={newProject.title}
              onChange={(e) => setNewProject({ ...newProject, title: e.target.value })}
            />
            <textarea
              value={newProject.description}
              onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
              className="w-full p-3 sm:p-6 border rounded-lg text-gray-600 bg-[#ffffff] mb-2 sm:mb-3 outline-none focus:ring-1 focus:ring-gray-500 text-sm sm:text-base"
              rows="3"
            />
            <div className="relative">
              <input
                type="file"
                className="hidden"
                id="projectImage"
                accept="image/*"
                onChange={handleImageChange}
              />
              <label
                htmlFor="projectImage"
                className="flex items-center gap-3 sm:gap-6 bg-gray-100 p-2 sm:p-3 rounded-lg mb-3 sm:mb-4 cursor-pointer"
                style={{ cursor: "pointer" }}
              >
                <LuUpload size={24} className="text-purple-600" />
                <span className="text-gray-700 text-xs sm:text-sm">
                  {newProject.previewImage ? "Change Image" : "Upload Project Image (Optional)"}
                </span>
              </label>
              {newProject.previewImage && (
                <img
                  src={newProject.previewImage}
                  alt="Preview"
                  className="mt-2 max-h-32 rounded"
                />
              )}
            </div>
            <div className="flex justify-end mt-3 sm:mt-4">
              <button
                className="bg-purple-600 text-white font-semibold py-2 px-6 sm:px-10 rounded-[14px] flex items-center justify-center gap-2 text-sm sm:text-base"
                onClick={handleAddProject}
                type="button"
              >
                <FaPlus size={12} className="text-white" />
                {editingProjectIndex !== null ? "Update project" : "Add project"}
              </button>
            </div>
          </div>

          {/* Display Added Projects */}
          <div className="max-h-[400px] p-6 overflow-y-auto scrollbar-hide">
            {projects.map((project, index) => (
              <div key={index} className="bg-white p-6 rounded-2xl shadow-md w-full max-w-4xl mx-auto mt-4 first:mt-0">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
                  <div className="flex-1">
                    <h2 className="text-base sm:text-lg text-gray-600 font-bold">
                      {project.title}
                    </h2>
                    <div className="text-gray-600 text-xs sm:text-sm">
                      {showFull[index] ? (
                        <>
                          <p>{project.description}</p>
                          <button
                            className="text-purple-600 font-semibold hover:underline ml-2"
                            onClick={() => toggleShowFull(index)}
                            type="button"
                          >
                            Read less
                          </button>
                        </>
                      ) : (
                        <>
                          {project.description?.substring(0, 200)}
                          {project.description && project.description.length > 200 && (
                            <>
                              <span className="mx-1">...</span>
                              <button
                                className="text-purple-600 font-semibold hover:underline ml-2"
                                onClick={() => toggleShowFull(index)}
                                type="button"
                              >
                                Read more
                              </button>
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2 mt-3 sm:mt-4">
                  <button
                    className="text-gray-500 hover:text-gray-700"
                    onClick={() => handleEditProject(index)}
                    title="Edit project"
                  >
                    <FiEdit2 size={18} className="sm:w-5 sm:h-5" />
                  </button>
                  <button onClick={() => handleAskRemoveProject(index)} className="text-red-500">
                    <RiDeleteBinLine size={18} className="sm:w-5 sm:h-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
          {isRankedChoice && (
            <div className="max-h-[400px] w-full mt-5 max-w-4xl flex items-center justify-center p-4 sm:p-6 overflow-y-auto scrollbar-hide bg-[#f9f3e0] rounded-xl">
              <h2 className="text-[#e5b835] text-center text-sm sm:text-base">
                Ranked voting requires at least 2 projects to be added
              </h2>
            </div>
          )}
          {/* Create Vote Button */}
          <div className="w-full flex justify-center mt-6 sm:mt-8 px-4 sm:px-0">
            <button
              className="w-full max-w-4xl bg-yellow-500 text-white font-semibold py-2 sm:py-3 rounded-lg flex items-center justify-center text-sm sm:text-base"
              onClick={handleSubmitVote}
              type="button"
            >
              Create vote
            </button>
          </div>
        </div>
      </div>

      {/* Delete Vote Modal */}
      {showDeleteModal && (
        <DeleteVoteModal
          onClose={() => setShowDeleteModal(false)}
          confirmText={confirmText}
          setConfirmText={setConfirmText}
          onDelete={handleConfirmRemoveProject}
        />
      )}
    </div>
  );
};
