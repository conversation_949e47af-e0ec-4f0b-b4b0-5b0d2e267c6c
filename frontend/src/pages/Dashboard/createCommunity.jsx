import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import panther from "../HomePage/panter.png";
import pluscircle from "../../assets/presale/plus-circle.png"; 
import search from "../../assets/presale/search1.png";
import user2 from "../../assets/presale/users.png";

export const CreateCommunity = () => {
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState(() => {
        return localStorage.getItem('activeTab') || 'create';
    });
    const [searchQuery, setSearchQuery] = useState('');

    const communities = [
        { name: 'Green Earth Initiative' },
        { name: 'Tech Innovators DAO' },
        { name: 'Digital Creators Hub' }
    ];

    useEffect(() => {
        localStorage.setItem('activeTab', activeTab);
    }, [activeTab]);

    return (
        <div className="min-h-screen bg-BP-lightbaige text-black p-4">
            {/* Back Button */}
            <div className="absolute top-4 left-4">
                <button
                    onClick={() => navigate("/")}
                    className="text-black text-sm mb-6 flex items-center space-x-2"
                >
                    <span className="mb-2 text-lg">&larr;</span>
                    <span>Back</span>
                </button>
            </div>

            {/* Main Container */}
            <div className="w-full max-w-3xl mx-auto">
                {/* Logo */}
                <div className="flex justify-center mb-6">
                    <img src={panther} alt="Panther Logo" className="w-12 h-12 bg-white rounded-xl" />
                </div>

                {/* Card Container */}
                <div className="bg-BP-opacited-white rounded-xl p-6 md:p-10 lg:p-12 shadow-lg">
                    <h2 className="text-2xl text-black font-bold font-title text-center mb-8">
                        Choose Your Community
                    </h2>

                    {/* Tab Buttons */}
                    <div className="flex flex-col sm:flex-row justify-center mb-8 space-y-4 sm:space-y-0 sm:space-x-4">
                        <button
                            onClick={() => setActiveTab('create')}
                            className={`text-lg px-4 py-2 rounded-lg ${
                                activeTab === 'create' ? 'bg-BP-yellow' : 'bg-BP-flatstate-gray'
                            }`}
                        >
                            <img src={pluscircle} alt="Create Community" className="inline-block mr-2" />
                            Create new community
                        </button>
                        <button
                            onClick={() => setActiveTab('join')}
                            className={`text-lg px-4 py-2 rounded-lg ${
                                activeTab === 'join' ? 'bg-[#a883d6]' : 'bg-BP-flatstate-gray'
                            }`}
                        >
                            <img src={user2} alt="Join Community" className="inline-block mr-2" />
                            Join an existing community
                        </button>
                    </div>

                    {/* Content Based on Active Tab */}
                    {activeTab === 'create' ? (
                        <div className="mb-8 bg-[#f8f5ed] p-6 sm:p-9 rounded-lg">
                            <h2 className="text-lg text-[#7a7977] font-medium mb-2">Community name</h2>
                            <input
                                type="text"
                                className="border-2 border-BP-gray-20-opc rounded-lg p-3 w-full bg-white text-center"
                                placeholder="Enter community name"
                            />
                        </div>
                    ) : (
                        <div className="mb-8 bg-[#f8f5ed] p-6 sm:p-9 rounded-lg">
                            <div className="relative mb-4">
                                <img
                                    src={search}
                                    alt="Search Icon"
                                    className="absolute left-3 mr-4 top-4 w-5 h-5"
                                />
                                <input
                                    type="text"
                                    className="border-2 border-BP-gray-20-opc rounded-lg p-3 pl-10 w-full bg-white"
                                    placeholder="Type to search..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                            </div>

                            <div className="space-y-3">
                                {communities.map((community, index) => (
                                    <div
                                        key={index}
                                        className="flex flex-col sm:flex-row justify-between items-center p-3 bg-white rounded-lg"
                                    >
                                        <span className="font-medium">{community.name}</span>
                                        <button className="bg-BP-custom-purple px-4 py-1 rounded-lg mt-2 sm:mt-0">
                                            Join
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Navigation Buttons */}
                    <div className="flex flex-col sm:flex-row justify-between mt-8 space-y-4 sm:space-y-0 sm:space-x-4">
                        <button
                            onClick={() => navigate("/")}
                            className="text-black text-sm flex items-center space-x-2 px-4 py-2"
                        >
                            <span className="text-lg">&larr;</span>
                            <span>Back</span>
                        </button>

                        <button className="bg-BP-custom-purple text-white px-6 py-2 rounded-lg">
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};