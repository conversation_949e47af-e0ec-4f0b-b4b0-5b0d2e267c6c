import React, { useState, useEffect } from 'react';
import { useNavigate } from "react-router-dom";
import bg from '../../assets/pro/bg.png';
import { FaCalendarAlt, FaDollarSign } from 'react-icons/fa';
import { ProjectService } from '../../services/projectService';
import { useAuthentication } from '../../components/utils/provider';

const PAGE_SIZE = 3; // Projects per page

export const Ongoingprojects = () => {
  const { currentUser } = useAuthentication();
  const [ongoingProjects, setOngoingProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isSubscriber, setIsSubscriber] = useState(currentUser.isSubscribed);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchOngoingProjects = async () => {
      try {
        setLoading(true);
        const projects = await ProjectService.getOngoingProjects();
        const sortedProjects = [...projects].sort(
          (a, b) => new Date(b.startDate) - new Date(a.startDate)
        );
        setOngoingProjects(sortedProjects);
      } catch (error) {
        console.error('Error fetching ongoing projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOngoingProjects();
  }, []);

  // Pagination logic
  const totalPages = Math.ceil(ongoingProjects.length / PAGE_SIZE);
  const paginatedProjects = ongoingProjects.slice(
    (currentPage - 1) * PAGE_SIZE,
    currentPage * PAGE_SIZE
  );

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) setCurrentPage(page);
  };

  return (
    <div className="text-gray-700 mx-auto px-0 w-full min-h-screen py-12">
      {/* Section Title */}
      <h1 className="text-3xl sm:text-4xl font-bold text-black text-left mt-[-60px] lg:ml-24 sm:ml-0">
        Ongoing Projects
      </h1>
      <p className="text-gray-600 mt-2 sm:mt-4 text-left text-base sm:text-lg lg:ml-24 sm:ml-0">
        Explore the medical research and health initiatives currently underway
        in our DAO.
      </p>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      ) : ongoingProjects.length === 0 ? (
        <div className="flex justify-center w-full mt-8">
          <div className="bg-white shadow-md rounded-lg md:p-48 p-24 font-karla md:text-3xl text-lg text-center">
            <p className="text-gray-600">No ongoing projects at the moment.</p>
          </div>
        </div>
      ) : (
        <>
          {paginatedProjects.map((project) => (
            <div key={project.id} className="flex justify-center w-full mt-8">
              <div className="relative bg-white shadow-xl rounded-2xl overflow-hidden w-full sm:w-full min-w-[90vw] md:min-w-[80vw] lg:min-w-[60vw] xl:min-w-[50vw] max-w-[920px]">
                {/* Background Image with Overlay */}
                <div className="relative">
                  <img
                    src={project.image ? project.image : bg}
                    alt={project.title}
                    crossOrigin="anonymous"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = bg;
                    }}
                    className="w-full h-[600px] sm:h-[500px] md:h-[550px] lg:h-[600px] object-cover rounded-2xl"
                  />

                  {/* Dark Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/40 to-black/60 rounded-2xl"></div>

                  {/* Project Title and Description */}
                  <div className="absolute left-6 sm:left-10 top-24 md:top-64 text-white text-left max-w-lg">
                    <h2 className="font-bold text-xl sm:text-2xl md:text-3xl leading-tight">
                      {project.title}
                    </h2>
                    <p className="mt-2 text-sm sm:text-base text-gray-200 line-clamp-3">
                      {project.description}
                    </p>
                  </div>
                </div>

                {/* Project Details */}
                <div className="absolute bottom-4 pb-4 left-4 right-4 flex flex-col sm:flex-row items-center sm:items-start sm:justify-between p-4 rounded-xl space-y-4 sm:space-y-0">
                  {/* Start & Completion Date */}
                  <div className="flex flex-col text-white bg-[#505050]/50 p-4 rounded-xl w-full sm:w-auto border border-[#505050]/30">
                    <div className="flex items-center gap-3">
                      <FaCalendarAlt className="text-purple-400 text-lg" />
                      <div className="text-left">
                        <p className="text-sm sm:text-base">Start Date</p>
                        <p className="text-base sm:text-lg">
                          {new Date(project.startDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3 flex items-center gap-3">
                      <FaCalendarAlt className="text-purple-400 text-lg" />
                      <div className="text-left">
                        <p className="text-sm sm:text-base">
                          Expected Completion date
                        </p>
                        <p className="text-base sm:text-lg">
                          {project.completionDate
                            ? new Date(
                                project.completionDate
                              ).toLocaleDateString()
                            : 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Funding */}
                  <div className="flex flex-col items-center bg-[#505050]/50 p-6 rounded-xl w-full sm:w-auto border-[#505050]/30">
                    <FaDollarSign className="text-yellow-400 text-2xl sm:text-3xl mb-2" />
                    <p className="text-white text-sm sm:text-base">Funding</p>
                    <p className="text-lg sm:text-2xl font-semibold text-yellow-400 mt-1">
                      $ {project.fundingAllocation.toLocaleString()}
                    </p>
                  </div>

                  {/* Status & View Button */}
                  <div className="flex flex-row md:flex-col items-center md:space-y-14 space-y-4">
                    {/* Status */}
                    <div className="flex items-center md:space-x-8 space-x-5 px-3 py-1 rounded-full">
                      <div className="relative w-4 h-4 sm:w-5 sm:h-5 flex items-center justify-center">
                        <span className="absolute w-4 h-4 sm:w-5 sm:h-5 bg-yellow-400 rounded-full opacity-100"></span>
                        <span className="absolute w-8 h-8 sm:w-10 sm:h-10 bg-yellow-400 opacity-30 rounded-full animate-pulse"></span>
                      </div>
                      <p className="text-yellow-400 text-sm sm:text-base font-medium">
                        {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                      </p>
                    </div>

                    {/* View Project Button */}
                    {isSubscriber &&
                      <button
                        className="px-4 sm:px-16 py-2 sm:py-4 border border-white/50 
                        text-white rounded-full 
                        transition-all duration-300 shadow-lg shadow-white/15
                        bg-[#505050]/80 hover:brightness-150"
                        onClick={() => navigate(`/dashboard/ongoing-projects/${project.id}`)}
                      >
                        View Project
                      </button>}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Pagination Controls */}
          <div className="flex justify-center items-center mt-10 gap-2">
            <button
              className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-medium disabled:opacity-50"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
            {[...Array(totalPages)].map((_, idx) => (
              <button
                key={idx}
                className={`px-3 py-2 rounded font-medium ${
                  currentPage === idx + 1
                    ? 'bg-yellow-400 text-white'
                    : 'bg-gray-100 text-gray-700'
                }`}
                onClick={() => handlePageChange(idx + 1)}
              >
                {idx + 1}
              </button>
            ))}
            <button
              className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-medium disabled:opacity-50"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
};
