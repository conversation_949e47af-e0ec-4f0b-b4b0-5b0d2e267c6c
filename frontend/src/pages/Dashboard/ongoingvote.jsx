import React, { useEffect, useState } from "react";
import sigma from '../../assets/Dao/sigma.png';
import appointment from "../../assets/admin/appointment.png";
import { IoIosArrowDown } from "react-icons/io";
import { IoClose } from "react-icons/io5";
import { VoteService } from "../../services/voteService";
import Swal from "sweetalert2";
import { useAuthentication } from '../../components/utils/provider';

const PAGE_SIZE = 1; // Adjust as needed

export const Ongoingvote = () => {
  const [ongoingVotes, setOngoingVotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showFull, setShowFull] = useState({});
  const [error, setError] = useState('');
  const [endingVoteId, setEndingVoteId] = useState(null);
  const [extendingVoteId, setExtendingVoteId] = useState(null);
  const [page, setPage] = useState(1); // <-- Pagination state
  const { currentUser } = useAuthentication();

  useEffect(() => {
    const fetchOngoingVotes = async () => {
      setLoading(true);
      setError('');
      try {
        const votes = await VoteService.getActiveVotes();
        setOngoingVotes(votes);
      } catch (err) {
        setError('Failed to fetch ongoing votes.');
      } finally {
        setLoading(false);
      }
    };
    fetchOngoingVotes();
  }, []);

  // Helper for showing/hiding full description per project
  const toggleShowFull = (voteId, projectId) => {
    setShowFull(prev => ({
      ...prev,
      [`${voteId}_${projectId}`]: !prev[`${voteId}_${projectId}`]
    }));
  };

  // End vote early logic
  const handleEndVoteEarly = async (voteId) => {
    setEndingVoteId(voteId);
    const confirmed = await Swal.fire({
      title: "End vote early?",
      text: "Are you sure you want to end this vote now? This action cannot be undone.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, end it",
      cancelButtonText: "Cancel",
    });
    if (confirmed.isConfirmed) {
      const success = await VoteService.endVoteEarly({ voteId }, false);
      if (success) {
        await Swal.fire({
          title: "Vote ended!",
          text: "The vote has been ended successfully.",
          icon: "success",
          confirmButtonText: "OK"
        });
        setOngoingVotes(prev => prev.filter(v => (v.id || v._id) !== voteId));
      }
    }
    setEndingVoteId(null);
  };

  // Extend vote logic
  const handleExtendVote = async (voteId) => {
    setExtendingVoteId(voteId);
    const confirmed = await Swal.fire({
      title: "Extend vote by 24 hours?",
      text: "Are you sure you want to extend this vote? Max duration is 14 days.",
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Yes, extend",
      cancelButtonText: "Cancel",
    });
    if (confirmed.isConfirmed) {
      const result = await VoteService.extendVote(voteId, false);
      if (result && result.newEndDate) {
        setOngoingVotes(prev =>
          prev.map(v =>
            (v.id || v._id) === voteId
              ? { ...v, endDate: result.newEndDate }
              : v
          )
        );
        await Swal.fire({
          title: "Vote extended!",
          text: "The vote duration has been extended by 24 hours.",
          icon: "success",
          confirmButtonText: "OK"
        });
      }
    }
    setExtendingVoteId(null);
  };

  // --- Pagination logic ---
  const totalPages = Math.ceil(ongoingVotes.length / PAGE_SIZE);
  const pagedVotes = ongoingVotes.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 my-2">{error}</div>;
  }

  return (
    <div className="text-gray-600 mx-auto text-center px-2 sm:px-4 md:px-10 lg:px-24">
      <div>
        <h2 className="text-4xl md:text-5xl font-normal text-center text-gray-800">
          Monitor Ongoing Votes
        </h2>
      </div>

      {ongoingVotes.length === 0 && (
        <div className="rounded-lg shadow-md bg-white p-20 text-center text-2xl text-gray-500 mt-2">
          No ongoing voting
        </div>
      )}

      {pagedVotes.map((vote) => (
        <div key={vote.id || vote._id}>
          {/* Voting Controls */}
          <div className="bg-white flex flex-col justify-between mt-5 p-4 sm:p-8 rounded-2xl shadow-lg w-full min-w-0 md:min-w-[70vw] lg:min-w-[60vw] max-w-none">
            <div className="flex flex-col items-start">
              <span className="text-gray-700 font-thin text-2xl text-left">Voting Controls</span>
              <span className="flex items-center text-gray-700 font-thin mb-4 text-left">
                <img src={appointment} alt="Calendar" className="w-4 h-4 mr-2" />
                {vote.startDate?.slice(0,10)} - {vote.endDate?.slice(0,10)}
              </span>
              <div className="flex flex-col sm:flex-row items-start sm:items-stretch justify-between gap-2 md:w-full w-full">
                {currentUser?.role === "admin" && (
                  <>
                    <button
                      className="bg-[#fcece7] flex items-center text-[#fb7185] px-6 sm:px-14 py-3 sm:py-1 font-karla rounded-lg shadow-md whitespace-nowrap text-lg sm:text-lg w-full sm:w-auto min-w-[180px]"
                      onClick={() => handleEndVoteEarly(vote.id || vote._id)}
                      disabled={endingVoteId === (vote.id || vote._id)}
                    >
                      <IoClose className="w-5 h-5 mr-2" />
                      {endingVoteId === (vote.id || vote._id) ? "Ending..." : "End vote early"}
                    </button>
                    <div className="flex gap-2 w-full sm:w-auto">
                      <button className="flex items-center justify-between bg-white text-gray-700 border border-gray-300 px-4 py-2 font-karla rounded-lg shadow-md whitespace-nowrap w-full sm:w-[140px]">
                        <span>24 hrs</span>
                        <IoIosArrowDown className="w-4 h-4 ml-2" />
                      </button>
                      <button
                        className="bg-purple-600 text-white px-4 py-2 font-karla rounded-lg shadow-md whitespace-nowrap w-full sm:w-[140px]"
                        onClick={() => handleExtendVote(vote.id || vote._id)}
                        disabled={extendingVoteId === (vote.id || vote._id)}
                      >
                        {extendingVoteId === (vote.id || vote._id) ? "Extending..." : "Extend"}
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Each Project */}
          {(vote.projects ?? []).map((project, idx) => {
            // Multi-vote stats
            const yesVotes = project.voteCount?.yes ?? 0;
            const noVotes = project.voteCount?.no ?? 0;
            const totalVotes = yesVotes + noVotes;
            const yesPercent = totalVotes > 0 ? Math.round((yesVotes / totalVotes) * 100) : 0;
            const noPercent = totalVotes > 0 ? 100 - yesPercent : 0;

            // Ranked-vote stats
            const rankVotes = project.rankings?.length ?? 0;
            const points = project.points ?? 0;
            const participation = vote.votingType === 'ranked'
              ? (vote.projects.length > 0 ? Math.round((rankVotes / vote.projects.length) * 100) : 0)
              : (totalVotes > 0 ? Math.round((totalVotes / 500) * 100) : 0); // 500 is a placeholder for total eligible voters

            return (
              <div key={project.id || project._id} className="bg-white mt-6 px-1 sm:px-7 md:px-8 lg:px-10 flex flex-col p-2 sm:p-8 rounded-3xl w-full min-w-0 sm:min-w-[80vw] md:min-w-[70vw] lg:min-w-[60vw] max-w-none sm:pb-12">
                <div className="flex flex-col md:flex-row justify-between items-start mb-4 gap-4">
                  <div className="flex items-center bg-white text-gray-700 px-3 py-1 rounded-xl text-sm font-medium shadow-lg">
                    <img src={sigma} className="w-3 h-3 mr-2" alt="sigma" />
                    {vote.votingType === 'multi' ? (
                      <span>{totalVotes} total votes</span>
                    ) : (
                      <span>{rankVotes} rank votes</span>
                    )}
                  </div>
                </div>
                <div className="flex flex-col px-3 sm:px-0">
                  <h3 className="text-2xl text-justify font-extrabold text-gray-900 mb-3">
                    {project.title}
                  </h3>
                  <div className="text-gray-500 text-base relative leading-relaxed max-w-3xl mb-6 text-justify">
                    {showFull[`${vote.id}_${project.id}`] ? (
                      <>
                        <p>{project.description}</p>
                        <button
                          className="text-purple-600 font-semibold absolute right-0 bottom-0 hover:underline mt-1"
                          onClick={() => toggleShowFull(vote.id, project.id)}
                        >
                          Read less
                        </button>
                      </>
                    ) : (
                      <p className="inline">
                        {project.description?.substring(0, 200)}
                        <span className="mx-1">...</span>
                        <button
                          className="text-purple-600 font-semibold hover:underline ml-2"
                          onClick={() => toggleShowFull(vote.id, project.id)}
                        >
                          Read more
                        </button>
                      </p>
                    )}
                  </div>
                </div>

                {/* Vote results */}
                {vote.votingType === 'multi' ? (
                  <div className="mb-6 px-2 sm:px-0">
                    <div className="flex flex-col sm:flex-row items-center mb-2 gap-2 w-11/12 md:w-9/12 lg:w-11/12 xl:11/12 sm:px-2">
                      <span className="font-semibold mr-2 flex items-center gap-1 text-teal-500">
                        Yes ({yesPercent}%)
                      </span>
                      <div className="flex-1 w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-teal-400 h-2 rounded-full transition-all duration-300" style={{ width: `${yesPercent}%` }}></div>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-center gap-2 w-11/12 md:w-9/12  lg:w-11/12 xl:11/12 sm:px-2">
                      <span className="font-semibold mr-2 flex items-center gap-1 text-rose-400">
                        No ({noPercent}%)
                      </span>
                      <div className="flex-1 w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-rose-300 h-2 rounded-full transition-all duration-300" style={{ width: `${noPercent}%` }}></div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="mb-6 px-2 sm:px-0">
                    <div className="flex flex-col sm:flex-row items-center mb-2 gap-2 w-11/12 md:w-9/12 lg:w-11/12 xl:11/12 sm:px-2">
                      <span className="font-semibold mr-2 flex items-center gap-1 text-teal-500">
                        Points: {points}
                      </span>
                      <span className="font-semibold mr-2 flex items-center gap-1 text-rose-400">
                        Rank votes: {rankVotes}
                      </span>
                    </div>
                  </div>
                )}

                {/* Stats Row */}
                <div className="flex flex-col items-center space-y-4 sm:space-y-6 w-full px-4 sm:px-0">
                  <div className="flex flex-col sm:flex-row items-center justify-center sm:space-x-6 lg:space-x-12 w-full">
                    {vote.votingType === 'multi' ? (
                      <>
                        <div className="flex flex-col bg-[#f8f5ed] py-3 px-4 sm:py-5 sm:px-10 rounded-md w-full max-w-[260px] sm:w-[240px] md:w-[260px] lg:w-[300px]">
                          <span className="text-gray-500 text-sm sm:text-base">Yes votes</span>
                          <span className="text-xl sm:text-2xl text-[#4fd1c5] font-bold">{yesVotes}</span>
                        </div>
                        <div className="flex flex-col bg-[#f8f5ed] py-3 px-4 sm:py-5 sm:px-10 rounded-md w-full max-w-[260px] sm:w-[240px] md:w-[260px] lg:w-[300px] mt-3 sm:mt-0">
                          <span className="text-gray-500 text-sm sm:text-base">No votes</span>
                          <span className="text-xl sm:text-2xl text-yellow-500 font-bold">{noVotes}</span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex flex-col bg-[#f8f5ed] py-3 px-4 sm:py-5 sm:px-10 rounded-md w-full max-w-[260px] sm:w-[240px] md:w-[260px] lg:w-[300px]">
                          <span className="text-gray-500 text-sm sm:text-base">Rank votes</span>
                          <span className="text-xl sm:text-2xl text-[#4fd1c5] font-bold">{rankVotes}</span>
                        </div>
                        <div className="flex flex-col bg-[#f8f5ed] py-3 px-4 sm:py-5 sm:px-10 rounded-md w-full max-w-[260px] sm:w-[240px] md:w-[260px] lg:w-[300px] mt-3 sm:mt-0">
                          <span className="text-gray-500 text-sm sm:text-base">Voter participation</span>
                          <span className="text-xl sm:text-2xl text-yellow-500 font-bold">{participation}%</span>
                        </div>
                      </>
                    )}
                  </div>
                  <div className="w-full flex justify-center mt-2 sm:mt-0">
                    <span className="text-gray-700 text-xs sm:text-sm md:text-base">
                      {
                    typeof vote.remainingDays === "number"
                      ? vote.remainingDays
                      : vote.endDate
                        ? Math.max(
                            0,
                            Math.ceil(
                              (new Date(vote.endDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
                            )
                          )
                        : 0
                  } days remaining
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ))}

      {/* --- Pagination Controls --- */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-10">
          <button
            className="px-3 py-1 rounded bg-gray-200 text-gray-700 disabled:opacity-50"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Prev
          </button>
          {[...Array(totalPages)].map((_, idx) => (
            <button
              key={idx}
              className={`px-3 py-1 rounded ${page === idx + 1 ? "bg-purple-600 text-white" : "bg-gray-100 text-gray-700"}`}
              onClick={() => setPage(idx + 1)}
            >
              {idx + 1}
            </button>
          ))}
          <button
            className="px-3 py-1 rounded bg-gray-200 text-gray-700 disabled:opacity-50"
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

























