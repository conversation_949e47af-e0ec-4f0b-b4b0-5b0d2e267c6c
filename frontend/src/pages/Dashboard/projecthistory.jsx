import React, { useEffect, useState } from 'react';
import { useNavigate} from "react-router-dom";
import { FaCalendarAlt } from 'react-icons/fa';
import { BsCheckCircle, BsCurrencyDollar } from 'react-icons/bs';
import bg from '../../assets/pro/bg.png';
import { toast } from 'react-toastify';
import { ProjectService } from '../../services/projectService';

export const Projecthistory = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCompletedProjects = async () => {
      try {
        const data = await ProjectService.getCompletedProjects();
        const sorted = [...data].sort(
          (a, b) => new Date(b.completionDate) - new Date(a.completionDate)
        );
        setProjects(sorted);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false); 
      }
    };

    fetchCompletedProjects();
  }, []);

  return (
    <div className="text-gray-700 mx-auto px-0 w-full min-h-screen py-12">
      <h2 className="text-3xl sm:text-4xl font-bold text-black text-left mt-[-60px] lg:ml-24 sm:ml-0]">
        Completed Projects
      </h2>
      <p className="text-gray-600 mt-2 sm:mt-4 text-left text-base sm:text-lg lg:ml-24 sm:ml-0">
        Explore the medical research and health initiatives completed in our
        DAO.
      </p>

      <div className="flex flex-col items-center mt-8 space-y-8">
        {loading ? (
          <div className="flex justify-center items-center w-full mt-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : projects.length > 0 ? (
          projects.map((project, index) => (
            <div
              key={project.id || index}
              className="bg-white rounded-xl shadow-lg p-6 w-full sm:w-full md:min-w-[80vw] min-w-[90vw] lg:min-w-[70vw]"
            >
              <img
                src={project.image ? project.image : bg}
                alt="Project"
                crossOrigin="anonymous"
                className="rounded-xl w-full h-48 sm:h-60 object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = bg;
                }}
              />
              <div className="p-4">
                <h3 className="font-bold text-xl sm:text-2xl md:text-3xl leading-tight">
                  {project.title}
                </h3>
                <p className="mt-2 text-sm sm:text-base line-clamp-3">
                  {project.description}
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-4">
                  <div className="border rounded-xl p-4 shadow-sm flex flex-col space-y-2">
                    <div className="flex items-center gap-2 text-purple-600">
                      <FaCalendarAlt />
                      <div className="flex flex-col text-left">
                        <p className="text-black text-sm font-medium">
                          Start Date
                        </p>
                        <p className="text-black text-sm font-medium">
                          {new Date(project.startDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 text-purple-600">
                      <FaCalendarAlt />
                      <div className="flex flex-col text-left">
                        <p className="text-black text-sm font-medium">
                          Completion Date
                        </p>
                        <p className="text-black text-sm font-medium">
                          {project.completionDate
                            ? new Date(
                                project.completionDate
                              ).toLocaleDateString()
                            : 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-xl p-6 shadow-sm flex flex-col items-center">
                    <BsCurrencyDollar className="text-yellow-500 text-2xl" />
                    <span className="text-black text-sm font-medium mt-2">
                      Funding
                    </span>
                    <p className="text-yellow-600 font-semibold text-xl mt-2">
                      $ {project.fundingAllocation.toLocaleString()}
                    </p>
                  </div>

                  <div className="flex flex-col items-center space-y-10">
                    <div className="flex items-center text-green-600 space-x-2">
                      <BsCheckCircle className="text-green-600 text-xl" />
                      <span className="font-medium"> {project.status.charAt(0).toUpperCase() + project.status.slice(1)}</span>
                    </div>

                    <button
                      className="bg-purple-600 text-white px-12 py-3 rounded-[24px] shadow-lg text-sm font-medium hover:bg-purple-700 transition"
                      onClick={() => navigate(`/dashboard/project-history/${project.id}`)}
                    >
                      View Project
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="flex justify-center w-full mt-8">
            <div className="bg-white shadow-md rounded-lg md:p-48 p-24 font-karla md:text-3xl text-lg text-center">
              <p className="text-gray-600">No completed projects available.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
