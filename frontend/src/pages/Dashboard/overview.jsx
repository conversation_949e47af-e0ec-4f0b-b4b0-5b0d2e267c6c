import React, { useState, useEffect } from 'react';
import { FaUserPlus, FaUser, FaEdit, FaQuestion } from 'react-icons/fa';
import { JoinRequestModal } from '../modals/JoinRequestModal';
import { InviteMembersModal } from '../modals/InviteMembersModal';
import { EditCommunityModal } from '../modals/EditCommunityModal';
import { UserService } from '../../services/userService';

export const Overview = () => {
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [recentActivity, setRecentActivity] = useState([
    { text: '<PERSON> joined 2 hours ago' },
    { text: '<PERSON> invited a new member' },
  ]);
  const [members, setMembers] = useState([]);

  // Add state for community name and description
  const [communityName, setCommunityName] = useState('Black Panther DAO');
  const [communityDescription, setCommunityDescription] = useState(
    'A powerful community for decentralized collaboration.'
  );

  useEffect(() => {
    const fetchMembers = async () => {
      const users = await UserService.getAllUsers();
      setMembers(users || []);
    };
    fetchMembers();
  }, []);

  const handleApprove = (name) => {
    setRecentActivity([...recentActivity, { text: `${name} joined just now` }]);
  };

  // Handler to update community info after editing
  const handleCommunitySaved = ({ name, description }) => {
    setCommunityName(name);
    setCommunityDescription(description);
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full max-w-8xl bg-white p-6 rounded-xl shadow-lg mx-auto">
        {/* DAO Info */}
        <div className="mb-4">
          <div className="flex justify-between items-center">
            <h2 className="text-4xl font-karla text-gray-700 mb-4">
              {communityName}
            </h2>
            <button
              onClick={() => setIsEditModalOpen(true)}
              className="flex items-center text-yellow-500 font-semibold"
            >
              <FaEdit className="w-5 h-5 mr-1" /> Edit
            </button>
          </div>
          <p className="text-gray-600 mb-10">{communityDescription}</p>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-2 grid-cols-1 gap-4 mb-6">
          <div className="p-4 text-center bg-gray-100 rounded-2xl shadow-sm">
            <p className="font-semibold text-gray-600">Total Members</p>
            <p className="text-2xl font-bold text-gray-900">{members.length}</p>
          </div>
          <div className="p-4 text-center bg-gray-100 rounded-2xl shadow-sm">
            <p className="font-semibold text-gray-600">Join Requests</p>
            <p className="text-2xl font-bold text-gray-900">70</p>
          </div>
        </div>

        {/* Members List */}
        {/* <div className="mb-6">
          <h3 className="font-semibold text-gray-700 mb-2">Members</h3>
          <ul className="text-gray-700 space-y-1 text-sm">
            {members.map((member, idx) => (
              <li key={member._id || idx} className="flex items-center">
                <FaUser className="text-purple-500 mr-2" />
                {member.name || member.username || member.email}
              </li>
            ))}
          </ul>
        </div> */}

        {/* Actions */}
        <div className="grid md:grid-cols-2 grid-cols-1 gap-4 mb-6">
          <button
            onClick={() => setIsInviteModalOpen(true)}
            className="bg-purple-600 hover:bg-purple-400 text-white flex items-center justify-center py-3 rounded-2xl shadow-md"
          >
            <FaUserPlus className="w-5 h-5 mr-2" /> Invite members
          </button>
          <button
            onClick={() => setIsJoinModalOpen(true)}
            className="bg-yellow-300 hover:bg-yellow-500 text-black flex items-center justify-center py-3 px-4 rounded-2xl shadow-md"
          >
            <div className="relative flex items-center justify-center w-6 h-6 mr-2">
              <FaUser className="text-white" size={16} />
              <div className="absolute top-0 right-0 w-4 h-4 bg-white rounded-sm flex items-center justify-center">
                <FaQuestion className="text-black" size={10} />
              </div>
            </div>
            View Request (70)
          </button>
        </div>

        {/* Recent Activity */}
        <div className="p-6 md:p-10 bg-gray-100 rounded-2xl shadow-sm">
          <h3 className="font-semibold text-gray-700 mb-2">Recent Activity</h3>
          <ul className="text-gray-700 space-y-2 text-sm">
            {[...members]
              .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              .slice(0, 5)
              .map((member, index) => (
                <li key={member._id || index} className="flex items-center">
                  <span className="text-yellow-500 mr-2">➤</span>
                  {member.firstName} {member.lastName} joined{' '}
                  {member.createdAt
                    ? new Date(member.createdAt).toLocaleDateString()
                    : 'Unknown date'}
                </li>
              ))}
          </ul>
        </div>
      </div>

      {/* Modals */}
      {isJoinModalOpen && (
        <JoinRequestModal onClose={() => setIsJoinModalOpen(false)} />
      )}
      {isInviteModalOpen && (
        <InviteMembersModal onClose={() => setIsInviteModalOpen(false)} />
      )}
      {isEditModalOpen && (
        <EditCommunityModal
          onClose={() => setIsEditModalOpen(false)}
          currentName={communityName}
          currentDescription={communityDescription}
          onSaved={handleCommunitySaved}
        />
      )}
    </div>
  );
};
