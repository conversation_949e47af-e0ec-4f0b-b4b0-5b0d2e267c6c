import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { FaRegEdit } from 'react-icons/fa';
import { EditFirstNameModal, EditLastNameModal, EditEmailModal, EditPasswordModal, EditCountryModal } from '../modals/EditProfileModals';
import { useAuthentication } from '../../components/utils/provider';
import { DeleteAccountModal } from '../modals/DeleteAccountModal';
import { AuthService } from '../../services/authService';
import { toast } from 'react-toastify';
export const Account = () => {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isFirstNameModalOpen, setIsFirstNameModalOpen] = useState(false);
  const [isLastNameModalOpen, setIsLastNameModalOpen] = useState(false);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [isCountryModalOpen, setIsCountryModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const { ensureLogin, currentUser, getCurrentUser } = useAuthentication();

  useEffect(() => {
    const fetchUser = async () => {
      await ensureLogin();
      await getCurrentUser();
      setIsLoading(false);
    }

    fetchUser();
  }, []) 

  const handleDeleteAccount = async () => {
    console.log('handleDeleteAccount called'); // Debugging log
    try {
      const success = await AuthService.DeleteUserAccount();
      if (success) {
        console.log('Account deleted successfully'); // Debugging log
        setShowDeleteAccountModal(false);
        toast.success('Account deleted successfully');
        navigate('/signup'); // Redirect to signup page
      } else {
        console.log('Account deletion failed'); // Debugging log
        toast.error('Failed to delete account. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting account:', error); // Debugging log
      toast.error('Failed to delete account. Please try again.');
    }
  };




  // const handleNameSave = (newName) => {
  //   // Handle name update logic here
  //   console.log('New name:', newName);
  //   setIsNameModalOpen(false);
  // };

  // const handleEmailSave = (newEmail) => {
  //   // Handle email update logic here
  //   console.log('New email:', newEmail);
  //   setIsEmailModalOpen(false);
  // };

  // const handlePasswordSave = (currentPassword, newPassword, confirmPassword) => {
  //   // Handle password update logic here
  //   console.log('Password update:', { currentPassword, newPassword, confirmPassword });
  //   setIsPasswordModalOpen(false);
  // };


  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex flex-col w-full">
        <div className="flex justify-center items-center sm:block">
          {/* Subscription Section */}
          <div className="sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
            <h2 className="text-xl font-semibold text-gray-800">
              Subscribe 
            </h2>
            <p className="text-gray-500 mt-3">
              Get notified about project updates and upcoming voting sessions.
            </p>

            <div className="flex items-center gap-6 mt-6">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={isSubscribed}
                  onChange={() => setIsSubscribed(!isSubscribed)}
                />
                {/* Toggle Background */}
                <div
                  className={`w-16 h-7 rounded-full transition-colors duration-300 ${
                    isSubscribed ? 'bg-purple-500' : 'bg-gray-300'
                  }`}
                >
                  {/* Toggle Circle */}
                  <div
                    className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-all duration-300 ${
                      isSubscribed ? 'translate-x-9' : 'translate-x-1'
                    }`}
                  ></div>
                </div>
              </label>
              {/* Status Text */}
              <span
                className={`font-medium ${isSubscribed ? 'text-green-600' : 'text-gray-400'}`}
              >
                {isSubscribed ? 'Subscribed' : 'Unsubscribed'}
              </span>
            </div>
          </div>
        </div>

        <div className="flex justify-center items-center sm:block">
          {/* Billing Section */}
          <div className="sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
            <h2 className="text-xl font-semibold text-gray-800">
              Manage Billing
            </h2>
            <p className="text-gray-500 mt-1">
              Click below to change your billing details.
            </p>

            <div className="mt-4 flex justify-end">
              <button className="bg-purple-500 text-white px-5 py-2 rounded-full shadow-md hover:bg-purple-600 transition">
                Manage billing
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-center items-center sm:block">
          {/* Profile Details */}
          <div className="sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
            <h2 className="text-2xl font-semibold text-gray-800 mb-10">
              Profile Details
            </h2>
            {/*First  Name */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-gray-700 font-medium">First Name</p>
                <p className="text-gray-500">{currentUser?.firstName || 'N/A'}</p>
              </div>
              <button 
                onClick={() => setIsFirstNameModalOpen(true)}
                className="cursor-pointer"
              >
                <FaRegEdit
                  className="text-purple-500 hover:text-purple-700"
                  size={20}
                />
              </button>
            </div>

            {/* Last Name*/}
            <div className='flex justify-between items-center mb-4'>
              <div>
                <p className="text-gray-700 font-medium">Last Name</p>
                <p className="text-gray-500">{currentUser?.lastName || 'N/A'}</p>
              </div>
              <button 
                onClick={() => setIsLastNameModalOpen(true)}
                className="cursor-pointer"
              >
                <FaRegEdit
                  className="text-purple-500 hover:text-purple-700"
                  size={20}
                />
              </button>

            </div>

            {/* Email Address */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-gray-700 font-medium">Email Address</p>
                <p className="text-gray-500">{currentUser?.email || 'N/A'}</p>
              </div>
              <button 
                onClick={() => setIsEmailModalOpen(true)}
                className="cursor-pointer"
              >
                <FaRegEdit
                  className="text-purple-500 hover:text-purple-700"
                  size={20}
                />
              </button>
            </div>
            {/* Country */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <p className="text-gray-700 font-medium">Country</p>
                <p className="text-gray-500">{currentUser?.country || 'united states'}</p>
              </div>
              <button 
                onClick={() => setIsCountryModalOpen(true)}
                className="cursor-pointer"
              >
                <FaRegEdit
                  className="text-purple-500 hover:text-purple-700"
                  size={20}
                />
              </button>
            </div>

            {/* Password */}
            <div className="flex justify-between items-center">
              <div>
                <p className="text-gray-700 font-medium">Password</p>
                <p className="text-gray-500">*************</p>
              </div>
              <button 
                onClick={() => setIsPasswordModalOpen(true)}
                className="cursor-pointer"
              >
                <FaRegEdit
                  className="text-purple-500 hover:text-purple-700"
                  size={20}
                />
              </button>
            </div>
          </div>
        </div>

        <div className="flex justify-center items-center mb-6 mt-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Cancel Contribution
          </h2>
        </div>

        <div className="flex justify-center items-center sm:block">
          {/* Billing Section */}
          <div className="sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
            <p className="text-gray-600 mb-4 text-center">
              Warning: Closing your account will remove all your data
              permanently.
            </p>

            <div className="flex justify-center">
              <button 
              onClick={() => setShowDeleteAccountModal(true)}
              className="bg-red-500 text-white px-6 py-3 rounded-full shadow-md hover:bg-red-600 transition">
                Close account
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {isFirstNameModalOpen && (
        <EditFirstNameModal
          onClose={() => setIsFirstNameModalOpen(false)}
          currentFirstName={currentUser?.firstName || ''}
          getCurrentUser={getCurrentUser}
        />
      )}
      {isLastNameModalOpen && (
        <EditLastNameModal
          onClose={() => setIsLastNameModalOpen(false)}
          currentLastName={currentUser?.lastName || ''}
          getCurrentUser={getCurrentUser}
        />
      )}
      
      {isEmailModalOpen && (
        <EditEmailModal
          onClose={() => setIsEmailModalOpen(false)}
          currentEmail={currentUser?.email || ''}
          getCurrentUser={getCurrentUser}
        />
      )}
      {isCountryModalOpen && (
        <EditCountryModal
          onClose={() => setIsCountryModalOpen(false)}
          currentCountry={currentUser?.country || ''}
          getCurrentUser={getCurrentUser}
        />
      )}
      
      {isPasswordModalOpen && (
        <EditPasswordModal
          onClose={() => setIsPasswordModalOpen(false)}
          getCurrentUser={getCurrentUser}
        />
      )}

      {
        showDeleteAccountModal && (
          <DeleteAccountModal 
            onClose={() => setShowDeleteAccountModal(false)}
            confirmText={confirmText}
            setConfirmText={setConfirmText}
            onDelete={handleDeleteAccount}
          />
        )
      }
    </div>
  );
}
