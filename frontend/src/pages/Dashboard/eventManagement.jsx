import React, { useState, useEffect } from "react";
import { 
  FiCalendar, FiVideo, FiUsers, FiPlus, FiEdit2, 
  FiTrash2, FiCopy, FiClock, FiLink, FiFileText, 
  FiCheckCircle, FiChevronDown, FiChevronUp, FiX, FiMessageSquare
} from 'react-icons/fi';

import { CiClock2 } from "react-icons/ci";
import { CreateEventModal } from "../modals/createEventModal.jsx";
import { UpdateEventModal } from "../modals/updateEventModal.jsx";
import { EventService } from "../../services/eventService";
import { useAuthentication } from '../../components/utils/provider';
import { DeleteEventModal } from "../modals/deleteEventModel.jsx"; 
import { DuplicateEventModal } from "../modals/duplicateEventModal.jsx";
import { AttendEventModal } from "../modals/attendEventModal.jsx";
import Swal from "sweetalert2";
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

export const EventManagement = () => {
  // Use localStorage for tab persistence
  const [activeTab, setActiveTab] = useState(() => localStorage.getItem("eventsTab") || "upcoming");
  const [isCreateEventModalOpen, setIsCreateEventModalOpen] = useState(false);
  const [expandedEvents, setExpandedEvents] = useState({});
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [pastEvents, setPastEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [eventToDelete, setEventToDelete] = useState(null);
  const [confirmText, setConfirmText] = useState("");
  const [isUpdateEventModalOpen, setIsUpdateEventModalOpen] = useState(false);
  const [eventToUpdate, setEventToUpdate] = useState(null);
  const [duplicateModalOpen, setDuplicateModalOpen] = useState(false);
  const [eventToDuplicate, setEventToDuplicate] = useState(null);
  const [showFullDescription, setShowFullDescription] = useState({});
  const [attendEmail, setAttendEmail] = useState('');
  const [attendName, setAttendName] = useState('');
  const [showAttendModal, setShowAttendModal] = useState(false);
  const [attending, setAttending] = useState(false);
  const [attendEventId, setAttendEventId] = useState(null);
  const { currentUser } = useAuthentication();
  const [showRecordingForm, setShowRecordingForm] = useState({});
  const [hoveredInput, setHoveredInput] = useState(1); 
  const [recordingInput, setRecordingInput] = useState({});
  const [addRecordingInput, setAddRecordingInput] = useState({});
const [editRecordingInput, setEditRecordingInput] = useState({});
const [editingIndex, setEditingIndex] = useState(null); 
  const [upcomingPage, setUpcomingPage] = useState(1);
const [pastPage, setPastPage] = useState(1);
const PAGE_SIZE = 2;
const PAST_PAGE_SIZE = 4;

function paginate(array, page, pageSize) {
  const start = (page - 1) * pageSize;
  return array.slice(start, start + pageSize);
}
const totalUpcomingPages = Math.max(1, Math.ceil(upcomingEvents.length / PAGE_SIZE));
const totalPastPages = Math.max(1, Math.ceil(pastEvents.length / PAST_PAGE_SIZE));
 
  // Fetch events from backend
  const fetchEvents = async () => {
    setLoading(true);
    const [upcoming, past] = await Promise.all([
      EventService.getAllEvents("upcoming"),
      EventService.getAllEvents("past"),
    ]);
    setUpcomingEvents(upcoming || []);
    setPastEvents(past || []);
    setLoading(false);
  };


  useEffect(() => {
    localStorage.setItem("eventsTab", activeTab);
    // Reset pagination when switching tabs
    if (activeTab === "upcoming") {
      setUpcomingPage(1);
    } else if (activeTab === "past") {
      setPastPage(1);
    }
  }, [activeTab]);


  useEffect(() => {
    fetchEvents();
    // eslint-disable-next-line
  }, []);

  const handleEventCreated = () => {
    fetchEvents();
  };

  const toggleExpand = (id) => {
    setExpandedEvents(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const toggleShowFullDescription = (eventId) => {
    setShowFullDescription((prev) => ({
      ...prev,
      [eventId]: !prev[eventId],
    }));
  };

  // Delete event logic
  const handleDeleteClick = (event) => {
    setEventToDelete(event);
    setConfirmText("");
    setDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!eventToDelete) return;
    const deleted = await EventService.deleteEvent(eventToDelete.id || eventToDelete._id);
    if (deleted) {
      setDeleteModalOpen(false);
      setEventToDelete(null);
      setConfirmText("");
      fetchEvents();
    }
  };

  // Duplicate event logic
  const handleDuplicateClick = (event) => {
    setEventToDuplicate(event);
    setConfirmText("");
    setDuplicateModalOpen(true);
  };

  const handleDuplicateConfirm = async () => {
    if (!eventToDuplicate) return;
    const duplicated = await EventService.duplicateEvent(eventToDuplicate.id || eventToDuplicate._id);
    if (duplicated) {
      setDuplicateModalOpen(false);
      setEventToDuplicate(null);
      setConfirmText("");
      fetchEvents();
      Swal.fire('Success!', 'Event duplicated successfully.', 'success');
    }
  };

  // Open Update Modal
  const handleOpenUpdateModal = (event) => {
    setEventToUpdate(event);
    setIsUpdateEventModalOpen(true);
  };

  // After update, refresh events
  const handleEventUpdated = () => {
    setIsUpdateEventModalOpen(false);
    setEventToUpdate(null);
    fetchEvents();
  };

  // Open Attend Modal
  const handleAttendEvent = (eventId) => {
    setAttendEventId(eventId);
    setShowAttendModal(true);
    setAttendEmail('');
    setAttendName('');
  };

  // Submit Attendance
  const handleAttendSubmit = async () => {
    if (!attendEmail || !attendName) return;
    setAttending(true);
    const res = await EventService.attendEvent(attendEventId, { name: attendName, email: attendEmail });
    setAttending(false);
    if (res) {
      Swal.fire('Success!', 'Attendance confirmed. Check your email for the meeting link.', 'success');
      setShowAttendModal(false);
      fetchEvents();
    }
  };

  // Handler to add a new recording link
const handleAddRecording = async (eventId) => {
  const link = (addRecordingInput[eventId] || "").trim();
  if (!link) return Swal.fire('Error', 'Please enter a recording link.', 'error');
  try {
    await EventService.addRecordingLink(eventId, link);
    setShowRecordingForm(prev => ({ ...prev, [eventId]: false }));
    setAddRecordingInput(prev => ({ ...prev, [eventId]: "" }));
    setEditingIndex(null);
    await fetchEvents();
    Swal.fire('Success', 'Recording link added successfully.', 'success');
  } catch (err) {
    Swal.fire('Error', 'Failed to add recording link.', 'error');
  }
};

const handleEditRecording = async (eventId, index) => {
  const link = (editRecordingInput[eventId] || "").trim();
  if (!link) return Swal.fire('Error', 'Please enter a recording link.', 'error');
  try {
    const res = await EventService.updateRecordingLink(eventId, index, link);
    // Only show success if backend confirms
    if (res && res.success) {
      setShowRecordingForm(prev => ({ ...prev, [eventId]: false }));
      setEditRecordingInput(prev => ({ ...prev, [eventId]: "" }));
      setEditingIndex(null);
      await fetchEvents();
      Swal.fire('Success', 'Recording link updated successfully.', 'success');
    } else {
      throw new Error('Update failed');
    }
  } catch (err) {
    Swal.fire('Error', 'Failed to update recording link.', 'error');
  }
};

// Call this when opening the form for add/edit
const openRecordingForm = (eventId, link = "", index = null) => {
  setShowRecordingForm(prev => ({ ...prev, [eventId]: true }));
  setRecordingInput(prev => ({ ...prev, [eventId]: link }));
  setEditingIndex(index);
  setHoveredInput(1);
};

// Call this to cancel
const handleCancelRecording = (eventId) => {
  setShowRecordingForm(prev => ({ ...prev, [eventId]: false }));
  setAddRecordingInput(prev => ({ ...prev, [eventId]: "" }));
  setEditRecordingInput(prev => ({ ...prev, [eventId]: "" }));
  setEditingIndex(null);
  setHoveredInput(1);
};

// Helper for empty state message (without democracy image)
  const renderEmptyState = (type) => (
    <div className="flex flex-col items-center justify-center rounded-2xl shadow-md bg-gradient-to-br from-white via-gray-50 to-purple-50 p-6 sm:p-10 font-karla text-center text-gray-600 mt-4 border border-gray-100">
      <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 text-gray-700">
        No {type === "upcoming" ? "Upcoming" : "Past"} Events Available
      </h3>
      <p className="text-base sm:text-lg text-gray-500 mb-4 max-w-xl">
        There are currently no {type === "upcoming" ? "upcoming" : "past"} events.<br />
        Please check back later or contact your DAO admin for more information.
      </p>
    </div>
  );

  // Helper for loading spinner
  const renderSpinner = () => (
    <div className="flex flex-col items-center justify-center py-16">
      <svg className="animate-spin h-12 w-12 text-[#2c5282] mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
      </svg>
      <span className="text-lg text-[#2c5282] font-karla">Loading events...</span>
    </div>
  );

  // Helper to format date (removes T00:00:00.000Z and makes it readable)
  function formatDate(dateStr) {
    if (!dateStr) return "";
    // If ISO string, just take the date part
    if (dateStr.includes("T")) return dateStr.split("T")[0];
    return dateStr;
  }


  // ...existing code...

function isEventHappeningNow(event) {
  if (!event.date || !event.time || !event.duration) return false;

  // Parse event start in EAT (UTC+3)
  const tzOffsetMinutes = 180;
  let [hours, minutes] = [0, 0];
  let t = (event.time || '').trim().toLowerCase();
  if (t.includes('am') || t.includes('pm')) {
    let match = t.match(/(\d{1,2}):(\d{2})\s*(am|pm)/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
      if (match[3] === 'pm' && hours !== 12) hours += 12;
      if (match[3] === 'am' && hours === 12) hours = 0;
    }
  } else {
    let match = t.match(/(\d{1,2}):(\d{2})/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
    }
  }
  // Create start date in EAT
  let start = new Date(event.date);
  start.setUTCHours(hours - tzOffsetMinutes / 60, minutes, 0, 0);
  // End time
  let end = new Date(start.getTime() + event.duration * 60000);

  // Current time in UTC
  let now = new Date();

  return now >= start && now < end;
}

  return (
    <div className="bg-[#f8f5ed] w-full max-w-full sm:max-w-5xl md:max-w-6xl  lg:max-w-7xl   p-0 sm:p-6">
      {/* Header and Create Event Button */}
      <div className="mb-6 flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="w-full md:w-auto">
         <h1 className="font-karla font-medium text-xl sm:text-2xl md:text-3xl lg:text-[48px] leading-[100%] tracking-[0] text-[#2c5282] mb-2 text-center md:text-left flex items-center">
            <FiVideo className="inline-block mr-2 w-[1.5rem] h-[1.5rem] sm:w-[2rem] sm:h-[2rem] md:w-[2.5rem] md:h-[2.5rem] lg:w-[48px] lg:h-[48px] min-w-[1.5rem] min-h-[1.5rem] sm:min-w-[2rem] sm:min-h-[2rem] md:min-w-[2.5rem] md:min-h-[2.5rem] lg:min-w-[54px] lg:min-h-[54px]" />
            Event Management
          </h1>
          <p className="font-karla font-light text-base sm:text-lg md:text-[24px] leading-[100%] tracking-[0] text-[#2C5282] mb-6  md:text-left sm:text-left">
            Create and manage DAO member events
          </p>
        </div>
        <div className="w-full md:w-auto flex justify-center md:justify-end">
          <button
            onClick={() => setIsCreateEventModalOpen(true)}
            className="flex items-center bg-[#2c5282] hover:bg-[#1a365d] transition-colors rounded-[20px] w-full sm:w-[220px] md:w-[260px] lg:w-[308px] h-[48px] sm:h-[60px] md:h-[70px] lg:h-[85px] px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4"
            style={{ minWidth: "0", minHeight: "0" }}
          >
            <span className="flex items-center text-white justify-center rounded-[20px] w-[28px] h-[28px] sm:w-[34px] sm:h-[34px] mr-3 sm:mr-4">
              <FiPlus className="w-[20px] h-[20px] sm:w-[24px] sm:h-[24px]" />
            </span>
            <span className="font-poppins font-normal text-base sm:text-lg md:text-xl lg:text-[32px] leading-[100%] tracking-[0] text-white">
              Create Event
            </span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-row gap-2 sm:gap-6 md:gap-8 mb-6 relative border-b border-gray-200 overflow-x-auto lg:overflow-x-visible scrollbar-hide">
        <div
          className={`font-medium cursor-pointer relative flex items-center pb-2 transition-colors text-xs sm:text-base md:text-lg ${
            activeTab === "upcoming" ? "text-[#2c5282]" : "text-gray-500"
          }`}
          style={{ minWidth: 0 }}
          onClick={() => setActiveTab("upcoming")}
        >
          <CiClock2 className="mr-1 sm:mr-2 w-[1.1rem] h-[1.1rem] sm:w-[1.2rem] sm:h-[1.2rem] md:w-[28px] md:h-[28px]" />
          <span className="truncate font-poppins font-normal text-base sm:text-lg md:text-[22px] lg:text-[26px] leading-[100%] tracking-[0]">
            Upcoming Events
          </span>
          {activeTab === "upcoming" && (
            <span className="absolute left-0 -bottom-[2px] w-full h-1 bg-[#2c5282] rounded transition-all duration-200"></span>
          )}
        </div>
        <span className="block sm:hidden w-4"></span>
        <div
          className={`font-medium cursor-pointer relative flex items-center pb-2 transition-colors text-xs sm:text-base md:text-lg ${
            activeTab === "past" ? "text-[#2c5282]" : "text-gray-500"
          }`}
          style={{ minWidth: 0 }}
          onClick={() => setActiveTab("past")}
        >
          <FiCheckCircle className="mr-1 sm:mr-2 w-[1.1rem] h-[1.1rem] sm:w-[1.2rem] sm:h-[1.2rem] md:w-[28px] md:h-[28px]" />
          <span className="truncate font-poppins font-normal text-base sm:text-lg md:text-[22px] lg:text-[26px] leading-[100%] tracking-[0]">
            Past Events
          </span>
          {activeTab === "past" && (
            <span className="absolute left-0 -bottom-[2px] w-full h-1 bg-[#2c5282] rounded transition-all duration-200"></span>
          )}
        </div>
      </div>

      {/* Upcoming Events Content */}
      {activeTab === "upcoming" && (
        <div className="min-h-[200px]">
          {loading ? (
            renderSpinner()
          ) : upcomingEvents.length === 0 ? (
            renderEmptyState("upcoming")
          ) : (
            <>
              {paginate(upcomingEvents, upcomingPage, PAGE_SIZE).map((event) => (
              <div 
                key={event.id || event._id}
                className="bg-white w-full p-2 sm:p-3 md:p-5 rounded-xl border border-gray-200 mb-6 relative"
              >
                {/* Action Buttons */}
               

               <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-5 lg:ml-5 md:ml-5 w-full">
                    {/* Icon + Title */}
                    <div className="flex flex-row items-center gap-2 sm:gap-3 flex-1 justify-start w-full">
                      <div className="flex items-center justify-center w-[38px] h-[38px] sm:w-[50px] sm:h-[50px] md:w-[70px] md:h-[70px] rounded-[12px] sm:rounded-[16px] md:rounded-[20px] bg-[#f8f5ed]">
                        <FiCalendar className="text-[#2C5282] w-[18px] h-[18px] sm:w-[24px] sm:h-[24px] md:w-[32px] md:h-[32px] border-[2px] border-[#2C5282] rounded-[8px] box-border p-1" />
                      </div>
                      <h3 className="font-karla font-medium text-lg sm:text-2xl md:text-[26px] lg:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                        {event.title}
                      </h3>
                    </div>
                    {/* Action Buttons */}
                    <div className="flex flex-row gap-2 z-10 mt-2 sm:mt-0 w-full sm:w-auto justify-start sm:justify-end md:mr-6 lg:mr-6">
                      <button
                        className="flex items-center justify-center gap-1 sm:gap-2 bg-[#f8f5ed] text-[#000000] hover:bg-gray-100 w-[120px] sm:w-[140px] md:w-[130px] lg:w-[198px] h-[48px] sm:h-[44px] md:h-[54px] lg:h-[67px] min-w-0 rounded-[16px] sm:rounded-[16px] md:rounded-[18px] lg:rounded-[20px] border border-[#a4a4a4] transition-all text-base sm:text-lg md:text-[22px] lg:text-[26px]"
                        onClick={() => handleDuplicateClick(event)}
                      >
                        <FiCopy className="text-[#2c5282] w-[22px] h-[22px] sm:w-[22px] sm:h-[22px] md:w-[26px] md:h-[26px] lg:w-[30px] lg:h-[30px]" />
                        <span className="font-karla font-normal leading-[100%] tracking-[0] text-[#2c5282]">
                          Duplicate
                        </span>
                      </button>
                      <button
                        className="flex items-center justify-center gap-1 sm:gap-2 bg-[#f8f5ed] text-[#FB7185] hover:bg-red-100 w-[120px] sm:w-[120px] md:w-[130px] lg:w-[198px] h-[48px] sm:h-[44px] md:h-[54px] lg:h-[67px] min-w-0 rounded-[16px] sm:rounded-[16px] md:rounded-[18px] lg:rounded-[20px] border border-[#FB7185] transition-all text-base sm:text-sm md:text-base lg:text-[26px]"
                        onClick={() => handleDeleteClick(event)}
                      >
                        <FiTrash2 className="text-[#FB7185] w-[22px] h-[22px] sm:w-[22px] sm:h-[22px] md:w-[26px] md:h-[26px] lg:w-[30px] lg:h-[30px]" />
                        <span className="font-karla font-normal leading-[100%] tracking-[0] text-[#FB7185]">
                          Delete
                        </span>
                      </button>
                    </div>
                  </div>

                <div className="font-karla font-light text-base sm:text-base md:text-[16px] lg:text-[26px] leading-[100%] tracking-[0] text-gray-600 mb-4 mx-4 sm:mx-8 md:mx-14 whitespace-pre-line">
                  {showFullDescription[event.id || event._id] ? (
                    <>
                      <span>{event.description}</span>
                      {event.description && event.description.length > 200 && (
                        <button
                          className="text-purple-600 font-semibold hover:underline ml-2"
                          onClick={() => toggleShowFullDescription(event.id || event._id)}
                          type="button"
                        >
                          Read less
                        </button>
                      )}
                    </>
                  ) : (
                    <>
                      <span>
                        {event.description?.substring(0, 200)}
                        {event.description && event.description.length > 200 && <span className="mx-1">...</span>}
                      </span>
                      {event.description && event.description.length > 200 && (
                        <button
                          className="text-purple-600 font-semibold hover:underline ml-2"
                          onClick={() => toggleShowFullDescription(event.id || event._id)}
                          type="button"
                        >
                          Read more
                        </button>
                      )}
                    </>
                  )}
                </div>

             
              {/* Event Info Strip */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2 md:gap-4">
              <div
                className="
                  flex flex-col
                  [@media(min-width:1193px)]:flex-row
                  items-stretch [@media(min-width:1193px)]:items-center
                  justify-between
                  mb-4
                  gap-2
                  [@media(min-width:1193px)]:gap-[0.25rem]
                  w-full
                  text-[#2C5282]
                "
              >
                <span className="flex items-center bg-[#f8f5ed] px-6 sm:px-8 md:px-10 lg:px-8 py-3 sm:py-4 md:py-5 lg:py-4 rounded-xl font-karla font-light text-lg sm:text-xl md:text-2xl lg:text-[26px] leading-[100%] tracking-[0] w-full justify-center text-center lg:w-auto lg:justify-start lg:text-left">
                    <FiClock className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                    {isEventHappeningNow(event)
                      ? <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full font-semibold text-xs sm:text-sm md:text-base">
                          (event happening right now)
                        </span>
                      : <>{formatDate(event.date)} · {event.time} ({event.duration} mins)</>
                    }
                  </span>
                <span className="flex items-center bg-[#f8f5ed] px-6 sm:px-8 md:px-10 lg:px-8 py-3 sm:py-4 md:py-5 lg:py-4 rounded-xl font-karla font-light text-lg sm:text-xl md:text-2xl lg:text-[26px] leading-[100%] tracking-[0] w-full justify-center text-center lg:w-auto lg:justify-start lg:text-left">
                  <FiUsers className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                  {event.attendees.length} attending
                </span>
                <span className="flex items-center bg-[#f8f5ed] px-6 sm:px-8 md:px-10 lg:px-8 py-3 sm:py-4 md:py-5 lg:py-4 rounded-xl font-karla font-light text-lg sm:text-xl md:text-2xl lg:text-[26px] leading-[100%] tracking-[0] w-full justify-center text-center lg:w-auto lg:justify-start lg:text-left">
                  <FiMessageSquare className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                  {event.questions.length} questions
                  {event.questions.length >= 3 && (
                    <span
                      className="ml-2 sm:ml-3 px-2 sm:px-3 py-1 sm:py-1.5 bg-[#ede9fe] text-[#6d28d9] rounded-lg text-xs sm:text-sm md:text-base font-semibold cursor-pointer"
                      title="Scroll to questions"
                      onClick={() => {
                        const questionsSection = document.getElementById(`questions-section-${event.id || event._id}`);
                        if (questionsSection) {
                          questionsSection.scrollIntoView({ behavior: "smooth", block: "center" });
                        }
                      }}
                    >
                      Scroll
                    </span>
                  )}
                </span>
              </div>
            </div>

                {/* Attendees, Senior Engineer & Questions */}
                <div className="flex flex-col  md:gap-8 gap-4 mb-4 items-stretch">
                  {/* Attendees */}
                  <div className="flex-1 min-w-0 md:min-w-[320px] p-2 sm:p-4 md:p-6 rounded-md bg-white flex flex-col">
                    <h4 className="font-karla font-medium text-base sm:text-xl md:text-[26px] lg:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                      <FiUsers className="mt-1 inline-block mr-1"/>
                      Attendees ({event.attendees.length})
                    </h4>
                    <div
                      className={`space-y-2 bg-[#f8f5ed] p-2 sm:p-3 md:p-4 rounded-md flex-1`}
                      style={{ minHeight: "3rem" }}
                    >
                      {event.attendees.length === 0 ? (
                        <div className="bg-yellow-50 p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-center justify-center h-full">
                          <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#b45309]">
                            No attendees yet.
                          </p>
                        </div>
                      ) : (
                        event.attendees.map((attendee, index) => (
                          <div key={index} className="bg-white p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-start sm:items-center justify-between" style={{ wordBreak: "break-word" }}>
                            <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#496a93]">{attendee.name}</p>
                            <p className="font-poppins font-light text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#2C5282]">{attendee.email}</p>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                 

                  {/* Questions */}
                 {/* Questions */}
                  <div className="flex-1 min-w-0 md:min-w-[320px] p-2 sm:p-4 md:p-6 rounded-md bg-white flex flex-col">
                    <h4 className="font-karla font-medium text-base sm:text-xl md:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                      <FiMessageSquare className="inline-block mr-1" />
                      Questions ({event.questions.length})
                    </h4>
                    <div
                      className={`space-y-2 sm:space-y-3 md:space-y-5 bg-[#f8f5ed] p-2 sm:p-3 md:p-4 rounded-md flex-1 ${
                        event.questions.length > 2 ? "overflow-y-auto" : ""
                      }`}
                      style={event.questions.length > 2 ? { maxHeight: "20rem" } : {}}
                      id={`questions-section-${event.id || event._id}`}
                    >
                      {event.questions.length === 0 ? (
                        <div className="bg-yellow-50 p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-center justify-center h-full">
                          <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#b45309]">
                            No questions yet.
                          </p>
                        </div>
                      ) : (
                        event.questions.map((question, index) => (
                          <div
                            key={index}
                            className="bg-white p-2 sm:p-3 md:p-4 rounded-lg shadow-sm"
                            style={{ wordBreak: "break-word", overflowX: "auto" }}
                          >
                            <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#000000] break-words">
                              {question.text}
                            </p>
                            <p className="font-poppins font-light text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#2C5282] mt-1">
                              Submitted by: {question.submittedBy}
                            </p>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>

                {/* Meeting Link */}
                <div className="p-2 sm:p-4 rounded-md">
                  <h4 className="font-karla font-medium text-base sm:text-xl md:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2 lg:ml-3">
                    <FiCopy className="inline-block mt-1" />
                    Meeting link
                  </h4>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center bg-[#F8F5ED] px-4 sm:px-6 md:px-10 py-4 sm:py-6 md:py-8 rounded-2xl">
                    <a
                      href={event.meetingLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-poppins font-light text-xs sm:text-base md:text-[24px] leading-[100%] tracking-[0] text-[#000000] hover:underline break-all"
                    >
                      {event.meetingLink}
                    </a>
                  </div>
                </div>

                {/* Attend Event Button */}
                <div className="mt-4 flex flex-col sm:flex-row lg:ml-5 md:ml-5 gap-2">
                  <button
                    className="flex items-center gap-2 min-w-[70px] sm:min-w-[120px] md:min-w-[180px] px-6 py-3 sm:px-6 sm:py-3 md:px-8 md:py-4 bg-[#2c5282] text-white text-xs sm:text-base md:text-[22px] font-medium rounded-lg shadow-md transition"
                    onClick={() => handleOpenUpdateModal(event)}
                  >
                    Update event
                  </button>
                </div>
              </div>
              ))}

              {/* Pagination for Upcoming Events */}
              {upcomingEvents.length > PAGE_SIZE && (
                <div className="flex flex-col items-center gap-4 mt-8 mb-4">
                  {/* Page Info */}
                  <div className="text-sm text-gray-600 font-karla">
                    Showing {((upcomingPage - 1) * PAGE_SIZE) + 1} to {Math.min(upcomingPage * PAGE_SIZE, upcomingEvents.length)} of {upcomingEvents.length} upcoming events
                  </div>

                  {/* Pagination Controls */}
                  <div className="flex items-center justify-center gap-2 sm:gap-4">
                    <button
                      onClick={() => setUpcomingPage(prev => Math.max(1, prev - 1))}
                      disabled={upcomingPage === 1}
                      className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-[#2c5282] text-white rounded-lg hover:bg-[#1a365d] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm sm:text-base"
                    >
                      <FiChevronLeft className="w-4 h-4" />
                      <span className="hidden sm:inline">Previous</span>
                    </button>

                    <div className="flex items-center gap-1 sm:gap-2">
                      {Array.from({ length: Math.min(totalUpcomingPages, 5) }, (_, i) => {
                        let page;
                        if (totalUpcomingPages <= 5) {
                          page = i + 1;
                        } else if (upcomingPage <= 3) {
                          page = i + 1;
                        } else if (upcomingPage >= totalUpcomingPages - 2) {
                          page = totalUpcomingPages - 4 + i;
                        } else {
                          page = upcomingPage - 2 + i;
                        }

                        return (
                          <button
                            key={page}
                            onClick={() => setUpcomingPage(page)}
                            className={`px-2 sm:px-3 py-2 rounded-lg transition-colors text-sm sm:text-base ${
                              page === upcomingPage
                                ? 'bg-[#2c5282] text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => setUpcomingPage(prev => Math.min(totalUpcomingPages, prev + 1))}
                      disabled={upcomingPage === totalUpcomingPages}
                      className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-[#2c5282] text-white rounded-lg hover:bg-[#1a365d] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm sm:text-base"
                    >
                      <span className="hidden sm:inline">Next</span>
                      <FiChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Past Events Content */}
      {activeTab === "past" && (
        <div className="min-h-[200px]">
          {loading ? (
            renderSpinner()
          ) : pastEvents.length === 0 ? (
            renderEmptyState("past")
          ) : (
            <>
              {paginate(pastEvents, pastPage, PAST_PAGE_SIZE).map((event) => (
              <div 
                key={event.id || event._id}
                className="bg-[#FFFFFF] w-full px-2 py-2 sm:px-4 sm:py-3 sm:p-5 rounded-2xl sm:rounded-3xl border mb-8 transition-colors duration-200"
              >
                {/* Event Header */}
                <div
                  className="cursor-pointer"
                  onClick={() => toggleExpand(event.id || event._id)}
                >
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 mb-5 w-full">
                      <div className="flex flex-row items-center gap-2 sm:gap-3 flex-1 justify-start w-full">
                        <div className="flex items-center justify-center w-[38px] h-[38px] sm:w-[70px] sm:h-[70px] rounded-[12px] sm:rounded-[20px] bg-[#f8f5ed]">
                          <FiCalendar className="text-[#2C5282] w-[18px] h-[18px] sm:w-[32px] sm:h-[32px] border-[2px] border-[#2C5282] rounded-[8px] box-border p-1" />
                        </div>
                        <h3 className="font-karla font-medium text-lg sm:text-[32px] leading-[100%] tracking-[0] text-[#496a93] flex items-center gap-2">
                          {event.title}
                        </h3>
                      

                      </div>
                      <div className="flex flex-row gap-2 z-10 mt-2 sm:mt-0 w-full sm:w-auto justify-start sm:justify-end">
                        <button
                          className="flex items-center justify-center gap-2 bg-[#f8f5ed] text-[#FB7185] hover:bg-red-100 w-[170px] sm:w-[198px] h-[50px] sm:h-[67px] min-w-0 rounded-[20px] sm:rounded-[20px] border border-[#FB7185] transition-all text-lg sm:text-[26px]"
                          onClick={(e) => { e.stopPropagation(); handleDeleteClick(event); }}
                        >
                          <FiTrash2 className="text-[#FB7185] w-[28px] h-[28px] sm:w-[30px] sm:h-[30px]" />
                          <span className="font-karla font-normal leading-[100%] tracking-[0] text-[#FB7185]">
                            Delete
                          </span>
                        </button>
                      </div>
                    </div>
                 <p className="font-karla whitespace-pre-line font-light text-base sm:text-[26px] leading-[100%] tracking-[0] text-[#000000] mb-4 ml-0 sm:ml-14 mt-4">
                      {showFullDescription[event.id || event._id] ? (
                        <>
                          <span>{event.description}</span>
                          {event.description && event.description.length > 200 && (
                            <button
                              className="text-purple-600 font-semibold hover:underline ml-2"
                              onClick={e => {
                                e.stopPropagation();
                                toggleShowFullDescription(event.id || event._id);
                              }}
                              type="button"
                            >
                              Read less
                            </button>
                          )}
                        </>
                      ) : (
                        <>
                          <span>
                            {event.description?.substring(0, 200)}
                            {event.description && event.description.length > 200 && <span className="mx-1">...</span>}
                          </span>
                          {event.description && event.description.length > 200 && (
                            <button
                              className="text-purple-600 font-semibold hover:underline ml-2"
                              onClick={e => {
                                e.stopPropagation();
                                toggleShowFullDescription(event.id || event._id);
                              }}
                              type="button"
                            >
                              Read more
                            </button>
                          )}
                        </>
                      )}
                    </p>
               {/* Event Info Strip with Toggle */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2 md:gap-4">
                    <div className="
                      flex flex-col
                      [@media(min-width:1193px)]:flex-row
                      items-stretch [@media(min-width:1193px)]:items-center
                      justify-between
                      mb-4 gap-2 md:gap-4
                      w-full
                      text-[#2C5282]
                    
                    ">
                      <span className="
                        flex items-center
                        bg-[#f8f5ed]
                        px-6 sm:px-8 md:px-10 lg:px-8
                        py-3 sm:py-4 md:py-5 lg:py-4
                        rounded-xl
                        font-karla font-light
                        text-lg sm:text-xl md:text-2xl lg:text-[26px]
                        leading-[100%] tracking-[0]
                        w-full
                        justify-center text-center
                        lg:w-auto lg:justify-start lg:text-left
                        ">
                        <FiClock className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                        {formatDate(event.date)} · {event.time} ({event.duration} mins)
                      </span>
                      <span className="
                        flex items-center
                        bg-[#f8f5ed]
                        px-6 sm:px-8 md:px-10 lg:px-8
                        py-3 sm:py-4 md:py-5 lg:py-4
                        rounded-xl
                        font-karla font-light
                        text-lg sm:text-xl md:text-2xl lg:text-[26px]
                        leading-[100%] tracking-[0]
                        w-full
                        justify-center text-center
                        lg:w-auto lg:justify-start lg:text-left
                        ">
                        <FiUsers className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                        {event.attendees.length} attending
                      </span>
                      <span className="
                        flex items-center
                        bg-[#f8f5ed]
                        px-6 sm:px-8 md:px-10 lg:px-8
                        py-3 sm:py-4 md:py-5 lg:py-4
                        rounded-xl
                        font-karla font-light
                        text-lg sm:text-xl md:text-2xl lg:text-[26px]
                        leading-[100%] tracking-[0]
                        w-full
                        justify-center text-center
                        lg:w-auto lg:justify-start lg:text-left
                        ">
                        <FiMessageSquare className="mr-2 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 lg:w-7 lg:h-7" />
                        {event.questions.length} questions
                        {event.questions.length >= 3 && (
                          <span
                            className="ml-2 sm:ml-3 px-2 sm:px-3 py-1 sm:py-1.5 bg-[#ede9fe] text-[#6d28d9] rounded-lg text-xs sm:text-sm md:text-base font-semibold cursor-pointer"
                            title="Scroll to questions"
                            onClick={() => {
                              const questionsSection = document.getElementById(`questions-section-${event.id || event._id}`);
                              if (questionsSection) {
                                questionsSection.scrollIntoView({ behavior: "smooth", block: "center" });
                              }
                            }}
                          >
                            Scroll
                          </span>
                        )}
                      </span>
                    </div>
                    {/* Toggle button, always visible, scales, never out of screen */}
                    <button
                      className="flex items-center justify-center ml-0 sm:ml-4 mt-4 sm:mt-0 rounded-full bg-[#ede9fe] hover:bg-[#c7bfff] transition-all w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 shadow-lg shrink-0"
                      aria-label={expandedEvents[event.id || event._id] ? "Collapse details" : "Expand details"}
                      onClick={e => { e.stopPropagation(); toggleExpand(event.id || event._id); }}
                      type="button"
                      tabIndex={0}
                    >
                      {expandedEvents[event.id || event._id] ? (
                        <FiChevronUp className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 text-[#2C5282]" />
                      ) : (
                        <FiChevronDown className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 text-[#2C5282]" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Expanded Content */}
                {expandedEvents[event.id || event._id] && (
                  <div className="bg-white w-full p-2 sm:p-5 rounded-xl mb-6 relative mt-4">
                    <hr className="my-6 border-t border-gray-200" />
                    {/* Attendees & Questions */}
                    <div className="flex flex-col md:flex-col gap-4 mb-4">
                      {/* Attendees */}
                      <div className="flex-1 p-2 sm:p-4 rounded-md bg-white">
                        <h4 className="font-karla font-medium text-xl sm:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                          <FiUsers className="mt-1 inline-block mr-1"/>
                          Attendees ({event.attendees.length})
                        </h4>
                        <div
                          className={`space-y-2 bg-[#f8f5ed] p-2 sm:p-3 md:p-4 rounded-md ${
                            event.attendees.length > 5 ? "max-h-60 overflow-y-auto" : ""
                          }`}
                          style={{ minHeight: "3rem" }}
                        >
                          {event.attendees.length === 0 ? (
                            <div className="bg-yellow-50 p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-center justify-center">
                              <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#b45309]">
                                There were no attendees.
                              </p>
                            </div>
                          ) : (
                            event.attendees.map((attendee, index) => (
                              <div key={index} className="bg-white p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-start sm:items-center justify-between" style={{ wordBreak: "break-word" }}>
                                <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#496a93]">{attendee.name}</p>
                                <p className="font-poppins font-light text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#2C5282]">{attendee.email}</p>
                              </div>
                            ))
                          )}
                        </div>
                      </div>

                      {/* Questions */}
                      {/* Questions */}
                      <div className="flex-1 min-w-0 md:min-w-[320px] p-2 sm:p-4 md:p-6 rounded-md bg-white flex flex-col">
                        <h4 className="font-karla font-medium text-base sm:text-xl md:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                          <FiMessageSquare className="inline-block mr-1" />
                          Questions ({event.questions.length})
                        </h4>
                        <div
                          className={`space-y-2 sm:space-y-3 md:space-y-5 bg-[#f8f5ed] p-2 sm:p-3 md:p-4 rounded-md flex-1 ${
                            event.questions.length > 4 ? "overflow-y-auto" : ""
                          }`}
                          style={event.questions.length > 4 ? { maxHeight: "20rem" } : {}}
                          id={`questions-section-${event.id || event._id}`}
                        >
                          {event.questions.length === 0 ? (
                            <div className="bg-yellow-50 p-2 sm:p-3 md:p-4 rounded-lg shadow-sm flex flex-col sm:flex-row items-center justify-center h-full">
                              <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#b45309]">
                                No questions yet.
                              </p>
                            </div>
                          ) : (
                            event.questions.map((question, index) => (
                              <div
                                key={index}
                                className="bg-white p-2 sm:p-3 md:p-4 rounded-lg shadow-sm"
                                style={{ wordBreak: "break-word", overflowX: "auto" }}
                              >
                                <p className="font-poppins font-normal text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#000000] break-words">
                                  {question.text}
                                </p>
                                <p className="font-poppins font-light text-base sm:text-[24px] leading-[100%] tracking-[0] text-[#2C5282] mt-1">
                                  Submitted by: {question.submittedBy}
                                </p>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Meeting Link */}
                   
                   
                   <div className="p-2 sm:p-4 rounded-md">
                        <h4 className="font-karla font-medium text-xl sm:text-[32px] leading-[100%] tracking-[0] text-[#496a93] mb-2 flex items-center gap-2">
                          <FiVideo className="inline-block mt-1" />
                          Recording
                        </h4>

                        {showRecordingForm[event.id || event._id] ? (
                          <div className="mt-4 bg-[#FFFFFF] rounded-[20px] p-4 sm:p-6">
                            <div className="space-y-4">
                              <div className="flex items-center">
                             <button
                                  type="button"
                                  className="flex flex-row items-center justify-center w-full max-w-full sm:w-[446px] h-[52px] sm:h-[85px] rounded-[20px] bg-[#2C5282] hover:bg-[#1a365d] transition-colors px-3"
                                  style={{ minWidth: 0, minHeight: 0 }}
                                  onClick={() =>
                                    setShowRecordingForm((prev) => ({
                                      ...prev,
                                      [event.id || event._id]: false,
                                    }))
                                  }
                                >
                                  <FiVideo className="text-white w-5 h-5 sm:w-8 sm:h-8 mr-2 sm:mr-4 flex-shrink-0" />
                                  <span className="font-poppins font-medium text-sm sm:text-xl md:text-2xl text-white text-left">
                                    Add Recording Link
                                  </span>
                                </button>
                              </div>

                              {/* First input: cancel always visible */}
                              <div
  className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full"
  onMouseEnter={() => setHoveredInput(1)}
  onMouseLeave={() => setHoveredInput(0)}
>
  <input
    type="text"
    placeholder="Enter recording URL"
    className="w-full sm:w-[350px] md:w-[500px] lg:w-[900px] h-[44px] sm:h-[60px] md:h-[80px] rounded-[20px] text-black border border-gray-500 px-4 sm:px-6 font-poppins text-base bg-white"
    style={{ minWidth: 0 }}
    value={addRecordingInput[event.id || event._id] || ""}
    onChange={e =>
      setAddRecordingInput(prev => ({
        ...prev,
        [event.id || event._id]: e.target.value,
      }))
    }
  />
  <div className="flex flex-row gap-2 sm:gap-2 items-center sm:items-center mt-2 sm:mt-0">
    <button
      type="button"
      className="flex items-center justify-center w-[44px] sm:w-[48px] md:w-[69px] h-[44px] sm:h-[48px] md:h-[74px] rounded-[20px] bg-[#4FD1C5] cursor-pointer"
      title="Save Recording Link"
      onClick={() => handleAddRecording(event.id || event._id)}
      style={{ minWidth: 0, minHeight: 0 }}
    >
      <FiCheckCircle className="w-[22px] sm:w-[24px] md:w-[30px] h-[22px] sm:h-[24px] md:h-[30px] text-white" />
    </button>
   <button
        type="button"
        className="flex items-center justify-center w-[36px] sm:w-[30px] md:w-[30px] h-[36px] sm:h-[30px] md:h-[30px] rounded-[20px] bg-[#FB7185] hover:bg-gray-300 cursor-pointer shadow relative lg:-mt-7 sm:mt-1"
        title="Clear"
        onClick={() => setAddRecordingInput(prev => ({
          ...prev,
          [event.id || event._id]: ""
        }))}
        style={{ minWidth: 0, minHeight: 0 }}
      >
        <FiX className="w-[15px] h-[15px] text-white" />
      </button>
  </div>
</div>

                              {/* Second input: Edit recording URL, responsive layout */}
                              <div
  className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full"
  onMouseEnter={() => setHoveredInput(2)}
  onMouseLeave={() => setHoveredInput(0)}
>
  <input
    type="text"
    placeholder="Edit recording URL"
    className="w-full sm:w-[350px] md:w-[500px] lg:w-[900px] h-[44px] sm:h-[60px] md:h-[80px] rounded-[20px] text-black border border-gray-500 px-4 sm:px-6 font-poppins text-base bg-white"
    style={{ minWidth: 0 }}
    value={editRecordingInput[event.id || event._id] ?? event.recordingLinks?.[0] ?? ""}
    onChange={e =>
      setEditRecordingInput(prev => ({
        ...prev,
        [event.id || event._id]: e.target.value,
      }))
    }
  />
  <div className="flex flex-row gap-2 items-center mt-2 sm:mt-0">
    <button
      type="button"
      className="flex items-center justify-center w-[44px] sm:w-[48px] md:w-[69px] h-[44px] sm:h-[48px] md:h-[74px] rounded-[20px] bg-[#4FD1C5] cursor-pointer"
      title="Edit Recording Link"
      onClick={() => handleEditRecording(event.id || event._id, 0)}
      style={{ minWidth: 0, minHeight: 0 }}
    >
      <FiEdit2 className="w-[22px] sm:w-[24px] md:w-[30px] h-[22px] sm:h-[24px] md:h-[30px] text-white" />
    </button>
    <button
      type="button"
      className="flex items-center justify-center w-[36px] sm:w-[30px] md:w-[30px] h-[36px] sm:h-[30px] md:h-[30px] rounded-[20px] bg-[#FB7185] hover:bg-gray-300 cursor-pointer shadow lg:-mt-7 sm:mt-1"
      title="Clear"
      onClick={() => setEditRecordingInput(prev => ({
        ...prev,
        [event.id || event._id]: ""
      }))}
      style={{ minWidth: 0, minHeight: 0 }}
    >
      <FiX className="w-[15px] h-[15px] text-white" />
    </button>
  </div>
</div>
                            </div>
                          </div>
                        ) : (
                          <button
                            type="button"
                            onClick={() =>
                              setShowRecordingForm((prev) => ({
                                ...prev,
                                [event.id || event._id]: !prev[event.id || event._id],
                              }))
                            }
                            className="flex items-center justify-center w-full max-w-full sm:w-[446px] h-[60px] sm:h-[85px] rounded-[20px] bg-[#2C5282] hover:bg-[#1a365d] transition-colors"
                            style={{ minWidth: 0, minHeight: 0 }}
                          >
                            <FiVideo className="text-white w-8 h-8 mr-4" />
                            <span className="font-poppins font-medium text-lg sm:text-xl md:text-2xl text-white">
                              Add Recording Link
                            </span>
                          </button>
                        )}
                  </div>

              
                  </div>
                )}
              </div>
              ))}

              {/* Pagination for Past Events */}
              {pastEvents.length > PAST_PAGE_SIZE && (
                <div className="flex flex-col items-center gap-4 mt-8 mb-4">
                  {/* Page Info */}
                  <div className="text-sm text-gray-600 font-karla">
                    Showing {((pastPage - 1) * PAST_PAGE_SIZE) + 1} to {Math.min(pastPage * PAST_PAGE_SIZE, pastEvents.length)} of {pastEvents.length} past events
                  </div>

                  {/* Pagination Controls */}
                  <div className="flex items-center justify-center gap-2 sm:gap-4">
                    <button
                      onClick={() => setPastPage(prev => Math.max(1, prev - 1))}
                      disabled={pastPage === 1}
                      className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-[#2c5282] text-white rounded-lg hover:bg-[#1a365d] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm sm:text-base"
                    >
                      <FiChevronLeft className="w-4 h-4" />
                      <span className="hidden sm:inline">Previous</span>
                    </button>

                    <div className="flex items-center gap-1 sm:gap-2">
                      {Array.from({ length: Math.min(totalPastPages, 5) }, (_, i) => {
                        let page;
                        if (totalPastPages <= 5) {
                          page = i + 1;
                        } else if (pastPage <= 3) {
                          page = i + 1;
                        } else if (pastPage >= totalPastPages - 2) {
                          page = totalPastPages - 4 + i;
                        } else {
                          page = pastPage - 2 + i;
                        }

                        return (
                          <button
                            key={page}
                            onClick={() => setPastPage(page)}
                            className={`px-2 sm:px-3 py-2 rounded-lg transition-colors text-sm sm:text-base ${
                              page === pastPage
                                ? 'bg-[#2c5282] text-white'
                                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => setPastPage(prev => Math.min(totalPastPages, prev + 1))}
                      disabled={pastPage === totalPastPages}
                      className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-[#2c5282] text-white rounded-lg hover:bg-[#1a365d] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm sm:text-base"
                    >
                      <span className="hidden sm:inline">Next</span>
                      <FiChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Modals */}
      {deleteModalOpen && (
        <DeleteEventModal
          onClose={() => setDeleteModalOpen(false)}
          confirmText={confirmText}
          setConfirmText={setConfirmText}
          onDelete={handleDeleteConfirm}
        />
      )}
      {isCreateEventModalOpen && (
        <CreateEventModal
          onClose={() => setIsCreateEventModalOpen(false)}
          onEventCreated={handleEventCreated}
        />
      )}
      {isUpdateEventModalOpen && (
        <UpdateEventModal
          onClose={() => setIsUpdateEventModalOpen(false)}
          onEventUpdated={handleEventUpdated}
          event={eventToUpdate}
        />
      )}
      {duplicateModalOpen && (
        <DuplicateEventModal
          onClose={() => setDuplicateModalOpen(false)}
          confirmText={confirmText}
          setConfirmText={setConfirmText}
          onDuplicate={handleDuplicateConfirm}
        />
      )}
      {/* Render AttendEventModal */}
      {showAttendModal && (
        <AttendEventModal
          open={showAttendModal}
          onClose={() => setShowAttendModal(false)}
          email={attendEmail}
          setEmail={setAttendEmail}
          name={attendName}
          setName={setAttendName}
          onSubmit={handleAttendSubmit}
          loading={attending}
        />
      )}
    </div>
  );
};