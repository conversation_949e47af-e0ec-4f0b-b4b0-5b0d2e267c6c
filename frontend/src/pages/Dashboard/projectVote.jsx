import React, { useState, useEffect } from "react";
import { FaCalendarAlt } from "react-icons/fa";
import { CiClock2 } from "react-icons/ci";
import { FaDollarSign, FaArrowRight } from "react-icons/fa6";
import { IoMdArrowDropdown } from "react-icons/io";
import { VoteService } from "../../services/voteService";
import democracy from "../../assets/pro/democracy.png";
import { ProjectMultiVote } from "./projectMultiVote";
import { ProjectRankVote } from "./projectRankVote";
import { TfiList } from "react-icons/tfi";
import { UpdateVoteModal } from "../modals/UpdateVoteModal";
import { useAuthentication } from '../../components/utils/provider';
import { FaEdit } from "react-icons/fa";

// Utility function for time remaining
function getTimeRemaining(startDate, endDate) {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (now < start) {
    const startsIn = Math.max(0, Math.ceil((start - now) / (1000 * 60 * 60 * 24)));
    return `Starts in ${startsIn} day${startsIn !== 1 ? "s" : ""}`;
  } else if (now >= start && now <= end) {
    const diff = end - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    if (days > 0) {
      return `${days} day${days !== 1 ? "s" : ""} remaining`;
    } else {
      const hours = Math.floor((diff / (1000 * 60 * 60)) % 24);
      const minutes = Math.floor((diff / (1000 * 60)) % 60);
      const seconds = Math.floor((diff / 1000) % 60);
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")} remaining`;
    }
  } else {
    return "Ended";
  }
}

export const ProjectRankMultiVotes = () => {
  const [activeTab, setActiveTab] = useState(() => localStorage.getItem("voteTab") || "ongoing");
  const [votes, setVotes] = useState([]);
  const [expanded, setExpanded] = useState({});
  const [loading, setLoading] = useState(true);
  const [hoveredVoteId, setHoveredVoteId] = useState(null);
  const [editVote, setEditVote] = useState(null); // For modal
  const { currentUser } = useAuthentication();
  const [tick, setTick] = useState(0); // For live countdown

  useEffect(() => {
    localStorage.setItem("voteTab", activeTab);
  }, [activeTab]);

  useEffect(() => {
    const fetchVotes = async () => {
      setLoading(true);
      try {
        const allVotes = await VoteService.getAllVotes();
        setVotes(allVotes || []);
      } catch {
        setVotes([]);
      }
      setLoading(false);
    };
    fetchVotes();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => setTick(t => t + 1), 1000);
    return () => clearInterval(interval);
  }, []);

  const handleExpand = (voteId) => {
    setExpanded((prev) => ({
      ...prev,
      [voteId]: !prev[voteId],
    }));
  };

  const now = new Date();

  // Ongoing = active and started
  const activeRankedVotes = votes.filter(
    (v) =>
      v.votingType === "ranked" &&
      v.status === "active" &&
      new Date(v.startDate) <= now &&
      new Date(v.endDate) >= now
  );
  const activeMultiVotes = votes.filter(
    (v) =>
      v.votingType === "multi" &&
      v.status === "active" &&
      new Date(v.startDate) <= now &&
      new Date(v.endDate) >= now
  );

  // Upcoming = not started yet or pending/upcoming
  const upcomingRankedVotes = votes.filter(
    (v) =>
      v.votingType === "ranked" &&
      (
        v.status === "pending" ||
        v.status === "upcoming" ||
        (v.status === "active" && new Date(v.startDate) > now)
      )
  );
  const upcomingMultiVotes = votes.filter(
    (v) =>
      v.votingType === "multi" &&
      (
        v.status === "pending" ||
        v.status === "upcoming" ||
        (v.status === "active" && new Date(v.startDate) > now)
      )
  );

  // Tab logic
  const showRankedVotes =
    activeTab === "ongoing"
      ? activeRankedVotes
      : upcomingRankedVotes;
  const showMultiVotes =
    activeTab === "ongoing"
      ? activeMultiVotes
      : upcomingMultiVotes;

  // Helper: is admin
  const isAdmin = currentUser?.role === "admin";

  // Handler for saving vote details
  const handleSaveVoteDetails = async (payload) => {
    if (!editVote) return;
    const res = await VoteService.updateVoteDetails(editVote._id, payload);
    if (res && !res.error) {
      setVotes(votes =>
        votes.map(v => v._id === editVote._id ? { ...v, ...res } : v)
      );
      setEditVote(null);
    }
  };

  return (
    <div className="bg-[#f8f5ed] w-full sm:max-w-4xl lg:max-w-7xl mx-auto p-0 sm:p-6">
      <h1 className="text-xl sm:text-2xl font-karla font-semibold text-[#2c5282] mb-2 text-center sm:text-left">DAO Voting Portal</h1>
      <p className="text-[#bcc5cf] font-medium mb-6 text-center sm:text-left">Shape the future of our community</p>

      {/* Tabs */}
      <div className="flex flex-row gap-2 sm:gap-8 mb-6 relative">
        <div
          className={`font-medium cursor-pointer relative flex items-center pb-1 transition-colors text-xs sm:text-base ${
            activeTab === "ongoing" ? "text-gray-800" : "text-gray-500"
          }`}
          style={{ minWidth: 0 }}
          onClick={() => setActiveTab("ongoing")}
        >
          <CiClock2 className="mr-1 sm:mr-2 text-base sm:text-lg" />
          <span className="truncate">Ongoing votes</span>
          <span className="bg-[#4fd1c5] flex items-center justify-center h-4 w-4 rounded-full text-xs ml-1 text-white absolute -top-3 left-full">
            {activeRankedVotes.length + activeMultiVotes.length}
          </span>
          {activeTab === "ongoing" && (
            <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-[#2c5282] rounded"></div>
          )}
        </div>
         <span className="block sm:hidden w-4"></span>
        <div
          className={`font-medium cursor-pointer relative flex items-center pb-1 transition-colors text-xs sm:text-base ${
            activeTab === "upcoming" ? "text-gray-800" : "text-gray-500"
          }`}
          style={{ minWidth: 0 }}
          onClick={() => setActiveTab("upcoming")}
        >
          <TfiList className="mr-1 sm:mr-2 text-base sm:text-lg"/>
          <span className="truncate">Upcoming votes</span>
          <span className="bg-[#4fd1c5] flex items-center justify-center h-4 w-4 rounded-full text-xs ml-1 text-white absolute -top-3 left-full">
            {upcomingRankedVotes.length + upcomingMultiVotes.length}
          </span>
          {activeTab === "upcoming" && (
            <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-[#2c5282] rounded"></div>
          )}
        </div>
      </div>

      {/* Vote Cards */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      ) : showRankedVotes.length + showMultiVotes.length === 0 ? (
        <div className="flex flex-col items-center justify-center rounded-2xl shadow-md bg-gradient-to-br from-white via-gray-50 to-purple-50 p-6 sm:p-10 font-karla text-center text-gray-600 mt-4 border border-gray-100">
          <img src={democracy} alt="No votes" className="w-12 h-12 sm:w-16 sm:h-16 mb-6 opacity-80" />
          <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 text-gray-700">
            No {activeTab === "ongoing" ? "Ongoing" : "Upcoming"} Votes Available
          </h3>
          <p className="text-base sm:text-lg text-gray-500 mb-4 max-w-xl">
            There are currently no {activeTab === "ongoing" ? "ongoing" : "upcoming"} votes.<br />
            Please check back later or contact your DAO admin for more information.
          </p>
        </div>
      ) : (
        <>
          {/* Show Ranked Votes */}
          {showRankedVotes.map((vote) => {
            const isActive = hoveredVoteId === vote._id || expanded[vote._id];
            return (
              <div
                key={vote._id}
                className={`bg-[#f7f7f7] w-full px-4 py-3 sm:p-5 rounded-2xl sm:rounded-3xl border mb-8 transition-colors duration-200 ${
                  isActive ? "border-2 border-[#2c5282]" : "border border-[#e7eaee]"
                }`}
                style={{ minWidth: 0 }}
                onMouseEnter={() => setHoveredVoteId(vote._id)}
                onMouseLeave={() => setHoveredVoteId(null)}
              >
                {/* Summary Row */}
                <div className="w-full flex flex-row justify-between items-start sm:items-start gap-1 sm:gap-2">
                  {/* Left: Badge, days remaining (mobile), project fund */}
                  <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                    <span className="inline-block bg-[#e7eaee] text-[#2c5282] text-xs px-2 py-1 rounded-xl mr-2">
                      Rank Vote
                    </span>
                    {/* Small screens: days remaining first, then project fund */}
                    <span className="sm:hidden text-[#2c5282] text-sm font-semibold flex items-center">
                      <CiClock2 className="mr-1" />
                      <span className="font-bold text-gray-900 mr-1">
                        {getTimeRemaining(vote.startDate, vote.endDate)}
                      </span>
                    </span>
                    <span className="block mb-2 mt-2 sm:hidden text-sm font-semibold flex items-center">
                      <FaDollarSign className="mr-1 text-[#2c5282]" />
                      <span className="text-[#2c5282]">Project fund:</span>
                      <span className="text-yellow-600 ml-1">
                        {typeof vote.totalFund === "number" ? `$${vote.totalFund.toLocaleString()}` : "-"}
                      </span>
                    </span>
                    {/* Large screens: project fund only */}
                    <span className="hidden sm:flex text-sm items-center">
                      <FaDollarSign className="mr-1 text-[#2c5282]" />
                      <span className="text-[#2c5282]">Project fund:</span>
                      <span className="text-yellow-600 ml-1">
                        {typeof vote.totalFund === "number" ? `$${vote.totalFund.toLocaleString()}` : "-"}
                      </span>
                    </span>
                  </div>
                  {/* Right: days remaining (desktop) and edit buttons */}
                  <div className="flex flex-row items-center gap-1 sm:gap-2 ml-auto">
                    {/* Large screens: days remaining only */}
                    <span className="hidden sm:flex text-[#2c5282] text-sm font-medium items-center">
                      <CiClock2 className="mr-2" />
                      <span className="font-semibold text-gray-900 mr-2">
                        {getTimeRemaining(vote.startDate, vote.endDate)}
                      </span>
                    </span>
                    {isAdmin && (
                      <>
                        {/* Mobile: show only icon */}
                        <button
                          className="sm:hidden flex items-center justify-center bg-purple-600 hover:bg-purple-700 text-white rounded-full p-2 ml-2 transition shadow-md"
                          style={{ minWidth: 36, minHeight: 36 }}
                          onClick={() => setEditVote(vote)}
                          aria-label="Edit Vote"
                        >
                          <FaEdit className="text-lg" />
                        </button>
                        {/* Desktop: show text button */}
                        <button
                          className="hidden sm:inline-flex bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-lg font-semibold transition text-base"
                          onClick={() => setEditVote(vote)}
                        >
                          Edit Vote
                        </button>
                      </>
                    )}
                  </div>
                </div>
                {/* Title, Description, Dropdown, Button */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-lg font-bold text-[#2c5282] mb-2 truncate">{vote.voteTitle || "Vote Title"}</h3>
                    <p className="text-[#7f95b3] mb-4 text-sm sm:text-base truncate">{vote.description || "Vote which projects to receive funding"}</p>
                  </div>
                  {/* Small screens: vertical stack, dropdown below button */}
                  <div className="flex flex-col items-end sm:hidden">
                    {!expanded[vote._id] && (
                      <button
                        className="flex items-end text-[#2c5282] font-medium hover:text-blue-800 transition-colors mt-2 text-xs sm:text-base"
                        onClick={() => handleExpand(vote._id)}
                      >
                        Click to view projects to vote for
                        <FaArrowRight className="mb-1 ml-2" />
                      </button>
                    )}
                    <button
                      className="flex items-center justify-center"
                      onClick={() => handleExpand(vote._id)}
                      aria-label="Expand vote details"
                    >
                      <IoMdArrowDropdown
                        className={`ml-2 w-8 h-8 text-[#2c5282] transition-transform duration-200 ${
                          expanded[vote._id] ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                  </div>
                  {/* Large screens: dropdown above, "Click to view..." below */}
                  <div className="hidden sm:flex flex-col items-end">
                    <button
                      className="flex items-center justify-center"
                      onClick={() => handleExpand(vote._id)}
                      aria-label="Expand vote details"
                    >
                      <IoMdArrowDropdown
                        className={`ml-2 w-8 h-8 text-[#2c5282] transition-transform duration-200 ${
                          expanded[vote._id] ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                    {!expanded[vote._id] && (
                      <button
                        className="flex items-end text-[#2c5282] font-medium hover:text-blue-800 transition-colors mt-2 text-xs sm:text-base"
                        onClick={() => handleExpand(vote._id)}
                      >
                        Click to view projects to vote for
                        <FaArrowRight className="mb-1 ml-2" />
                      </button>
                    )}
                  </div>
                </div>
                {/* Expanded Vote Details */}
                {expanded[vote._id] && (
                  <div className="flex flex-col gap-8 sm:gap-12 mt-6 sm:mt-10">
                    <ProjectRankVote vote={vote} tab={activeTab} />
                  </div>
                )}
                {/* Modal */}
                {editVote && editVote._id === vote._id && (
                  <UpdateVoteModal
                    vote={editVote}
                    onClose={() => setEditVote(null)}
                    onSave={handleSaveVoteDetails}
                  />
                )}
              </div>
            );
          })}
          {/* Show Multi Votes */}
          {showMultiVotes.map((vote) => {
            const isActive = hoveredVoteId === vote._id || expanded[vote._id];
            return (
              <div
                key={vote._id}
                className={`bg-[#f7f7f7] w-full px-4 py-3 sm:p-5 rounded-2xl sm:rounded-3xl border mb-8 transition-colors duration-200 ${
                  isActive ? "border-2 border-[#2c5282]" : "border border-[#e7eaee]"
                }`}
                onMouseEnter={() => setHoveredVoteId(vote._id)}
                onMouseLeave={() => setHoveredVoteId(null)}
              >
                {/* Summary Row for Multi Vote */}
                <div className="w-full flex flex-row justify-between items-start sm:items-start gap-1 sm:gap-2">
                  {/* Left: Badge, days remaining (mobile), project fund */}
                  <div className="flex flex-wrap items-center gap-1 sm:gap-2">
                    <span className="inline-block bg-[#e7eaee] text-[#2c5282] text-xs px-2 py-1 rounded-xl mr-2">
                      Multi Vote
                    </span>
                    {/* Mobile: days remaining */}
                    <span className="sm:hidden text-[#2c5282] text-sm font-semibold flex items-center">
                      <CiClock2 className="mr-1" />
                      <span className="font-bold text-gray-900 mr-1">
                        {getTimeRemaining(vote.startDate, vote.endDate)}
                      </span>
                    </span>
                    <span className="block mb-2 mt-2 sm:hidden text-sm font-semibold flex items-center">
                      <FaDollarSign className="mr-1 text-[#2c5282]" />
                      <span className="text-[#2c5282]">Project fund:</span>
                      <span className="text-yellow-600 ml-1">
                        {typeof vote.totalFund === "number" ? `$${vote.totalFund.toLocaleString()}` : "-"}
                      </span>
                    </span>
                    {/* Large screens: project fund only */}
                    <span className="hidden sm:flex text-sm items-center">
                      <FaDollarSign className="mr-1 text-[#2c5282]" />
                      <span className="text-[#2c5282]">Project fund:</span>
                      <span className="text-yellow-600 ml-1">
                        {typeof vote.totalFund === "number" ? `$${vote.totalFund.toLocaleString()}` : "-"}
                      </span>
                    </span>
                  </div>
                  {/* Right: days remaining (desktop) and edit buttons */}
                  <div className="flex flex-row items-center gap-1 sm:gap-2 ml-auto">
                    {/* Desktop: days remaining */}
                    <span className="hidden sm:flex text-[#2c5282] text-sm font-medium items-center">
                      <CiClock2 className="mr-2" />
                      <span className="font-semibold text-gray-900 mr-2">
                        {getTimeRemaining(vote.startDate, vote.endDate)}
                      </span>
                    </span>
                    {/* Edit buttons */}
                    {isAdmin && (
                      <>
                        {/* Mobile: show only icon */}
                        <button
                          className="sm:hidden flex items-center justify-center bg-purple-600 hover:bg-purple-700 text-white rounded-full p-2 ml-2 transition shadow-md"
                          style={{ minWidth: 36, minHeight: 36 }}
                          onClick={() => setEditVote(vote)}
                          aria-label="Edit Vote"
                        >
                          <FaEdit className="text-lg" />
                        </button>
                        {/* Desktop: show text button */}
                        <button
                          className="hidden sm:inline-flex bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-lg font-semibold transition text-base"
                          onClick={() => setEditVote(vote)}
                        >
                          Edit Vote
                        </button>
                      </>
                    )}
                  </div>
                </div>
                {/* Title, Description, Dropdown, Button */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-lg font-bold text-[#2c5282] mb-2 truncate">{vote.voteTitle || "Project Title"}</h3>
                    <p className="text-[#7f95b3] mb-4 text-sm sm:text-base truncate">{vote.description || "Vote which projects to receive funding"}</p>
                  </div>
                  {/* Small screens: vertical stack, dropdown below button */}
                  <div className="flex flex-col items-end sm:hidden">
                    {!expanded[vote._id] && (
                      <button
                        className="flex items-end text-[#2c5282] font-medium hover:text-blue-800 transition-colors mt-2 text-xs sm:text-base"
                        onClick={() => handleExpand(vote._id)}
                      >
                        Click to view projects to vote for
                        <FaArrowRight className="mb-1 ml-2" />
                      </button>
                    )}
                    <button
                      className="flex items-center justify-center"
                      onClick={() => handleExpand(vote._id)}
                      aria-label="Expand vote details"
                    >
                      <IoMdArrowDropdown
                        className={`ml-2 w-8 h-8 text-[#2c5282] transition-transform duration-200 ${
                          expanded[vote._id] ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                  </div>
                  {/* Large screens: dropdown above, "Click to view..." below */}
                  <div className="hidden sm:flex flex-col items-end">
                    <button
                      className="flex items-center justify-center"
                      onClick={() => handleExpand(vote._id)}
                      aria-label="Expand vote details"
                    >
                      <IoMdArrowDropdown
                        className={`ml-2 w-8 h-8 text-[#2c5282] transition-transform duration-200 ${
                          expanded[vote._id] ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                    {!expanded[vote._id] && (
                      <button
                        className="flex items-end text-[#2c5282] font-medium hover:text-blue-800 transition-colors mt-2 text-xs sm:text-base"
                        onClick={() => handleExpand(vote._id)}
                      >
                        Click to view projects to vote for
                        <FaArrowRight className="mb-1 ml-2" />
                      </button>
                    )}
                  </div>
                </div>
                {/* Expanded Vote Details */}
                {expanded[vote._id] && (
                  <div className="flex flex-col gap-8 sm:gap-12 mt-6 sm:mt-10">
                    <ProjectMultiVote vote={vote} />
                  </div>
                )}
                {/* Modal */}
                {editVote && editVote._id === vote._id && (
                  <UpdateVoteModal
                    vote={editVote}
                    onClose={() => setEditVote(null)}
                    onSave={handleSaveVoteDetails}
                  />
                )}
              </div>
            );
          })}
        </>
      )}
    </div>
  );
};