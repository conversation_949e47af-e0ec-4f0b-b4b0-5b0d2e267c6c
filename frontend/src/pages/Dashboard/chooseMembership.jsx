import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';
import { AppRoutesPaths } from '../../route/app_route';
import { FiCheck, FiUnlock } from "react-icons/fi";
import { FiUser } from "react-icons/fi";
import { FaCheck } from "react-icons/fa6";
import { HiOutlineBolt } from "react-icons/hi2";
import { IoIosTrendingUp } from "react-icons/io";

export const ChooseMembership = () => {
    const [isPremium, setIsPremium] = useState(true);
    const navigate = useNavigate();

    const handleSelect = (premium) => {
        setIsPremium(premium);
        console.log(premium ? "Premium selected" : "Free selected");
    };

    return (
        <div className='bg-white rounded-md shadow-lg mb-10 p-5 sm:p-10'>
            <div className="flex flex-col gap-4">
                {/* Header */}
                <div className="flex items-center gap-3">
                    <div className="bg-BP-light-purple rounded-lg p-2">
                        <FiUnlock className='text-BP-purple w-7 h-7' />
                    </div>
                    <div>
                        <div className="font-semibold text-lg text-BP-black">Fuel the DAO's Growth</div>
                        <div className="text-gray-500 text-sm">Your monthly contribution funds community-chosen projects</div>
                    </div>
                </div>
                {/* Membership Options */}
                <div className="flex flex-col md:flex-row gap-4 mt-4">
                    {/* Current Access */}
                    <div
                        className={`flex-1 border rounded-lg p-5 cursor-pointer transition 
              ${!isPremium ? 'border-purple-400 bg-purple-50 shadow-md' : 'border-gray-200 bg-white'}
            `}
                        onClick={() => handleSelect(false)}
                    >
                        <div className="flex items-center justify-between mb-2">
                            <span className="font-semibold text-BP-black">Current Access</span>
                            <span className="bg-gray-200 text-green-600 text-xs px-2 py-0.5 rounded-full font-medium">Free</span>
                        </div>
                        <ul className="space-y-3 mt-4">
                            <li className="flex items-center gap-2 text-gray-700">
                                <span className={`${!isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <FiUser className="w-5 h-5" />
                                </span>
                                <span className={`${!isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>

                                    View proposals
                                </span>
                            </li>
                            <li className="flex items-center gap-2 text-gray-700">
                                <span className={`${!isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <FaCheck className="w-5 h-5" />
                                </span>
                                <span className={`${!isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>

                                    Basic community access
                                </span>
                            </li>
                            <li className="flex items-center gap-2 text-gray-700">
                                <span className={`${!isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <FaCheck className="w-5 h-5" />
                                </span>
                                <span className={`${!isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>

                                    Read-only governance
                                </span>
                            </li>
                        </ul>
                    </div>
                    {/* Funding Membership */}
                    <div
                        className={`flex-1 border rounded-lg p-5 cursor-pointer transition 
              ${isPremium ? 'border-purple-400 bg-purple-50 shadow-md' : 'border-gray-200 bg-white'}
            `}
                        onClick={() => handleSelect(true)}
                    >
                        <div className="flex items-center justify-between mb-2">
                            <span className="font-semibold text-BP-black">Funding Membership</span>
                            <span className="bg-purple-100 text-purple-600 text-xs px-2 py-0.5 rounded-full font-medium">premium</span>
                        </div>
                        <ul className="space-y-3 mt-4">
                            <li className="flex items-center gap-2 font-medium">
                                <span className={`${isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <HiOutlineBolt className="w-5 h-5" />
                                </span>
                                <span className={`${isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>
                                    Fund projects you believe in
                                </span>
                            </li>
                            <li className="flex items-center gap-2 font-medium">
                                <span className={`${isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <IoIosTrendingUp className="w-5 h-5" />
                                </span>
                                <span className={`${isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>
                                View proposals
                                </span>
                            </li>
                            <li className="flex items-center gap-2 font-medium">
                                <span className={`${isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <FiUnlock className="w-5 h-5" />
                                </span>
                                <span className={`${isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>
                                Exclusive forums
                                </span>
                            </li>
                            <li className="flex items-center gap-2 font-medium">
                                <span className={`${isPremium ? 'text-BP-purple bg-BP-light-purple' : 'text-gray-400 bg-gray-100 '} rounded-full p-1`}>
                                    <FaCheck className="w-5 h-5" />
                                </span>
                                <span className={`${isPremium ? 'text-BP-purple ' : 'text-gray-400 '} rounded-full p-1`}>
                                Early and all features access
                                </span>
                            </li>

                        </ul>
                        {/* Recent Funding Examples */}
                        <div className="mt-6">
                            <div className="text-gray-700 font-semibold text-sm mb-1">Recent Funding Examples</div>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                                <span className="w-2 h-2 rounded-full bg-gray-300 inline-block"></span>
                                Last month: $42k funded for developer tools
                            </div>
                        </div>
                    </div>
                </div>
                {/* Premium Info */}
                <div className="mt-4 text-center max-sm:p-1 text-xs text-BP-nav-gray bg-BP-light-purple rounded-md py-2">
                    <span className="font-medium text-BP-purple">Premium members</span> report 3x more rewards and 2x greater satisfaction with their DAO experience.
                </div>
                {/* CTA Button */}
                <div className="mt-4">
                    <button onClick={isPremium ? ()=>{navigate(AppRoutesPaths.monthlyContribution)} : ()=>{}} className={`${isPremium ? 'bg-BP-purple hover:bg-BP-hovered-purple' : 'bg-BP-gray-20-opc cursor-not-allowed'} text-white font-semibold px-8 py-3 rounded-lg shadow transition flex items-center gap-2 max-sm:text-xs`}>
                        Start contributing monthly
                        <FiCheck className="w-5 h-5" />
                    </button>
                </div>
            </div>
        </div>
    )
}
