import { useState, useEffect } from 'react';
import { FaSearch } from 'react-icons/fa';
import { UserService } from '../../services/userService';

// Simple confirmation modal
const ConfirmModal = ({ open, onClose, onConfirm, message }) => {
  if (!open) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 shadow-lg max-w-sm w-full">
        <p className="mb-6 text-gray-800">{message}</p>
        <div className="flex justify-end gap-3">
          <button
            className="px-4 py-2 rounded bg-gray-200 text-gray-700"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 rounded bg-red-500 text-white"
            onClick={onConfirm}
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

export const Members = () => {
  const [members, setMembers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(4);
  const [searchQuery, setSearchQuery] = useState('');
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [modalType, setModalType] = useState(''); // 'promote' or 'remove'

  useEffect(() => {
    const fetchMembers = async () => {
      const users = await UserService.getAllUsers();
      setMembers(users || []);
    };
    fetchMembers();
  }, []);

  // Filter members based on search query
  const filteredMembers = members.filter(member =>
    (`${member.firstName} ${member.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (member.role || '').toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const totalPages = Math.ceil(filteredMembers.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const currentMembers = [...filteredMembers]
  .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  .slice(startIndex, startIndex + pageSize);

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Reset to first page when search query changes
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  // Modal logic
  const handlePromote = (user) => {
    setSelectedUser(user);
    setModalType('promote');
    setModalOpen(true);
  };

  const handleRemove = (user) => {
    setSelectedUser(user);
    setModalType('remove');
    setModalOpen(true);
  };

  const handleConfirm = async () => {
    if (modalType === 'promote') {
      await UserService.promoteToAdmin(selectedUser._id);
    } else if (modalType === 'remove') {
      await UserService.demoteToMember(selectedUser._id); // Use demote, not remove
    }
    // Refresh members list
    const users = await UserService.getAllUsers();
    setMembers(users || []);
    setModalOpen(false);
    setSelectedUser(null);
    setModalType('');
  };

  return (
    <div className="bg-[#fcfbf7] rounded-2xl shadow-md p-4 max-w-6xl mx-auto">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">
        Manage Members
      </h3>

      {/* Search Input */}
      <div className="relative mb-6 text-gray-700">
        <input
          type="text"
          placeholder="Search members..."
          value={searchQuery}
          onChange={handleSearch}
          className="w-full py-3 pl-10 pr-4 bg-gray-100 rounded-xl focus:outline-none text-sm"
        />
        <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
      </div>

      {/* Table */}
      <div className="overflow-x-auto md:overflow-x-visible rounded-xl max-w-6xl shadow-sm w-full">
        <table className="min-w-[600px] md:min-w-full text-left border-collapse bg-white overflow-hidden">
          <thead className="bg-gray-100">
            <tr className="text-gray-700 text-sm font-semibold">
              <th className="px-4 py-3">Name</th>
              <th className="px-4 py-3">Role</th>
              <th className="px-4 py-3">Joined</th>
              <th className="px-4 py-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {currentMembers.map((member, idx) => (
              <tr key={member._id || idx} className="border-t text-sm text-gray-800">
                <td className="px-6 py-6">{member.firstName} {member.lastName}</td>
                <td className="px-4 py-6">
                  {member.role && member.role.toLowerCase() === 'admin'
                    ? 'Admin'
                    : 'Member'}
                </td>
                <td className="px-4 py-6">
                  {member.createdAt
                    ? new Date(member.createdAt).toLocaleDateString()
                    : 'Unknown'}
                </td>
                <td className="px-4 py-4 space-x-5">
                  {member.role && member.role.toLowerCase() === 'admin' ? (
                    <button
                      className="text-red-500 hover:underline"
                      onClick={() => handleRemove(member)}
                    >
                      Remove
                    </button>
                  ) : (
                    <button
                      className="text-yellow-500 hover:underline"
                      onClick={() => handlePromote(member)}
                    >
                      Promote
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Show message when no results found */}
      {filteredMembers.length === 0 && (
        <div className="text-center py-4 text-gray-500">
          No members found matching your search.
        </div>
      )}

      {/* Pagination Controls */}
      {filteredMembers.length > 0 && (
        <div className="flex justify-left items-center mt-6 space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-2 py-1 text-sm rounded-md border border-gray-300 text-gray-500 disabled:opacity-40"
          >
            &lt;
          </button>

          {[...Array(totalPages)].map((_, i) => (
            <button
              key={i}
              onClick={() => handlePageChange(i + 1)}
              className={`px-3 py-1 text-sm rounded-md border ${
                currentPage === i + 1
                  ? 'border-blue-500 text-blue-600'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-100'
              }`}
            >
              {i + 1}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-2 py-1 text-sm rounded-md border border-gray-300 text-gray-500 disabled:opacity-40"
          >
            &gt;
          </button>

          <select
            value={pageSize}
            onChange={(e) => {
              setCurrentPage(1);
              setPageSize(Number(e.target.value));
            }}
            className="ml-3 text-sm border bg-gray-100 text-gray-800 border-gray-300 rounded-md px-2 py-1"
          >
            {[4, 6, 8, 10].map((size) => (
              <option key={size} value={size}>
                {size} / page
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onConfirm={handleConfirm}
        message={
          modalType === 'promote'
            ? `Are you sure you want to promote ${selectedUser?.firstName} ${selectedUser?.lastName} to Admin?`
            : `Are you sure you want to demote ${selectedUser?.firstName} ${selectedUser?.lastName} to Member?`
        }
      />
    </div>
  );
};
