import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCalendarAlt } from 'react-icons/fa';
import history from '../../assets/admin/history.png';
import check from '../../assets/admin/check.png';
import { IoBanOutline } from 'react-icons/io5';
import { VoteService } from '../../services/voteService';

export const Voteresults = () => {
  const [endedVotes, setEndedVotes] = useState([]);
  const [startingProject, setStartingProject] = useState(false);
  const [startedProjects, setStartedProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showFull, setShowFull] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    fetchEndedVotes();
  }, []);

  const fetchEndedVotes = async () => {
    try {
      setLoading(true);
      const response = await VoteService.getEndedVotes();
      if (response && response.votes) {
        const votes = response.votes;
        setEndedVotes(votes.filter((vote) => !vote.projectStarted));
        setStartedProjects(votes.filter((vote) => vote.projectStarted));
      } else {
        console.error('Invalid response structure:', response);
        setEndedVotes([]);
        setStartedProjects([]);
      }
    } catch (error) {
      console.error('Error fetching ended votes:', error);
      setEndedVotes([]);
      setStartedProjects([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStartProject = async (voteId) => {
    setStartingProject(true);
    try {
      const success = await VoteService.startProject(voteId);
      if (success) {
        fetchEndedVotes();
      }
    } catch (error) {
      console.error('Error starting project:', error);
    } finally {
      setStartingProject(false);
    }
  };

  // Toggle read more/less for project descriptions
  const toggleShowFull = (voteId, projectId) => {
    setShowFull((prev) => ({
      ...prev,
      [`${voteId}_${projectId}`]: !prev[`${voteId}_${projectId}`],
    }));
  };

  // Helpers for rank voting
  const getRankedVotes = (votes) => votes.filter(v => v.votingType === "ranked");
  const getMultiVotes = (votes) => votes.filter(v => v.votingType !== "ranked");

  const getRankWinner = (projects) => {
    if (!projects || projects.length === 0) return null;
    return projects.reduce(
      (max, project) =>
        (project.points ?? 0) > (max.points ?? 0) ? project : max,
      projects[0]
    );
  };

  const getVoterParticipation = (vote) => {
    // Count unique userIds across all project.rankings
    const userIds = new Set();
    (vote.projects || []).forEach(project => {
      (project.rankings || []).forEach(r => userIds.add(r.userId));
    });
    return userIds.size;
  };

  // Add this helper for multi voting winner
  const getWinningProject = (projects) => {
    if (!projects || projects.length === 0) return null;
    return projects.reduce(
      (max, project) =>
        (project.voteCount?.yes ?? 0) > (max.voteCount?.yes ?? 0) ? project : max,
      projects[0]
    );
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full sm:w-full md:min-w-[80vw] min-w-[90vw] lg:max-w-[80vw] mt-[-10px]">
        <h1 className="text-3xl font-karla text-gray-700 text-center mb-6">
          Votes Results
        </h1>

        {/* --- Multi Voting Results --- */}
        <h2 className="text-lg text-gray-700 font-medium border-b-2 pb-2">
          Just completed multi voting
        </h2>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : (
          <>
            {Array.isArray(endedVotes) && endedVotes.length > 0 ? (
              endedVotes.map((vote) => {
                const winningProject = getWinningProject(vote.projects);
                return (
                  <div key={vote._id} className="bg-white rounded-lg shadow-md p-4 mt-2">
                    <div className="flex items-center gap-4 text-gray-600">
                      <img src={check} alt="Check" className="w-8 h-8 text-yellow-500" />
                      <span className="font-medium capitalize">multi voting</span>
                      <span className="flex items-center text-sm text-gray-500 gap-1 mx-auto">
                        <FaCalendarAlt className="text-gray-500" />{" "}
                        {new Date(vote.endDate).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="mt-4 space-y-4">
                      {vote.projects.map((project, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b pb-4 last:border-none gap-2"
                        >
                          <div className="space-y-1 sm:w-1/2">
                            <p className="font-semibold text-gray-900">{project.title}</p>
                            <div className="text-gray-500 text-base relative leading-relaxed max-w-3xl mb-2 text-justify">
                              {showFull[`${vote._id}_${project._id}`] ? (
                                <>
                                  <span>{project.description}</span>
                                  {project.description && project.description.length > 200 && (
                                    <button
                                      className="text-purple-600 font-semibold hover:underline ml-2"
                                      onClick={() => toggleShowFull(vote._id, project._id)}
                                    >
                                      Read less
                                    </button>
                                  )}
                                </>
                              ) : (
                                <>
                                  <span>
                                    {project.description?.substring(0, 200)}
                                    {project.description && project.description.length > 200 && (
                                      <>
                                        <span className="mx-1">...</span>
                                        <button
                                          className="text-purple-600 font-semibold hover:underline ml-2"
                                          onClick={() => toggleShowFull(vote._id, project._id)}
                                        >
                                          Read more
                                        </button>
                                      </>
                                    )}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-row gap-4 sm:gap-8 mt-2 sm:mt-0 sm:w-1/2 justify-start sm:justify-end">
                            <div className="flex items-center text-green-500 font-medium whitespace-nowrap">
                              <span className="mr-1">✓</span>
                              <span>
                                {project.voteCount?.yes ?? 0} Yes (
                                {project.voteCount
                                  ? Math.round(
                                      (project.voteCount.yes /
                                        ((project.voteCount.yes ?? 0) +
                                          (project.voteCount.no ?? 0) || 1)) *
                                        100
                                    )
                                  : 0}
                                %)
                              </span>
                            </div>
                            <div className="flex items-center text-red-400 font-medium whitespace-nowrap">
                              <span className="mr-1">-</span>
                              <span>
                                {project.voteCount?.no ?? 0} No (
                                {project.voteCount
                                  ? Math.round(
                                      (project.voteCount.no /
                                        ((project.voteCount.yes ?? 0) +
                                          (project.voteCount.no ?? 0) || 1)) *
                                        100
                                    )
                                  : 0}
                                %)
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {winningProject && (
                      <div className="bg-gray-100 p-4 rounded-lg mt-4 flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-700 capitalize">multi voting</p>
                          <p className="text-gray-600 text-sm">
                            Winner:{" "}
                            <span className="text-yellow-500 font-medium">
                              {winningProject.title}
                            </span>
                          </p>
                        </div>
                        <button
                          className="bg-yellow-500 text-white px-8 py-2 rounded-[10px] font-medium hover:bg-yellow-600 disabled:opacity-70"
                          onClick={() => handleStartProject(vote._id)}
                          disabled={startingProject}
                        >
                          Start
                        </button>
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="rounded-lg shadow-md bg-white p-32 font-karla text-3xl text-center text-gray-600 mt-4">
                No completed Multi votes yet.
              </div>
            )}

            {/* --- Rank Voting Results --- */}
            <h2 className="text-lg text-gray-700 font-medium border-b-2 pb-2 mt-10">
              Just completed rank voting
            </h2>
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
              </div>
            ) : (
              <>
                {getRankedVotes(endedVotes).length > 0 ? (
                  getRankedVotes(endedVotes).map((vote) => {
                    const winningProject = getRankWinner(vote.projects);
                    const voterCount = getVoterParticipation(vote);
                    return (
                      <div key={vote._id} className="bg-white rounded-lg shadow-md p-4 mt-2">
                        <div className="flex items-center gap-4 text-gray-600">
                          <img src={check} alt="Check" className="w-8 h-8 text-yellow-500" />
                          <span className="font-medium capitalize">ranked voting</span>
                          <span className="flex items-center text-sm text-gray-500 gap-1 mx-auto">
                            <FaCalendarAlt className="text-gray-500" />{" "}
                            {new Date(vote.endDate).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="mt-4 space-y-4">
                          {vote.projects.map((project, index) => (
                            <div
                              key={index}
                              className="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b pb-4 last:border-none gap-2"
                            >
                              <div className="space-y-1 sm:w-1/2">
                                <p className="font-semibold text-gray-900">{project.title}</p>
                                <div className="text-gray-500 text-base relative leading-relaxed max-w-3xl mb-2 text-justify">
                                  {showFull[`${vote._id}_${project._id}`] ? (
                                    <>
                                      <span>{project.description}</span>
                                      {project.description && project.description.length > 200 && (
                                        <button
                                          className="text-purple-600 font-semibold hover:underline ml-2"
                                          onClick={() => toggleShowFull(vote._id, project._id)}
                                        >
                                          Read less
                                        </button>
                                      )}
                                    </>
                                  ) : (
                                    <>
                                      <span>
                                        {project.description?.substring(0, 200)}
                                        {project.description && project.description.length > 200 && (
                                          <>
                                            <span className="mx-1">...</span>
                                            <button
                                              className="text-purple-600 font-semibold hover:underline ml-2"
                                              onClick={() => toggleShowFull(vote._id, project._id)}
                                            >
                                              Read more
                                            </button>
                                          </>
                                        )}
                                      </span>
                                    </>
                                  )}
                                </div>
                              </div>
                              <div className="flex flex-row gap-4 sm:gap-8 mt-2 sm:mt-0 sm:w-1/2 justify-start sm:justify-end">
                                <div className="flex items-center text-purple-600 font-medium whitespace-nowrap">
                                  <span className="mr-1">★</span>
                                  <span>
                                    {project.points ?? 0} Points
                                  </span>
                                </div>
                                <div className="flex items-center text-gray-500 font-medium whitespace-nowrap">
                                  <span className="mr-1">👥</span>
                                  <span>
                                    {project.rankings ? project.rankings.length : 0} Voters
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        <div className="flex justify-between items-center bg-gray-100 p-4 rounded-lg mt-4">
                          <div>
                            <p className="font-medium text-gray-700 capitalize">ranked voting</p>
                            <p className="text-gray-600 text-sm">
                              Winner:{" "}
                              <span className="text-yellow-500 font-medium">
                                {winningProject?.title}
                              </span>
                            </p>
                            <p className="text-gray-500 text-xs mt-1">
                              Voter participation: <span className="font-semibold">{voterCount}</span>
                            </p>
                          </div>
                          <button
                            className="bg-yellow-500 text-white px-8 py-2 rounded-[10px] font-medium hover:bg-yellow-600 disabled:opacity-70"
                            onClick={() => handleStartProject(vote._id)}
                            disabled={startingProject}
                          >
                            Start
                          </button>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="rounded-lg shadow-md bg-white p-32 font-karla text-3xl text-center text-gray-600 mt-4">
                    No completed rank votes yet.
                  </div>
                )}
              </>
            )}

            {/* --- Multi Voting History --- */}
            <h2 className="text-lg font-semibold mb-4 text-gray-700 mt-14 border-b-2 pb-2">
              Multi Voting history
            </h2>
            {Array.isArray(startedProjects) && startedProjects.length > 0 ? (
              startedProjects.map((vote) => {
                const winningProject = getWinningProject(vote.projects);
                return (
                  <div key={vote._id} className="rounded-lg shadow-md bg-white p-6 mb-4">
                    <div className="flex items-center gap-4 text-gray-600">
                      <img src={history} alt="Clock" className="w-8 h-8 text-yellow-500" />
                      <span className="font-medium capitalize">multi voting</span>
                      <span className="flex items-center text-sm text-gray-500 gap-1 mx-auto">
                        <FaCalendarAlt className="text-gray-500" />{" "}
                        {new Date(vote.endDate).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="mt-4 space-y-4">
                      {vote.projects.map((project, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b pb-4 last:border-none gap-2"
                        >
                          <div className="space-y-1 sm:w-1/2">
                            <p className="font-semibold text-gray-900">{project.title}</p>
                            <div className="text-gray-500 text-base relative leading-relaxed max-w-3xl mb-2 text-justify">
                              {showFull[`${vote._id}_${project._id}`] ? (
                                <>
                                  <span>{project.description}</span>
                                  {project.description && project.description.length > 200 && (
                                    <button
                                      className="text-purple-600 font-semibold hover:underline ml-2"
                                      onClick={() => toggleShowFull(vote._id, project._id)}
                                    >
                                      Read less
                                    </button>
                                  )}
                                </>
                              ) : (
                                <>
                                  <span>
                                    {project.description?.substring(0, 200)}
                                    {project.description && project.description.length > 200 && (
                                      <>
                                        <span className="mx-1">...</span>
                                        <button
                                          className="text-purple-600 font-semibold hover:underline ml-2"
                                          onClick={() => toggleShowFull(vote._id, project._id)}
                                        >
                                          Read more
                                        </button>
                                      </>
                                    )}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-row gap-4 sm:gap-8 mt-2 sm:mt-0 sm:w-1/2 justify-start sm:justify-end">
                            <div className="flex items-center text-green-500 font-medium whitespace-nowrap">
                              <span className="mr-1">✓</span>
                              <span>
                                {project.voteCount?.yes ?? 0} Yes (
                                {project.voteCount
                                  ? Math.round(
                                      (project.voteCount.yes /
                                        ((project.voteCount.yes ?? 0) +
                                          (project.voteCount.no ?? 0) || 1)) *
                                        100
                                    )
                                  : 0}
                                %)
                              </span>
                            </div>
                            <div className="flex items-center text-red-400 font-medium whitespace-nowrap">
                              <span className="mr-1">-</span>
                              <span>
                                {project.voteCount?.no ?? 0} No (
                                {project.voteCount
                                  ? Math.round(
                                      (project.voteCount.no /
                                        ((project.voteCount.yes ?? 0) +
                                          (project.voteCount.no ?? 0) || 1)) *
                                        100
                                    )
                                  : 0}
                                %)
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {winningProject && (
                      <div className="bg-gray-100 p-4 rounded-lg mt-4 flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-700 capitalize">multi voting</p>
                          <p className="text-gray-600 text-sm">
                            Winner:{" "}
                            <span className="text-yellow-500 font-medium">
                              {winningProject.title}
                            </span>
                          </p>
                        </div>
                        <button className="bg-[#d2d6db] text-gray-400 flex items-center px-8 py-2 gap-2 rounded-md font-medium cursor-not-allowed">
                          <IoBanOutline size={24} />
                          Started
                        </button>
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="rounded-lg shadow-md bg-white p-32 font-karla text-3xl text-center text-gray-600 mt-4">
                No started Multi projects in history yet.
              </div>
            )}

            {/* --- Rank Voting History --- */}
            <h2 className="text-lg font-semibold mb-4 text-gray-700 mt-14 border-b-2 pb-2">
              Rank voting history
            </h2>
            {getRankedVotes(startedProjects).length > 0 ? (
              getRankedVotes(startedProjects).map((vote) => {
                const winningProject = getRankWinner(vote.projects);
                const voterCount = getVoterParticipation(vote);
                return (
                  <div key={vote._id} className="rounded-lg shadow-md bg-white p-6 mb-4">
                    <div className="flex items-center gap-4 text-gray-600">
                      <img src={history} alt="Clock" className="w-8 h-8 text-yellow-500" />
                      <span className="font-medium capitalize">ranked voting</span>
                      <span className="flex items-center text-sm text-gray-500 gap-1 mx-auto">
                        <FaCalendarAlt className="text-gray-500" />{" "}
                        {new Date(vote.endDate).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="mt-4 space-y-4">
                      {vote.projects.map((project, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b pb-4 last:border-none gap-2"
                        >
                          <div className="space-y-1 sm:w-1/2">
                            <p className="font-semibold text-gray-900">{project.title}</p>
                            <div className="text-gray-500 text-base relative leading-relaxed max-w-3xl mb-2 text-justify">
                              {showFull[`${vote._id}_${project._id}`] ? (
                                <>
                                  <span>{project.description}</span>
                                  {project.description && project.description.length > 200 && (
                                    <button
                                      className="text-purple-600 font-semibold hover:underline ml-2"
                                      onClick={() => toggleShowFull(vote._id, project._id)}
                                    >
                                      Read less
                                    </button>
                                  )}
                                </>
                              ) : (
                                <>
                                  <span>
                                    {project.description?.substring(0, 200)}
                                    {project.description && project.description.length > 200 && (
                                      <>
                                        <span className="mx-1">...</span>
                                        <button
                                          className="text-purple-600 font-semibold hover:underline ml-2"
                                          onClick={() => toggleShowFull(vote._id, project._id)}
                                        >
                                          Read more
                                        </button>
                                      </>
                                    )}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-row gap-4 sm:gap-8 mt-2 sm:mt-0 sm:w-1/2 justify-start sm:justify-end">
                            <div className="flex items-center text-purple-600 font-medium whitespace-nowrap">
                              <span className="mr-1">★</span>
                              <span>
                                {project.points ?? 0} Points
                              </span>
                            </div>
                            <div className="flex items-center text-gray-500 font-medium whitespace-nowrap">
                              <span className="mr-1">👥</span>
                              <span>
                                {project.rankings ? project.rankings.length : 0} Voters
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between items-center bg-gray-100 p-4 rounded-lg mt-4">
                      <div>
                        <p className="font-medium text-gray-700 capitalize">ranked voting</p>
                        <p className="text-gray-600 text-sm">
                          Winner:{" "}
                          <span className="text-yellow-500 font-medium">
                            {winningProject?.title}
                          </span>
                        </p>
                        <p className="text-gray-500 text-xs mt-1">
                          Voter participation: <span className="font-semibold">{voterCount}</span>
                        </p>
                      </div>
                      <button className="bg-[#d2d6db] text-gray-400 flex items-center px-8 py-2 gap-2 rounded-md font-medium cursor-not-allowed">
                        <IoBanOutline size={24} />
                        Started
                      </button>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="rounded-lg shadow-md bg-white p-32 font-karla text-3xl text-center text-gray-600 mt-4">
                No started rank votes in history yet.
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};