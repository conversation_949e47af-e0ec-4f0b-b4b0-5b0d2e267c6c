import React, { useEffect, useState } from 'react';
import barchart from "../../assets/Dao/bar-chart.png";
import gold from "../../assets/Dao/Rectangle23.png";
import { IoIosArrowDown } from "react-icons/io";
import { VoteService } from '../../services/voteService';
import { useNavigate } from "react-router-dom";
import { useAuthentication } from '../../components/utils/provider';
import { FiAward } from "react-icons/fi";
import checkmark from '../../assets/Dao/check-mark.png';
import checkmarkvoted from '../../assets/Dao/check-markvoted.png';
import checkplain from '../../assets/Dao/checkplain.png';

export const ProjectRankVote = ({ tab = "ongoing", vote: singleVote }) => {
  const [votes, setVotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRanks, setSelectedRanks] = useState({});
  const [openDropdown, setOpenDropdown] = useState({});
  const [submitting, setSubmitting] = useState(false);
  const [hoveredIdx, setHoveredIdx] = useState(null);
  const [showFull, setShowFull] = useState({});
  const { currentUser } = useAuthentication();

  // Fetch votes only if not passed a singleVote
  useEffect(() => {
    if (singleVote) {
      setVotes([singleVote]);
      setLoading(false);
      setError('');
      return;
    }
    const fetchVotes = async () => {
      setLoading(true);
      setError('');
      try {
        const allVotes = await VoteService.getAllVotes();
        setVotes(
          (allVotes || []).filter(
            v => v.votingType === "ranked" && new Date(v.endDate) >= new Date()
          )
        );
      } catch (err) {
        setError('Failed to fetch votes.');
      } finally {
        setLoading(false);
      }
    };
    fetchVotes();
  }, [singleVote]);

  // Build selectedRanks for the current user
  useEffect(() => {
    if (votes.length && currentUser) {
      const newSelectedRanks = {};
      votes.forEach(vote => {
        const ranks = {};
        (vote.projects || []).forEach(project => {
          const userRanking = project.rankings?.find(
            r => r.userId === currentUser.id || r.userId === currentUser._id
          );
          if (userRanking) ranks[project.id || project._id] = userRanking.rank;
        });
        newSelectedRanks[vote.id || vote._id] = ranks;
      });
      setSelectedRanks(newSelectedRanks);
    }
  }, [votes, currentUser]);

  // Helper: get available ranks for a vote (no duplicate ranks)
  const getAvailableRanks = (vote, voteId, projectId) => {
    const usedRanks = Object.values(selectedRanks[voteId] || {}).filter(Boolean);
    const maxRank = Math.min(vote.projects.length, 7);
    return Array.from({ length: maxRank }, (_, i) => i + 1).filter(r => !usedRanks.includes(r) || r === (selectedRanks[voteId][projectId]));
  };

  // Handle rank selection
  const handleRankSelect = async (voteId, projectId, rank) => {
    setSubmitting(true);
    setError('');
    const prevRanks = { ...(selectedRanks[voteId] || {}) };
    if (prevRanks[projectId] === rank) {
      delete prevRanks[projectId];
    } else {
      Object.keys(prevRanks).forEach(pid => {
        if (prevRanks[pid] === rank) {
          delete prevRanks[pid];
        }
      });
      prevRanks[projectId] = rank;
    }
    const rankings = Object.entries(prevRanks).map(([pid, r]) => ({
      projectId: pid,
      rank: r
    }));
    setSelectedRanks(prev => ({ ...prev, [voteId]: { ...prevRanks } }));
    setOpenDropdown(prev => ({ ...prev, [voteId]: null }));
    try {
      const res = await VoteService.submitRankedVote({ voteId, rankings });
      if (!res || !res.projects) throw new Error('Invalid response from server');
      setVotes(prevVotes =>
        prevVotes.map(v =>
          (v.id || v._id) === voteId
            ? { ...v, projects: res.projects }
            : v
        )
      );
    } catch (err) {
      setError('Failed to submit ranked vote. Please try again.');
    }
    setSubmitting(false);
  };

  // Handle dropdown open/close
  const handleDropdown = (voteId, projectId) => {
    setOpenDropdown(prev => ({
      ...prev,
      [voteId]: prev[voteId] === projectId ? null : projectId
    }));
  };

  const toggleShowFull = (voteId, projectId) => {
    setShowFull(prev => ({
      ...prev,
      [`${voteId}_${projectId}`]: !prev[`${voteId}_${projectId}`]
    }));
  };

  // Filtering logic based on tab
  const now = new Date();
  const filteredVotes = votes.filter(vote => {
    if (tab === "ongoing") {
      return (
        vote.status === "active" &&
        new Date(vote.startDate) <= now &&
        new Date(vote.endDate) >= now
      );
    } else if (tab === "upcoming") {
      return (
        vote.status === "pending" ||
        vote.status === "upcoming" ||
        (vote.status === "active" && new Date(vote.startDate) > now)
      );
    }
    return false;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 my-2">{error}</div>;
  }

  if (!filteredVotes.length) {
    return (
      <div className="text-gray-500 text-center py-8">
        No {tab === "ongoing" ? "ongoing" : "upcoming"} rank votes available.
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center px-2 sm:px-4 md:px-8 lg:px-0">
      <div className="rounded-xl shadow-lg p-4 mt-4">
        {filteredVotes.map((vote) => (
          <div key={vote.id || vote._id} className="mt-8">
            {/* Pending Vote Message */}
            {vote.status === 'pending' && (
              <div className="bg-yellow-100 text-yellow-700 p-4 mt-6 rounded mb-4">
                Voting will open on <b>{new Date(vote.startDate).toLocaleString()}</b>.
              </div>
            )}

            {/* Points Allocation System */}
            <div className="bg-[#ffffff] shadow-md rounded-lg py-6 px-2 sm:px-8 mt-6">
              <div className="flex items-center mb-4">
                <FiAward className='text-[#325786]' />
                <h2 className="text-xl text-black font-light ml-2">Point allocation</h2>
              </div>
              <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-7 gap-2">
                {Object.entries(
                  (() => {
                    if (vote.pointSystem && typeof vote.pointSystem === 'object') {
                      if (vote.pointSystem instanceof Map) {
                        return Object.fromEntries(vote.pointSystem);
                      }
                      if (Object.keys(vote.pointSystem).length > 0) {
                        return vote.pointSystem;
                      }
                    }
                    return { 1: 5, 2: 3, 3: 2, 4: 1, 5: 1, 6: 1 };
                  })()
                ).map(([rank, points]) => (
                  <div key={`rank-${rank}`} className="bg-[#f7f7f7] rounded-xl p-3 text-[#325786] flex flex-col items-center">
                    <p className="text-sm text-gray-600">Rank {rank}</p>
                    <p className="text-lg text-[#2c5282] font-medium">{points} Point{points !== 1 ? 's' : ''}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Project Cards */}
            <div className="flex flex-col gap-6 mt-6">
              {(vote.projects ?? []).map((project, idx) => {
                const userRanking = project.rankings?.find(
                  r => r.userId === currentUser?.id || r.userId === currentUser?._id
                );
                const userRank = userRanking?.rank;
                const totalPoints = project.points ?? 0;
                const totalVotes = project.rankings?.length ?? 0;

                const showBorder = hoveredIdx === idx || (hoveredIdx === null && idx === 0);
                const borderClass = showBorder
                  ? "border-2 border-purple-600"
                  : "border border-transparent";
                const hoverBorder = "hover:border-2 hover:border-purple-600";

                // Only allow ranking if vote is ongoing (not upcoming)
                const isUpcoming = new Date(vote.startDate) > now;

                return (
                  <div
                    key={project.id || project._id}
                    className={`bg-[#ffffff] shadow-md rounded-lg px-3 sm:px-5 py-6 sm:py-8 mt-4 flex flex-col md:flex-row h-auto md:h-26 space-y-3 md:space-y-0 transition-all duration-200 ${borderClass} ${hoverBorder}`}
                    onMouseEnter={() => setHoveredIdx(idx)}
                    onMouseLeave={() => setHoveredIdx(null)}
                  >
                   
                     <div className="w-20 h-20 rounded-full border-4 border-gray-200 flex flex-col items-center justify-center shadow-2xl bg-white mx-auto md:mx-0">
                        <img
                          src={userRank ? checkmarkvoted : checkmark}
                          alt="vote status"
                          className="w-7 h-7 mb-1"
                          style={{ filter: userRank ? 'none' : 'grayscale(1)' }}
                        />
                        <p className={`text-xs font-medium ${userRank ? 'text-purple-600' : 'text-gray-400'}`}>
                          {userRank ? 'Your vote' : 'Not voted'}
                        </p>
                      </div>
                    <div className='flex-1'>
                      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 gap-2'>
                        <h1 className='text-black text-xl sm:text-2xl font-bold'>{project.title}</h1>
                        <div className='rounded-full bg-[#fdf7e6] px-3 py-1 flex items-center mt-2 sm:mt-0'>
                          <img src={barchart} alt='barchart' className="w-4 h-4 sm:w-5 sm:h-5" />
                          <span className='font-medium text-black text-sm sm:text-base'>{totalVotes} votes</span>
                        </div>
                      </div>
                      <div className="text-gray-500 text-base leading-relaxed max-w-3xl mb-6 text-justify">
                        {showFull[`${vote.id || vote._id}_${project.id || project._id}`] ? (
                          <>
                            <p>{project.description}</p>
                            <button
                              className="text-purple-600 font-semibold hover:underline ml-2"
                              onClick={() => toggleShowFull(vote.id || vote._id, project.id || project._id)}
                            >
                              Read less
                            </button>
                          </>
                        ) : (
                          <span>
                            {project.description?.substring(0, 200)}
                            {project.description && project.description.length > 200 && (
                              <>
                                <span className="mx-1">...</span>
                                <button
                                  className="text-purple-600 font-semibold hover:underline ml-2"
                                  onClick={() => toggleShowFull(vote.id || vote._id, project.id || project._id)}
                                >
                                  Read more
                                </button>
                              </>
                            )}
                          </span>
                        )}
                      </div>
                      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
                        <div className='flex flex-row items-center'>
                          <img src={gold} alt="w-4 h-4 mr-2" className="w-4 h-4 mr-2" />
                          <p className='text-black text-sm sm:text-base'>
                            Total Points: <span className='font-bold text-black'>{totalPoints}</span>
                          </p>
                        </div>
                        <div className="relative w-full sm:w-auto">
                          <button
                            className={`flex items-center justify-between px-4 py-2 font-karla rounded-lg shadow-md whitespace-nowrap w-full sm:w-[140px] 
                              ${isUpcoming ? "bg-gray-100 text-gray-400 border border-gray-200 cursor-not-allowed" : "bg-white text-gray-700 border border-gray-300"}`}
                            onClick={() => !isUpcoming && handleDropdown(vote.id || vote._id, project.id || project._id)}
                            disabled={submitting || isUpcoming}
                            title={isUpcoming ? "Voting not open yet" : ""}
                          >
                            <span>
                              {isUpcoming
                                ? "Voting not open"
                                : userRank
                                ? userRank
                                : "Select rank"}
                            </span>
                            <IoIosArrowDown className="w-4 h-4 ml-2" />
                          </button>
                          {!isUpcoming && vote.status === 'active' && openDropdown[vote.id || vote._id] === (project.id || project._id) && (
                            <div className="absolute bg-white shadow-md rounded-lg mt-2 w-32 right-0 z-10">
                              {getAvailableRanks(vote, vote.id || vote._id, project.id || project._id).map((r) => (
                                <div
                                  key={r}
                                  className={`p-2 hover:bg-gray-200 cursor-pointer text-black ${userRank === r ? 'font-bold text-purple-600' : ''}`}
                                  onClick={() => handleRankSelect(vote.id || vote._id, project.id || project._id, r)}
                                >
                                  Rank {r} {userRank === r ? '(Unrank)' : ''}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

