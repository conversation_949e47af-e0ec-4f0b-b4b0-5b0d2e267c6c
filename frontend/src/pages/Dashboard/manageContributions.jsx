import { LogDisbursement } from "../modals/logDisbursement.jsx";
import { TotalContributions } from "../modals/TotalContributions.jsx";
import { TotalOverheads } from "../modals/TotalOverheads.jsx";
import { CashDeployed } from "../modals/CashDeployed.jsx";
import { MemberContribution } from "../modals/MemberContribution.jsx";
import { PendingContribution } from "../modals/PendingContribution.jsx";

import React, { useState } from "react";

export const ManageContributions = () => {
  const contributions = [
    { contribution: "July Total Contribution", amount: "$5000", key: "total" },
    { contribution: "July Overheads", amount: "$5000" },
    { contribution: "Cash on hand", amount: "$5000" },
    { contribution: "Total Contribution", amount: "$5000", key: "total_contribution" },
    { contribution: "Total Overheads", amount: "$5000", key: "total_overheads" },
    { contribution: "Cash Deployed", amount: "$5000", key: "cash_deployed" },
  ];

  // Helper to check if a grid should show "View report"
  const showReport = (item) =>
    ["Total Contribution", "Total Overheads", "Cash Deployed"].includes(item.contribution);

  const [showModal, setShowModal] = useState(false);
  const [activeReport, setActiveReport] = useState(null);
  const [activeList, setActiveList] = useState(null); // NEW

  // Map keys to modals
  const renderReportModal = () => {
    if (activeReport === "total_contribution") {
      return <TotalContributions onClose={() => setActiveReport(null)} />;
    }
    if (activeReport === "total_overheads") {
      return <TotalOverheads onClose={() => setActiveReport(null)} />;
    }
    if (activeReport === "cash_deployed") {
      return <CashDeployed onClose={() => setActiveReport(null)} />;
    }
    return null;
  };

  // Render member/pending contribution modals
  const renderListModal = () => {
    if (activeList === "member") {
      return <MemberContribution onClose={() => setActiveList(null)} />;
    }
    if (activeList === "pending") {
      return <PendingContribution onClose={() => setActiveList(null)} />;
    }
    return null;
  };

  return (
    <div className="bg-[#f8f5ed] w-full max-w-full sm:max-w-5xl md:max-w-6xl lg:max-w-7xl mx-auto p-0 sm:p-6">
      {showModal && <LogDisbursement onClose={() => setShowModal(false)} />}
      {renderReportModal()}
      {renderListModal()}
      <div className="mb-6 flex flex-col md:flex-row items-center md:items-start justify-between gap-4 w-full">
        <div className="flex-1 min-w-0">
         <h1
            className="font-karla font-medium text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-[48px] leading-[100%] tracking-[0] text-[#2c5282] mb-2 text-center md:text-left flex items-center break-words"
            style={{ wordBreak: "break-word" }}
          >
            Manage Contributions
          </h1>
          <p
            className="font-karla font-light text-base sm:text-lg md:text-xl lg:text-2xl xl:text-[24px] leading-[100%] tracking-[0] text-[#2C5282] mb-6 text-center md:text-left break-words"
            style={{ wordBreak: "break-word" }}
          >
            Overview
          </p>
        </div>
        <div className="flex-shrink-0 flex justify-center md:justify-end w-full md:w-auto">
          <button
            className="flex items-center bg-[#a855f7] transition-colors rounded-[20px] w-full sm:w-[220px] md:w-[260px] lg:w-[308px] h-[48px] sm:h-[60px] md:h-[70px] lg:h-[70px] px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4"
            style={{ minWidth: "0", minHeight: "0" }}
            onClick={() => setShowModal(true)}
          >
            <span className="font-poppins font-normal text-base sm:text-lg md:text-xl lg:text-[24px] leading-[100%] tracking-[0] text-white">
              Log Disbursement
            </span>
          </button>
        </div>
      </div>
      <div className="bg-white w-full p-2 sm:p-3 md:p-5 rounded-2xl border border-gray-200 mb-6 relative">
        <h3 className="font-karla font-medium text-lg sm:text-2xl md:text-[26px] lg:text-[32px] leading-[100%] tracking-[0] text-[#2C5282] mb-2 flex items-center gap-2 lg:ml-4">
          Contribution Overview
        </h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4  py-8">
          {contributions.map((item, idx) => (
            <div
              key={idx}
              className="bg-[#F3F4F6] rounded-[20px] w-full min-w-[180px] max-w-[297px] h-[136px] flex flex-col justify-center items-center shadow-sm mx-auto gap-1"
            >
              <span className="font-karla font-medium text-base md:text-lg lg:text-xl text-[#2c5282] mb-2 text-center px-2">
                {item.contribution}
              </span>
              <span className={`font-poppins font-bold text-xl md:text-2xl lg:text-3xl text-[#2c5282]`}>
                {item.amount}
              </span>
              {showReport(item) && (
                <>
                  <span
                    className="font-poppins font-semibold text-[20px] leading-[100%] mb-1 tracking-[0] underline underline-offset-2 decoration-solid decoration-2 cursor-pointer"
                    style={{
                      color: "#A855F7",
                      textUnderlineOffset: "0.15em",
                      textDecorationColor: "#A855F7",
                      textDecorationSkipInk: "true",
                    }}
                    onClick={() => {
                      if (item.key === "total_contribution") setActiveReport("total_contribution");
                      if (item.key === "total_overheads") setActiveReport("total_overheads");
                      if (item.key === "cash_deployed") setActiveReport("cash_deployed");
                    }}
                  >
                    View report
                  </span>
                  <div className="mb-4"></div>
                </>
              )}
            </div>
          ))}
        </div>

        <div className="bg-[#F3F4F6] rounded-[20px] mx-auto mb-10 w-full max-w-[1090px] pb-8 px-4 sm:px-6 md:px-8 py-8 mt-9">
          <div className="flex flex-col h-full">
            {/* Header */}
            <h2 className="text-2xl font-semibold text-[#2C5282] mb-8 ml-4">Compliance Overview</h2>

            <div className="flex flex-col flex-1 gap-8">
              {/* Main Compliance Section with Linear Progress Bar */}
              <div className="flex-1 p-8 shadow-sm flex flex-col">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-[#2c5282]">Compliance rate</h3>
                  <span className="font-poppins font-semibold text-[20px] leading-[100%] tracking-[0] text-[#A855F7]">
                    80%
                  </span>
                </div>

                {/* Progress Bar Container */}
                <div className="flex flex-col max-w-[1090px] w-full h-[24px] rounded-[20px] bg-[#F5C21A] mx-auto mb-4 gap-[10px]">
                  {/* Progress Bar - 80% */}
                  <div className="h-full rounded-[20px] bg-[#A855F7]" style={{ width: '80%' }}></div>
                </div>

                {/* Percentage Display */}
              </div>

              <div className="flex flex-col lg:flex-row gap-6 justify-center items-center mb-8">
                {/* Members Contributed Card */}
                <div className="bg-[#e9dff6] rounded-[20px] border border-[#A855F7] flex flex-col items-center w-full max-w-xs sm:max-w-sm md:max-w-md lg:w-[414px] h-auto min-h-[180px] pt-6 pb-6 px-6 gap-[15px]">
                  <h3 className="font-poppins font-medium text-[18px] sm:text-[20px] leading-[100%] tracking-[0] text-black mb-2 text-center">
                    Members Contributed
                  </h3>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 text-center">300</p>
                  <button
                    className="font-poppins font-semibold text-[18px] sm:text-[20px] leading-[100%] tracking-[0] underline decoration-solid underline-offset-0 decoration-2 decoration-[#8361B0] text-[#8361B0] hover:opacity-80"
                    style={{
                      textDecorationSkipInk: "true",
                    }}
                    onClick={() => setActiveList("member")}
                  >
                    View List
                  </button>
                </div>
                {/* Pending Contributions Card */}
                <div className="bg-[#f3ecd3] rounded-[20px] border border-[#F5C21A] flex flex-col items-center w-full max-w-xs sm:max-w-sm md:max-w-md lg:w-[414px] h-auto min-h-[180px] pt-6 pb-6 px-6 gap-[15px]">
                  <h3 className="font-poppins font-medium text-[18px] sm:text-[20px] leading-[100%] tracking-[0] text-black mb-2 text-center">
                    Pending Contributions
                  </h3>
                  <p className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 text-center">75</p>
                  <button
                    className="font-poppins font-semibold text-[18px] sm:text-[20px] leading-[100%] tracking-[0] underline decoration-solid underline-offset-0 decoration-2 decoration-[#8361B0] text-[#8361B0] hover:opacity-80"
                    style={{
                      textDecorationSkipInk: "true",
                    }}
                    onClick={() => setActiveList("pending")}
                  >
                    View List
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};