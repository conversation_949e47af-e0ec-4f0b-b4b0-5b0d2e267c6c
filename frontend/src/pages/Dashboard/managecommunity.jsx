import React, { useState, useEffect } from 'react';
import { Overview } from './overview';
import { Members } from './members';
import { Settings } from './settings';

export const Managecommunity = () => {
  const [activeTab, setActiveTab] = useState('Overview');

  useEffect(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  const email = urlParams.get('email');

  if (token) {
    fetch(`/generate-link/validate-token-access?token=${token}${email ? `&email=${encodeURIComponent(email)}` : ''}`)
      .then((res) => res.json())
      .then((data) => {
        if (data.message === 'Link is valid') {
          console.log('Invite is valid and counted:', data);
        } else {
          console.warn('Invalid or expired invite link:', data.message);
          // Optional: redirect or show blocked UI
        }
      })
      .catch((err) => {
        console.error('Error validating token:', err);
      });
  }
}, []);


  const renderTabContent = () => {
    switch (activeTab) {
      case 'Overview':
        return <Overview />;
      case 'Members':
        return <Members />;
      case 'Settings':
        return <Settings />;
      default:
        return null;
    }
  };

  return (
    <div className="flex justify-center items-center">
      <div className="w-full sm:w-full md:min-w-[80vw] min-w-[90vw] lg:max-w-[80vw] mt-[-10px]">
        <h1 className="text-4xl font-karla text-gray-700 mb-6">
          Manage Community
        </h1>

        {/* Tabs */}
        <div className="flex space-x-4 mb-6">
          {['Overview', 'Members', 'Settings'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-md font-semibold ${
                activeTab === tab
                  ? 'bg-yellow-400 text-black'
                  : 'bg-gray-200 text-black'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Tab content */}
        {renderTabContent()}
      </div>
    </div>
  );
};
