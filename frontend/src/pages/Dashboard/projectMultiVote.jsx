import  { useEffect, useState } from 'react';
import stopwatch from '../../assets/pro/stopwatch.png';
import dollar from '../../assets/pro/dollar-symbol.png';
import democracy from '../../assets/pro/democracy.png';
import sigma from '../../assets/Dao/sigma.png';
import checkmark from '../../assets/Dao/check-mark.png';
import checkmarkvoted from '../../assets/Dao/check-markvoted.png';
import checkplain from '../../assets/Dao/checkplain.png';
import { VoteService } from '../../services/voteService';
//import { useNavigate } from "react-router-dom";
// import { AppRoutesPaths } from '../../route/app_route';

export const ProjectMultiVote = ({ vote }) => {
  const [votes, setVotes] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
    // const navigate = useNavigate();

  useEffect(() => {
    if (vote) {
      setVotes([vote]);
      setLoading(false);
      return;
    }
    const fetchVotes = async () => {
      setLoading(true);
      setError('');
      try {
        const allVotes = await VoteService.getAllVotes();
        setVotes(allVotes || []);
      } catch (err) {
        setError('Failed to fetch votes.');
      } finally {
        setLoading(false);
      }
    };
    fetchVotes();
  }, [vote]);

  const handleVote = async (voteId, projectId, userVote) => {
    setSubmitting(true);
    setError('');
    try {
      const res = await VoteService.submitMultiVote({ voteId, projectId, vote: userVote });
      if (res && !res.error) {
        setVotes((prevVotes) =>
          prevVotes.map((vote) =>
            vote._id === voteId
              ? {
                  ...vote,
                  projects: vote.projects.map((proj) =>
                    proj._id === projectId
                      ? {
                          ...proj,
                          voteCount: res.voteCount,
                          percentages: res.percentages,
                          userVote: res.userVote, // This will be null if deselected, or 'yes'/'no'
                        }
                      : proj
                  ),
                }
              : vote
          )
        );
      } else {
        setError(res.error || 'Failed to submit vote.');
      }
    } catch (e) {
      setError('Failed to submit vote.');
    }
    setSubmitting(false);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
         <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500 my-2">{error}</div>;
  }

  return (
    <div className="text-gray-600  text-center px-2 sm:px-4 md:px-10 lg:px-18">
      <div className='flex justify-between items-center mb-8'>
      </div>
      {votes.filter(
        vote =>
          vote.votingType === "multi" &&
          (vote.status === "active" || vote.status === "pending") &&
           new Date(vote.endDate) >= new Date()
      ).length === 0 && (
        <div className="flex flex-col items-center justify-center rounded-2xl shadow-md bg-gradient-to-br from-white via-gray-50 to-purple-50 p-10 sm:p-32 font-karla text-center text-gray-600 mt-4 border border-gray-100">
          <img src={democracy} alt="No votes" className="w-16 h-16 mb-6 opacity-80" />
          <h3 className="text-2xl sm:text-3xl font-bold mb-2 text-gray-700">No Active Updown Votes Available</h3>
          <p className="text-base sm:text-lg text-gray-500 mb-4 max-w-xl">
            There are currently no up-down projects open for voting.<br />
            Please check back later or contact your DAO admin for more information.
          </p>
        </div>
      )}
      <div className="flex flex-col gap-12 mt-10">
        {votes
          .filter(
            vote =>
              vote.votingType === "multi" &&
              (vote.status === "active" || vote.status === "pending") &&
              new Date(vote.endDate) >= new Date()
          )
          .map((vote) => (
            <div key={vote._id} className="mb-12">
              {vote.status === 'pending' && (
                <div className="bg-yellow-100 text-yellow-700 p-4 rounded mb-4">
                  Voting will open on <b>{new Date(vote.startDate).toLocaleString()}</b>.
                </div>
              )}
              <div className="flex flex-col gap-8">
                {(vote.projects ?? []).map((project) => {
                  const yesVotes = project.voteCount?.yes ?? 0;
                  const noVotes = project.voteCount?.no ?? 0;
                  const totalVotes = yesVotes + noVotes;
                  const yesPercent = totalVotes > 0 ? Math.round((yesVotes / totalVotes) * 100) : 0;
                  const noPercent = totalVotes > 0 ? 100 - yesPercent : 0;
                  return (
                    <div key={project._id} className="bg-white px-2 sm:px-4 md:px-8 lg:px-16 flex flex-col p-4 sm:p-8 rounded-3xl w-full shadow-lg">
                      <div className="flex flex-col md:flex-row justify-between items-start mb-4 gap-4">
                        <div className="flex items-center bg-white text-gray-700 px-4 py-1 rounded-xl text-sm font-medium shadow-lg">
                          <img src={sigma} className="w-3 h-3 mr-2" alt="sigma" />
                          <span>{totalVotes} total votes</span>
                        </div>
                        <div className="w-20 h-20 rounded-full border-4 border-gray-200 flex flex-col items-center justify-center shadow-2xl bg-white mx-auto md:mx-0">
                          <img
                            src={project.userVote ? checkmarkvoted : checkmark}
                            alt="vote status"
                            className="w-7 h-7 mb-1"
                            style={{ filter: project.userVote ? 'none' : 'grayscale(1)' }}
                          />
                          <p className={`text-xs font-medium ${project.userVote ? 'text-purple-600' : 'text-gray-400'}`}>
                            {project.userVote ? 'Your vote' : 'Not voted'}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <h3 className="text-2xl text-justify font-extrabold text-gray-900 mb-3">
                          {project.title}
                        </h3>
                        <p className="text-gray-500 text-base leading-relaxed max-w-3xl mb-6 text-justify">
                          {project.description}
                        </p>
                      </div>
                      {/* Vote results */}
                      <div className="mb-6">
                        <div className="flex flex-col sm:flex-row items-center mb-2 gap-2">
                          <span className={`font-semibold mr-2 flex items-center gap-1 ${project.userVote === 'yes' ? 'text-purple-600' : 'text-teal-500'}`}>
                            Yes ({yesPercent}%)
                            {project.userVote === 'yes' && (
                              <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </span>
                          <div className="flex-1 w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-teal-400 h-2 rounded-full transition-all duration-300" style={{ width: `${yesPercent}%` }}></div>
                          </div>
                        </div>
                        <div className="flex flex-col sm:flex-row items-center gap-2">
                          <span className={`font-semibold mr-2 flex items-center gap-1 ${project.userVote === 'no' ? 'text-pink-500' : 'text-rose-400'}`}>
                            No ({noPercent}%)
                            {project.userVote === 'no' && (
                              <svg className="w-4 h-4 text-pink-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </span>
                          <div className="flex-1 w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-rose-300 h-2 rounded-full transition-all duration-300" style={{ width: `${noPercent}%` }}></div>
                          </div>
                        </div>
                      </div>
                      {/* Buttons */}
                      <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4">
                        <button
                          className={`font-medium py-4 px-10 sm:px-24 rounded-xl min-w-full sm:min-w-[130px] w-full sm:w-auto shadow-lg flex items-center justify-center gap-2 transition-colors
                            ${project.userVote === 'yes'
                              ? 'bg-purple-500 text-white'
                              : 'bg-[#f7eeff] text-gray-700 hover:bg-purple-100'}
                          `}
                          disabled={submitting || vote.status !== 'active'}
                          onClick={() =>
                            handleVote(
                              vote._id,
                              project._id,
                              project.userVote === 'yes' ? null : 'yes'
                            )
                          }
                        >
                          <span>Vote Yes</span>
                          {project.userVote === 'yes' && (
                            <img src={checkplain} alt="checked" className="w-5 h-5 ml-2" />
                          )}
                        </button>
                        <button
                          className={`font-medium py-4 px-10 sm:px-24 rounded-xl min-w-full sm:min-w-[130px] w-full sm:w-auto shadow-lg flex items-center justify-center gap-2 transition-colors
                            ${project.userVote === 'no'
                              ? 'bg-[#fb7185] text-white'
                              : 'bg-[#fff1f3] text-gray-700 hover:bg-pink-100'}
                          `}
                          disabled={submitting || vote.status !== 'active'}
                          onClick={() =>
                            handleVote(
                              vote._id,
                              project._id,
                              project.userVote === 'no' ? null : 'no'
                            )
                          }
                        >
                          <span>Vote No</span>
                          {project.userVote === 'no' && (
                            <img src={checkplain} alt="checked" className="w-5 h-5 ml-2" />
                          )}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};