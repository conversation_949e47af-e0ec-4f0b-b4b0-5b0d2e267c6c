import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useParams } from 'react-router-dom';
import { FiDollarSign, FiCalendar } from 'react-icons/fi';
import bging from '../../assets/dashboard/bging.png';
import { ProjectService } from '../../services/projectService';
import { useAuthentication } from '../../components/utils/provider';

export const ProjectDetails = ({ type = 'ongoing' }) => {
  const { id: projectId } = useParams();
  const { currentUser } = useAuthentication();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [expandedUpdate, setExpandedUpdate] = useState(null); // <-- Add this
  const [descExpanded, setDescExpanded] = useState(false);
  const [isSubscriber, setIsSubscriber] = useState(currentUser.isSubscribed);


  useEffect(() => {
    const fetchProject = async () => {
      setLoading(true);
      let projects = [];
      if (type === 'completed') {
        projects = await ProjectService.getCompletedProjects();
      } else {
        projects = await ProjectService.getOngoingProjects();
      }
      // Compare as strings to avoid type mismatch
      const found = projects.find((p) => String(p.id) === String(projectId));
      setProject(found || null);
      setLoading(false);
    };
    fetchProject();
  }, [projectId, type]);

  if (loading) {
    return (
      <div className="flex justify-center items-center w-full mt-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex justify-center w-full mt-8">
        <div className="bg-white shadow-md rounded-lg md:p-48 p-24 font-karla md:text-3xl text-lg text-center">
          <p className="text-gray-600">Project not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-[99vw] sm:max-w-[98vw] md:max-w-[96vw] lg:max-w-[90vw] xl:max-w-[80vw] 2xl:max-w-[1400px] mx-auto font-sans text-gray-900 px-1 sm:px-4 md:px-8 py-8 min-h-screen">
      {/* Hero Section */}
      <div className="relative rounded-[2rem] overflow-hidden text-white mb-8">
        <img
          src={project.image}
          alt={project.title}
          crossorigin="anonymous"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = bging;
          }}
          className="w-full h-[500px] sm:h-[450px] md:h-150 object-cover"
        />
        <div className="absolute inset-0 bg-black/50 p-4 sm:p-8 flex flex-col justify-end">
          <span className="bg-white/20 px-3 py-1 rounded-full text-xs sm:text-sm w-fit mb-2 sm:mb-4">
            {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            {/* {project.status === 'completed' ? ' - ' + project.completionDate : ''} */}
            {/* Development */}
          </span>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 leading-tight">
            {project.title} <br className="hidden sm:block" />
            {/* (kenya) */}
          </h2>
          <div
            className="w-full max-w-3xl  mx-auto flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0 bg-gradient-to-br from-[#4d4236]/80 to-[#2d2925]/80 p-4 sm:p-8 rounded-2xl shadow-lg backdrop-blur-md"
            style={{
              backdropFilter: 'blur(8px)',
              border: '1px solid rgba(255,255,255,0.08)',
            }}
          >
            <div className="flex items-center gap-3 flex-1 justify-center sm:justify-start">
              <FiDollarSign className="text-2xl sm:text-3xl text-white" />
              <div>
                <p className="text-base sm:text-lg font-medium">Funding Goal</p>
                <p className="text-lg sm:text-xl font-semibold">
                  $ {project.fundingAllocation.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="hidden sm:block h-14 w-px bg-white/20 mx-6" />
            <div className="flex items-center gap-3 flex-1 justify-center sm:justify-start">
              <FiDollarSign className="text-2xl sm:text-3xl text-[#2ED8B6]" />
              <div>
                <p className="text-base sm:text-lg font-medium">
                  Current Funding
                </p>
                <p className="text-lg sm:text-xl font-semibold">
                  $ {project.fundingAllocation.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="hidden sm:block h-14 w-px bg-white/20 mx-6" />
            <div className="flex items-center gap-3 flex-1 justify-center sm:justify-start">
              <FiCalendar className="text-2xl sm:text-3xl text-white" />
              <div>
                <p className="text-base sm:text-lg font-medium">Start date</p>
                <p className="text-lg sm:text-xl font-semibold">
                  {new Date(project.startDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="p-4 sm:p-6  -mt-2">
        <p className="mb-4 text-sm sm:text-base leading-relaxed">
          {descExpanded || !project.description || project.description.length <= 200
            ? project.description
            : `${project.description.slice(0, 200)}...`}
        </p>
        {project.description && project.description.length > 200 && (
          <button
            type="button"
            className="text-purple-600 font-semibold flex items-center gap-1 text-sm sm:text-base focus:outline-none"
            onClick={() => setDescExpanded((prev) => !prev)}
          >
            {descExpanded ? (
              <>
                Show less <span className="text-xl">↑</span>
              </>
            ) : (
              <>
                Read more <span className="text-xl">→</span>
              </>
            )}
          </button>
        )}
      </div>

      {/* Section Title */}
      <div className="mt-10 px-2 sm:px-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="w-6 sm:w-8 h-1 rounded-full bg-purple-600" />
          <h3 className="text-xl sm:text-2xl font-bold">Projects updates</h3>
        </div>

        {/* Timeline Items */}
        {Array.isArray(project.updates) && project.updates.length > 0 ? (
          project.updates.map((update, idx) => {
            const isLong = update.text && update.text.length > 250;
            const isExpanded = expandedUpdate === idx;
            return (
              <div
                key={update._id || idx} // Use _id as the key
                className="relative pl-8 sm:pl-10 mt-6"
              >
                <div className="absolute left-[7px] sm:left-[9px] top-0 w-[2px] h-full bg-[#D3D7E2]" />
                <div className="absolute left-[1px] sm:left-[3px] top-0 w-3 sm:w-4 h-3 sm:h-4 bg-purple-500 rounded-full z-10" />

                <div className="w-full max-w-6xl sm:max-w-full bg-[#f3f2f0] shadow-xl rounded-2xl p-4 sm:p-6">
                  <div className="flex items-center gap-2 text-gray-600 text-xs sm:text-sm mb-2">
                    <FiCalendar className="text-base sm:text-lg" />
                    <span>
                      {update.createdAt
                        ? new Date(update.createdAt).toLocaleDateString()
                        : ''}
                    </span>
                  </div>
                  <h4 className="text-lg sm:text-xl text-purple-600 font-poppins mb-2">
                    {update.title || `Latest update ${idx + 1}`}
                  </h4>
                  <p className="text-gray-700 mb-4 text-sm sm:text-base">
                    {isLong && !isExpanded
                      ? `${update.text.slice(0, 250)}...`
                      : update.text}
                    {isLong && !isExpanded && (
                      <button
                        className="ml-2 text-purple-600 font-medium hover:underline"
                        onClick={() => setExpandedUpdate(idx)}
                      >
                        Read more
                      </button>
                    )}
                    {isLong && isExpanded && (
                      <button
                        className="ml-2 text-purple-600 font-medium hover:underline"
                        onClick={() => setExpandedUpdate(null)}
                      >
                        Show less
                      </button>
                    )}
                  </p>
                  {typeof update.image === 'string' &&
                    update.image.trim() !== '' &&
                    update.image !== 'null' &&
                    update.image !== 'undefined' && (
                      <img
                        src={update.image}
                        crossOrigin="anonymous"
                        alt="Project update"
                        className="rounded-xl w-full object-cover h-40 sm:h-56"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    )}
                </div>
              </div>
            );
          })
        ) : (
          <div className="flex justify-center w-full mt-8">
            <div className="bg-white shadow-md rounded-lg md:p-48 p-24 font-karla md:text-3xl text-lg text-center">
              <p className="text-gray-600">
                No project updates at the moment.
              </p>
            </div>
          </div>
        )}

        {/* Contributor Call-to-Action */}
        {!isSubscriber &&
          <div className="mt-10 px-0 sm:px-10 flex justify-center">
          <div className="w-full max-w-2xl bg-gradient-to-br from-[#315685] to-[#55739b] text-white p-6 sm:p-8 rounded-xl sm:rounded-xl shadow-lg flex flex-col items-start">
            <h3 className="text-xl sm:text-2xl font-bold mb-2">
              Are you a contributor?
            </h3>
            <p className="text-base sm:text-lg mb-6 leading-snug">
              Join the discussion or participate in development
            </p>
            <Link
              to="/monthly-contribution"
              className="w-full text-center bg-white text-[#34518b] font-semibold text-lg sm:text-xl px-0 py-4 rounded-xl hover:bg-gray-100 transition shadow-none"
              style={{ boxShadow: '0 2px 8px 0 rgba(44, 62, 80, 0.08)' }}
            >
              Get involved
            </Link>
          </div>
        </div>}

        {/* More Updates Placeholder */}
        {project.status === 'ongoing' && 
          <div className="mt-6 text-center">
          <button className="inline-flex items-center gap-2 bg-white text-gray-400 font-medium px-4 sm:px-6 py-2 sm:py-3 border border-dashed border-gray-300 rounded-full cursor-default text-sm sm:text-base">
            More updates coming soon
            <span className="text-lg">→</span>
          </button>
        </div>}
      </div>
    </div>
  );
};
