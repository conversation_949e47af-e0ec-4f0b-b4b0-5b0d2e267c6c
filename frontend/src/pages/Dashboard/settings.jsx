import React, { useState, useEffect } from 'react';
import { MdInfoOutline, MdToggleOn, MdToggleOff } from 'react-icons/md';
import { DeleteModal } from '../modals/DeleteModal';
import { PrivacyConfirmationModal } from '../modals/PrivacyConfirmationModal';
import { TransferOwnershipModal } from '../modals/TransferOwnershipModal';
import { CommunityService } from '../../services/CommunityService';
import { UserService } from '../../services/userService';
import { useAuthentication } from '../../components/utils/provider';
import { toast } from 'react-toastify';

export const Settings = ({ communityId = "yourCommunityId" }) => {
  const [isPublicConfirmed, setIsPublicConfirmed] = useState(false);
  const [allowInvites, setAllowInvites] = useState(true);
  const [requireApproval, setRequireApproval] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [transferLoading, setTransferLoading] = useState(false);
  const [adminMembers, setAdminMembers] = useState([]);
  const { currentUser, setCurrentUser } = useAuthentication();

  // Fetch admin users on mount
  useEffect(() => {
    const fetchAdmins = async () => {
      const users = await UserService.getAllUsers();
      const admins = (users || []).filter(u => (u.role || '').toLowerCase() === 'admin' && u._id !== currentUser._id);
      setAdminMembers(admins);
    };
    fetchAdmins();
  }, [currentUser._id]);

  const Toggle = ({ checked, onChange, size = 'h-10 w-10' }) => (
    <div onClick={() => onChange(!checked)} className="cursor-pointer">
      {checked ? (
        <MdToggleOn className={`${size} text-purple-600`} />
      ) : (
        <MdToggleOff className={`${size} text-gray-400`} />
      )}
    </div>
  );

  const handleToggleChange = async (value) => {
    if (value) {
      const confirmed = await openConfirmationModal();
      if (confirmed) setIsPublicConfirmed(true);
    } else {
      setIsPublicConfirmed(false);
    }
  };

  const openConfirmationModal = () => {
    return new Promise((resolve) => {
      setShowPrivacyModal(true);
      window._resolvePrivacyConfirm = resolve;
    });
  };

  // --- Transfer Logic ---
  const handleTransferOwnership = async (newOwnerId) => {
    setTransferLoading(true);
    try {
      await CommunityService.initiateTransfer(communityId, newOwnerId);
      setShowTransferModal(false);
      toast.success("Transfer initiated. The new owner will receive an email to accept ownership.");
      // Optionally, you can poll or listen for transfer acceptance and update dashboard accordingly.
    } catch (e) {
      // Error toast handled in service
    } finally {
      setTransferLoading(false);
    }
  };

  // Listen for transfer acceptance (optional, for instant dashboard update)
  useEffect(() => {
    // This is a placeholder for a real-time update or polling mechanism.
    // For now, after transfer, user should re-login or refresh to see new dashboard details.
    // If you have a websocket or polling, update currentUser here after acceptance.
    // Example:
    // setCurrentUser(updatedUser);
  }, []);

  return (
    <div className="max-w-8xl mx-auto rounded-lg space-y-6 p-6 bg-white min-h-screen">
      <h1 className="text-2xl font-semibold text-gray-600">
        Community Settings
      </h1>

      {/* Privacy Section */}
      <div className="rounded-lg shadow-sm p-4 border border-gray-300">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="font-semibold text-lg text-gray-600">
              Community Privacy
            </h2>
            <p className="text-sm text-gray-600 mt-2">
              Control who can find and join your community.
            </p>
          </div>
          <Toggle
            checked={isPublicConfirmed}
            onChange={handleToggleChange}
            size="h-16 w-16"
          />
        </div>

        {isPublicConfirmed && (
          <div className="flex items-start gap-2 mt-6 p-3 rounded-lg bg-gray-100 text-sm text-gray-700">
            <MdInfoOutline className="h-6 w-6 mt-0.5 text-indigo-600" />
            <div>
              <span className="font-medium">Public Mode:</span> Anyone can find
              and join your community. Your community will be visible in search
              results.
            </div>
          </div>
        )}
      </div>

      {/* Invitation Settings */}
      <div className="rounded-lg shadow-sm p-4 border border-gray-300">
        <div className="space-y-6">
          <div>
            <h2 className="font-semibold text-lg text-gray-600">
              Invitation Settings
            </h2>
          </div>

          <div className="flex items-center justify-between text-gray-600">
            <div>
              <p className="font-medium text-gray-600">Member Invitations</p>
              <p className="text-sm text-gray-600 mt-2">
                Allow members to invite others
              </p>
            </div>
            <Toggle
              checked={allowInvites}
              onChange={setAllowInvites}
              size="h-16 w-16"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-600">Approval Required</p>
              <p className="text-sm text-gray-600 mt-2">
                New members require admin approval
              </p>
            </div>
            <Toggle
              checked={requireApproval}
              onChange={setRequireApproval}
              size="h-16 w-16"
            />
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div className="rounded-lg shadow-sm p-6 border border-gray-300">
        <h2 className="font-semibold text-lg text-red-600">Danger Zone</h2>

        <div className="mt-4 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-600">Transfer Ownership</p>
              <p className="text-sm text-gray-600 mt-2">
                Transfer this community to another admin. They will receive an email to accept ownership.
              </p>
            </div>
            <button
              onClick={() => setShowTransferModal(true)}
              className="border border-gray-300 rounded-md px-6 py-2 mb-4 text-gray-600 text-sm font-medium"
            >
              Transfer
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-600">Delete Community</p>
              <p className="text-sm text-gray-600 mt-2">
                Permanently delete this community and all its data
              </p>
            </div>
            <button
              onClick={() => setShowDeleteModal(true)}
              className="bg-red-100 text-red-600 font-semibold px-6 py-2 rounded-md"
            >
              Delete
            </button>
          </div>
        </div>
      </div>

      {/* Modals */}
      {showPrivacyModal && (
        <PrivacyConfirmationModal
          onClose={() => {
            setShowPrivacyModal(false);
            window._resolvePrivacyConfirm?.(false);
          }}
          onConfirm={() => {
            setShowPrivacyModal(false);
            window._resolvePrivacyConfirm?.(true);
          }}
        />
      )}

      {showTransferModal && (
        <TransferOwnershipModal
          onClose={() => setShowTransferModal(false)}
          members={adminMembers}
          onTransfer={handleTransferOwnership}
          loading={transferLoading}
        />
      )}

      {showDeleteModal && (
        <DeleteModal
          onClose={() => setShowDeleteModal(false)}
          confirmText={confirmText}
          setConfirmText={setConfirmText}
        />
      )}
    </div>
  );
};
