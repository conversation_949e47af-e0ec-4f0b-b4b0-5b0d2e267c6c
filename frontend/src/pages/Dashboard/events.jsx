import React, { useState, useEffect } from 'react';
import { CiClock2 } from "react-icons/ci";
import { FiCheckCircle } from "react-icons/fi";
import play from '../../assets/dashboard/play.png';
import schedule from '../../assets/dashboard/schedule.png';
import { EventService } from '../../services/eventService';
import Swal from 'sweetalert2'; 
import { SubmitQuestionModal } from "../modals/submitQuestionModal"; 
import { useAuthentication } from '../../components/utils/provider';
import { AttendEventModal } from "../modals/attendEventModal";

export const Events = () => {
  // Use localStorage for tab persistence
  const [activeTab, setActiveTab] = useState(() => localStorage.getItem("eventsTab") || "upcoming");
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [pastEvents, setPastEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [questionText, setQuestionText] = useState('');
  const [submittedBy, setSubmittedBy] = useState('');
  const [questionEventId, setQuestionEventId] = useState(null);
  const [submittingQuestion, setSubmittingQuestion] = useState(false);
  const [showAttendModal, setShowAttendModal] = useState(false);
  const [attendEmail, setAttendEmail] = useState('');
  const [attendName, setAttendName] = useState(''); 
  const [attendEventId, setAttendEventId] = useState(null);
  const [attending, setAttending] = useState(false);
  const [reminderLoading, setReminderLoading] = useState({});
  const [reminderChecked, setReminderChecked] = useState({});
  const [showFullDescription, setShowFullDescription] = useState({});
  const { currentUser } = useAuthentication();

  // Fetch events and initialize reminderChecked state
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      const [upcoming, past] = await Promise.all([
        EventService.getAllEvents("upcoming"),
        EventService.getAllEvents("past"),
      ]);
      setUpcomingEvents(upcoming || []);
      setPastEvents(past || []);
      // Set reminderChecked for each event
      const checked = {};
      (upcoming || []).forEach(event => {
        checked[event.id || event._id] = (event.reminders || []).includes(currentUser?.email);
      });
      setReminderChecked(checked);
      setLoading(false);
    };
    fetchEvents();
    // eslint-disable-next-line
  }, [currentUser?.email]);

  // Save tab to localStorage on change
  useEffect(() => {
    localStorage.setItem("eventsTab", activeTab);
  }, [activeTab]);

  // Helper for empty state message
  const renderEmptyState = (type) => (
    <div className="flex flex-col items-center justify-center rounded-2xl shadow-md bg-gradient-to-br from-white via-gray-50 to-purple-50 p-6 sm:p-10 font-karla text-center text-gray-600 mt-4 border border-gray-100">
      <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 text-gray-700">
        No {type === "upcoming" ? "Upcoming" : "Past"} Events Available
      </h3>
      <p className="text-base sm:text-lg text-gray-500 mb-4 max-w-xl">
        There are currently no {type === "upcoming" ? "upcoming" : "past"} events.<br />
        Please check back later or contact your DAO admin for more information.
      </p>
    </div>
  );

  function formatDate(dateStr) {
    if (!dateStr) return "";
    if (dateStr.includes("T")) return dateStr.split("T")[0];
    return dateStr;
  }

  // Attend event handler (only email needed, name fetched by backend)
  const handleAttendEvent = (eventId) => {
    setAttendEventId(eventId);
    setShowAttendModal(true);
    setAttendEmail(currentUser?.email || '');
    setAttendName(''); // <-- reset name
  };

  const handleAttendSubmit = async () => {
    if (!attendEmail.trim() || !attendName.trim()) return;
    setAttending(true);
    const res = await EventService.attendEvent(attendEventId, { name: attendName.trim(), email: attendEmail.trim() });
    setAttending(false);
    if (res) {
      Swal.fire('Success!', 'Attendance confirmed. Check your email for the meeting link.', 'success');
      setShowAttendModal(false);
    }
  };

  const handleOpenQuestionModal = (eventId) => {
    setQuestionEventId(eventId);
    setShowQuestionModal(true);
    setQuestionText('');
    setSubmittedBy('');
  };

  const handleSubmitQuestion = async () => {
    if (!questionText) return;
    setSubmittingQuestion(true);
    const res = await EventService.submitQuestion(questionEventId, {
      text: questionText,
      submittedBy: currentUser?.firstName || "Anonymous"
    });
    setSubmittingQuestion(false);
    if (res) {
      Swal.fire('Success!', 'Your question has been submitted.', 'success');
      setShowQuestionModal(false);
    }
  };

  // Senior: Per-event reminder subscribe/unsubscribe
  const handleReminderToggle = async (eventId, checked) => {
    if (!currentUser?.email) {
      Swal.fire('Error', 'You must be logged in to subscribe for reminders.', 'error');
      return;
    }
    setReminderLoading(prev => ({ ...prev, [eventId]: true }));
    try {
      if (checked) {
        await EventService.subscribeReminder(eventId, currentUser.email);
        Swal.fire('Subscribed!', 'You will receive a reminder before the event.', 'success');
      } else {
        await EventService.unsubscribeReminder(eventId, currentUser.email);
        Swal.fire('Unsubscribed', 'You will not receive a reminder for this event.', 'info');
      }
      setReminderChecked(prev => ({ ...prev, [eventId]: checked }));
    } catch (err) {
      Swal.fire('Error', 'Could not update reminder subscription.', 'error');
    }
    setReminderLoading(prev => ({ ...prev, [eventId]: false }));
  };

  // Toggle description expand/collapse per event
  const toggleShowFullDescription = (eventId) => {
    setShowFullDescription((prev) => ({
      ...prev,
      [eventId]: !prev[eventId],
    }));
  };


  function isEventHappeningNow(event) {
  if (!event.date || !event.time || !event.duration) return false;
  const tzOffsetMinutes = 180; // EAT (UTC+3)
  let [hours, minutes] = [0, 0];
  let t = (event.time || '').trim().toLowerCase();
  if (t.includes('am') || t.includes('pm')) {
    let match = t.match(/(\d{1,2}):(\d{2})\s*(am|pm)/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
      if (match[3] === 'pm' && hours !== 12) hours += 12;
      if (match[3] === 'am' && hours === 12) hours = 0;
    }
  } else {
    let match = t.match(/(\d{1,2}):(\d{2})/);
    if (match) {
      hours = parseInt(match[1], 10);
      minutes = parseInt(match[2], 10);
    }
  }
  let start = new Date(event.date);
  start.setUTCHours(hours - tzOffsetMinutes / 60, minutes, 0, 0);
  let end = new Date(start.getTime() + event.duration * 60000);
  let now = new Date();
  return now >= start && now < end;
}

  return (
    <div className="min-h-screen p-4 -mt-10 flex justify-center">
      <div className="w-full sm:max-w-full rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
        <div className="mb-6">
          <h1 className="font-karla font-medium text-[48px] leading-[100%] tracking-[0] text-[#2c5282] mb-2 text-left flex items-center">
            Events
          </h1>
        </div>
        {/* Tabs */}
        <div className="flex flex-row gap-2 sm:gap-6 md:gap-8 mb-6 relative border-b border-gray-200 overflow-x-auto lg:overflow-x-visible scrollbar-hide w-full justify-start">
          <div
            className={`font-medium cursor-pointer relative flex items-center pb-2 transition-colors text-xs sm:text-base md:text-lg ${
              activeTab === "upcoming" ? "text-[#2c5282]" : "text-gray-500"
            }`}
            style={{ minWidth: 0 }}
            onClick={() => setActiveTab("upcoming")}
          >
            <CiClock2 className="mr-1 sm:mr-2 w-[1.1rem] h-[1.1rem] sm:w-[1.2rem] sm:h-[1.2rem] md:w-[28px] md:h-[28px]" />
            <span className="truncate font-poppins font-normal text-base sm:text-lg md:text-[22px] lg:text-[26px] leading-[100%] tracking-[0]">
              Upcoming Events
            </span>
            {activeTab === "upcoming" && (
              <span className="absolute left-0 -bottom-[2px] w-full h-1 bg-[#2c5282] rounded transition-all duration-200"></span>
            )}
          </div>
          <span className="block sm:hidden w-4"></span>
          <div
            className={`font-medium cursor-pointer relative flex items-center pb-2 transition-colors text-xs sm:text-base md:text-lg ${
              activeTab === "past" ? "text-[#2c5282]" : "text-gray-500"
            }`}
            style={{ minWidth: 0 }}
            onClick={() => setActiveTab("past")}
          >
            <FiCheckCircle className="mr-1 sm:mr-2 w-[1.1rem] h-[1.1rem] sm:w-[1.2rem] sm:h-[1.2rem] md:w-[28px] md:h-[28px]" />
            <span className="truncate font-poppins font-normal text-base sm:text-lg md:text-[22px] lg:text-[26px] leading-[100%] tracking-[0]">
              Past Events
            </span>
            {activeTab === "past" && (
              <span className="absolute left-0 -bottom-[2px] w-full h-1 bg-[#2c5282] rounded transition-all duration-200"></span>
            )}
          </div>
        </div>
        
        {/* Tab Content */}
        {activeTab === "upcoming" && (
          <section>
            {loading ? (
              <div className="flex flex-col items-center justify-center py-16">
                <svg className="animate-spin h-12 w-12 text-[#2c5282] mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
                <span className="text-lg text-[#2c5282] font-karla">Loading events...</span>
              </div>
            ) : upcomingEvents.length === 0 ? (
              renderEmptyState("upcoming")
            ) : (
              upcomingEvents.map((event) => {
                const eventId = event.id || event._id;
                return (
                  <div key={eventId} className="flex justify-center items-center sm:block">
                    <div className="bg-[#ffffff] p-6 sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
                      <h3 className="text-lg font-bold text-gray-800 mb-5">
                        {event.title}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                          {isEventHappeningNow(event)
                            ? <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full font-semibold text-xs sm:text-sm md:text-base">
                                (event happening right now)
                              </span>
                            : <>{formatDate(event.date)} | {event.time}</>
                          }
                        </p>
                      <p className="mt-2 text-gray-700 whitespace-pre-line">
                        {showFullDescription[eventId]
                          ? event.description
                          : event.description?.length > 200
                            ? `${event.description.substring(0, 200)}...`
                            : event.description}
                        {event.description && event.description.length > 200 && (
                          <button
                            className="text-purple-600 font-semibold hover:underline ml-2"
                            onClick={() => toggleShowFullDescription(eventId)}
                            type="button"
                          >
                            {showFullDescription[eventId] ? "Read less" : "Read more"}
                          </button>
                        )}
                      </p>
                      {/* Buttons Section */}
                      <div className="mt-4 flex flex-wrap md:gap-24 gap-6">
                        <button
                          className="flex justify-start items-center gap-2 min-w-[250px] px-6 py-2 bg-[#9333EA] text-white text-[26px] font-medium rounded-lg shadow-md  transition"
                          onClick={() => handleAttendEvent(eventId)}
                        >
                          <img src={schedule} alt="Attend event" className="w-8 h-8" />
                          Attend event
                        </button>
                        <button
                          className="flex justify-start items-center gap-2 min-w-[250px] px-6 py-2 bg-BP-gold-gradient-end text-white text-[26px] font-semibold rounded-lg shadow-md  transition"
                          onClick={() => handleOpenQuestionModal(eventId)}
                        >
                          <img src={play} alt="Submit a question" className="w-8 h-8" />
                          Submit a question
                        </button>
                      </div>
                      {/* Subscribe Checkbox */}
                      <div className="mt-10 flex items-center">
                        <div
                          onClick={async () => {
                            if (reminderLoading[eventId]) return;
                            await handleReminderToggle(eventId, !reminderChecked[eventId]);
                          }}
                          className={`mr-2 h-6 w-6 flex items-center justify-center border border-gray-400 rounded-md cursor-pointer transition-all ${
                            reminderChecked[eventId] ? 'border-[#9333EA] text-[#9333EA]' : ''
                          } ${reminderLoading[eventId] ? 'opacity-50 pointer-events-none' : ''}`}
                        >
                          {reminderChecked[eventId] && '✔'}
                        </div>
                        <label
                          onClick={async () => {
                            if (reminderLoading[eventId]) return;
                            await handleReminderToggle(eventId, !reminderChecked[eventId]);
                          }}
                          className={`text-sm text-gray-600 cursor-pointer ${reminderLoading[eventId] ? 'opacity-50 pointer-events-none' : ''}`}
                        >
                          Subscribe for a reminder
                        </label>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </section>
        )}

        {activeTab === "past" && (
          <section>
            {loading ? (
              <div className="flex flex-col items-center justify-center py-16">
                <svg className="animate-spin h-12 w-12 text-[#2c5282] mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                </svg>
                <span className="text-lg text-[#2c5282] font-karla">Loading events...</span>
              </div>
            ) : pastEvents.length === 0 ? (
              renderEmptyState("past")
            ) : (
              pastEvents.map((event) => {
                const eventId = event.id || event._id;
                 const hasRecording = event.recordingLinks && event.recordingLinks.length > 0;
                return (
                  <div key={eventId} className="flex justify-center items-center sm:block">
                    <div className="bg-[#ffffff] p-6 sm:w-full min-w-[90vw] md:min-w-[70vw] lg:min-w-[50vw] xl:min-w-[40vw] bg-white shadow-md rounded-[16px] p-6 sm:p-10 mb-8 mx-auto">
                      <h3 className="text-lg font-bold text-gray-800 mb-4">
                        {event.title}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                        {formatDate(event.date)} | {event.time}
                      </p>
                      <p className="mt-2 text-gray-700 whitespace-pre-line">
                        {showFullDescription[eventId]
                          ? event.description
                          : event.description?.length > 200
                            ? `${event.description.substring(0, 200)}...`
                            : event.description}
                        {event.description && event.description.length > 200 && (
                          <button
                            className="text-purple-600 font-semibold hover:underline ml-2"
                            onClick={() => toggleShowFullDescription(eventId)}
                            type="button"
                          >
                            {showFullDescription[eventId] ? "Read less" : "Read more"}
                          </button>
                        )}
                      </p>
                      {/* Watch Recording Button */}
                      <div className="mt-8">
                        {hasRecording ? (
                  <a
                    href={event.recordingLinks[0]}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-start gap-2 w-64 px-6 py-3 border text-[20px] border-purple-400 text-purple-600 font-semibold rounded-lg shadow-md hover:bg-purple-100 transition"
                  >
                    <img src={play} alt="Watch recording" className="w-8 h-8" />
                    Watch recording
                  </a>
                ) : (
                  <div className="text-gray-400 text-lg font-semibold flex items-center gap-2">
                    <img src={play} alt="No recording" className="w-8 h-8 opacity-40" />
                    No recording yet
                  </div>
                )}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </section>
        )}
        {/* Render AttendEventModal */}
        {showAttendModal && (
          <AttendEventModal
            open={showAttendModal}
            onClose={() => setShowAttendModal(false)}
            email={attendEmail}
            setEmail={setAttendEmail}
            name={attendName}
            setName={setAttendName}
            onSubmit={handleAttendSubmit}
            loading={attending}
          />
        )}
        {/* Render SubmitQuestionModal */}
        {showQuestionModal && (
          <SubmitQuestionModal
            onClose={() => setShowQuestionModal(false)}
            questionText={questionText}
            setQuestionText={setQuestionText}
            onSubmit={handleSubmitQuestion}
            loading={submittingQuestion}
          />
        )}
      </div>
    </div>
  );
};

