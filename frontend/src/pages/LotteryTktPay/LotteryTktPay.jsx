import React, { useState, useEffect } from 'react';
import ticketsYellow from "../../assets/images/ticketsYellow.png";
import { FaWallet } from "react-icons/fa";
import { IoIosCheckmarkCircle } from "react-icons/io";
import { useAuthentication } from '../../components/utils/provider';
import calendardis from '../../assets/sahelion/calendardis.png';
import { Spin } from 'antd';
import ConcentricArcLoader from "../../utils/ConcentricArcLoader.jsx";
import { toast } from "react-toastify";


export const LotteryTktPay = ({
  allTicketData,
  closeNumsModal,
  closeInvalidInputs,
  closeConfirmAssignModal,
  setTicketPurchased,
  closeTktPay
}) => {
  const [lotteryTktPay, setLotteryTktPay] = useState([]);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [purchaseIsLoading, setPurchaseIsLoading] = useState(false);
  const [upcomingTicketsIsLoading, setUpcomingTicketsIsLoading] = useState(false);
  const [showUpcomingTickets, setShowUpcomingTickets] = useState(false);
  const [upcomingTickets, setUpcomingTickets] = useState([]);

  // Get wallet connection functions from useAuthentication
  const {
    connectWallet,
    currentUserWallet,
    disconnectWallet,
    buyLotteryTicket,
    getPlayerUpcomingTickets,
  } = useAuthentication();

    const confirmPurchaseRef = React.useRef(null); // Create a ref for the confirmPurchase button
  
    const scrollToConfirmPurchase = () => {
      if (confirmPurchaseRef.current) {
        confirmPurchaseRef.current.scrollIntoView({ behavior: 'smooth' }); // Smooth scroll to the component
      }
    };

  // Store props in local state once on mount
  useEffect(() => {
    setLotteryTktPay(allTicketData);
  }, [allTicketData]);

  useEffect(() => {
    scrollToConfirmPurchase()
  }, []);


  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  const buyTickets = async () => {
    if (!currentUserWallet) {
      toast.error("Please connect your wallet to proceed with the purchase.");
      return;
    }
    setPurchaseIsLoading(true);
    if (allTicketData.length > 0) {
      let tickets = allTicketData.map(ticket => ticket.ticketNumber)
      console.log("Tickets to purchase:", tickets);
      const result = await buyLotteryTicket(tickets)
      if (result.success) {
        setShowPaymentSuccess(true);
      } else {
        toast.error(result.error)
      }
      setPurchaseIsLoading(false)
      closeNumsModal();
      closeInvalidInputs();
      closeConfirmAssignModal();
      setTicketPurchased()

    } else {
      toast.error("Please add tickets before proceeding to purchase.");
      setPurchaseIsLoading(false);
    }
  }

  const getPlayerTickets = () => {
    setUpcomingTicketsIsLoading(true)
    setShowUpcomingTickets(true)
    setShowPaymentSuccess(false)
    getPlayerUpcomingTickets().then((tickets) => {
      console.log("Upcoming Tickets:", tickets);
      setUpcomingTickets(tickets);
      setUpcomingTicketsIsLoading(false)
    }).catch((error) => {
      console.error("Error fetching upcoming tickets:", error);
      setUpcomingTicketsIsLoading(false)
    });
  }


  return (
    <div className="h-full w-full md:w-[50%] lg:w-[35%] mx-[5%] max-md:mt-20">
      <div className="py-5 space-y-2">
        <div className="flex space-x-2">
          <img src={ticketsYellow} alt="" className="w-10 h-10 my-auto" />
          <p className="my-auto text-2xl font-bold">Crypto Jackpot Tickets</p>
        </div>
        <p className="px-2.5 text-BP-nav-gray">Review your selections</p>
      </div>

      <p className="border-b border-white/30 pb-2 text-lg">Your tickets</p>

      {showUpcomingTickets && (upcomingTicketsIsLoading ? <div className="mt-5 items-center"><ConcentricArcLoader size={60} /></div> : upcomingTickets.map((ticket, index) => {
        return (
          <div key={index} className="bg-[#1f2937]/60 rounded-2xl p-6 my-3">
            <div className="flex items-center justify-between text-gray-400 mb-4 w-full">
              <div className="flex items-center gap-2 text-gray-400">
                <img
                  src={calendardis}
                  alt="Prize"
                  className="w-4 h-4 sm:w-5 sm:h-5"
                />
                {/* <span className="text-sm">{lastSunday}</span> */}
              </div>
              <span className="w-5 h-5 rounded-full border border-yellow-400 flex items-center justify-center">
                <span className="text-yellow-400 text-xs">i</span>
              </span>
            </div>
            <div className="flex gap-6">
              {ticket.numbers.map((number, idx) => {
                return (
                  <div
                    key={idx}
                    className="w-10 h-10 flex items-center justify-center text-white"
                  >
                    {number.toNumber()}
                  </div>
                )
              })}
            </div>
          </div>
        )

      }))}
      {showUpcomingTickets && !upcomingTicketsIsLoading && <button onClick={closeTktPay} className="w-full bg-BP-yellow hover:bg-BP-hovered-yellow hover:scale-95 transition-all duration-300 ease-out text-black font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg flex items-center justify-center gap-2 mt-10 mx-auto">
        Buy more
      </button>}

      {!showUpcomingTickets && lotteryTktPay.map((ticket, index) => (
        <div key={index} className="relative bg-[#1F2937] rounded-3xl border border-white/30 p-5 my-5">

          {/* <button
                  onClick={() => {}}
                  className="absolute -left-3 -top-3 text-white text-lg"
              >
                  <MdEdit
                      color="#ffffff"
                      className="w-8 h-8 bg-BP-gray-50-opc hover:bg-[#ffffff60] p-1.5 rounded-full"
                  />
              </button>
              <button
                  onClick={() => {}}
                  className="absolute -right-3 -top-3 text-white text-lg"
              >
                  <IoClose
                      color="#ffffff"
                      className="w-8 h-8 bg-BP-gray-50-opc hover:bg-[#ffffff60] p-1.5 rounded-full"
                  />
              </button> */}

          <div className="flex justify-between text-sm">
            <p className="text-BP-hovered-gray">Ticket #{ticket.ticket}</p>
            <p className="text-BP-gold">1 USD</p>
          </div>

          <div className="flex justify-between space-x-1.5 mt-3">
            {ticket.ticketNumber.map((num, i) => (
              <div
                key={i}
                className="text-BP-opacited-white text-lg font-bold bg-[#384152] w-fit p-2 rounded-3xl"
              >
                {num}
              </div>
            ))}
          </div>
        </div>
      ))}

      {!showUpcomingTickets &&
        <div className="relative bg-[#1F2937] rounded-3xl border border-white/30 p-5 my-5 space-y-3">
          <div className="flex justify-between text-base">
            <p className="text-white/70">Tickets</p>
            <p>
              <span className="text-white">{lotteryTktPay.length}</span>
              <span className="text-white/70"> x </span>
              <span className="text-BP-gold">1 SHLN</span>
            </p>
          </div>

          <div className="border-t border-white/20 my-2"></div>

          <div className="flex justify-between text-lg font-semibold">
            <p className="text-white">Total</p>
            <p className="text-BP-gold">{(lotteryTktPay.length).toFixed(1)} SHLN</p>
          </div>

          <p className="text-white/40 text-sm text-right">
            ({lotteryTktPay.length} SHLN)
          </p>
        </div>
      }
      {
        !showUpcomingTickets && <div>
          <p className="text-BP-yellow">You will make two transaction: First to approve payment and second to purchase tickets</p>

          {!currentUserWallet ? (
            <button onClick={async () => { await connectWallet() }} className="w-full bg-BP-yellow hover:bg-BP-hovered-yellow hover:scale-95 transition-all duration-300 ease-out text-black font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg flex items-center justify-center gap-2 mt-10 mx-auto">
              <FaWallet size={20} />
              Connect wallet to pay {lotteryTktPay.length} SHLN
            </button>
          ) : (
            <button ref={confirmPurchaseRef} disabled={purchaseIsLoading} onClick={buyTickets} className="w-full bg-gradient-to-tr from-[#2C5282] to-[#58759D] hover:scale-95 transition-all duration-300 ease-out text-BP-opacited-white font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg mt-10 mx-auto">
              {purchaseIsLoading ? <Spin size="large" color="white" /> : `Confirm payment of ${lotteryTktPay.length} SHLN`}
            </button>)
          }
        </div>
      }

      {currentUserWallet && <p className="text-center text-BP-opacited-white my-5">Wallet connected: {formatWalletAddress(currentUserWallet)}</p>
      }
      <div className="h-10" />

      {showPaymentSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center" style={{ zIndex: 110 }} >
          <div className="bg-[#1A2238] text-white max-sm:mx-2 p-6 rounded-2xl max-w-sm w-full shadow-lg relative border border-BP-gold-gradient-end border-opacity-50">
            <div className="flex justify-center">
              <IoIosCheckmarkCircle size={48} color="#4FD1C5" />
            </div>
            <h2 className="text-2xl font-semibold text-[#E9B308] text-center mt-4">Payment Successful!</h2>
            <p className="text-center mt-2">You've purchased <strong>{lotteryTktPay.length} tickets</strong> for <strong>{lotteryTktPay.length} SHLN</strong></p>
            <hr className="my-4 border-gray-600" />
            {/* <p className="text-center mb-3">Create an account to:</p>
            <ul className="text-sm space-y-2">
              <li className="flex items-center gap-2">
                <IoIosCheckmarkCircle size={20} color="#4FD1C5" />
                Track your tickets
              </li>
              <li className="flex items-center gap-2">
                <IoIosCheckmarkCircle size={20} color="#4FD1C5" />
                Get winning notifications
              </li>
              <li className="flex items-center gap-2">
                <IoIosCheckmarkCircle size={20} color="#4FD1C5" />
                Save payment methods
              </li>
            </ul> */}
            <button onClick={getPlayerTickets} className="mt-6 w-full bg-[#E9B308] text-black font-semibold py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-yellow-400 transition">
              <span className="text-3xl"><FaWallet /></span>
              View my tickets
            </button>
            {/* <p onClick={() => setShowPaymentSuccess(false)} className="text-center text-sm text-gray-400 mt-3 cursor-pointer hover:underline">Maybe later</p> */}
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-white"
              onClick={getPlayerTickets}
            >
              ✕
            </button>
          </div>
        </div>

      )}

    </div>
  );
};
