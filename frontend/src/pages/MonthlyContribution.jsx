import { useState, useEffect } from 'react';
import WebLogo from '../assets/newBPlogo.png';
import donation from '../assets/images/donation2.png';
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import { AppRoutesPaths } from '../route/app_route';
import { useAuthentication } from '../components/utils/provider';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

import SuccessAlert from "../pages/modals/successModal/success";
import FailedAlert from "../pages/modals/failed";

import { motion } from 'framer-motion';
import { FiDollarSign, FiCreditCard, FiCalendar, FiRepeat, FiLink } from 'react-icons/fi';
import { ContributionService } from '../services/ContributionService';
import { Modal, Spin, Tooltip } from 'antd';
import { FaInfo, FaInfoCircle } from 'react-icons/fa';
import ErrorModal from './Presale/errorModal';
import { sahelionAddress } from '../constants/constants';
import { FaRegCopy, FaCheck } from "react-icons/fa";

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// Payment Form Component
const PaymentForm = ({ userId, isPaymentProcessing, amount, onContributionActivated, subscriptionId, onError }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  useEffect(() => {
    isPaymentProcessing(processing);
  }, [processing]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setProcessing(true);
    setErrorMessage(null);

    if (!stripe || !elements) {
      setProcessing(false);
      return;
    }

    // STEP 1: Create a PaymentMethod from card details
    const { error: pmError, paymentMethod } = await stripe.createPaymentMethod({
      type: 'card',
      card: elements.getElement(CardElement),
    });

    if (pmError) {
      setErrorMessage(pmError.message);
      setProcessing(false);
      onError && onError(pmError);
      return;
    }
    // STEP 2: Send PaymentMethod ID, userId, and amount to backend to create payment intent and get clientSecret
    let intentData;
    const intentRes = await ContributionService.generatePaymentIntent({
      userId,
      amount,
      paymentMethodId: paymentMethod.id,
      action:'activate'
    });
    intentData = intentRes.data;
    if(intentRes.success === false){

      setErrorMessage(intentRes.message);
      setProcessing(false);
      onError && onError(intentRes);
      return;
      }
      

    // STEP 3: Use clientSecret to confirm the payment with Stripe.js
    const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(intentData.clientSecret);
    if (confirmError) {
      setErrorMessage(confirmError.message);
      setProcessing(false);
      onError && onError(confirmError);
      return;
    }
    if (!paymentIntent || paymentIntent.status !== 'succeeded') {
      setErrorMessage('Payment not successful!');
      setProcessing(false);
      return;
    }

    // STEP 4: Call backend to activate the contribution (after payment is confirmed)
    
      if(intentData?.clientSecret){
      const activateRes = await ContributionService.activateContribution( {
        userId,
        amount,
        currency: 'USD',
        paymentMethod: 'stripe',
        subscriptionId: intentData.subscriptionId,
        paymentIntentId:paymentIntent?.id
      }); 
      if(activateRes.success){
        onContributionActivated && onContributionActivated(activateRes.data);
      }
      }else{
        setErrorMessage(intentData.message);
        setProcessing(false);
        onError && onError(intentData);
        return;
      }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-4">
      <div className="relative my-8 bg-yellow-500 bg-opacity-20 p-3 rounded-md">
        <CardElement />
        {errorMessage && (
          <div className="text-red-500 text-sm mt-2">{errorMessage}</div>
        )}
      </div>
    
      
      <button
        type="submit"
        disabled={!stripe || processing}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
          processing
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-purple-600 hover:bg-purple-700 text-white'
          }`}
      >
        {processing ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            Processing...
          </div>
        ) : (
          'Pay Now'
        )}
      </button>
    </form>
  );
};


export const MonthlyContribution = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState('fiat');
  const [amountOption, setAmountOption] = useState('10');
  const [customAmount, setCustomAmount] = useState('');
  const [convertedAmount, setConvertedAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [clientSecret, setClientSecret] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [subscriptionId, setSubscriptionId] = useState(null);
  const { currentUser, currentUserWallet, connectWallet, disconnectWallet, bPnthrBalance,
    transferSHLNToCommunityWalletAddressForContribution
  } = useAuthentication();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentReference, setPaymentReference] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState(null); // 'success' or 'failed'
  const [todayDate, setTodayDate] = useState('');
  const [today, setToday] = useState('');
  const [showSuccessCard, setShowSuccessCard] = useState(false);
  const [paymentMethodId, setPaymentMethodId] = useState(null)
  const [amountError, setAmountError] = useState('');
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorDetail, setErrorDetail] = useState('');
  const [copied, setCopied] = useState(false);
  const [isConfiguringAccount, setIsConfiguringAccount] = useState(false);

  // Check URL parameters on component mount
  useEffect(() => {
    const status = searchParams.get('status');
    if (status === 'success' && !showSuccessCard) {
      const storedSetupIntent = searchParams.get('setup_intent') || localStorage.getItem('stripe_payment_ref');
      const paramSubscriptionId = searchParams.get('subscriptionId');
      const paramAmount = searchParams.get('amount');


      if (paymentMethod === 'fiat' && storedSetupIntent && currentUser?._id) {
        // Update amount states from URL if available
        if (paramAmount) {
          setCustomAmount(paramAmount);
          setAmountOption(paramAmount);
          // console.log('MonthlyContribution - Updated amount from URL:', paramAmount);
        }
        
        setSubscriptionId(paramSubscriptionId);
        setPaymentReference(storedSetupIntent);
        activateContribution('stripe', paramSubscriptionId, storedSetupIntent);
      }
    } else if (status === 'failed' && !isModalOpen) {
      const error = searchParams.get('error');
      setErrorMessage(error || 'Payment failed. Please try again.');
      setModalType('failed');
      setIsModalOpen(true);
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [searchParams, paymentMethod, currentUser, showSuccessCard]);

  useEffect(() => {
    if (amountOption === 'custom') {
      setConvertedAmount(customAmount || '0');
    } else {
      setConvertedAmount(amountOption);
    }
  }, [customAmount, amountOption]);

  useEffect(() => {
    function getOrdinalDate() {
      const date = new Date();
      const day = date.getDate();

      const getOrdinal = (n) => {
        const s = ["th", "st", "nd", "rd"];
        const v = n % 100;
        return n + (s[(v - 20) % 10] || s[v] || s[0]);
      };

      setTodayDate(date)
      return getOrdinal(day);
    }

    setToday(getOrdinalDate());
  }, []);

  useEffect(() => {
    // Check if the user has a connected wallet
    if (currentUserWallet) {
      setIsWalletConnected(true);
      setWalletAddress(currentUserWallet);
    } else {
      setIsWalletConnected(false); 
      setWalletAddress('');
    }
    setIsLoading(false);

  }, [currentUserWallet]);

  const handleCopy = () => {
    navigator.clipboard.writeText(sahelionAddress);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000); // Reset after 2s
  };

  const oncloseSuccess = () => {
    setIsLoading(false);
    setIsModalOpen(false);
    setModalType(null);
    setSuccessMessage('');
    navigate(AppRoutesPaths.dashboard?.root);
  };

  const oncloseFailed = () => {
    setIsLoading(false);
    setIsModalOpen(false);
    setModalType(null);
    setErrorMessage('');
  };

  const activateContribution = async (method, subscriptionId = null, _ref = null) => {
    if (showSuccessCard) return;
    setIsLoading(true)
    try {
      // Get amount from URL params first, then state, then localStorage as fallback
      const urlAmount = searchParams.get('amount');
      const storedAmount = localStorage.getItem('monthly_contribution_amount');
      let finalAmount = amountOption === 'custom' ? customAmount : amountOption;
      
      if (urlAmount) {
        finalAmount = urlAmount;
      } else if (storedAmount && (!finalAmount || finalAmount === '10')) {
        finalAmount = storedAmount;
      }
      
      
      const contributionData = {
        userId: currentUser._id,
        amount: finalAmount,
        currency: method === 'stripe' ? 'USD' : 'SHLN',
        paymentMethod: method,
        setupIntentId: _ref,
        subscriptionId: subscriptionId
      };

      const result = await ContributionService.activateContribution(contributionData);

      if (!result.success) {
        throw new Error(result.message || 'Failed to activate contribution');
      }

      // Clear stored amount after successful activation
      localStorage.removeItem('stripe_payment_ref');
      localStorage.removeItem('monthly_contribution_amount');
      setShowSuccessCard(true);
      setClientSecret(null);

  //     const newUrl = window.location.pathname;
  //     window.history.replaceState({}, '', newUrl);

      return result.data;
    } catch (error) {
      console.error('Activation error:', error);
      setModalType('failed');
      setIsModalOpen(true);
      setErrorMessage(error.message || 'Failed to activate contribution');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (isSubmitting || isLoading) return;

    if (amountOption < 5) {
      setErrorMessage('Minimum contribution amount is $5');
      setModalType('failed');
      setIsModalOpen(true);
      return;
    }

    setIsSubmitting(true);
    setIsLoading(true);

    try {
      if (paymentMethod === 'fiat') {
      
        // const response = await httpClient.post('/contribution/generate-intent', {
        //   amount:  convertedAmount ,
        //   userId: currentUser?._id,
        //   paymentMethodId: paymentMethodId

        // }, {
        //   headers: {
        //     'Content-Type': 'application/json',
        //     'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
        //   },
        // });

        // const data = await response.data;

        // if (data.clientSecret) {
        //   setClientSecret(data.clientSecret);
        //   setSubscriptionId(data.subscriptionId);
        // } else {
        //   throw new Error(data.message || 'Failed to generate payment intent');
        // }
      } else {
        if (!isWalletConnected) {
          setErrorMessage('Please connect your wallet first');
          setModalType('failed');
          setIsModalOpen(true);
          return;
        }

        const result = await transferSHLNToCommunityWalletAddressForContribution("******************************************", amountOption);

        if (result.success) {
          setIsLoading(false);
          setIsConfiguringAccount(true);


          const verificationData = {
            userId: currentUser?._id,
            transactionHash: result.transactionHash,
            from: walletAddress,
            to: "******************************************",
            amount: amountOption,
            tokenContract: sahelionAddress
          };

          const response = await ContributionService.verifyCryptoPayment(verificationData);

          if (!response.success) {
            throw new Error(response.message || 'Crypto payment verification failed');
        }

          setIsConfiguringAccount(false);
          setSuccessMessage(response.message || 'Payment successful! Your contribution has been activated.');
        setModalType('success');
        setIsModalOpen(true);

        } else {
          setIsLoading(false)
          setErrorDetail(result.error)
          setShowErrorModal(true)
        }

      }
      // setIsSubmitting(false);

    } catch (error) {
      console.error('Payment error:', error);
      const errorMsg = error.message || 'Payment processing failed. Please try again.';
      setErrorMessage(errorMsg);
      setModalType('failed');
      setIsModalOpen(true);
      // setIsSubmitting(false);

    } finally {
      setIsLoading(false);
      setIsSubmitting(false);
      setIsConfiguringAccount(false);
    }
  };

  const handlePaymentSuccess = (paymentRef) => {
    setPaymentReference(paymentRef);
    // setModalType('success');
    setShowSuccessCard(true)
    setIsModalOpen(true);
    setIsLoading(false);
  };

  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    const errorMsg = error.message || 'Payment failed. Please try again.';
    setErrorMessage(errorMsg);
    setModalType('failed');
    setIsModalOpen(true);
    setIsLoading(false);
  };

  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };
  return (

    <div className="min-h-screen flex-col items-center justify-center bg-[#F8F5ED] md:p-[5%] space-y-10 text-BP-black">
      {showErrorModal && (
        < ErrorModal
          onClose={() => setShowErrorModal(false)}
          errorDetails={errorDetail}
        />
      )}

      <Modal
        open={isConfiguringAccount}
        footer={null}
        closable={false}
        centered
      >
        <div className="text-center space-y-4 py-6">
          <Spin size="large" />
          <p className="text-lg font-semibold">Hold on while we verify and configure your account...</p>
        </div>
      </Modal>

      <div className="flex justify-center mb-6 bg-white w-fit mx-auto p-5 rounded-xl shadow-xl border-2">
        <Link to="/">
          <img src={WebLogo} alt="Logo" className="h-10" />
        </Link>

      </div>

      <div className="bg-white rounded-2xl shadow-md md:p-[3vw] p-[4vw] w-[96%] md:w-[45vw] text-center mx-auto space-y-6 border">
        {showSuccessCard ? (
          <div className="flex flex-col items-center justify-center py-8 space-y-6">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <IoMdCheckmarkCircleOutline className="w-12 h-12 text-green-600" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">Payment Successful!</h3>
            <p className="text-gray-600">Your monthly contribution has been set up successfully.</p>
            <div className="w-full max-w-md bg-gray-50 rounded-lg p-6 space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-medium">${amountOption === 'custom' ? customAmount : amountOption}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Method:</span>
                <span className="font-medium">{paymentMethod === 'fiat' ? 'Cash (USD)' : 'Crypto (SHLN)'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Next Payment:</span>
                <span className="font-medium">{today} of every month</span>
              </div>
            </div>
            <button
              onClick={() => {
                setShowSuccessCard(false);
                navigate(AppRoutesPaths.dashboard?.root || '/');
              }}
              className="mt-4 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Return to Dashboard
            </button>
          </div>
        ) : (
          <>
            <div className="text-2xl font-semibold mb-2 flex items-center gap-2">
              <img src={donation} alt="" className="w-10 h-10" />
              <h3 className="font-title text-[6.6vw] md:text-[3vw] font-semibold borde">
                Monthly Contribution
              </h3>
            </div>

            {/* Payment Method Selection */}
            <div className=''>
              <h2 className="text-lg font-normal text-[#4B5563] flex items-center font-body">
                <FiCreditCard className="mr-2 text-[#4B5563]" />
                Payment Method
              </h2>
              <div className="py-3 flex flex-row justify-between md:w-[75%] w-full">
                <motion.button
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={() => setPaymentMethod('fiat')}
                  className={`py-3 px-4 rounded-xl border flex justify-center items-center ${paymentMethod === 'fiat' ? 'border-[#9747FF] bg-indigo-50' : 'border-gray-200 hover:border-gray-300'}`}
                >
                  <FiDollarSign className={`w-5 h-5 ${paymentMethod === "fiat" ? 'text-[#9747FF]' : "text-[#4B5563]"}`} />
                  <span className={`text-[#4B5563] ${paymentMethod === "fiat" ? 'text-[#9747FF]' : "text-[#4B5563]"}`}>Cash(USD)</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={() => setPaymentMethod('crypto')}
                  className={`py-3 px-4 rounded-xl border flex justify-center items-center ${paymentMethod === 'crypto' ? 'border-[#9747FF] bg-indigo-50' : 'border-gray-200 hover:border-gray-300'}`}
                >
                  <FiDollarSign className={`w-5 h-5 ${paymentMethod === "crypto" ? 'text-[#9747FF]' : "text-[#4B5563]"}`} />
                  <span className={`text-[#4B5563] ${paymentMethod === "crypto" ? 'text-[#9747FF]' : "text-[#4B5563]"}`}>Crypto(SHLN)</span>
                </motion.button>
              </div>

              {paymentMethod === 'crypto' && (<>
                <div className="flex items-center justify-between mt-4">
                  <h2 className="text-sm font-normal text-[#4B5563] flex items-center font-body">
                    <FiRepeat className="mr-2 text-[#4B5563]" />
                    SHLN Contract Address
                  </h2>
                  <p className="text-sm text-[#4B5563] font-body">{sahelionAddress.slice(0, 10) + "..." + sahelionAddress.slice(sahelionAddress.length - 10)}</p>
                  <Tooltip title="Click to copy SHLN contract address" placement="top">
                    <button
                      onClick={() => handleCopy()}
                      className="text-sm text-blue-500 hover:underline"
                    >
                      {copied ? <FaCheck className="w-5 h-5 text-BP-hovered-purple" /> : <FaRegCopy className="w-5 h-5 text-BP-hovered-purple" />}
                    </button>
                  </Tooltip>
                </div>
                <div className='flex flex-col items-start'>
                  <a href={`${window.location.origin}` + "/dashboard-sahelion/portfolio"} target="_blank" rel="noopener noreferrer">
                    <p className="text-sm text-[#4B5563] font-body mt-2 underline cursor-pointer hover:text-BP-purple">
                      Click to Buy SHLN
                    </p>
                  </a>
                </div>
              </>)}

              {(paymentMethod === "crypto" && !isWalletConnected) ? (
                <button
                  onClick={() => handleConnectWallet()}
                  className='w-full md:h-[4vw] h-[12vw] mt-4 md:rounded-[1vw] rounded-[3vw] bg-[#9747FF] text-white font-body text-[1.2rem] shadow-[0px_10px_15px_rgba(0,0,0,0.1)] hover:bg-[#7A2FDB]'
                >
                  Connect wallet
                </button>
              ) : ((paymentMethod === "crypto" && isWalletConnected) ? (
                <button onClick={() => handleConnectWallet()} className='w-full h-[4vw] mt-4 rounded-[1vw] bg-[#4FD1C5]/1 font-body border border-[#4FD1C5] bg-[#4FD1C5]/10 flex items-center text-[#4B5563] text-[0.875rem]'>
                  <FiLink className="h-5 w-5 text-[#4FD1C5] ml-[3%]" />
                  <p className='ml-[3%]'>
                    Connected: {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)} (Click to disconnect)
                  </p>
                </button>
              ) : null)}
            </div>

            {/* Payment Form or Amount Selection */}
            
              <>
                {/* Amount Selection */}
                <div className="">
                  <p className="text-[#4B5563] text-start font-body font-normal">Choose your contribution</p>
                  <div className='flex flex-row justify-between mt-4'>
                    {['5', '10', '20'].map((option) => (
                      <motion.button
                        key={option}
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                        type="button"
                        onClick={() => {
                          setAmountOption(option);
                          setCustomAmount(option); // Also set the custom amount so it shows in the input
                        }}
                        className={`py-4 px-[2] md:w-[11vw] w-[28vw] md:rounded-[1.2vw] rounded-[3vw] border-2 transition-all bg-[#505050]/10 ${amountOption === option ? 'border-[#F5C21A] bg-[#F5C21A]' : 'border-gray-200 hover:border-gray-300'}`}
                      >
                        <div className="flex flex-col items-center">
                          <span className={`text-xl font-bold ${amountOption === option ? 'text-white' : 'text-gray-700'}`}>
                            {paymentMethod === 'fiat' ? `$${option}` : `${option} SHLN`}
                          </span>
                          <span className="text-xs text-gray-500 mt-1">
                            {paymentMethod === 'fiat' ? `${option} SHLN` : `$${option}`}
                          </span>
                        </div>
                      </motion.button>
                    ))}
                  </div>

                  {/* Custom Amount Input */}
                  <div>
                      <h2 className="text-lg  mt-4 font-normal text-[#4B5563] flex items-center font-body text-[1rem]">
                        Custom input
                      </h2>
                      <input
                        onChange={(e) => {
                          const value = e.target.value.replace(/[^0-9.]/g, '');
                          const decimalCount = value.split('.').length - 1;
                          if (decimalCount <= 1) {
                          const numValue = parseFloat(value);
                          if (value === '' || isNaN(numValue)) {
                            setAmountOption(value);
                            setAmountError('');
                          } else if (numValue < 5) {
                            setAmountOption(value);
                            setAmountError('Minimum amount is $5');
                          } else {
                            setAmountOption(value);
                            setAmountError('');
                          }
                          }
                        }}
                        type="text"
                        inputMode="decimal"
                        className="border border-[#E5E7EB] w-full md:h-[4vw] h-[15vw] md:rounded-[1vw] rounded-[3vw] text-center font-body font-light bg-[#505050]/10 focus:outline focus:outline-purple-400 focus:outline-1"
                        placeholder='Enter custom amount'
                        value={amountOption}
                      />
                    {amountError && (
                      <p className="text-red-500 text-sm mt-1 text-left">{amountError}</p>
                    )}
                    </div>
                </div>

                {/* Monthly Contribution Section */}
                <div>
                  <h2 className="text-lg font-normal text-[#4B5563] flex items-center font-body">
                    <FiCalendar className="mr-2 text-[#4B5563]" />
                    Date of contribution
                  </h2>
                  <div className="md:mt-4 mt-5 md:h-[5vw] h-[20vw] md:rounded-[1.2vw] rounded-[3vw] bg-[#F9FAFB] flex items-center">
                    <p className="text-[#4B5563] font-body font-light md:text-[0.875rem] text-[0.84rem] md:ml-[5%] ml-[2%]">Next Payment:</p>
                    <p className="text-BP-purple ml-[4%] font-body md:text-[0.9rem] text-[0.875rem]">{today} of every month</p>
                  </div>

                  {(amountOption && amountOption !== "0") && (
                    <div className="md:mt-4 mt-5 md:h-[10vw] h-[30vw] rounded-[1.2vw] bg-[#F9FAFB] flex flex-col justify-center md:gap-[2vw] gap-[5vw]">
                      <div className="flex w-[90%] ml-[5%]">
                        <p className="text-[#4B5563] font-body font-light text-[0.875rem]">You'll contribute:</p>
                        <p className="ml-[6%] font-body text-[0.9rem] font-medium">
                          {`${(amountOption === 'custom' ? customAmount : amountOption)} ${paymentMethod === "fiat" ? "Cash" : "Crypto"}`}
                        </p>
                      </div>
                      <div className="flex w-[90%] ml-[5%]">
                        <p className="text-[#4B5563] font-body font-light text-[0.875rem]">Payment method:</p>
                        <p className="ml-[4%] font-body text-[0.9rem] font-medium">{paymentMethod === "fiat" ? "Cash" : "Crypto"}</p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-4">
                  {isWalletConnected === false ? (<>
                    <p className='text-sm text-BP-gold'>Inorder to be able to contribute, you need to be a holder of Black Panther Tokens.
                      Please connect your wallet containing your BPNTHRQ Tokens</p>
                  </>) : (<>
                    <p className='text-sm text-BP-gold'>BPNTHRQ Balance: ${bPnthrBalance}</p>
                  </>)}
                  {isWalletConnected === false ? (<>
                  
                    <button onClick={() => handleConnectWallet()} className='w-full md:h-[4vw] h-[12vw] mt-4 md:rounded-[1vw] rounded-[3vw] bg-[#9747FF] text-white font-body text-[1.2rem] shadow-[0px_10px_15px_rgba(0,0,0,0.1)] hover:bg-[#7A2FDB]'>
                      Connect wallet
                    </button>
                  </>) : (<>
                    <button onClick={() => handleConnectWallet()} className='w-full h-[4vw] mt-4 rounded-[1vw] bg-[#4FD1C5]/1 font-body border border-[#4FD1C5] bg-[#4FD1C5]/10 flex items-center text-[#4B5563] text-[0.875rem]'>

                      <FiLink className="h-5 w-5 text-[#4FD1C5] ml-[3%]" />

                      <p className='ml-[3%]'>
                        Connected: {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)} (Click to disconnect)
                      </p>
                    </button>
                  </>)}
                </div>

                {/* Submit Button */}
                {isWalletConnected && (
                  <div className="relative w-full group">
                    {bPnthrBalance > 0 ? (paymentMethod === 'fiat' ?(
                  <Elements stripe={stripePromise}>
                  <PaymentForm
                    userId={currentUser?._id}
                    amount={convertedAmount}
                    onContributionActivated={handlePaymentSuccess}
                    onError={handlePaymentError}
                    isPaymentProcessing={setIsSubmitting}
                  />
                  </Elements>):
                      <button
                        onClick={handleSubmit}
                        disabled={paymentMethod === 'crypto' && !isWalletConnected || isLoading || isSubmitting}
                        className={`
                      ${paymentMethod === 'crypto' && !isWalletConnected || isLoading || isSubmitting
                            ? 'bg-gray-300 cursor-not-allowed'
                            : 'bg-purple-400 hover:bg-purple-500 cursor-pointer'
                          } 
                        text-white w-full py-3 rounded-full font-medium flex justify-center items-center gap-2 
                        shadow-lg transition-all duration-200 relative overflow-hidden
                        ${isLoading || isSubmitting ? 'cursor-wait' : ''}
                        `}
                      >
                        {(isLoading || isSubmitting) && (
                          <div className="absolute inset-0 bg-purple-500 opacity-70 flex items-center justify-center">
                            <svg
                              className="animate-spin h-5 w-5 text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          </div>
                        )}
                        <span className={`flex items-center gap-2 ${isLoading || isSubmitting ? 'opacity-0' : 'opacity-100'}`}>
                          {isSubmitting ? 'Processing...' : 'Proceed'}
                          <IoMdCheckmarkCircleOutline className='text-white w-5 h-5' />
                        </span>
                      </button>
                      )
                     : (<>
                      <Tooltip title="You need to hold BPNTHRQ tokens to contribute. Click to buy Black Panther Tokens" placement="top">
                        <a
                          href="https://www.pinksale.finance/launchpad/bsc/0x513B589006846Ea16FD2Bc0AB1B6413545e28D54"
                          target="_blank"
                          rel="noopener noreferrer"
                          className='flex items-center justify-center w-full md:h-[4vw] h-[12vw] mt-4 md:rounded-[1vw] rounded-[3vw] bg-[#9747FF] text-white hover:text-white font-body text-[1.2rem] shadow-[0px_10px_15px_rgba(0,0,0,0.1)] hover:bg-[#7A2FDB]'
                        >
                          <FaInfoCircle className="h-5 w-5 text-BP-opacited-white mx-[3%]" />
                          Buy BPNTHRQ
                        </a>
                      </Tooltip>
                    </>)}

                    {/* Enhanced responsive tooltip */}
                    {paymentMethod === 'crypto' && !isWalletConnected && (
                      <div className="
                    absolute -top-2 left-1/2 transform -translate-x-1/2 -translate-y-full
                    bg-[#F3F2F0] text-[#4B5563] text-xs sm:text-sm px-3 py-2 rounded-lg
                    opacity-0 group-hover:opacity-100 transition-opacity duration-300
                    shadow-lg z-20 max-w-xs w-full text-center
                    before:content-[''] before:absolute before:top-full before:left-1/2 
                    before:-translate-x-1/2 before:border-8 before:border-transparent 
                    before:border-t-[#F3F2F0]
                    hover:opacity-100
                    ">
                        <span className="font-medium">Wallet Required</span>
                        <p className="mt-1 font-body">You need to connect your wallet address first to continue paying with crypto</p>
                        <div className="absolute bottom-0 left-0 right-0 mx-auto w-20 h-0.5 bg-purple-300 rounded-full animate-pulse"></div>
                      </div>
                    )}
                  </div>
                )}
               
              </>
         
          </>
        )}
      </div>

      {isModalOpen && modalType === 'failed' && (
        <FailedAlert
          message={errorMessage || "Payment processing failed. Please try again."}
          onClose={oncloseFailed}
        />
      )}
      {isModalOpen && modalType === 'success' && (
        <SuccessAlert
          message="Your monthly contribution has been successfully activated!"
          onClose={oncloseSuccess}
        />
      )}
    </div>
  );
}; 
