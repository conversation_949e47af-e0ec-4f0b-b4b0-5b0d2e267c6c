import React from 'react'
import HeaderNew from '../components/Header/HeaderNew'
import donation from "../assets/presale/donation.png"
import  Transaction  from '../assets/presale/transaction.png'
import web3 from "../assets/presale/web3.png";
import money from "../assets/presale/gold.png";
import cybersecurity from "../assets/presale/cybersecurity.png";
import dataserver from "../assets/presale/dataserver.png";
import ticket from "../assets/presale/ticket.png";
import dao from "../assets/presale/dao.png";
import nft from "../assets//presale/nft2.png";
import console from "../assets/presale/console.png";
import mask from "../assets/presale/mask.png";
import iage from "../assets/presale/iage.jpg"
import  Footer  from '../components/Footer/footer';
import bpcoin from '../assets/game/BPCOIN.png'

export const About = () => {
 return (
   <div className='min-h-screen bg-[#111828]'>
    <div className='absolute top-0  left-0 w-full'>
      <HeaderNew />
    </div>
       <div className='text-white flex flex-col items-center justify-center h-screen py-16     px-6' style={{
          backgroundImage: `url(${iage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
       >
            <h2 className='text-4xl sm:text-5xl md:text-6xl font-bold mb-8 text-center'>About Black Panther</h2>
           <p className='sm:max-w-xl   text-justify md:max-w-3xl text-lg leading-relaxed'>
               It's a practical tool in the digital realm offering real world utility. The native token of the Black Panther Cryptosphere, it's a potent symbol of change, a harbinger of a new era in finance across the African continent, presenting a number of Web3 tools & products.
           </p>
       </div>
       <div className="grid grid-cols-1 md:grid-cols-2 gap-8 px-6 pb-16 max-w-6xl mx-auto">
         {features.map((feature, index) => (
           <div
             key={index}
              className="border border-white/30 bg-gradient-to-b from-[#111828] from-[50%] to-[#72519F99] p-8 rounded-3xl text-white shadow-lg h-[400px] sm:h-[320px] w-full flex flex-col"
           >
              <div
        className={`w-12 h-12 flex items-center justify-center mr-6 mb-3 flex-shrink-0 ${
          index > 1 ? 'bg-gray-700 rounded-full' : ''
        }`}
      >
               <img src={feature.icon} className='' />
             </div>
             <div className="flex flex-col">
               <h3 className="text-xl font-semibold mb-3 text-left text-gray-300">
                 {feature.title}
               </h3>
               <p className="text-gray-300 text-base leading-relaxed text-left">
                 {feature.description}
               </p>
             </div>
           </div>
         ))}
       </div>


       <div className="text-white py-16 px-6">
       <div className="max-w-7xl mx-auto">
         <div className="text-center mb-16">
           <h2 className="text-4xl md:text-5xl font-bold mb-6">Our other services and Products</h2>
         </div>


         {/* Services grid */}
         <div className="grid grid-cols-1 lg:grid-cols-1 gap-8 max-w-3xl mx-auto">
           {otherServices.map((service, index) => (
             <div
               key={index}
               className="bg-gray-800 p-8 rounded-xl space-y-4 "
             >
               <div className="w-12 h-12   rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                 <img src={service.icon}/>
               </div>
               <h3 className={`text-2xl font-bold   ${service.title.includes('$') ? 'text-BP-yellow' : 'text-white'}`}>
                 {service.title}
               </h3>
               <p className="text-gray-300 leading-relaxed">
                 {service.description}
               </p>
             </div>
           ))}
         </div>
       </div>
     </div>
     <Footer/>




   </div>
 )
}




const features = [
  {


    icon: bpcoin,
    title: '$BPNTHRQ Token',
    description: 'The central utility token within the Cryptosphere, used in-game transactions, NFT minting and trading, DAO governance, charity contributions, and lottery ticket purchases.'
  },
  {
    icon: money,
    title: 'Sahelion Stable Coin',
    description: 'Akin to Tether (USDT), BUSD, or USDC, offering a stable coin alternative that is authentically African.'
  },
 {
  icon: ticket,
  title: "Global Lottery",
  description: "A provably fair and transparent lottery system where users can purchase tickets using $BPNTHRQ tokens for a chance to win prizes"
},
{
  icon: donation,
  title: 'Charity Wallet',
  description: 'Funds health education, awareness, treatment, & research for diseases affecting African people globally, such as sickle cell anemia & supports education in Web3/blockchain & crypto financial literacy.'
},
{
  icon: console,
  title: 'Play-to-Earn Game',
  description: 'An engaging game with a Black Panther theme where players can earn $BPNTHRQ tokens through gameplay. The game features characters with unique abilities, upgrade paths, and is accessible across multiple platforms.'
},
{
  icon: Transaction,
  title: 'Transactions Token',
  description: 'Designed to slash fees on mobile money transactions.'
},
];




const otherServices = [
  {
    icon: dao,
    title: 'DAO Governance',
    description: 'A Decentralized Autonomous Organization that allows $BPNTHRQ token holders to vote on proposals related to charity allocations and Web3 project investments.'
  },
  {
    icon: nft,
    title: 'NFTs',
    description: 'Unique Black Panther-themed NFTs that can be collected and traded. NFTs are tied to in-game achievements and character progression.'
  },
  {
    icon: cybersecurity,
    title: 'Secure Crypto Wallet',
    description: 'We will deploy a crypto wallet with multi-currency support & optional offline storage!'
  },
  {
    icon: web3,
    title: 'Web3 Texting & Money Transfer App',
    description: 'A texting app that uses phone numbers will be developed, doubling as a Web3 money transfer application.'
  },
  {
    icon: dataserver,
    title: 'Black Panther Blockchain/Protocol',
    description: 'The Black Panther Protocol is envisioned as a future blockchain infrastructure designed to support and expand African Decentralized Finance (DeFi).'
  }


];