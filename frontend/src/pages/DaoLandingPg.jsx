import React from 'react'
import HeaderNew from '../components/Header/HeaderNew'
import Footer from '../components/Footer/footer'
import newBPlogo from "../assets/newBPlogo.png"
import daoWavyLines from "../assets/Dao/daoWavyLines.svg"
import blockchainEducatorsLogo from "../assets/Dao/Blockchain Educators Logo Design.png";
import nftCreatorsGuildLogo from "../assets/Dao/NFT Creators Guild Logo Design.png";
import web3InnovatorsLogo from "../assets/Dao/Web3 Innovators Logo Design.png";
import handHeart from "../assets/Dao/handHeart.png";
import group from "../assets/Dao/group.svg"
import unLock from "../assets/Dao/unLock.svg"
import { BiSearch } from "react-icons/bi";
import { HiOutlineUserGroup } from "react-icons/hi2";
import { AppRoutesPaths } from '../route/app_route';
import { useNavigate } from 'react-router-dom';

export const DaoLandingPg = () => {

    const navigate = useNavigate();

    const allCommunitiesInfo = [
      {
        id: 1,
        title: 'DeFi Pioneers',
        description: 'Exploring decentralized finance innovations',
        members: 100,
        tag: 'Finance',
        image: null,
        status: 'Active',
        access: 'public',
      },
      {
        id: 2,
        title: 'NFT Creators Guild',
        description: 'For creators and collectors of unique digital assets',
        members: 156,
        tag: 'art',
        image: nftCreatorsGuildLogo,
        status: 'Active',
        access: 'public',
      },
      {
        id: 3,
        title: 'Web3 Innovators',
        description: 'Building the future of decentralized applications',
        members: 120,
        tag: 'technology',
        image: web3InnovatorsLogo,
        status: 'Active',
        access: 'public',
      },
      {
        id: 4,
        title: 'Blockchain Educators',
        description: 'Teaching the next generation of web3 developers',
        members: 100,
        tag: 'Finance',
        image: blockchainEducatorsLogo,
        status: 'Active',
        access: 'private',
      },
      {
        id: 5,
        title: 'DAO Governance',
        description: 'Sharing best practices for decentralized governance',
        members: 156,
        tag: 'art',
        image: null,
        status: 'Active',
        access: 'public',
      },
      {
        id: 6,
        title: 'Web3 Innovators',
        description: 'Building the future of decentralized applications',
        members: 120,
        tag: 'technology',
        image: null,
        status: 'Active',
        access: 'private',
      },
    ]

  return (
    <div className='bg-BP-dark-grayish-blue min-h-[100vh] relative'
        style={{}}>
        {/* Wavy lines background at top right only */}
        <img src={daoWavyLines} alt="wavy lines" className="absolute top-0 right-0 w-[600px] max-w-[60vw] pointer-events-none select-none z-0" style={{height: 'auto'}} />

        <div className='absolute top-0  left-0 w-full'>
            <HeaderNew />
        </div>

        <div className="p-[10%] pt-28">

            <div className="">
                
                <p className="text-[7vw] md:text-[5vw] text-white/40 font-bold">
                    Discover Black Panther <br /> DAO Communities
                </p>
                
                <p className="text-BP-hovered-gray md:text-xl my-10">
                    Looking to start your own DAO <br /> community?
                </p>

                <button onClick={()=>{navigate(AppRoutesPaths.createCommunity)}} className="border border-BP-gold rounded-lg px-3 py-2 text-BP-gold bg-white/5 hover:bg-BP-gold hover:text-BP-opacited-white">Create new community</button>

                <div className="border border-white/20 my-20 p-5 rounded-2xl bg-[#1F2937] flex flex-col md:flex-row items-center md:space-x-10 space-y-5 md:space-y-0">

                  <div className="h-36 w-36 flex-shrink-0 relative mx-auto md:mx-0 bg-gradient-to-b from-[#E3EAFB] to-[#B6B9C4] rounded-2xl">
                    <div className="bg-white p-1 rounded-full w-fit h-fit shadow-xl absolute top-2 left-2">
                      <img src={newBPlogo} alt="" className="w-5 h-5" />
                    </div>
                    <img src={handHeart} alt="" className="h-36 w-36 object-cover rounded-2xl" />
                    <div className="bg-[#1F2937] w-fit absolute bottom-0 rounded-[50px_10px_0_0] text-xs py-1 pl-4 pr-2 text-BP-gold">
                      Featured
                      <div className="rounded-bl-lg shadow-[-5px_10px_5px_1px_#1F2937] w-5 h-5 absolute -right-5 bottom-0" />
                    </div>
                  </div>

                  <div className="w-full space-y-5">
                    <p className="text-2xl">Black Panther DAO</p>
                    <p className="text-sm">Building the future of decentralized applications</p>
                    <div className="w-full flex flex-col md:flex-row md:space-x-7 space-y-3 md:space-y-0">
                      <div className="flex space-x-3">
                        <img src={group} alt="" className="mt-2.5 w-5 h-5" />
                        <span className="text-BP-hovered-gray text-nowrap my-auto">100 members</span>
                      </div>
                      <div className="flex space-x-3">
                        <img src={unLock} alt="" className="my-auto w-5 h-5" />
                        <span className="text-BP-hovered-gray text-nowrap my-auto">Open access</span>
                      </div>
                      <button onClick={()=>{navigate(AppRoutesPaths.bpDao)}} className="my-auto border border-BP-gold rounded-full px-5 py-1 text-BP-dark-grayish-blue hover:text-BP-gold hover:bg-white/5 bg-BP-gold">View community</button>
                    </div>
                  </div>

                </div>

            </div>

            <div className="space-y-5">

                <p className="font-semibold text-4xl">Discover DAO Communities</p>
                <p className="text-BP-hovered-gray">Join communities that align with your interests and contribute to governance</p>

                <div className="md:flex w-full max-md:space-y-5 md:space-x-5">
                  {/* Search Bar */}
                  <div className="relative flex-1">
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-BP-nav-gray">
                      <BiSearch className="w-5 h-5" />
                    </span>
                    <input
                      type="text"
                      placeholder="Search communities"
                      className="bg-[#232B3A] text-white placeholder:text-white/40 rounded-lg pl-12 pr-4 py-2 w-full focus:outline-none border border-transparent focus:border-white/30 transition"
                    />
                  </div>
                  {/* Access Type Dropdown */}
                  <div className="w-56 relative">
                    <select
                      className="bg-[#232B3A] text-white rounded-lg px-6 py-2 w-full focus:outline-none border border-transparent focus:border-white/30 transition"
                      defaultValue="all"
                      style={{ appearance: 'auto', WebkitAppearance: 'auto', MozAppearance: 'auto' }}
                    >
                      <option value="all">All access types</option>
                      <option value="open">Open communities</option>
                      <option value="approval">Approval required</option>
                    </select>
                  </div>
                  {/* Sort Dropdown */}
                  <div className="w-56 relative">
                    <select
                      className="bg-[#232B3A] text-white rounded-lg px-6 py-2 w-full focus:outline-none border border-transparent focus:border-white/30 transition"
                      defaultValue="popular"
                      style={{ appearance: 'auto', WebkitAppearance: 'auto', MozAppearance: 'auto' }}
                    >
                      <option value="popular">Most popular</option>
                      <option value="active">Most Active</option>
                      <option value="newest">Newest</option>
                    </select>
                  </div>
                </div>

                {/* DAO Community Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8 justify-items-center">
                  {allCommunitiesInfo.map((community) => (
                    <div key={community.id} className="bg-gradient-to-b from-[#7E828200] to-[#7E82821A] border border-white/15 rounded-2xl p-6 w-full max-w-xs sm:max-w-md md:max-w-md lg:max-w-xs xl:max-w-xs flex-shrink-0 shadow-lg relative flex flex-col justify-between">
                      {/* Top: Image and Status */}
                      <div className="flex items-center justify-between mb-4">
                        {community.image ? (
                          <img src={community.image} alt="community" className="rounded-xl w-12 h-12 object-cover bg-[#E3EAFB]" />
                        ) : (
                          <div className="rounded-xl w-12 h-12 bg-[#E3EAFB] flex items-center justify-center font-bold text-BP-black text-lg select-none">
                            {community.title.split(" ").map(word => word[0]?.toUpperCase() || '').join(' ')}
                          </div>
                        )}
                        <span className="flex items-center gap-2 bg-[#4FD1C530] px-3 py-1 rounded-full text-xs font-semibold text-[#4FD1C5]">
                          <span className="inline-block w-3 h-3 rounded-full bg-[#4FD1C5] mr-1"></span>
                          {community.status}
                        </span>
                      </div>
                      {/* Title */}
                      <div className="font-semibold text-xl text-white mb-1">{community.title}</div>
                      {/* Description */}
                      <div className="text-BP-hovered-gray text-sm mb-4">{community.description}</div>
                      {/* Members and Tag */}
                      <div className="flex items-center justify-between gap-3 mb-4">
                        <span className="flex items-center gap-1 text-BP-hovered-gray text-sm">
                          <HiOutlineUserGroup className="w-6 h-6 text-BP-gold" />
                          {community.members} Members
                        </span>
                        <span className="bg-BP-dark-grayish-blue text-white text-xs px-3 py-1 rounded-full capitalize">{community.tag}</span>
                      </div>
                      {/* Join Button */}
                      {community.access === 'public' ? (
                        <button className="w-full bg-BP-gold hover:bg-BP-hovered-yellow text-BP-black font-semibold py-2 rounded-lg mt-auto transition">Join community</button>
                      ) : (
                        <button className="w-full border border-BP-gold bg-white/5 hover:bg-BP-gold hover:text-BP-dark-grayish-blue text-BP-gold font-semibold py-2 rounded-lg mt-auto transition">Request to join</button>
                      )}
                    </div>
                  ))}
                </div>

            </div>

        </div>

        <Footer />

    </div>
  )
}
