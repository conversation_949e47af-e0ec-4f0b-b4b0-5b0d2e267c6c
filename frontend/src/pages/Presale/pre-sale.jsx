import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import HeaderNew from '../../components/Header/HeaderNew';
import bg from "../../assets/presale/Desktop - 10.png";
import wallet from "../../assets/presale/wallet.png";
import logoutimage from "../../assets/presale/sign-out-option.png";
import star from "../../assets/presale/star.png";
import '../Presale.css';
import DepositModal from '../modal';
import { WalletModal } from './walletModal';
import { AuthService } from '../../services/authService'; // Ensure AuthService is imported
import cart from "../../assets/presale/grocery-store.png";
import { useAuthentication } from '../../components/utils/provider';
import { WalletService } from '../../services/walletService';
import { bpnthrToken } from '../../constants/constants';
import axios from 'axios';
import { AppRoutesPaths } from '../../route/app_route';
import { ConvertFromUSDToBNB } from '../../components/utils/convertFromUSDToBNB';
import { Spin } from 'antd';
import Footer from '../../components/Footer/footer';
import ErrorModal from './errorModal';

export const Presale = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false)
    const navigate = useNavigate();
    const [usdInputValue, setUsdInputValue] = useState('');
    const [bpnthrInputValue, setBpnthrInputValue] = useState('');
    const [bnbValue, setBNBValue] = useState('')
    const [price, setPrice] = useState(0.0001)
    const [loadingUSD, setLoadingUSD] = useState(false)
    const [loadingPanther, setLoadingPanther] = useState(false)
    const [isHovered, setIsHovered] = useState(false);
    const [showErrorModal, setShowErrorModal] = useState(false);
    const [errorDetail, setErrorDetail] = useState('');

    const { ensureLogin, currentUser, onLogout, getCurrentUser, setPointsToSave,
        PointsToSave, onSavePointsLocally, connectWallet, currentUserWallet,
        disconnectWallet, bPnthrBalance, BuyTokenFromPresale } = useAuthentication()

    const contractAddress = bpnthrToken || "Please contact Admin";

    // useEffect(() => {
    //     ensureLogin();
    // }, []);

    const handleWalletUpdate = async (newWalletAddress) => {
        if (!newWalletAddress?.trim()) {
            toast.error('Please enter a valid wallet address');
            return;
        }

        try {
            if (!currentUser?.walletAddress) {
                let payload = {
                    walletAddress: newWalletAddress
                }
                const result = await WalletService.AddWalletAddress(payload, getCurrentUser);
                if (result) {
                    setIsWalletModalOpen(false);
                } else {

                }
            } else {
                let payload = {
                    newWalletAddress: newWalletAddress,
                    currentWalletAddress: currentUser.walletAddress
                }

                const result = await WalletService.UpdateWalletAddress(payload, getCurrentUser);
                if (result) {
                    setIsWalletModalOpen(false);
                } else {

                }
            }
        } catch (error) {
            console.error('Error updating wallet:', error);
        }
    };

    const handleLogout = async () => {
        try {
            onLogout();
            window.location.href = "/login"
            toast.success('Logged out successfully');
        } catch (error) {
            console.error('Error logging out:', error);
            toast.error('Failed to logout');
        }
    };

    const onSetUSDInput = async (val) => {
        if (val < 0) return;
        setPointsToSave(0);
        onSavePointsLocally(0);
        setUsdInputValue(val);
        setLoadingPanther(true);
        setTimeout(async () => {
            const bnthr = await calculateBNTHRToGet(val);
            setLoadingPanther(true)
            const bnb = await ConvertFromUSDToBNB(val)
            setBNBValue(bnb.toFixed(8));
            setBpnthrInputValue(bnthr.toFixed(8));
            setPointsToSave(bnthr.toFixed(8));
            onSavePointsLocally(bnthr.toFixed(8));
            setLoadingPanther(false)

        }, 500);

    };

    const onSetBpnthr = async (val) => {
        if (val < 0) return;
        setPointsToSave(0);
        onSavePointsLocally(0);
        setBpnthrInputValue(val);
        setPointsToSave(val);
        onSavePointsLocally(val);
        setLoadingUSD(true);
        setTimeout(async () => {
            const usd = await calculateUSDToPay(val);
            setLoadingUSD(true)
            const bnb = await ConvertFromUSDToBNB(usd)
            setBNBValue(bnb.toFixed(8))
            setUsdInputValue(usd.toFixed(8));
            setLoadingUSD(false)
        }, 500);
    };

    const calculateBNTHRToGet = async (val) => {
        return val / (price);
    };

    const calculateUSDToPay = async (val) => {
        return val * (price);
    };

    // Uncomment this for Radom

    // const handleProceedToBuy = () => {
    //     // Ensure the user is authenticated
    //     if (!currentUser) {
    //         toast.error("Please you are currently not signed in.")
    //         return;
    //     }

    //     setIsLoading(true)

    //     const options = {
    //         method: 'POST',
    //         url: 'https://api.radom.com/product/create',
    //         headers: {
    //             'Content-Type': 'application/json',
    //             Authorization: import.meta.env.VITE_TOKEN_KEY
    //         },
    //         data: {
    //             currency: 'USD',
    //             description: 'Black panther token, meme coin with purpose',
    //             name: `You are going to recieve ${PointsToSave} BPNTHRQ Points`,
    //             price: usdInputValue,
    //             addOns: [],
    //             image: null,
    //             sendSubscriptionEmails: true,
    //             productType: {
    //                 Presale: {
    //                     Token: {
    //                         ticker: "BPNTHRQ",
    //                         decimals: 18,
    //                         totalRaiseAmount: 10000000
    //                     }
    //                 }
    //             }
    //         }
    //     };

    //     axios.request(options).then(function (response) {
    //         console.log(response.data);
    //         const options = {
    //             method: 'POST',
    //             url: 'https://api.radom.com/checkout_session',
    //             headers: {
    //                 'Content-Type': 'application/json',
    //                 Authorization: import.meta.env.VITE_TOKEN_KEY
    //             },
    //             data: {
    //                 lineItems: [
    //                     {
    //                         productId: response.data.id,
    //                         itemData: {
    //                             name: 'Black panther Token',
    //                             description: 'Black panther token, meme coin with purpose',
    //                             chargingIntervalSeconds: 0,
    //                             price: usdInputValue,
    //                             imageUrl: 'string',
    //                             isMetered: false,
    //                             currency: 'USD',
    //                             sendSubscriptionEmails: true
    //                         }
    //                     }
    //                 ],
    //                 currency: 'USD',
    //                 gateway: {
    //                     managed: {
    //                         methods: [
    //                             { network: 'SepoliaTestnet', token: null, discountPercentOff: 0 }, // show this for testing
    //                             { network: 'BNB', token: null, discountPercentOff: 0 },
    //                             { network: 'Ethereum', token: null, discountPercentOff: 0 },
    //                             { network: 'Base', token: null, discountPercentOff: 0 },
    //                             { network: 'BNB', token: "******************************************", discountPercentOff: 0 },
    //                             { network: 'Ethereum', token: "******************************************", discountPercentOff: 0 },
    //                             { network: 'Base', token: "******************************************", discountPercentOff: 0 },
    //                             { network: 'BNB', token: "******************************************", discountPercentOff: 0 },
    //                             { network: 'Ethereum', token: "******************************************", discountPercentOff: 0 },
    //                             { network: 'BNB', token: "******************************************", discountPercentOff: 0 }
    //                         ]
    //                     }
    //                 },
    //                 successUrl: import.meta.env.VITE_SUCCESS_URL,
    //                 cancelUrl: import.meta.env.VITE_CANCEL_URL,
    //                 metadata: [{ key: 'string', value: 'string' }],
    //                 expiresAt: getFutureTimestamp(1), // Set expiration time to 1 minute from now
    //                 customizations: {
    //                     leftPanelColor: '#F8F5ED',
    //                     primaryButtonColor: 'blue',
    //                     slantedEdge: true,
    //                     allowDiscountCodes: false
    //                 },
    //                 chargeCustomerNetworkFee: true
    //             }
    //         };

    //         axios.request(options).then(function (response) {
    //             console.log(response.data);
    //             setIsLoading(false)
    //             window.location.href = response.data.checkoutSessionUrl;
    //         }).catch(function (error) {
    //             setPointsToSave(0)
    //             localStorage.removeItem("Bpnthr_pt_sv");
    //             console.error(error);
    //         });
    //     }).catch(function (error) {
    //         setPointsToSave(0)
    //         localStorage.removeItem("Bpnthr_pt_sv");
    //         console.error(error);
    //     });
    // };

    const handleProceedToBuy = async () => {
        if (usdInputValue === "") {
            toast.error("Please enter USD value")
            return
        }

        if (bpnthrInputValue === "") {
            toast.error("Please enter BPNTHRQ value")
            return
        }

        if (bnbValue === "") {
            toast.error("Failed to convert usd to bnb")
            return
        }
        setIsLoading(true)
        const result = await BuyTokenFromPresale(bnbValue)
        if (result.success) {
            toast.success("Bought successfully")
            setBpnthrInputValue("")
            setUsdInputValue("")
            setIsLoading(false)
        } else {
            // toast.error("Failed to buy BPNTHRQ")
            setIsLoading(false)
            setErrorDetail(result.error)
            setShowErrorModal(true)
        }
    }

// Function to get a future Unix timestamp in seconds
const getFutureTimestamp = (minutes) => {
    const now = Math.floor(Date.now() / 1000); // Current time in seconds
    return now + (minutes * 60); // Add the specified number of minutes
};

const onConnectWalletClick = async () => {
    if (currentUserWallet) {
        await disconnectWallet()
    } else {
        await connectWallet()
    }
}

return (
    <>
        <div className="min-h-screen bg-[#111828] flex flex-col items-center justify-center text-white  bg-center bg-cover">
            <HeaderNew />
            {showErrorModal && (
                < ErrorModal
                    onClose={() => setShowErrorModal(false)}
                    errorDetails={errorDetail}
                />
            )}
            <div className="max-w-md w-full mt-12 mb-10">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center sm:text-left">
                    Buy and manage your tokens with ease.
                </h2>

                <div className="mb-4 mt-3 flex flex-col items-center justify-between md:flex-row">
                    {/* Wallet info - appears first on desktop, second on mobile */}
                    <div className="flex items-center gap-2 order-2 md:order-none">
                        <img
                            src={wallet}
                            alt="Wallet"
                            className="bg-yellow-500 text-black px-4 py-2 h-10 rounded-full text-sm font-semibold"
                        />
                        <p className="text-gray-300">
                            {isLoading ? (
                                "Loading..."
                            ) : (
                                currentUserWallet ?
                                    `${currentUserWallet.substring(0, 6)}...${currentUserWallet.substring(currentUserWallet.length - 4)}`
                                    : "No wallet connected"
                            )}
                        </p>
                    </div>

                    {/* Connect button - appears second on desktop, first on mobile */}
                    <button
                        onClick={() => onConnectWalletClick()}
                        disabled={isLoading}
                        className="bg-BP-yellow hover:bg-BP-hovered-yellow  px-4 py-2 rounded-full text-sm font-semibold transition-colors disabled:opacity-50 w-[14rem] md:w-auto order-1 md:order-none mb-2 md:mb-0"
                    >
                        {isLoading ? 'Loading...' : (currentUserWallet ? 'Disconnect' : 'Connect Wallet')}
                    </button>
                </div>

                <div>
                    <p className="text-gray-400 text-sm text-center mb-4">
                        • Current Price of 1 BPNTHRQ is 0.0001 USD <br />
                    </p>
                </div>

                <div className="mt-6 bg-[#302f29] p-6 rounded-xl shadow-lg">
                    <div className="mb-4 text-center">
                        <p className='text-purple-600 font-bold'>
                            Please ensure your wallet is set to ‘BNB Smart Chain’ & you have BNB in it
                        </p>
                    </div>
                    {/* USD Input with Spinner */}
                    <div className="relative w-full">
                        <input
                            type="number"
                            placeholder="USD"
                            value={usdInputValue}
                            onChange={(e) => onSetUSDInput(e.target.value)}
                            className="w-full p-3 rounded-full bg-[#9a9994] text-black placeholder-black text-center leading-tight border border-gray-600 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                            disabled={loadingUSD} // Optional
                        />
                        {loadingUSD && (
                            <div className="absolute inset-0 flex items-center justify-center bg-[#9a9994]/60 rounded-full">
                                <Spin size="small" />
                            </div>
                        )}
                    </div>

                    {/* BPNTHRQ Input with Spinner */}
                    <div className="relative w-full mt-3">
                        <input
                            type="number"
                            placeholder="$ BPNTHRQ"
                            value={bpnthrInputValue}
                            onChange={(e) => onSetBpnthr(e.target.value)}
                            className="w-full p-3 rounded-full bg-[#9a9994] text-black placeholder-black text-center leading-tight border border-gray-600 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                            disabled={loadingPanther} // Optional
                        />
                        {loadingPanther && (
                            <div className="absolute inset-0 flex items-center justify-center bg-[#9a9994]/60 rounded-full">
                                <Spin size="small" />
                            </div>
                        )}
                    </div>

                    {bnbValue !== "" && (
                        <div className="my-3 text-center">
                            <p className="text-sm text-gray-400">You will pay {bnbValue} BNB</p>
                        </div>
                    )}

                    {currentUserWallet ? (<>
                        <button
                            className="mt-4 bg-BP-yellow hover:bg-BP-hovered-yellow  px-6 py-3 w-full rounded-full text-white flex items-center justify-center gap-2 font-semibold button-hover"
                            onClick={handleProceedToBuy}
                            disabled={isLoading}
                        >
                            <img src={cart} alt="Buy" className="w-5 h-5 icon-move" />
                            <span className="text-move">
                                {isLoading ? 'Loading...' : "Proceed To Buy"}
                            </span>
                        </button>
                    </>) : (<>
                        {/* <button
                            className="mt-4 bg-yellow-500 hover:bg-yellow-600 px-6 py-3 w-full rounded-full text-white flex items-center justify-center gap-2 font-semibold button-hover"
                            onClick={() => setIsWalletModalOpen(true)}
                            disabled={isLoading}
                        >
                            <span className="">
                                {isLoading ? 'Loading...' : "Add wallet to buy"}
                            </span>
                        </button> */}
                        <button
                            className="mt-4 bg-BP-yellow hover:bg-BP-hovered-yellow  px-6 py-3 w-full rounded-full text-white flex items-center justify-center gap-2 font-semibold button-hover"
                            onClick={() => onConnectWalletClick()}
                            disabled={isLoading}
                            onMouseEnter={() => setIsHovered(true)}
                            onMouseLeave={() => setIsHovered(false)}
                        >
                            <span className="">
                                {isLoading ? 'Loading...' : (isHovered ? "Connect your wallet to buy" : "Proceed to buy")}
                            </span>
                        </button>
                    </>)}

                    <div className='my-4'>
                        <p className='text-sm text-gray-400'>NOTE: BNB fluctuates, the number of BPNTHRQ can be slightly greater or less than the specified amount.</p>
                    </div>
                </div>

                <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="p-4 rounded-xl text-center bg-[#302f29]">
                        {/* <p className="text-sm text-gray-400">Your Points</p> */}
                        <p className="text-sm text-gray-400">Your BPNTHRQ</p>
                        {/* <p className="text-lg font-bold">{currentUser?.points} Points</p> */}
                        {currentUserWallet ? (<>
                            <p className="text-lg font-bold">{bPnthrBalance} BPNTHRQ</p>
                        </>) : (<>
                            <p className='text-sm text-gray-400'>Connect your wallet to view your token balance</p>
                        </>)}
                        <div className="flex justify-center sm:justify-start">
                            <button onClick={() => navigate(AppRoutesPaths.mypoints)} className="mt-10 bg-purple-600 hover:bg-purple-700 px-4 py-2 flex flex-row rounded-full text-white font-semibold button-hover-star">
                                {/* Manage points */}
                                Manage Tokens
                                <img src={star} alt="Star" className="w-5 h-5 ml-2 star-rotate" />
                            </button>
                        </div>
                    </div>

                    <div className="p-4 rounded-xl text-center bg-[#302f29]">
                        <p className="text-sm text-gray-400">Contract Information</p>
                        <p className="text-sm mt-1">
                            Copy the contract address below to import the BPNTHRQ ticker into your wallet.
                        </p>
                        <div
                            className="mt-2 bg-[#727069] p-2 rounded-lg text-gray-300 text-sm cursor-pointer hover:bg-[#828079] transition-colors"
                            onClick={() => setIsModalOpen(true)}
                        >
                            <>
                                {contractAddress.substring(0, 6)} ... {contractAddress.substring(contractAddress.length - 4)}
                            </>

                        </div>
                    </div>
                </div>

                {currentUser && (
                    <button
                        onClick={handleLogout}
                        className="mt-6 bg-red-600 hover:bg-red-700 px-6 py-3 w-full sm:w-1/2 mx-auto rounded-full text-white flex items-center justify-center font-semibold button-hover-logout"
                    >
                        Logout
                        <img src={logoutimage} alt="Logout" className="w-5 h-5 ml-3 logout-icon" />
                    </button>
                )}
            </div>

            {isWalletModalOpen && (
                <WalletModal
                    currentWallet={currentUser?.walletAddress}
                    onSave={handleWalletUpdate}
                    onClose={() => setIsWalletModalOpen(false)}
                />
            )}

            {isModalOpen && (
                <DepositModal
                    walletAddress={contractAddress}
                    onClose={() => setIsModalOpen(false)}
                />
            )}
        </div>

        <Footer />
    </>
);
};