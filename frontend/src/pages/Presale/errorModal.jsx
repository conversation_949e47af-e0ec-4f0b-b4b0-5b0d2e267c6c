import { useState } from "react";

export default function ErrorModal({ onClose, errorDetails }) {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-BP-dark-grayish-blue max-w-md w-full rounded-lg shadow-lg p-6 border-l-4 border-red-500">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold text-red-500">Failed to buy BPNTHRQ</h2>
            <p className="text-BP-hovered-gray mt-2">
              An error occurred while processing your transaction.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-700 hover:bg-BP-hovered-gray rounded-full px-1.5 transition ml-4"
          >
            ✕
          </button>
        </div>

        <button
          onClick={() => setShowDetails(!showDetails)}
          className="mt-4 text-sm text-BP-gold hover:underline focus:outline-none"
        >
          {showDetails ? "Hide Details" : "See Details"}
        </button>

        {showDetails && (
          <div className="mt-3 bg-BP-gray-50-opc p-3 rounded text-sm text-BP-hovered-gray">
            {errorDetails || "No additional information available."}
          </div>
        )}
      </div>
    </div>
  );
}
