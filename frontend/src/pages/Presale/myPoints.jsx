import {
  <PERSON>Outlined,
  <PERSON>Outlined,
  MessageOutlined,
  UserOutlined,
  TeamOutlined,
  GiftOutlined
} from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { BsTwitterX } from "react-icons/bs";
import { FaLinkedin, FaDiscord, FaTelegram, FaTiktok, FaInstagram, FaFacebook, FaYoutube, FaArrowLeft } from "react-icons/fa";
import HeaderNew from "../../components/Header/HeaderNew";
import vector1 from "../../assets/presale/Vector.svg";
import vector2 from "../../assets/presale/Vector.svg";
import announce from "../../assets/presale/announce.jpg";
import { useAuthentication } from '../../components/utils/provider';
import httpClient from '../../components/httpClient/httpClient';
import { AppRoutesPaths } from '../../route/app_route';
import Footer from '../../components/Footer/footer';

const MyPoints = () => {
  const { currentUser, ensureLogin, PointsToSave, getCurrentUser, setPointsToSave, bPnthrBalance } = useAuthentication()
  const navigate = useNavigate()

  useEffect(() => {
    // const checkingForLogin = async () => {
    //   await ensureLogin()
    // }

    // checkingForLogin()

    if (currentUser) {
      if (PointsToSave > 0) {
        // savePointsToDbAsunc()  // Use this when using Radom
      }
    }

  }, [currentUser?._id])

  const savePointsToDbAsunc = async () => {
    let info = {
      points: PointsToSave
    }
    const response = await httpClient.put("user/update_points", info)
    if (response?.status === 201) {
      localStorage.removeItem("Bpnthr_pt_sv");
      setPointsToSave(0)
      await getCurrentUser()
    } else {
      console.log("Something went wrong, contact Administration")
    }
  }


  return (
    <div className="min-h-screen bg-[#111828] flex flex-col items-center text-white p-6 sm:p-8 md:p-10 lg:p-0">
      <HeaderNew />
      <div className="w-full max-w-4xl flex flex-col items-center mb-10">
        <button
          className="flex items-center gap-2 text-BP-gold hover:text-BP-opacited-white px-4 py-2 rounded-md self-start"
          onClick={() => navigate(AppRoutesPaths.presale)} 
        >
          <FaArrowLeft /> Back
        </button>
        <div className="flex flex-col items-center md:flex-row gap-8 w-full mt-11">
          <PointsCard currentUser={currentUser} bPnthrBalance={bPnthrBalance} />
          <CountdownCard />
        </div>

        <EarnPointsCard />
      </div>
      <Footer />
    </div>
  );
};

const PointsCard = ({ currentUser, bPnthrBalance }) => {
  const stars = [
    { top: "23%", left: "47%" },
    { top: "18%", left: "70%" },
    { top: "35%", right: "25%" },
    { top: "35%", left: "15%" },
    { top: "40%", left: "30%" },
    { top: "47%", right: "27%" },
    { top: "45%", right: "12%" },
    { bottom: "15%", left: "20%" },
    { bottom: "33%", right: "55%" },
    { bottom: "25%", right: "25%" },

    // Center area
    { top: "50%", left: "50%", transform: "translate(-50%, -50%)" }
  ];
  return (
    <div className="bg-gradient-to-tr from-[#111827] to-[#272d39] p-6 md:p-8 rounded-[40px] text-center relative w-full max-w-[487px] h-[504px] overflow-hidden border border-white border-opacity-20">
      {stars.map((star, i) => (
        <div
          key={i}
          className="absolute w-1 h-1 bg-white opacity-50 rounded-full"
          style={{
            ...star,
            animation: "move 5s infinite linear, glow 1.5s infinite alternate",
            boxShadow: "0px 0px 10px 2px #ffffff",
          }}
        />
      ))}
      <h2 className="text-2xl mt-15 font-semibold text-left text-white">My Tokens</h2>
      <div className="absolute inset-0 top-[23%] left-[15%] h-1/2 w-3/4"
        style={{
          backgroundImage: `linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px)`,
          backgroundSize: "68px 42px",
        }}

      />

      <div
        className="absolute border-t-4 border-dashed border-[#363c47] w-[480px] h-[400px] top-[47%] left-1/2 transform -translate-x-1/2 pointer-events-none rounded-full scale-y-110">
      </div>
      <div className="relative z-10 h-full flex flex-col items-center justify-center py-8">
        <div className="w-24 h-24 md:w-32 md:h-32 bg-[#272d3a] rounded-full flex items-center justify-center mb-6 md:mb-8 shadow-[0_0_20px_rgba(83,88,97,0.5)] border border-white border-opacity-10">
          <GiftOutlined className="text-4xl md:text-5xl text-BP-gold" />
        </div>

        <button className=" p-4 max-w-xs mx-auto rounded-full border-l-[#535861] shadow-[4px_-4px_10px_#535861]">
          {/* <div className="text-2xl md:text-5xl font-bold text-white">{currentUser?.points}</div> */}
          <div className="text-2xl md:text-3xl font-bold text-white">{bPnthrBalance}</div>
        </button>
      </div>
    </div>
  )
};


const CountdownCard = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [targetDate, setTargetDate] = useState(new Date("2025-06-01T00:00:00Z"));
  const [nextDrawDate, setNextDrawDate] = useState("");

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date();
      let difference = targetDate - now;

      if (difference <= 0) {
        const newTargetDate = new Date(targetDate);
        newTargetDate.setDate(newTargetDate.getDate() + 5);
        newTargetDate.setHours(0, 0, 0, 0);
        setTargetDate(newTargetDate);
        difference = newTargetDate - now;
      }

      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference / (1000 * 60 * 60)) % 24);
      const minutes = Math.floor((difference / (1000 * 60)) % 60);
      const seconds = Math.floor((difference / 1000) % 60);

      return { days, hours, minutes, seconds };
    };

    const calculateNextDrawDate = () => {
      return targetDate.toLocaleString("en-US", {
        timeZone: "UTC",
        weekday: "short",
        day: "numeric",
        month: "short",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    };

    const updateCountdown = () => {
      setTimeLeft(calculateTimeLeft());
      setNextDrawDate(calculateNextDrawDate());
    };

    updateCountdown(); // Initial call
    const timer = setInterval(updateCountdown, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  return (
    <div className="bg-gradient-to-bl from-[#111827] to-[#272d39] p-6 md:p-8 rounded-[40px] text-center relative w-full max-w-[487px] h-[504px] overflow-hidden border border-white border-opacity-20">
      <div className="absolute w-[599px] h-[599px] rounded-full border border-white border-opacity-10 top-[65%] left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute w-[380px] h-[380px] rounded-full border border-white border-opacity-10 top-[60%] left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute w-[200px] h-[200px] flex rounded-full border border-white border-opacity-10 top-[60%] left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className='flex flex-row items-center justify-between ml-[3.8rem]'>
          <img src={vector1} alt="" className='mr-2' style={{ transform: "scaleX(-1)" }} />
          <img src={vector2} className='ml-4' style={{ transform: "scaleX(1)" }} />
        </div>
      </div>

      <div className="relative z-10 h-full flex flex-col justify-between items-center py-8">
        <div className="text-left mr-11">
          <h2 className="text-sm md:text-lg font-semibold text-white">
          Upon Deployment on (DeX) Pancake Swap, Presale Tokens can be traded for other assets subject to vesting period rules
          </h2>
          <p className="text-BP-gold mt-4 md:mt-6 text-base md:text-lg">Presale Ends</p>
          <p className="text-xs md:text-sm mt-2 text-gray-400">{nextDrawDate}</p>
        </div>
        <div className="flex justify-center mt-[2.1rem] items-baseline gap-3 md:gap-8 md:mt-[1.9rem]">
          <div className='bg-[#343a45] px-4 rounded-3xl py-1'>
            <span className="text-2xl md:text-3xl font-bold text-[#858990]">{timeLeft.days}d</span>
          </div>
          <div className='bg-[#343a45] px-4 py-1 rounded-3xl animate-border-gradient border-2 border-[#32322b]'>
            <span className="text-2xl md:text-3xl font-bold text-[#858990]">{timeLeft.hours}h</span>
          </div>
          <div className='bg-[#343a45] px-4 py-1 rounded-3xl'>
            <span className="text-2xl md:text-3xl font-bold text-[#858990]">{timeLeft.minutes}m</span>
          </div>
        </div>

        <div className="mt-12 md:mt-16 flex flex-row items-center">
          <span className="text-4xl md:text-5xl font-bold text-[#858990]">{timeLeft.seconds}</span>
          <span className="text-xs md:text-sm font-normal text-[#858990]">sec</span>
        </div>
      </div>
    </div>
  );
};


const EarnPointsCard = () => (
  <div className="bg-gray-800 p-6 md:p-8 rounded-[40px] w-full sm:max-w-lg md:max-w-4xl overflow-hidden border border-white border-opacity-20 mt-6 md:mt-8">
    <h2 className="text-xl md:text-2xl font-semibold text-white mb-6 md:mb-8 text-center">Earn More Tokens</h2>

    <ul className="space-y-4 md:space-y-6 text-left px-4 md:px-8">
      {[
        { icon: <MessageOutlined />, text: "Talk about $BPNTHRQ on social media." },
        { icon: <TeamOutlined />, text: "Share with family and friends." },
        { icon: <LinkOutlined />, text: "Follow us on social media" }
      ].map((item, index) => (
        <li key={index} className="flex items-center space-x-3 md:space-x-4">
          <span className="text-yellow-400 text-xl md:text-2xl min-w-[28px] md:min-w-[32px]">
            {item.icon}
          </span>
          <p className="text-base md:text-lg text-white">
            {item.text.includes("$BPNTHRQ") ? (
              <>
                Talk about <span className="font-bold text-[#e1e2e4]">$BPNTHRQ</span> on social media.
              </>
            ) : (
              item.text
            )}
          </p>
        </li>
      ))}
    </ul>

    <div className="flex flex-wrap justify-center gap-4 md:gap-6 mt-6 md:mt-8 mb-2">
      <a
        href="https://t.me/blackpanthertkn"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaTelegram className="text-3xl md:text-4xl" />
      </a>
      <a
        href="https://x.com/bpnthrx"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <BsTwitterX className="text-3xl md:text-4xl" />
      </a>
      <a
        href="https://discord.com/invite/wVA7dqGDu2"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaDiscord className="text-3xl md:text-4xl" />
      </a>
      <a
        href="https://www.tiktok.com/@blackpanthertkn"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaTiktok className="text-3xl md:text-4xl" />
      </a>
      <a
        href="https://www.facebook.com/profile.php?id=100063660784177"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
        >
          <FaFacebook className="text-3xl md:text-4xl" />
        </a>
      <a
        href="https://www.instagram.com/blackpanthercoin"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaInstagram className="text-3xl md:text-4xl" />
      </a> 
      <a
        href="https://www.youtube.com/@blackpanthertoken"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaYoutube className="text-3xl md:text-4xl" />
      </a>
      <a
        href="https://www.linkedin.com/company/blackpanthertkn/"
        target="_blank"
        rel="noopener noreferrer"
        className="text-yellow-500 hover:text-gray-300"
      >
        <FaLinkedin className="text-3xl md:text-4xl" />
      </a>
    </div>
  </div>
);

export default MyPoints;











