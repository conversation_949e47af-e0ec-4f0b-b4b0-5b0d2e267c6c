import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { AuthService } from '../../services/authService';

export const WalletModal = ({ currentWallet, onSave, onClose }) => {
    const [walletAddress, setWalletAddress] = useState(currentWallet || '');
    const [newWalletAddress, setNewWalletAddress] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const validateWalletAddress = (address) => {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    };

    const handleWalletChange = (value, isNew = false) => {
        if (isNew) {
            setNewWalletAddress(value);
        } else {
            setWalletAddress(value);
        }

        if (value && !validateWalletAddress(value)) {
            toast.info('Please enter a valid Ethereum wallet address');
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            const addressToUse = currentWallet ? newWalletAddress : walletAddress;

            if (!validateWalletAddress(addressToUse)) {
                toast.error('Please enter a valid wallet address');
                setIsLoading(false);
                return;
            }

            await onSave(addressToUse);
        } catch (error) {
            console.error('Wallet operation error:', error);
            toast.error(error.message || 'Failed to process wallet operation');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-[#302f29] rounded-xl p-6 max-w-md w-full">
                <h3 className="text-xl font-bold text-white mb-4">
                    {currentWallet ? 'Update Wallet Address' : 'Add Wallet Address'}
                </h3>

                <form onSubmit={handleSubmit} className="space-y-4">
                    {currentWallet ? (
                        <>
                            <div>
                                <label className="text-gray-300 text-sm mb-1 block">
                                    Current Wallet Address
                                </label>
                                <input
                                    type="text"
                                    value={walletAddress}
                                    className="w-full p-3 rounded-full bg-[#9a9994] text-black placeholder-gray-600 text-center"
                                    readOnly
                                />
                            </div>
                            <div>
                                <label className="text-gray-300 text-sm mb-1 block">
                                    New Wallet Address
                                </label>
                                <input
                                    type="text"
                                    value={newWalletAddress}
                                    onChange={(e) => handleWalletChange(e.target.value, true)}
                                    placeholder="Enter new wallet address"
                                    className="w-full p-3 rounded-full bg-[#9a9994] text-black placeholder-gray-600 text-center"
                                    required
                                />
                            </div>
                        </>
                    ) : (
                        <input
                            type="text"
                            value={walletAddress}
                            onChange={(e) => handleWalletChange(e.target.value)}
                            placeholder="Enter wallet address"
                            className="w-full p-3 rounded-full bg-[#9a9994] text-black placeholder-gray-600 text-center"
                            required
                        />
                    )}

                    <div className="flex gap-3">
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full py-2 transition-colors disabled:opacity-50"
                        >
                            {isLoading ? 'Processing...' : (currentWallet ? 'Update' : 'Add')}
                        </button>
                        <button
                            type="button"
                            onClick={onClose}
                            disabled={isLoading}
                            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white rounded-full py-2 transition-colors disabled:opacity-50"
                        >
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};