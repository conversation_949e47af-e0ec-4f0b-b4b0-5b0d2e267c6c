import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiCalendar, FiClock } from 'react-icons/fi';
import WebLogo from '../assets/newBPlogo.png';
import { AppRoutesPaths } from '../route/app_route';
import { FiCheckCircle } from "react-icons/fi";
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import ContributionAmountInput from '../components/MonthlyContribution/ContributionAmountInput';
import { useAuthentication } from '../components/utils/provider';
import SuccessAlert from '../pages/modals/successModal/success';
import { ContributionService } from '../services/ContributionService';
import FailedAlert from './modals/failed';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// Custom PaymentForm for updating contribution (immediate)
const UpdatePaymentForm = ({ userId, amount, onContributionActivated, onError, isPaymentProcessing }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [processing, setProcessing] = useState(false);
    const [errorMessage, setErrorMessage] = useState(null);

    useEffect(() => {
        isPaymentProcessing(processing);
    }, [processing]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setProcessing(true);
        setErrorMessage(null);

        if (!stripe || !elements) {
            setProcessing(false);
            return;
        }

        // STEP 1: Create a PaymentMethod from card details
        const { error: pmError, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: elements.getElement(CardElement),
        });

        if (pmError) {
            setErrorMessage(pmError.message);
            setProcessing(false);
            onError && onError(pmError);
            return;
        }
        // STEP 2: Send PaymentMethod ID, userId, and amount to backend to create payment intent and get clientSecret (action: 'update')
        let intentData;
        const intentRes = await ContributionService.generatePaymentIntent({
            userId,
            amount,
            paymentMethodId: paymentMethod.id,
            action: 'update'
        });
        intentData = intentRes.data;
        if (intentRes.success === false) {
            setErrorMessage(intentRes.message);
            setProcessing(false);
            onError && onError(intentRes);
            return;
        }

        // STEP 3: Use clientSecret to confirm the payment with Stripe.js
        const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(intentData.clientSecret);
        if (confirmError) {
            setErrorMessage(confirmError.message);
            setProcessing(false);
            onError && onError(confirmError);
            return;
        }
        if (!paymentIntent || paymentIntent.status !== 'succeeded') {
            setErrorMessage('Payment not successful!');
            setProcessing(false);
            return;
        }

        // STEP 4: Call backend to update the contribution (after payment is confirmed)
        if (intentData?.clientSecret) {
            onContributionActivated && onContributionActivated({
                paymentIntentId: paymentIntent?.id,
                subscriptionId: intentData.subscriptionId
            });
        } else {
            setErrorMessage(intentData.message);
            setProcessing(false);
            onError && onError(intentData);
            return;
        }
    };

    return (
        <form onSubmit={handleSubmit} className="w-full space-y-4">
            <div className="relative my-8 bg-yellow-500 bg-opacity-20 p-3 rounded-md">
                <CardElement />
                {errorMessage && (
                    <div className="text-red-500 text-sm mt-2">{errorMessage}</div>
                )}
            </div>
            <button
                type="submit"
                disabled={!stripe || processing}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                    processing
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-purple-600 hover:bg-purple-700 text-white'
                    }`}
            >
                {processing ? (
                    <div className="flex items-center justify-center">

                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Processing...
                    </div>
                ) : (
                    'Pay Now'
                )}
            </button>
        </form>
    );
};

export const MonthlyContributionChangeDate = () => {
    const [selectedOption, setSelectedOption] = useState('immediately'); // 'immediately' or 'scheduled'
    const [selectedMonth, setSelectedMonth] = useState('August 2025');
    const [amountOption, setAmountOption] = useState('10');
    const [customAmount, setCustomAmount] = useState('10');
    const [amountError, setAmountError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [updateError, setUpdateError] = useState('');
    const [showSuccess, setShowSuccess] = useState(false);
    const [currentBillingDate, setCurrentBillingDate] = useState('');
    const [subscriptionId, setSubscriptionId] = useState('');
    const [planId, setPlanId] = useState('');
    const [userId, setUserId] = useState('');
    const [nextPaymentDate, setNextPaymentDate] = useState('');
    const { currentUser } = useAuthentication();
    const [scheduledContribution, setScheduledContribution] = useState(null);

    // Generate month options for the next 12 months
    const monthOptions = Array.from({ length: 12 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() + i + 1);
        return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    });

    useEffect(() => {
        if (currentUser && currentUser._id) {
            setUserId(currentUser._id);
            ContributionService.getCurrentContribution().then(res => {
                if (res.success && res.data) {
                    setCurrentBillingDate(res.data.nextPaymentDate || '');
                    setNextPaymentDate(res.data.nextPaymentDate || '');
                    setSubscriptionId(res.data.subscriptionId || '');
                    // setPlanId(res.data.planId || '');
                    if (res.data.amount) {
                        setAmountOption(res.data.amount.toString());
                        setCustomAmount(res.data.amount.toString());
                    }
                }
                ContributionService.getUserScheduledContribution(currentUser._id).then(res => {
                    if (res.success && res.data) {
                        setScheduledContribution(res?.data);
                    }else{
                        setScheduledContribution(null);
                    }
                });
            });
        }
    }, [currentUser]);

    // Helper to get first day of selected month
    function getFirstDayOfMonth(monthYear) {
        const [month, year] = monthYear.split(' ');
        const date = new Date(`${month} 1, ${year}`);
        return date.toISOString().split('T')[0];
    }

    function getOrdinalSuffix(day) {
        const suffixes = ["th", "st", "nd", "rd"];
        const v = day % 100;
        return suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
    }

    // Helper to get next payment date in selected month (same day as current next payment date)
    function getScheduledDateForSelectedMonth(selectedMonth, currentNextPaymentDate) {
        if (!currentNextPaymentDate) return getFirstDayOfMonth(selectedMonth);
        const [month, year] = selectedMonth.split(' ');
        const currentDate = new Date(currentNextPaymentDate);
        const day = currentDate.getDate();
        const date = new Date(`${month} ${day}, ${year}`);
        return date.toISOString().split('T')[0];
    }

    // SCHEDULED: update contribution for future
    const handleScheduledUpdate = async () => {
        setIsSubmitting(true);
        setUpdateError('');
        try {
            const payload = {
                userId,
                amount: amountOption === 'custom' ? customAmount : amountOption,
                currency: 'USD',
                paymentMethod: 'stripe',
                updateType: 'scheduled',
                subscriptionId,
                // newPlanId: planId,
                scheduledDate: getScheduledDateForSelectedMonth(selectedMonth, nextPaymentDate),
            };
            const res = await ContributionService.updateContribution(payload);
            if (res.success) {
                setShowSuccess(true);
            } else {
                setUpdateError(res.message || 'Failed to schedule update.');
            }
        } catch (err) {
            setUpdateError(err.message || 'Failed to schedule update.');
        } finally {
            setIsSubmitting(false);
        }
    };

    // IMMEDIATE: handle payment, then update
    const handleImmediatePaymentSuccess = async (paymentData) => {
        setIsSubmitting(true);
        setUpdateError('');
        try {
            // paymentData should include paymentIntentId and subscriptionId
            const payload = {
                userId,
                amount: amountOption === 'custom' ? customAmount : amountOption,
                currency: 'USD',
                paymentMethod: 'stripe',
                updateType: 'immediate',
                subscriptionId: paymentData?.subscriptionId || subscriptionId,
                // newPlanId: planId,
                paymentIntentId: paymentData?.paymentIntentId,
                scheduledDate: new Date().toISOString().split('T')[0], // immediate: use now
            };
            const res = await ContributionService.updateContribution(payload);
            if (res.success) {
                setShowSuccess(true);
            } else {
                setUpdateError(res.message || 'Failed to update contribution.');
            }
        } catch (err) {
            setUpdateError(err.message || 'Failed to update contribution.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen flex-col items-center justify-center bg-[#F8F5ED] p-4 md:p-[5%] space-y-6 md:space-y-10 text-BP-black">
            {/* Logo Header */}
            <div className="flex justify-center mb-4 md:mb-6 bg-white w-fit mx-auto p-3 md:p-5 rounded-xl shadow-xl border-2">
                <Link to="/">
                    <img src={WebLogo} alt="Logo" className="h-8 md:h-10" />
                </Link>
            </div>

            {/* Main Content Card */}
            <div className="bg-white rounded-2xl shadow-md px-4 py-6 md:px-6 md:py-8 w-full max-w-[600px] mx-auto space-y-6 md:space-y-8">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-start gap-4 sm:gap-8 md:gap-14">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto sm:mx-0 sm:mt-1">
                        <FiCalendar className="w-5 h-5 md:w-6 md:h-6 text-gray-600" />
                    </div>
                    <div className="text-center sm:text-left">
                        <h1 className="text-xl md:text-2xl font-semibold text-gray-900">
                            Change Contribution Cycle & Amount
                        </h1>
                        <p className="text-sm text-gray-500 mt-1">
                            Update your monthly payment Cycle and amount
                        </p>
                    </div>
                </div>

                {/* Current billing date */}
                <div className="bg-[#F1F5F9] rounded-xl py-3 px-4 md:py-4 md:px-5">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-8 md:gap-14">
                        <div className="w-7 h-7 md:w-8 md:h-8 bg-white rounded-lg flex items-center justify-center mx-auto sm:mx-0">
                            <FiCalendar className="w-3.5 h-3.5 md:w-4 md:h-4 text-gray-600" />
                        </div>
                        <div className="text-center sm:text-left">
                            <p className="text-sm text-gray-500 mb-1 md:mb-2">Next Payment Date</p>
                            <p className="text-base font-semibold text-gray-900">
                                {currentBillingDate ? new Date(currentBillingDate).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' }) : 'Loading...'}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Amount Input */}
                <ContributionAmountInput
                  amountOption={amountOption}
                  setAmountOption={setAmountOption}
                  customAmount={customAmount}
                  setCustomAmount={setCustomAmount}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  paymentMethod="fiat"
                />

                {/* Change Options */}
                <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-700">
                        When should this change take effect?
                    </label>

                    {/* Immediate Option */}
                    <div
                        onClick={() => setSelectedOption("immediately")}
                        className={`rounded-lg md:rounded-xl p-3 md:p-4 border cursor-pointer transition-all ${selectedOption === "immediately"
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-300 hover:border-gray-400 bg-white"
                            }`}
                    >
                        <div className="flex items-start gap-3">
                            <div className="pt-0.5">
                                {selectedOption === "immediately" ? (
                                    <FiCheckCircle className="w-4 h-4 md:w-5 md:h-5 text-[#2c5282]" />
                                ) : (
                                    <div className="w-4 h-4 md:w-5 md:h-5 border-2 border-gray-300 rounded-full"></div>
                                )}
                            </div>
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <FiClock className="text-gray-600 w-3.5 h-3.5 md:w-4 md:h-4" />
                                    <h4 className="text-sm font-medium text-gray-900">Change immediately</h4>
                                </div>
                                <p className="text-xs md:text-sm text-gray-600">
                                    Your next contribution cycle will become {new Date(nextPaymentDate).getDate()}{getOrdinalSuffix(new Date(nextPaymentDate).getDate())} of every month and {"$"}{amountOption === 'custom' ? customAmount : amountOption} (payment required)
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Schedule Option */}
                    <div
                        onClick={() => { !scheduledContribution ? setSelectedOption("scheduled") : setUpdateError("You have a scheduled contribution already.")}}
                        className={`rounded-lg ${scheduledContribution && 'opacity-50 cursor-not-allowed'} border cursor-pointer transition-all ${selectedOption === "scheduled"
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-300 hover:border-gray-400 bg-gray-50"
                            }`}
                    >
                        <div className="p-3 md:p-4">
                            <div className="flex items-start gap-3">
                                <div className="pt-0.5">
                                    {selectedOption === "scheduled" ? (
                                        <FiCheckCircle className="w-4 h-4 md:w-5 md:h-5 text-[#2c5282]" />
                                    ) : (
                                        <div className="w-4 h-4 md:w-5 md:h-5 border-2 border-gray-300 rounded-full"></div>
                                    )}
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                        <FiCalendar className="text-gray-600 w-3.5 h-3.5 md:w-4 md:h-4" />
                                        <h4 className="text-sm font-medium text-gray-900">Schedule for future</h4>
                                    </div>
                                    <p className="text-xs md:text-sm text-gray-600 mb-3">
                                        Select when the change should take effect
                                    </p>
                                </div>
                            </div>

                            {/* Month dropdown */}
                            <div className="mt-3">
                                <div className="relative">
                                    <select
                                        value={selectedMonth}
                                        onChange={(e) => setSelectedMonth(e.target.value)}
                                        onClick={(e) => e.stopPropagation()}
                                        className="w-full py-2 md:py-2.5 px-3 md:px-4 pr-8 md:pr-10 border border-gray-300 rounded-lg md:rounded-xl text-sm md:text-base text-gray-800 bg-white focus:outline-none focus:ring-1 focus:ring-[#2c5282] focus:border-transparent cursor-pointer appearance-none"
                                    >
                                        {monthOptions.map((month) => (
                                            <option key={month} value={month}>
                                                {month}
                                            </option>
                                        ))}
                                    </select>
                                    <div className="absolute inset-y-0 right-3 md:right-4 flex items-center pointer-events-none">
                                        <svg
                                            className="w-3.5 h-3.5 md:w-4 md:h-4 text-gray-400"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M19 9l-7 7-7-7"
                                            />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Stripe Payment or Update Button */}
                {selectedOption === 'immediately' ? (
                  <div className="mt-6">
                    <Elements stripe={stripePromise}>
                      <UpdatePaymentForm
                        userId={userId}
                        amount={amountOption === 'custom' ? customAmount : amountOption}
                        onContributionActivated={(data) => handleImmediatePaymentSuccess({
                            paymentIntentId: data?.paymentIntentId,
                            subscriptionId: data?.subscriptionId
                        })}
                        onError={(err) => {setUpdateError(err.message || 'Payment failed.');}}
                        isPaymentProcessing={setIsSubmitting}
                      />
                    </Elements>
                  </div>
                ) : (
                <button
                    onClick={handleScheduledUpdate}
                    className={`w-full ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''} py-3 md:py-4 text-center font-semibold rounded-full shadow-md transition-all duration-150 text-sm md:text-base ${selectedOption === 'scheduled' ? 'bg-[#9B5FFF] hover:bg-[#8e52f2] text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                    disabled={selectedOption !== 'scheduled' || isSubmitting}
                >
                    Update contribution date 
                </button>
                )}

                {/* Success/Error Messages */}
                {showSuccess && (
                  <SuccessAlert
                    message={selectedOption === 'immediately' ? 'Your contribution has been updated and payment processed!' : 'Your contribution update has been scheduled!'}
                    onClose={() => { setShowSuccess(false); window.location.href = AppRoutesPaths.dashboard?.root || '/'; }}
                  />
                )}
                {updateError && (
                    <FailedAlert message={updateError} onClose={() => {setUpdateError('');}} />
                )}
            </div>
        </div>
    );
};