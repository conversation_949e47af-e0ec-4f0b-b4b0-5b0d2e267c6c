import  { useState, useEffect } from 'react';
import HeaderNew from '../components/Header/HeaderNew'
import <PERSON><PERSON><PERSON> from "../assets/images/Daohello.png"
import smilingkidDao from "../assets/images/smilingkidDao.png"
import problemsolving from "../assets/images/problem-solving.png"
import join from '../assets/images/join.png'
import transparency from '../assets/images/transparency.png'
import whythismatters from '../assets/images/whythismatters.png'
import Daohelloimage from '../assets/images/daohelloimage.png'
import donation from "../assets/images/donation.png"
import cashondelivery from "../assets/images/cash-on-delivery.png"
import operation from "../assets/images/operation.png"
import deployment from "../assets/images/deployment.png"
import { Link } from 'react-router-dom';
import PantherDao from '../assets/images/PantherDao.png';
import donation2 from '../assets/images/donation2.png'
import { useAuthentication } from '../components/utils/provider';
import Footer from "../components/Footer/footer";
import { AppRoutesPaths } from '../route/app_route';
import { useNavigate } from 'react-router-dom';
import { UserService } from '../services/userService';
import { ContributionService } from '../services/ContributionService.js';

export const Dao = () => {

  const { currentUser, ensureLogin } = useAuthentication()
  const [showContributionFlow, setShowContributionFlow] = useState(false);
  const [currentPage, setCurrentPage] = useState('contribution');
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [selectedPaymentDay, setSelectedPaymentDay] = useState(null);
  const [communityName, setCommunityName] = useState('');
  const navigate = useNavigate();
  const [membersCount, setMembersCount] = useState(0);
  const [summary, setSummary] = useState(null);


  useEffect(() => {
    // Capture URL parameters and store them
    const urlParams = new URLSearchParams(location.search);
    const refCode = urlParams.get('ref');
    const token = urlParams.get('token');
    
    if (refCode && token) {
      // Store both ref code and token for validation
      localStorage.setItem('referral_code', refCode);
      localStorage.setItem('referral_token', token);
      localStorage.setItem('referral_link_used', 'true');
    }
  }, [location.search]);

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        const count = await UserService.getAllUserCount();
        setMembersCount(count || 0);
      } catch (error) {
        setMembersCount(0);
      }
    };
    fetchMembers();
  }, []);

  const handleContributionSelect = (amount) => {
    setSelectedAmount(amount);
  };

  const fetchCurrentContribution = async () => {
        setContributionLoading(true);
        try {
          console.log('Fetching current contribution...');
          const result = await ContributionService.getCurrentContribution();
          console.log('Contribution result:', result);
          
          if (result.success && result.data) {
            console.log('Setting contribution amount:', result.data.amount);
            setCurrentContribution(result.data.amount || '0');
            setContributionCurrency(result.data.currency || 'USD');
            setAmountUpgrade(result.data.amountUpgrade || null);
            // setHasContribution(true);
          } else {
            console.log('No contribution found or failed:', result.message);
            setCurrentContribution('0');
            setContributionCurrency('USD');
            setAmountUpgrade(null);
          }
        } catch (error) {
          console.error('Error fetching current contribution:', error);
          setCurrentContribution('0');
          setContributionCurrency('USD');
          setAmountUpgrade(null);
        } finally {
          setContributionLoading(false);
        }
      };

  const handlePaymentDaySelect = (day) => {
    setSelectedPaymentDay(day);
  };

  const handleProceed = () => {
    if (selectedAmount && selectedPaymentDay) {
      setCurrentPage('community');
    }
  };

  const handleCreateCommunity = () => {
    // logic for creating a community??
    console.log('Creating community:', communityName);
    setShowContributionFlow(false);
    //  submission ??
  };

  const openContributionFlow = () => {
    setShowContributionFlow(true);
    setCurrentPage('contribution');
  };
  const [showCommunitySearch, setShowCommunitySearch] = useState(false);
  const handleShowCommunitySearch = () => {
    setShowCommunitySearch(true);
  };

   useEffect(() => {
    ContributionService.getDashboardSummary().then(res => {
      if (res.success) setSummary(res.data);
    });
  }, []);

  return (
    <div className="">
      
      <div className="min-h-[100vh] bg-BP-dark-grayish-blue">

        <HeaderNew />

<div className="lg:flex">
        <div className="p-[5%] md:p-[10%] lg:pr-0 space-y-10 lg:w-full">
          <div className="">
            <h1 className="text-4xl md:text-6xl font-bold font-title mb-4">
              Together, We Heal.
            </h1>
            <h1 className="text-4xl md:text-6xl font-bold font-title mb-6">
              Together, We
            </h1>
            <h1 className="text-4xl md:text-6xl font-bold font-title mb-6">
              Empower.
            </h1>
            <p className="text-lg md:text-xl max-w-xl font-body">
              Every heartbeat matters. Every choice transforms lives. Your contribution funds
              groundbreaking research, life-saving treatments, and vital healthcare for
              communities affected by sickle cell anemia, snake bites, and neglected
              tropical diseases.
            </p>

              {/* a same button exists below the join community button do changes there too */}

          <div className="py-5 text-center max-lg:hidden mt-5">
            <Link 
              to={currentUser ? AppRoutesPaths.dashboard.root : `${AppRoutesPaths.login}${location.search}`}
              onClick={() => {
                localStorage.setItem("login_origin", AppRoutesPaths.dao);
                // Preserve referral tracking for login flow
                const refCode = localStorage.getItem('referral_code');
                const token = localStorage.getItem('referral_token');
                if (refCode && token) {
                  localStorage.setItem('referral_flow', 'login');
                }
              }}
            >
              <button className="px-8 py-2 bg-gray-800/50 hover:bg-gray-700/70 text-white rounded-full border border-gray-600 transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                {currentUser ? "Dashboard" : "Sign in"}
              </button>
            </Link>
          </div>

          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center justify-center w-12 h-20 bg-transparent border border-yellow-500 rounded-full">
              <div className="w-8 h-8 bg-yellow-500 bg-opacity-25 flex justify-center items-center rounded-full">
                <div className="w-5 h-5 bg-yellow-500 rounded-full" />
              </div>
            </div>
            <div className='space-y-3'>
              <h3 className="text-3xl font-bold">{membersCount}</h3>
              <p className="text-sm font-title">Active members</p>
            </div>
          </div>
        </div>

        <div className="p-1 md:p-10 lg:p-[10%] lg:w-full">


          <div
            className="relative flex-col justify-e bg-cover bg-center h-screen w-full lg:w-96 lg:h-[90%] rounded-3xl lg:rounded-[60px]"
            style={{ backgroundImage: `url(${smilingkidDao})` }}
          >
            <div className="absolute bottom-10 left-1/2 lg:-bottom-16 lg:-left-20 transform -translate-x-1/2 bg-[#FFFFFFCC]/100 text-black p-4 rounded-2xl w-[90%] lg:w-[40vw] mx-auto md:flex justify-between">
              <div className="text-left mb-2">
                <h3 className="text-3xl font-bold font-title">$500,000+</h3>
                <p className="text-sm font-body">deployed to life-saving health initiatives</p>
              </div>
              <button
                onClick={() => {
                  localStorage.setItem("login_origin", AppRoutesPaths.dao);
                  const refCode = localStorage.getItem('referral_code');
                  const token = localStorage.getItem('referral_token');
                  if (refCode && token) {
                    localStorage.setItem('referral_flow', 'signup');
                  }
                  navigate(`${AppRoutesPaths.signup}${location.search}`);
                }}
                className="px-6 py-2 bg-BP-yellow hover:bg-BP-hovered-yellow text-black rounded-full transition duration-300 w-full h-fit my-auto shadow-lg md:w-auto"
              >
                Join community
              </button>
            </div>
          </div>


        </div>

</div>

      {/* a same button exists above the active members numbers do changes there too */}

        <div className="py-5 text-center lg:hidden">
          <Link 
            to={currentUser ? "/dashboard" : `/login${location.search}`}
            onClick={() => {
              localStorage.setItem("login_origin", AppRoutesPaths.dao);
              const refCode = localStorage.getItem('referral_code');
              const token = localStorage.getItem('referral_token');
              if (refCode && token) {
                localStorage.setItem('referral_flow', 'login');
              }
            }}
          >
            <button className="px-8 py-2 bg-gray-800/50 hover:bg-gray-700/70 text-white rounded-full border border-gray-600 transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
              {currentUser ? "Dashboard" : "Sign in"}
            </button>
          </Link>
        </div>

      </div>


      {/* next comp */}

      <div className="bg-[#F9F5EB] py-16 px-6 md:px-16 lg:px-24">
        <div className="max-w-3xl mx-auto text-[#111111]">
          <h2 className="text-4xl md:text-5xl font-bold font-title mb-4 text-center ">How it works</h2>
          <p className="text-lg mb-12 text-center font-body">Be part of the movement. Your contribution funds life-saving health initiatives.</p>

          <div className="flex flex-col gap-6">
            <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
              <div className="flex flex-col md:flex-row md:items-start gap-4">
                <div className="bg-[#111827] w-16 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                  <img src={join} alt="Sign Up" className="w-8 h-8" />
                </div>
                <div className='w-full'>
                  <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Sign Up & Contribute</h3>
                  <p className="font-body">
                    Become a member by signing up and contributing a minimum of $5. Your contribution goes directly into the DAO's charity wallet, funding life-changing health initiatives like sickle cell treatment, snake bite response, and research on tropical diseases.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
              <div className="flex flex-col md:flex-row md:items-start gap-4">
                <div className="bg-[#111827] w-16 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                  <img src={problemsolving} alt="Vote" className="w-8 h-8" />
                </div>
                <div className='w-full'>
                  <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Vote on Impactful Projects</h3>
                  <p className="font-body">
                    As a member, you help decide where the funds go. Vote on medical research, community health programs, and hospital development projects that matter most to you. Your voice shapes real-world impact.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
              <div className="flex flex-col md:flex-row md:items-start gap-4">
                <div className="bg-[#111827] w-16 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                  <img src={transparency} alt="Transparency" className="w-8 h-8" />
                </div>
                <div className='w-full'>
                  <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Full Transparency & Financial Access</h3>
                  <p className="font-body">
                    We believe in absolute transparency. Get real-time access to financial records, spending reports, and project updates. See exactly how your contributions are being used to create change.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className=" py-16 px-6 md:px-16 lg:px-24">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="w-full md:w-1/2 relative text-white p-8 md:p-12 rounded-2xl flex flex-col justify-center overflow-hidden bg-BP-dark-grayish-blue">
                {/* <img 
                  src={Daohelloimage} 
                  alt="" 
                  className="absolute inset-0 w-full h-full object-cover z-0"
                /> */}
                        <div className="my-auto shadow-[35px_-10px_50px_50px_#571C8660] rounded-full bg-[#571C8660] blur-lg w-28 h-28 absolute top-0 -left-5" />
                        <div className="my-auto shadow-[0px_-30px_50px_50px_#571C8660] w-0 h-10 absolute bottom-28 right-0" />

                
                <div className="relative z-20">
                  <h2 className="text-5xl md:text-6xl font-bold font-title mb-6">Why this matters</h2>
                  <p className="text-lg leading-relaxed font-body">
                    Millions suffer from sickle cell anemia, snake bites, and neglected tropical diseases. 
                    Research funding is scarce, and treatments are inaccessible. Our DAO ensures that people 
                    affected receive the care they deserve.
                  </p>
                </div>
              </div>
              
              <div className="w-full md:w-1/2 relative rounded-2xl overflow-hidden">
                <img
                  src={whythismatters}
                  alt="Healthcare provider with patients"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent flex items-end justify-center p-8">
                  <Link to={currentUser ? "/dashboard" : "/signup"}>
                    <button 
                      onClick={() => {
                        if (!currentUser) {
                          localStorage.setItem("login_origin", AppRoutesPaths.dao);
                          const refCode = localStorage.getItem('referral_code');
                          const token = localStorage.getItem('referral_token');
                          if (refCode && token) {
                            localStorage.setItem('referral_flow', 'signup');
                          }
                        }
                      }}
                      className="px-12 py-4 bg-white text-black rounded-full text-lg font-medium hover:bg-gray-100 transition duration-300 shadow-lg"
                    >
                      {currentUser ? "Dashboard" : "Get started"}
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

      <div className="bg-[#111828] w-full md:w-[90%] text-white py-16 px-6 md:px-16 lg:px-24 rounded-lg  mx-auto">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-bold font-title mb-12 text-left">Transparency & Impact</h2>
          <div className="flex flex-col">
            <div className="flex flex-col md:flex-row justify-between gap-6 mb-12">
              <div className="flex gap-4 items-center">
                <div className="w-0.5 h-16 bg-yellow-500"></div>
                <p className="text-lg font-body">Live updates on fund distribution and project progress</p>
              </div>

              <div className="flex gap-4 items-center">
                <div className="w-0.5 h-16 bg-yellow-500"></div>
                <p className="text-lg font-body">100% of funds go to research and healthcare projects</p>
              </div>

              <div className="flex gap-4 items-center">
                <div className="w-0.5 h-16 bg-yellow-500"></div>
                <p className="text-lg font-body">Community-driven decision-making</p>
              </div>
            </div>

            <div className="flex justify-center mb-12">
              <Link to="/daotransactions">
                <button className="px-8 py-4 bg-BP-yellow hover:bg-BP-hovered-yellow text-black rounded-full transition duration-200 text-lg font-medium hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                  View transactions
                </button>
              </Link>

            </div>

            <div className="space-y-4">
              <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                  <div className="flex items-center justify-center w-8 h-8 bg-transparent border border-yellow-500 rounded-full relative">
                    <div className="w-4 h-4 bg-yellow-500 rounded-full absolute animate-pulseglow"></div>
                  </div>
                </div>
                <div className="text-2xl font-bold font-title mr-4">{membersCount}</div>
                <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Active members</div>
              </div>

              <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                  <img src={donation} alt="Contributions icon" className="w-8 h-8 rounded-full" />
                </div>
                <div className="text-2xl font-bold font-title mr-4">$ {summary?.monthlyContributions?.toLocaleString() ?? 0}</div>
                <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly contributions</div>
              </div>

              <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                  <img src={cashondelivery} alt="Cash icon" className="w-8 h-8 rounded-full" />
                </div>
                <div className="text-2xl font-bold font-title mr-4">$ {summary?.cashOnHand?.toLocaleString() ?? 0}</div>
                <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash on hand</div>
              </div>

              <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                  <img src={operation} alt="Overheads icon" className="w-8 h-8 rounded-full" />
                </div>
                <div className="text-2xl font-bold mr-4 font-title">$ {summary?.monthlyOverheads?.toLocaleString() ?? 0}</div>
                <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly overheads</div>
              </div>

              <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                  <img src={deployment} alt="Deployed cash icon" className="w-8 h-8 rounded-full" />
                </div>
                <div className="text-2xl font-bold font-title mr-4">$ {summary?.cashDeployed?.toLocaleString() ?? 0}</div>
                <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash deployed</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-[#F8F4E9] py-16 px-6 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium font-body mb-4 text-center italic text-black">
            Be the heartbeat of change. Join us in funding life saving solutions.
          </h2>

          <div className="mt-10">
            <button
              onClick={openContributionFlow}
              className="inline-block px-12 py-4 bg-BP-yellow text-black rounded-full text-xl font-medium transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short"
            >
              Join Now
            </button>
          </div>

         {!currentUser && <div className="mt-6">
            <p className="text-gray-700 font-body">or already have an account ?</p>
            <a 
              href={`/login${location.search}`} 
              className="inline-block mt-2 text-xl font-medium font-body text-black border-b border-[#002B36] hover:border-b-2 transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short"
              onClick={() => {
                localStorage.setItem("login_origin", AppRoutesPaths.dao);
                const refCode = localStorage.getItem('referral_code');
                const token = localStorage.getItem('referral_token');
                if (refCode && token) {
                  localStorage.setItem('referral_flow', 'login');
                }
              }}
            >
              Sign in
            </a>
          </div>}
        </div>
      </div>

      <Footer />

    </div>
  );
};
