import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowUp,
  FaArrowDown,
  FaShieldAlt,
  FaExternalLinkAlt,
  FaArrowLeft,
} from 'react-icons/fa';
import { IoShieldCheckmarkOutline } from "react-icons/io5";
import { MdArrowOutward } from "react-icons/md";
import { FiArrowDownLeft } from "react-icons/fi";
import { FiRefreshCw } from 'react-icons/fi';
import { BiTrendingUp } from 'react-icons/bi';
import goldCoin from '../../assets/sahelion/gold.png'; // Adjust path as needed
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import BuySahelion from './BuySahelion';
import SellSahelion from './SellSahelion';
import { useAuthentication } from '../../components/utils/provider';
import { Tooltip } from 'antd';
import { FaCheck, FaCopy } from 'react-icons/fa6';
import { sahelionAddress } from '../../constants/constants';

const SahelionPortfolio = ({ hideNavs = false }) => {
  const navigate = useNavigate();
  const [showBuySahelion, setShowBuySahelion] = useState(false);
  const [showSellSahelion, setShowSellSahelion] = useState(false);

  // Get wallet connection functions from useAuthentication
  const {
    connectWallet,
    currentUserWallet,
    disconnectWallet,
    shlnBalance,
    usdEquivalent,
    shlnTousd,
    totalSHLNSupply
  } = useAuthentication();

  const pageRef = useRef();

  const [copySuccess, setCopySuccess] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(sahelionAddress);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };

  useEffect(() => {
    if (pageRef.current && !hideNavs) {
      pageRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, []);

  const percentChange = '+2.3%';
  const volume24h = '$4.2M';
  const marketCap = totalSHLNSupply ? `$${(totalSHLNSupply * shlnTousd).toLocaleString()}` : '$0';


  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  if (showBuySahelion) {
    return <BuySahelion
      onBack={() => setShowBuySahelion(false)}
      onSell={() => {
        setShowBuySahelion(false);
        setShowSellSahelion(true);
      }}
      hideNavs={hideNavs}
    />;
  }

  if (showSellSahelion) {
    return <SellSahelion
      onBack={() => setShowSellSahelion(false)}
      onBuy={() => {
        setShowSellSahelion(false);
        setShowBuySahelion(true);
      }}
      hideNavs={hideNavs}
    />;
  }

  return (
    <div ref={pageRef} className="min-h-screen bg-[#111828] text-white flex flex-col">
      {!hideNavs && (
        <HeaderNew />
      )}

      <div className="flex-grow flex flex-col items-center p-6">
        {!hideNavs && (<>
          {/* Back Button */}
          <div className="w-full max-w-3xl mb-4">
            <button
              onClick={() => navigate('/sahelion')}
              className="flex items-center bg-gray-800 text-white px-5 py-2 rounded-full hover:bg-gray-700"
            >
              <FaArrowLeft className="mr-3" />
              Back
            </button>
          </div>

          {/* Header */}
          <div className="w-full max-w-3xl flex justify-between items-center mb-8">
            <h1 className="text-4xl font-karla">Sahelion Portfolio</h1>
            <img onClick={() => navigate('/sahelion')} src={goldCoin} alt="Sahelion Logo" className="w-16 h-16" />
          </div>
        </>)}

        {/* Wallet Info */}
        <div className="w-full max-w-3xl flex flex-col sm:flex-row items-start sm:items-center mb-8">
          <div className="text-gray-400 mb-4 sm:mb-0">Wallet: {formatWalletAddress(currentUserWallet)}</div>
          <button
            onClick={handleConnectWallet}
            className="mt-4 sm:mt-0 sm:ml-6 flex items-center gap-2 sm:gap-6 bg-[#1E2736] text-yellow-400 px-4 py-2 rounded-full hover:bg-[#252f42] transition-colors"
          >
            <FiRefreshCw className="text-lg sm:text-xl" />
            <span className="text-sm sm:text-base">
              {currentUserWallet ? 'Disconnect wallet' : 'Connect Wallet'}
            </span>
          </button>
        </div>

        {/* Balance Card */}
        <div className="w-full max-w-3xl bg-[#1F2937] border border-gray-700 rounded-2xl p-6 mb-6">
          <div className="text-gray-400 mb-3">Total Balance</div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-baseline">
              {currentUserWallet ? (
                <>
                  <span className="text-3xl md:text-5xl font-bold text-[#E1A80D]">{shlnBalance}</span>
                  <span className="ml-2 text-gray-400">SHLN</span>
                  <label className="flex flex-row items-center text-gray-400 text-sm md:text-xl p-2">( CA : {sahelionAddress.slice(0, 8)} ... {sahelionAddress.slice(sahelionAddress.length - 8)})
                    <Tooltip title="Click to Copy Sahelion Address" placeholder="top">
                      {copySuccess ? (<>
                        <FaCheck color='green' className='ml-2' />
                      </>) : (<>
                        <FaCopy onClick={copyToClipboard} color='#571C86' className='ml-2 cursor-pointer' />
                      </>)}
                    </Tooltip>
                  </label>
                </>
              ) : (
                <span className="text-3xl font-semibold">Please connect wallet to see balance</span>
              )}
            </div>
            <div className="bg-[#4ADE80]/10 text-green-400 px-4 py-1.5 rounded-full flex items-center">
              <BiTrendingUp className="mr-1" />
              <span>{percentChange}</span>
            </div>
          </div>
          {currentUserWallet && (
            <div className="text-gray-400 text-sm">≈ ${usdEquivalent}</div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="w-full max-w-3xl grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
          <button
            onClick={() => setShowBuySahelion(true)}
            className="bg-[#1F2937] hover:bg-[#2B2A3F] rounded-2xl p-6 border border-gray-700 flex flex-col items-center justify-center transition-colors duration-300"
          >
            <div className="w-16 h-16 bg-[#2d2a4a] rounded-full flex items-center justify-center mb-4">
              <FiArrowDownLeft className="text-purple-400 text-2xl" />
            </div>
            <span className="text-xl">Buy</span>
          </button>

          <button
            onClick={() => setShowSellSahelion(true)}
            className="bg-[#1F2937] hover:bg-[#2a384b] rounded-2xl p-6 border border-gray-700 flex flex-col items-center justify-center"
          >
            <div className="w-16 h-16 bg-[#1e2a3a] rounded-full flex items-center justify-center mb-4">
              <MdArrowOutward className="text-blue-400 text-2xl" />
            </div>
            <span className="text-xl">Sell</span>
          </button>
        </div>

        {/* Market Overview */}
        <div className="w-full max-w-3xl bg-[#1F2937] border border-gray-700 rounded-2xl p-6 mb-6">
          <div className="flex items-center mb-4">
            <BiTrendingUp className="text-yellow-500 text-xl mr-2" />
            <span className="text-xl font-semibold">Market Overview</span>
          </div>

          <div className="border-b border-gray-700 py-4 flex justify-between">
            <div className="text-gray-400">Current Price</div>
            <div className="text-[#E9B308]">${shlnTousd}</div>
          </div>

          <div className="border-b border-gray-700 py-4 flex justify-between">
            <div className="text-gray-400">24h Volume</div>
            <div>{volume24h}</div>
          </div>

          <div className="border-b border-gray-700 py-4 flex justify-between">
            <div className="text-gray-400">Market Cap</div>
            <div>{marketCap}</div>
          </div>

          <div className="mt-4 bg-[#2D3748]/30 rounded-xl border border-gray-700 p-4 flex justify-between items-center">
            <div>
              <div className="text-gray-300">
                Also available on PancakeSwap
              </div>
              <div className="text-gray-500 text-sm mt-1">
                Standard DEX fees apply
              </div>
            </div>
            {/* <a
              href="https://pancakeswap.finance"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-[#2a3a4a] text-white px-4 py-2.5 rounded-xl inline-flex items-center"
            >
              <FaExternalLinkAlt className="mr-2" />
              <span className="hidden sm:inline">PancakeSwap</span>
              <span className="sm:hidden text-center">
                Pancake<br />Swap
              </span>
            </a> */}
            <p className="bg-[#2a3a4a] text-white px-4 py-2.5 rounded-xl inline-flex items-center">Coming Soon</p>
          </div>
        </div>

        {/* Security Notice */}
        <div
          className="w-full max-w-3xl rounded-2xl p-6"
          style={{ background: "linear-gradient(90deg, #2C5282 0%, #58759D 100%)" }}
        >
          <div className="flex items-start">
            <div className="p-2 rounded-full mr-4">
              <IoShieldCheckmarkOutline className="text-white text-4xl" />
            </div>
            <div>
              <div className="font-semibold mb-1">Black panther Secured</div>
              <div className="text-gray-300 text-sm mt-4">
                All transactions are encrypted with Sahelion-grade security
              </div>
            </div>
          </div>
        </div>
      </div>

      {!hideNavs && <Footer />}
    </div>
  );
};

export default SahelionPortfolio;













