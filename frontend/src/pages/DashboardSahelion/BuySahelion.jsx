import React, { useState } from 'react';
import { FaChevronDown, FaArrowLeft, FaRocket } from 'react-icons/fa';
import { ImCross } from 'react-icons/im';
import { motion } from 'framer-motion';
import { BsArrowRepeat } from 'react-icons/bs';
import { MdOutlineAccountBalanceWallet } from 'react-icons/md';
import { RiExchangeDollarLine } from 'react-icons/ri';
import { FiRefreshCw } from 'react-icons/fi';
import profit from '../../assets/sahelion/profit.png';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import credit2 from '../../assets/pro/credit-card2.png';
import wallet from '../../assets/pro/wallet.png';
import sell from '../../assets/sahelion/sell.png';
import { useAuthentication } from '../../components/utils/provider';
import { toast } from 'react-toastify';
import { Spin, Tooltip } from 'antd';
import ErrorModal from '../Presale/errorModal';
import { sahelionAddress } from '../../constants/constants';
import { FaCheck, FaCopy } from 'react-icons/fa6';


const BuySahelion = ({ onBack, onSell, hideNavs = false }) => {
  const [paymentMethod, setPaymentMethod] = useState('crypto');
  const [cryptoAmount, setCryptoAmount] = useState('0.00');
  const [cashAmount, setCashAmount] = useState('0.00');
  const [sahelionAmount, setSahelionAmount] = useState('0.00');
  const [paymentOption, setPaymentOption] = useState('Credit/Debit card');
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [paymentToken, setPaymentToken] = useState('0');
  const [isLoading, setIsLoading] = useState(false)
  const [errorDetail, setErrorDetail] = useState('');
  const [showErrorModal, setShowErrorModal] = useState(false);

  // Get wallet connection from useAuthentication
  const { currentUserWallet, connectWallet, disconnectWallet, ApprovePaymentForCrypto, BuySHLNUsingCrypto, shlnBalance, shlnTousd } = useAuthentication();

  const [copySuccess, setCopySuccess] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(sahelionAddress);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };

  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  const handleCryptoAmountChange = (e) => {
    const value = e.target.value;
    setCryptoAmount(value);
    // 1:1 conversion rate as shown in the design
    setSahelionAmount(value);
  };

  const handleCashAmountChange = (e) => {
    const value = e.target.value;
    setCashAmount(value);
    // 1:1 conversion rate as shown in the design
    setSahelionAmount(value);
  };

  const handleCashClick = () => {
    setShowComingSoon(true);
    // Set payment method to cash so the button appears selected
    // setPaymentMethod('cash');
  };

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  const handleChange = (e) => {
    setPaymentToken(e.target.value);
  };

  const onBuySHLN = async () => {
    if (currentUserWallet === '') {
      toast.error("Please connect your wallet to proceed.");
      return;
    }

    if (paymentMethod === 'crypto') {
      if (Number(cryptoAmount) > 0) {
        setIsLoading(true);
        const result = await ApprovePaymentForCrypto("usdt", Number(cryptoAmount));
        if (result.success) {
          const response = await BuySHLNUsingCrypto(Number(paymentToken), Number(cryptoAmount));
          if (response.success) {
            toast.success("Transaction successful!");
            setIsLoading(false);
            setCryptoAmount('0.00');
            setSahelionAmount('0.00');
          } else {
            setErrorDetail(response.error);
            setIsLoading(false);
            setShowErrorModal(true);
          }
        } else {
          setErrorDetail(result.error);
          setIsLoading(false);
          setShowErrorModal(true);
        }
      }

      else {
        toast.error("Please enter a valid amount to buy SHLN.");
        return;
      }
    }
  }

  return (
    <div className="min-h-screen bg-[#111828] text-white flex flex-col">
      {!hideNavs && <HeaderNew />}
      {showErrorModal && (
        < ErrorModal
          onClose={() => setShowErrorModal(false)}
          errorDetails={errorDetail}
        />
      )}

      <div className="flex-grow flex flex-col items-center p-6 max-w-2xl mx-auto w-full">
        {/* Back Button */}
        {onBack && (
          <div className="w-full mb-6 flex justify-between">
            <button
              onClick={onBack}
              className="flex items-center bg-gray-800 text-white px-5 py-2 rounded-full hover:bg-gray-700"
            >
              <FaArrowLeft className="mr-3" />
              Back
            </button>

            {onSell && (
              <button
                onClick={onSell}
                className="flex items-center bg-gray-800 text-white px-5 py-2 rounded-full hover:bg-gray-700"
              >
                <img src={sell} alt="Sell Icon" className="w-7 h-7 mr-3" />
                Sell Sahelion
              </button>
            )}
          </div>
        )}

        {/* Header with Logo */}
        <div className="flex items-center mb-4 w-full">
          <div className="flex items-center">
            <img src={profit} alt="Sahelion Coin" className="w-10 h-10 mr-3" />
            <h1 className="text-4xl font-karla mt-2">Buy Sahelion</h1>
          </div>
        </div>

        {/* Wallet Info */}
        <div className="text-gray-400 text-md mb-2 w-full">
          Wallet: {formatWalletAddress(currentUserWallet)} {currentUserWallet && <span className="text-BP-yellow">({shlnBalance} SHLN)</span>}
        </div>

        {/* Exchange Rate */}
        <div className="text-gray-400 text-xl p-2 mb-6 w-full">
          1 SHLN = ${shlnTousd} USD
        </div>

        {/* Payment Method Selector */}
        <div className="w-full bg-[#1F2937] rounded-xl p-2 mb-6">
          <div className="flex justify-between">
            <div className="flex-1 flex justify-center">
              <button
                className={`py-3 px-4 h-10 rounded-[10px] flex items-center justify-center ${paymentMethod === 'crypto' ? 'bg-BP-purple w-32 text-white' : 'text-gray-400'}`}
                onClick={() => setPaymentMethod('crypto')}

              >
                <FiRefreshCw className="mr-2" />
                Crypto
              </button>
            </div>
            <div className="flex-1 flex justify-center">
              <button
                className={`py-3 px-4 h-10 rounded-[10px] flex items-center justify-center ${paymentMethod === 'cash' ? 'bg-BP-purple w-32 text-white' : 'text-gray-400'}`}
                onClick={handleCashClick}
              // onClick={() => setPaymentMethod('cash')}
              >
                <img
                  src={paymentMethod === 'cash' ? wallet : credit2}
                  alt="Credit Card Icon"
                  className="w-6 h-6 mr-3"
                />
                Cash
              </button>
            </div>
          </div>
        </div>

        {/* Coming Soon Modal */}
        {showComingSoon && (
          <div style={{ zIndex: 1000 }} className="h-[110vh] w-full bg-BP-black bg-opacity-50 fixed -top-10 left-0 flex justify-center items-center">
            <div className="w-[90vw] max-w-[500px] h-72 -mt-16 rounded-xl bg-[#1F2937] relative">
              <ImCross
                onClick={() => { setShowComingSoon(false) }}
                className='absolute right-3 top-3 rounded-full hover:bg-gray-500 p-1.5 hover:cursor-pointer'
                size={28}
              />
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="flex flex-col justify-center items-center h-full text-center px-4"
              >
                <FaRocket
                  className="text-6xl mb-4 text-BP-hovered-gray animate-bounce drop-shadow-lg"
                />
                <h1 className="text-4xl font-bold mb-2 text-BP-lightbaige drop-shadow-sm">
                  Coming Soon
                </h1>
                <div className="h-1 w-24 rounded-full mb-4 bg-BP-opacited-white opacity-70" />
                <p className="text-base text-BP-opacited-white max-w-md">
                  We're working on something exciting behind the scenes.<br />
                  Thanks for your patience — we'll be live soon!
                </p>
              </motion.div>
            </div>
          </div>
        )}

        {paymentMethod === 'crypto' ? (
          <>
            {/* Pay With Section - Crypto */}
            <div className="w-full mb-6">
              <label className="text-gray-400 text-xl mb-2 block p-2">Pay with</label>
              <select
                value={paymentToken}
                onChange={handleChange}
                className="bg-[#1F2937] text-white rounded-xl p-4 w-full text-xl focus:outline-none"
              >
                <option value="0">USDT</option>
              </select>
            </div>

            {/* Amount Section - Crypto */}
            <div className="w-full mb-6">
              <label className="text-gray-400 text-xl mb-2 block p-2">Amount</label>
              <div className="bg-[#1F2937] rounded-xl p-4 flex justify-between items-center">
                <input
                  type="text"
                  value={cryptoAmount}
                  onChange={handleCryptoAmountChange}
                  className="bg-transparent text-xl outline-none w-full"
                  placeholder="0.00"
                />
                <span className="text-gray-400">USDT</span>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Amount Section - Cash */}
            <div className="w-full mb-6">
              <label className="text-gray-400 mb-2 text-xl block">Amount (USD)</label>
              <div className="bg-[#1F2937] rounded-xl p-4">
                <input
                  type="text"
                  value={cashAmount}
                  onChange={handleCashAmountChange}
                  className="bg-transparent text-xl outline-none w-full"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Payment Method Section - Cash */}
            <div className="w-full mb-6">
              <label className="text-gray-400 mb-2 block text-xl p-2">Payment Method</label>
              <div className="bg-[#1F2937] rounded-xl p-4 flex justify-between items-center">
                <span className="text-xl">{paymentOption}</span>
                <FaChevronDown className="text-gray-400" />
              </div>
            </div>
          </>
        )}

        {/* Sahelion Units Section */}
        <div className="w-full mb-8">
          <div className="flex flex-col mb-2">
            <label className="flex flex-row items-center text-gray-400 text-sm md:text-xl p-2">Sahelion ( CA : {sahelionAddress.slice(0, 8)} ... {sahelionAddress.slice(sahelionAddress.length - 8)})
              <Tooltip title="Click to Copy Sahelion Address" placeholder="top">
                {copySuccess ? (<>
                <FaCheck color='green' className='ml-2' />
                </>) : (<>
                <FaCopy onClick={copyToClipboard} color='#571C86' className='ml-2 cursor-pointer' />
                </>)}
              </Tooltip>
            </label>
            <div className="text-BP-yellow text-sm flex items-center">
              <span className="mr-1">⚡</span>
              Enter amount to see conversion
            </div>
          </div>
          <div className="bg-[#262825] p-4 rounded-xl">
            <div className="relative">
              {isLoading ? (<>
                <div className="relative cursor-pointer bg-gradient-to-b from-BP-yellow via-amber-500 to-BP-purple rounded-xl p-4 m-[1px] flex justify-center items-center">
                  <span className="text-2xl font-bold text-black hover:text-white">
                    <Spin size="large" />
                  </span>
                </div>
              </>) : (<>
                <div onClick={() => onBuySHLN()} className="relative cursor-pointer bg-gradient-to-b from-BP-yellow via-amber-500 to-BP-purple rounded-xl p-4 m-[1px] flex justify-center items-center">
                  <span className="text-2xl font-bold text-black hover:text-white">
                    Click to Buy {sahelionAmount === '0.00' || sahelionAmount === '' ? '0.00' : sahelionAmount} SHLN
                  </span>
                </div>
              </>)}
            </div>
          </div>
        </div>

        {currentUserWallet && Number(cryptoAmount) > 0 && (<>
          <p className="text-yellow-500 bg-BP-dark-grayish-blue text-sm md:text-base p-4 rounded-lg mb-4 border-l-4 border-yellow-500">
            <strong>NB:</strong> To complete your purchase, you will need to confirm two transactions in your wallet:
            <br />
            1. Approve this platform to spend your selected token (e.g., USDT).
            <br />
            2. Confirm the actual purchase of SHLN.
          </p>

        </>)}

        {/* Connect Wallet Button */}
        <button className="w-full bg-BP-purple text-white py-4 rounded-xl mb-6 flex items-center justify-center" onClick={handleConnectWallet}>
          <img src={wallet} alt="Wallet Icon" className="w-8 h-8 mr-3" />
          {currentUserWallet ? "Disconnect Wallet" : "Connect Wallet"}
        </button>

        {/* Transaction Info */}
        <div className="w-full bg-[#1F2937] border border-gray-700 rounded-xl p-4 text-center text-gray-400 text-sm">
          {paymentMethod === 'crypto'
            ? "Transactions typically complete in 2-5 minutes"
            : "Fiat purchases may take 1-3 business days to process"}
        </div>
      </div>

      {!hideNavs && <Footer />}
    </div>
  );
};

export default BuySahelion;



















