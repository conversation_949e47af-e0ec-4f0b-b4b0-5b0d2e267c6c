import React, { useState } from 'react';
import { FaChevronDown, FaArrowLeft, FaRocket } from 'react-icons/fa';
import { ImCross } from 'react-icons/im';
import { motion } from 'framer-motion';
import { BsArrowRepeat } from 'react-icons/bs';
import { RiExchangeDollarLine } from 'react-icons/ri';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import { FiRefreshCw } from 'react-icons/fi';
import sell from '../../assets/sahelion/sell.png';
import profit from '../../assets/sahelion/profit.png';
import credit from '../../assets/pro/credit-card.png';
import credit2 from '../../assets/pro/credit-card2.png';
import wallet from '../../assets/pro/wallet.png';
import { useAuthentication } from '../../components/utils/provider';
import { toast } from 'react-toastify';
import ErrorModal from '../Presale/errorModal';
import { Spin } from 'antd';


const SellSahelion = ({ onBack, onBuy, hideNavs=false }) => {
  const [paymentMethod, setPaymentMethod] = useState('crypto');
  const [sellAmount, setSellAmount] = useState('0.00');
  const [receiveAmount, setReceiveAmount] = useState('0.00');
  const [selectedFiat, setSelectedFiat] = useState('PayPal');
  const [fee, setFee] = useState('-0.00');
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [receiveToken, setReceiveToken] = useState('0');
  const [isLoading, setIsLoading] = useState(false)
  const [errorDetail, setErrorDetail] = useState('');
  const [showErrorModal, setShowErrorModal] = useState(false);


  // Get wallet connection from useAuthentication
  const { currentUserWallet, connectWallet, disconnectWallet, shlnBalance, shlnTousd, ApprovePaymentForCrypto, SellSHLNUsingCrypto } = useAuthentication();

  // Use the actual balance from the wallet
  const availableBalance = shlnBalance ? `${shlnBalance} SHLN` : '0.00 SHLN';

  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  const handleSellAmountChange = (e) => {
    const value = e.target.value;
    setSellAmount(value);
    // 1:1 conversion rate
    setReceiveAmount(value);
    // Calculate fee (example: 1%)
    setFee(`-${(parseFloat(value) * 0.01).toFixed(2)}`);
  };

  const handleMaxClick = () => {
    if (shlnBalance) {
      setSellAmount(shlnBalance);
      setReceiveAmount(shlnBalance);
      setFee(`-${(parseFloat(shlnBalance) * 0.01).toFixed(2)}`);
    } else {
      setSellAmount('0.00');
      setReceiveAmount('0.00');
      setFee('-0.00');
    }
  };

  const handleCashClick = () => {
    setShowComingSoon(true);
    // Still set the payment method to cash so it appears selected
    // setPaymentMethod('cash');
  };

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  const handleChange = (e) => {
    setReceiveToken(e.target.value);
  };

  const onConfirmSell = async () => {
    if (currentUserWallet === "") {
      toast.error("Please connect your wallet first");
      return;
    }

    if (paymentMethod === 'crypto') {
      const amountToSell = parseFloat(sellAmount);
      if (isNaN(amountToSell) || amountToSell <= 0) {
        toast.error("Please enter a valid amount to sell");
        return;
      }

      setIsLoading(true);
      const result = await ApprovePaymentForCrypto("shln", Number(amountToSell));
      if (result.success) {
        const response = await SellSHLNUsingCrypto(Number(receiveToken), Number(amountToSell));
        if (response.success) {
          toast.success("Transaction successful!");
          setIsLoading(false);
          setSellAmount('0.00');
          setReceiveAmount('0.00');
        } else {
          setErrorDetail(response.error);
          setIsLoading(false);
          setShowErrorModal(true);
        }
      } else {
        setErrorDetail(result.error);
        setIsLoading(false);
        setShowErrorModal(true);
      }
    }
  }

  return (
    <div className="min-h-screen bg-[#111828] text-white flex flex-col">
      {!hideNavs && <HeaderNew />}
      {showErrorModal && (
        < ErrorModal
          onClose={() => setShowErrorModal(false)}
          errorDetails={errorDetail}
        />
      )}

      <div className="flex-grow flex flex-col items-center p-6 max-w-2xl mx-auto w-full">
        {/* Back Button */}
        {onBack && (
          <div className="w-full mb-6 flex justify-between">
            <button
              onClick={onBack}
              className="flex items-center bg-gray-800 text-white px-5 py-2 rounded-full hover:bg-gray-700"
            >
              <FaArrowLeft className="mr-3" />
              Back
            </button>

            {onBuy && (
              <button
                onClick={onBuy}
                className="flex items-center bg-gray-800 text-white px-5 py-2 rounded-full hover:bg-gray-700"
              >
                <img src={profit} alt="Sahelion Coin" className="w-7 h-7 mr-3" />
                <span className='p-1'>Buy Sahelion</span>
              </button>
            )}
          </div>
        )}

        {/* Header with Logo */}
        <div className="flex items-center mb-4 w-full">
          <div className="flex items-center">
            <img src={sell} alt="Sell Icon" className="w-10 h-10 mr-3" />
            <h1 className="text-4xl font-karla mt-2">Sell Sahelion</h1>
          </div>
        </div>

        {/* Wallet Info */}
        <div className="text-gray-400 text-md mb-2 w-full">
          Wallet: {formatWalletAddress(currentUserWallet)} {currentUserWallet && <span className="text-BP-yellow">({availableBalance})</span>}
        </div>

        {/* Exchange Rate */}
        <div className="text-gray-400 text-xl p-2 mb-6 w-full">
          1 SHLN = ${shlnTousd} USD
        </div>

        {/* Payment Method Selector */}
        <div className="w-full bg-[#1F2937] rounded-xl p-2 mb-6">
          <div className="flex justify-between">
            <div className="flex-1 flex justify-center">
              <button
                className={`py-3 px-4 h-12 rounded-lg flex items-center justify-center ${paymentMethod === 'crypto' ? 'bg-BP-purple w-32 text-white' : 'text-gray-400'}`}
                onClick={() => setPaymentMethod('crypto')}
              >
                <FiRefreshCw className="mr-2" />
                Crypto
              </button>
            </div>
            <div className="flex-1 flex justify-center">
              <button
                className={`py-3 px-4 h-12 rounded-lg flex items-center justify-center ${paymentMethod === 'cash' ? 'bg-BP-purple w-32 text-white' : 'text-gray-400'}`}
                onClick={handleCashClick}
              // onClick={() => setPaymentMethod('cash')}
              >
                <img
                  src={paymentMethod === 'cash' ? credit : credit2}
                  alt="Credit Card Icon"
                  className="w-6 h-6 mr-3"
                />
                Cash
              </button>
            </div>
          </div>
        </div>

        {/* Coming Soon Modal */}
        {showComingSoon && (
          <div style={{ zIndex: 1000 }} className="h-[110vh] w-full bg-BP-black bg-opacity-50 fixed -top-10 left-0 flex justify-center items-center">
            <div className="w-[90vw] max-w-[500px] h-72 -mt-16 rounded-xl bg-[#1F2937] relative">
              <ImCross
                onClick={() => { setShowComingSoon(false) }}
                className='absolute right-3 top-3 rounded-full hover:bg-gray-500 p-1.5 hover:cursor-pointer'
                size={28}
              />
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="flex flex-col justify-center items-center h-full text-center px-4"
              >
                <FaRocket
                  className="text-6xl mb-4 text-BP-hovered-gray animate-bounce drop-shadow-lg"
                />
                <h1 className="text-4xl font-bold mb-2 text-BP-lightbaige drop-shadow-sm">
                  Coming Soon
                </h1>
                <div className="h-1 w-24 rounded-full mb-4 bg-BP-opacited-white opacity-70" />
                <p className="text-base text-BP-opacited-white max-w-md">
                  We're working on something exciting behind the scenes.<br />
                  Thanks for your patience — we'll be live soon!
                </p>
              </motion.div>
            </div>
          </div>
        )}

        {/* Amount to Sell Section */}
        <div className="w-full mb-6">
          <label className="text-gray-400 mb-2 block text-sm">Amount to Sell</label>
          <div className="bg-[#1F2937] rounded-xl p-4 flex justify-between items-center">
            <input
              type="text"
              value={sellAmount}
              onChange={handleSellAmountChange}
              className="bg-transparent text-xl outline-none w-full"
              placeholder="0.00"
            />
            <div className="flex items-center">
              <button
                onClick={handleMaxClick}
                className="bg-[#2c5282] text-white px-4 py-1 rounded-lg mr-2 hover:bg-[#345681]"
              >
                MAX
              </button>
              <span className="text-gray-400">SHLN</span>
            </div>
          </div>
          <div className="text-[#3fa29d] text-sm text-right mt-2">
            Available: {availableBalance}
          </div>
        </div>

        {/* Receive Section - Changes based on payment method */}
        {paymentMethod === 'crypto' ? (
          <div className="w-full mb-6">
            <label className="text-gray-400 text-xl mb-2 block p-2">Receive as Crypto</label>
            <select
              value={receiveToken}
              onChange={handleChange}
              className="bg-[#1F2937] text-white rounded-xl p-4 w-full text-xl focus:outline-none"
            >
              <option value="0">USDT</option>
            </select>
          </div>
        ) : (
          <div className="w-full mb-6">
            <label className="text-gray-400 mb-2 block text-sm">Receive as Fiat</label>
            <div className="bg-[#1F2937] rounded-xl p-4 flex justify-between items-center">
              <span className="text-xl">{selectedFiat}</span>
              <FaChevronDown className="text-gray-400" />
            </div>
          </div>
        )}

        {/* You'll Receive Section */}
        <div className="w-full mb-8 border border-gray-600 rounded-xl p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-400">You'll receive:</span>
            {paymentMethod === 'crypto' ? (
              <span className="text-[#3fa29d] text-xl">{receiveAmount} USDT</span>
            ) : (
              <span className="text-[#3fa29d] text-xl">{receiveAmount} USD</span>
            )}
          </div>
          {/* <div className="flex justify-between items-center">
            <span className="text-gray-400">Fee:</span>
            <span className="text-red-500">{fee} USD</span>
          </div> */}
        </div>

        {/* Confirm Sell Button */}
        {isLoading ? (<>
          <button className="w-full bg-yellow-500 text-black hover:text-white py-4 rounded-xl mb-6 font-semibold text-lg">
            <Spin size="large" />
          </button></>) : (<>
            <button onClick={() => onConfirmSell()} className="w-full bg-yellow-500 text-black hover:text-white py-4 rounded-xl mb-6 font-semibold text-lg">
              Confirm sell
            </button>
          </>)}

        {currentUserWallet && Number(sellAmount) > 0 && (<>
          <p className="text-yellow-500 bg-BP-dark-grayish-blue text-sm md:text-base p-4 rounded-lg mb-4 border-l-4 border-yellow-500">
            <strong>NB:</strong> To complete your Sell, you will need to confirm two transactions in your wallet:
            <br />
            1. Approve this platform to spend your selected token (e.g., SHLN).
            <br />
            2. Confirm the actual purchase of USDT.
          </p>

        </>)}

        {/* Connect Wallet Button */}
        <button className="w-full bg-BP-purple text-white py-4 rounded-xl mb-6 flex items-center justify-center" onClick={handleConnectWallet}>
          <img src={wallet} alt="Wallet Icon" className="w-8 h-8 mr-3" />
          {currentUserWallet ? "Disconnect Wallet" : "Connect Wallet"}
        </button>

        {/* Transaction Info */}
        <div className="w-full bg-[#1F2937] border border-gray-700 rounded-xl p-4 text-center text-gray-400 text-sm">
          {paymentMethod === 'crypto'
            ? "Transactions typically complete in 2-5 minutes"
            : "Fiat purchases may take 1-3 business days to process"}
        </div>

      </div>

      {!hideNavs && <Footer />}
    </div>
  );
};

export default SellSahelion;





