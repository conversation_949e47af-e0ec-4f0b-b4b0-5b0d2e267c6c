import React, {useState,useEffect} from 'react'
import HeaderNew from '../components/Header/HeaderNew'
import lotteryHelloSection from "../assets/images/lotteryhello.png"
import lotterysmall from "../assets/images/lottery.png"
import phonecopyhellosection from "../assets/images/phonecopyhellosection.png"
import buyticketsnow from "../assets/images/buyticketsnow.png"
import milestone from '../assets/images/milestone.png'
import clockblack from '../assets/images/clockblack.png'
import ticketblack from '../assets/images/ticket.png'
import wallet from '../assets/images/wallet.png'
import giftbox from '../assets/images/giftbox.png'
import PurchaseTickets from './Purchase_tickets'


import { motion } from "framer-motion";

export const Lottery = () => {
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth <= 768);
  const steps = [
    { text: "Connect Your Wallet", icon: wallet },
    { text: "Buy Lottery Tickets", icon: ticketblack },
    { text: "Wait for Weekly Draw", icon: clockblack },
    { text: "Check If You Won!", icon: giftbox },
  ];
  
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  function calculateTimeLeft() {
    const targetDate = new Date("2025-04-16T20:00:00Z"); // Next draw time
    const now = new Date();
    const difference = targetDate - now;

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / (1000 * 60)) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  
  return (
    <div className="h-screen  border-white/10 bg-cover bg-center bg" style={{ backgroundImage: `url(${isSmallScreen ? phonecopyhellosection : lotteryHelloSection})` }}>
      {/* Navbar */}
      <HeaderNew />

      {/* Hello section */}
      <div className="flex flex-col md:flex-row justify-center items-center gap-6 max-w-6xl mx-auto">
        
        {/* Section 1: Lottery Section */}
        <div className="w-[90%] md:w-[400px] h-[400px] md:h-[500px] rounded-[40px] p-6 opacity-80 relative"
          style={{
            background: "linear-gradient(180deg, #111111 44.58%, #72519F 100%)",
            border: "1px solid #FFFFFF33",
          }}>
          
          <div className="inset-0 h-[55%] rounded-[40px]"
            style={{
              backgroundImage: `linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
                                linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px)`,
              backgroundSize: "33px 33px",
            }}>
            <div className="relative">
              {/* Title */}
              <h1 className="text-white font-karla font-medium text-4xl md:text-5xl lg:text-[60px]">
                $BPNTHRQ
              </h1>
              <h1 className="text-white font-karla font-medium text-4xl md:text-5xl lg:text-[60px] ml-6 md:ml-8">
                Lottery
              </h1>
              {/* Subtitle */}
              <p className="text-white font-poppins font-light text-sm md:text-base mt-4 max-w-[250px]">
                Play for a chance to win big with a provably fair blockchain lottery.
              </p>
            </div>
            {/* ecclipse and stars */}
            <div className="relative w-full h-40 md:h-48 mt-4 items-center justify-center">
              {/* Circles */}
              <div className="relative w-full h-40 md:h-48 flex items-center justify-center">
                {/* Outer Circle -Fade Effect */}
                <div
                  className="absolute w-[60%] md:w-[160px] aspect-square rounded-full border border-white/40"
                  style={{
                    maskImage: "linear-gradient(to bottom, rgba(255,255,255,1) 60%, rgba(255,255,255,0) 100%)",
                    WebkitMaskImage: "linear-gradient(to bottom, rgba(255,255,255,1) 60%, rgba(255,255,255,0) 100%)",
                  }}>
                </div>
                {/* middle Circle */}
                <div className="absolute w-[40%] md:w-[100px] aspect-square rounded-full border border-white/60"></div>

                {/* Core Circle */}
                <div className="absolute w-[20%] md:w-[50px] aspect-square rounded-full border border-white"></div>
              </div>


              
              {/* Center Image */}
              <img
                src={lotterysmall}
                alt="Center Icon"
                className="absolute left-[45%] md:left-[50%] top-[40%] w-[8%] md:w-[30px] h-[8vw] md:h-[30px] max-h-[30px]"
              />
              
              {/* Stars */}
              <div className="absolute w-full h-full">
                {[
                  { top: "60%", left: "20%", bg: "#D9D9D9" },
                  { top: "80%", left: "20%", bg: "#FFFFFF4D" },
                  { top: "90%", left: "25%", bg: "#FFFFFF4D" },
                  { top: "90%", left: "70%", bg: "#FFFFFF4D" },
                  { top: "85%", left: "70%", bg: "#FFFFFF4D" },
                  { top: "75%", left: "60%", bg: "#FFFFFF4D" },
                  { top: "65%", left: "45%", bg: "#FFFFFF4D" },
                  { top: "60%", left: "70%", bg: "#D9D9D9" },
                  { top: "50%", left: "70%", bg: "#D9D9D9" },
                  { top: "40%", left: "70%", bg: "#D9D9D9" },
                  { top: "35%", left: "45%", bg: "#D9D9D9" },
                ].map((dot, i) => (
                  <div 
                    key={i} 
                    className="absolute w-[3px] h-[3px] rounded-full" 
                    style={{ top: dot.top, left: dot.left, background: dot.bg }}
                  ></div>
                ))}
              </div>
              
              {/* Right Gradient Line */}
              <div
                className="absolute h-[1px] w-[30%] md:w-[120px] top-[65%] right-[10%]"
                style={{
                  background: "linear-gradient(90deg, #FFFFFF 0%, rgba(153, 153, 153, 0) 100%)",
                }}
              ></div>
              
              {/* Left Gradient Line */}
              <div
                className="absolute h-[1px] w-[30%] md:w-[120px] top-[65%] left-[10%]"
                style={{
                  background: "linear-gradient(270deg, #FFFFFF 0.09%, rgba(153, 153, 153, 0) 99.91%)",
                }}
              ></div>
              
              {/* Small Circles */}
              <div className="absolute top-[64%] right-[15%] w-[8px] h-[8px] bg-white rounded-full"></div>
              <div className="absolute top-[64%] left-[42%] w-[8px] h-[8px] bg-white rounded-full"></div>
              
              {/* Blur Effect */}
              <div
                className="absolute w-[15%] md:w-[60px] h-[15vw] md:h-[60px] max-h-[60px] top-[45%] left-[42%] rounded-full bg-[#E9B308] blur-[25px]"
              ></div>
            </div>
          </div>
        </div>
        
        {/* Container for Prize Pool and Buy Tickets sections */}
        <div className="w-[90%] md:w-[500px] flex flex-col gap-6">
          
          {/* Section 2: Prize Pool */}
          <div className="w-full h-[320px] md:h-[320px] rounded-[40px] relative bg-[#111111] border border-[#FFFFFF4D]">
            {/* Prize Pool Title and Amount */}
            <div className="px-6 pt-4 md:pt-6">
              <div className="font-karla font-bold text-xl md:text-[25px] leading-tight">
                Current Prize Pool
              </div>
              <div className="font-karla font-bold text-3xl md:text-[40px] text-[#E9B308] leading-tight mt-1">
                500,000 BPNTHRQ
              </div>
            </div>
            
            {/* Next Draw Info */}
            <div className="px-6 mt-6">
              <h1 className="text-white font-poppins font-light text-sm md:text-[16px] leading-tight">Next draw</h1>
              <p className="text-white font-poppins font-light text-sm md:text-[16px] leading-tight mt-1">Sun, 16 Feb 2025</p>
              <p className="text-white font-poppins font-light text-sm md:text-[16px] leading-tight mt-1">20:00:00 GMT</p>
            </div>
            
            {/* Countdown Timer */}
            <div className="flex flex-col md:flex-row px-6 md:px-6 mt-6">
              {/* Timer Numbers */}
              <div className="flex-1">
                <p className="text-white text-xl md:text-2xl font-semibold flex space-x-6 md:space-x-8">
                  <span>{timeLeft.days}</span>
                  <span>{timeLeft.hours}</span>
                  <span>{timeLeft.minutes}</span>
                  <span>{timeLeft.seconds}</span>
                </p>
                <p className="text-white font-karla font-light text-xs md:text-[14px] text-[#FFFFFFB2] flex space-x-6 md:space-x-6 mt-1">
                  <span className="w-6 md:w-8">Days</span>
                  <span className="w-6 md:w-8">Hrs</span>
                  <span className="w-6 md:w-8">Mins</span>
                  <span className="w-6 md:w-10">Secs</span>
                </p>
              </div>
              
              {/* Circular Timer - Hidden on small screens, visible on medium and up */}
              <div className="hidden md:block relative w-[170px] h-[170px] bottom-24 right-10">
                {/* Outer Circle */}
                <div className="absolute top-0 right-0 w-[170px] h-[170px] bg-[#FFFFFF1A] border border-[#FFFFFF1A] rounded-full">
                  {/* Rotating Pointer */}
                  <div className="absolute w-[10px] h-[10px] bg-white top-[-5px] left-1/2 transform -translate-x-1/2 rounded-full animate-spin-slow origin-center">
                  </div>
                </div>
                
                {/* Inner Circle with Glow */}
                <div
                  className="absolute top-5 right-5 w-[130px] h-[130px]
                      bg-[radial-gradient(50%_50%_at_50%_50%,_#424240_0%,_#111111_100%)]
                      shadow-[4px_4px_20px_0px_#E9B308,-4px_-4px_20px_0px_#E9B308]
                      rounded-full flex items-center justify-center text-center">
                  {/* Faded Background Numbers */}
                  <p className="absolute text-[30px] font-bold text-[#FFFFFF1A] top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    {timeLeft.days - 2}
                  </p>
                  <p className="absolute text-[20px] font-bold text-[#FFFFFF66] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    {timeLeft.days - 1}
                  </p>
                    
                  {/* Main Countdown Number */}
                  <p className="text-[50px] font-bold text-[#E9B308]">{timeLeft.days}</p>
                    
                  {/* Days Remaining Text */}
                  <p className="absolute bottom-4 text-[12px] text-white">Days Remaining</p>
                </div>
              </div>
            </div>
            
            {/* Small screen circular timer - Visible only on small screens */}
            <div className="md:hidden flex justify-center mt-4 relative bottom-28 left-24">
              <div className="relative w-[120px] h-[120px]">
                {/* Outer Circle */}
                <div className="absolute w-[120px] h-[120px] bg-[#FFFFFF1A] border border-[#FFFFFF1A] rounded-full">
                  {/* Rotating Pointer */}
                  <div className="absolute w-[8px] h-[8px] bg-white top-[-4px] left-1/2 transform -translate-x-1/2 rounded-full animate-spin-slow origin-center">
                  </div>
                </div>
                
                {/* Inner Circle with Glow */}
                <div
                  className="absolute top-[10px] left-[10px] w-[100px] h-[100px]
                      bg-[radial-gradient(50%_50%_at_50%_50%,_#424240_0%,_#111111_100%)]
                      shadow-[3px_3px_15px_0px_#E9B308,-3px_-3px_15px_0px_#E9B308]
                      rounded-full flex items-center justify-center text-center">
                  {/* Faded Background Numbers */}
                  <p className="absolute text-[20px] font-bold text-[#FFFFFF1A] top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    {timeLeft.days - 2}
                  </p>
                  <p className="absolute text-[16px] font-bold text-[#FFFFFF66] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    {timeLeft.days - 1}
                  </p>
                    
                  {/* Main Countdown Number */}
                  <p className="text-[36px] font-bold text-[#E9B308]">{timeLeft.days}</p>
                    
                  {/* Days Remaining Text */}
                  <p className="absolute bottom-2 text-[10px] text-white">Days Remaining</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Section 3: Buy Tickets Now */}
          <div className="w-full h-[140px] rounded-[40px] relative overflow-hidden"
            style={{
              background: `linear-gradient(0deg, #111111, #111111),
                          linear-gradient(180deg, rgba(233, 179, 8, 0) 0%, #000000 100%)`,
              backgroundImage: `url(${buyticketsnow})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              border: "1px solid #FFFFFF4D"
            }}>
            
            {/* Buy tickets button */}
            <div className="absolute bottom-4 left-4 md:left-10 flex items-center">
              <button className="w-[125px] h-[35px] rounded-[50px] bg-[#E1AB0D] shadow-[0px_8px_4px_0px_#00000033] flex items-center justify-center">
                <span className="text-black font-poppins font-medium text-sm md:text-base">
                  Buy tickets now
                </span>
              </button>
            </div>
          </div>
          
        </div>
      </div>

    
      

      {/* About The Lottery Section */}
      {/* <div></div> put the about lotter div in here to add background */} 
      <div className="relative mx-auto max-w-[1200px] w-[90%] min-h-[300px] py-10 md:py-16 my-20 md:my-44 rounded-[20px] md:rounded-[40px] bg-gradient-to-r from-[#836504] via-[#111111] to-[#836504] flex flex-col items-center justify-center text-center px-4">          <h1 className='text-white font-[Karla] font-medium text-[36px] md:text-[60px] leading-tight md:leading-[112.22px] mb-4'>About The Lottery</h1>
        <p className='font-[Poppins] font-light text-[14px] md:text-[16px] text-center md:text-left text-white max-w-[600px] mb-6'>The $BPNTHRQ Lottery is a decentralized blockchain-based game where participants can buy tickets for a chance to win a large prize pool. The lottery operates on a provably fair mechanism, ensuring transparency and trust. Draws occur weekly, and winnings are distributed automatically to the lucky winners.</p>
        <button className='bg-white text-[#111111] font-semibold text-base md:text-lg px-4 md:px-6 py-2 md:py-3 rounded-full hover:bg-gray-200 transition'>Get Started</button>
      </div>
       
      {/* How it works */}
      <div className="bg-[#111828] min-h-screen flex items-center justify-center p-4">
        <div 
          className="relative w-full max-w-[1154px] h-[850px] md:h-[450px] mx-auto my-44 rounded-[40px] bg-[#505050]/30 backdrop-blur-[100px]"
          style={{ 
            backdropFilter: 'blur(100px)'
          }}>


            {/* ecclipse purple change the design make responsive */}
            {/* <div className="absolute w-[150px] h-[150px] rounded-full bg-[#72519F]"
                        style={{
              right: '-100px',
              top: '50%',
              transform: 'translateY(-50%)',
              filter: 'blur(50px)'}}>

            </div> */}


          <div className="flex flex-col items-center text-white mb-10 mt-10 md:mt-0 md:absolute md:top-20 md:left-10">
            <img src={milestone} alt="Process" className="w-20 h-20 mb-4" />
            <h2 className="text-5xl md:text-4xl font-karla font-medium  text-center">How it <br /> works</h2>
          </div>

          {/* 4 containers */}
          <div className="flex flex-col md:flex-row md:flex-wrap gap-6 mt-10 items-center justify-center">
            {steps.map((step, index) => (
              <div
              key={index}
              className={`relative flex items-center gap-4 p-6 w-[90%] md:w-[330px] h-[120px] rounded-[30px] border border-gray-600 md:absolute ${
                index === 0 ? "md:top-[-60px] md:right-[-60px]" :
                index === 1 ? "md:top-[80px] md:right-36" :
                index === 2 ? "md:top-[220px] md:right-[-60px]" :
                index === 3 ? "md:top-[360px] md:right-36" : "md:top-0"
              }`}
              style={{ background: "#505050" }}
              >
                <div
                  className="absolute inset-0 rounded-[30px] p-[1px]"
                  style={{
                    background:
                      "linear-gradient(270deg, rgba(126, 130, 130, 0.6) 50%, #FFFFFF 98.19%)",
                    WebkitMask:
                      "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    WebkitMaskComposite: "xor",
                    maskComposite: "exclude",
                    pointerEvents: "none",
                  }}
                ></div>
                <div className="absolute top-2 left-2 w-14 h-14 flex items-center justify-center bg-[#E9B308] rounded-full">
                  <img src={step.icon} alt="Icon" className="w-10 h-10 z-10" />
                </div>

                <p className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white font-light z-10 text-lg leading-tight">
                {step.text.split(" ").slice(0, 2).join(" ")} <br />
                {step.text.split(" ").slice(2).join(" ")}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
      <PurchaseTickets/>
    </div>
  );
};