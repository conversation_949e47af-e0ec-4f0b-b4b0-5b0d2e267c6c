import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import letter from '../assets/sahelion/letter.png';
import HeaderNew from '../components/Header/HeaderNew';
import lock from '../assets/sahelion/lock.png';
import sign from '../assets/sahelion/sign.png';
import chart from '../assets/sahelion/chart.png';
import design from '../assets/sahelion/design.png';
import w from '../assets/sahelion/ww.png';
import money from '../assets/sahelion/money-exchange.png';
import cryptocurrency from '../assets/sahelion/cryptocurrency.png';
import bala from '../assets/sahelion/bala.png';
import card from '../assets/sahelion/card.png';
import mobile from '../assets/sahelion/mobile-banking.png';
import binance from '../assets/sahelion/binance.png';
import interaction from '../assets/sahelion/interaction.png';
import exchange from '../assets/sahelion/exchange.png';
import pesa from '../assets/sahelion/pesa.png';
import gold from '../assets/sahelion/gold.png';
import { FaExternalLinkAlt } from 'react-icons/fa';
import Footer from '../components/Footer/footer';

import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';
import httpClient from '../components/httpClient/httpClient';
import { useAuthentication } from '../components/utils/provider';
import SahelionPortfolio from './DashboardSahelion/SahelionPortfolio';
import { usdtAddress } from '../constants/constants';
import { AppRoutesPaths } from '../route/app_route';
//import { Footer } from 'antd/es/layout/layout';

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const dateLabel = label; // e.g., 'Jan 2024'

    return (
      <div className="bg-[#1B1E30] border border-[#FFFFFF1A] p-2 rounded-lg shadow-lg text-white/80 text-sm">
        <div className="flex items-center gap-1">
          <img src={gold} alt="icon" className="w-5 h-5" />
          <span className="text-lg font-semibold">{Number(payload[0].value).toLocaleString()}</span>
        </div>
        <p className="text-xs text-gray-400">{dateLabel}</p>
      </div>
    );
  }
  return null;
};

export const Sahelion = () => {
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const { totalCryptoBacking , BuyTestUSDTForTesting, currentUserWallet} = useAuthentication()
  const [environment, setEnvironment] = useState(import.meta.env.VITE_ENVIRONMENT);

  useEffect(() => {
    const fetchData = async () => {
      httpClient.get('supply/get')
        .then((res) => {
          const transformed = res.data.map((entry) => ({
            name: `${entry.month} ${entry.year}`,
            value: entry.totalSupply,
          }));
          setData(transformed);
        })
        .catch((err) => console.error('Failed to fetch chart data:', err));
    };

    fetchData();
  }, []);

  const onGetTestUSDT = async () => { 
    await BuyTestUSDTForTesting()
  }


  return (
    <div
      className="min-h-screen text-white"
      style={{
        background:
          'linear-gradient(180deg, rgba(119, 96, 226, 0.3) -6.74%, rgba(17, 24, 40, 0.5) 30.18%), #111828',
      }}
    >
      <HeaderNew />
      {/* Title Section */}
      <div className="text-center mt-5 px-4">
        <p className="font-Karla font-bold text-[32px] sm:text-[48px] md:text-[64px] lg:text-[128px] leading-tight tracking-normal max-w-[90%] mx-auto">
          Sahelion
        </p>
        <p className="font-Poppins text-[16px] sm:text-[20px] md:text-[24px] leading-normal tracking-normal max-w-[90%] mx-auto text-gray-300">
          The StableCoin of the Black Panther Ecosystem
        </p>
      </div>

      {/* Cards Section */}
      <div className="mt-10 flex flex-wrap justify-center gap-8 md:flex-row sm:flex-col w-[76%] mx-auto">
        {/* Stable and Backed by Reserves */}
        <div
          className="relative w-[500px] h-[600px] md:w-[450px] md:h-[550px] sm:w-full sm:h-[450px] p-6 sm:p-7 rounded-[50px] text-white 
          shadow-4xl border border-gray-100 overflow-hidden flex flex-col items-center justify-center 
          bg-[radial-gradient(126.08%_126.08%_at_50%_50%,rgba(0,0,0,0.2)_0%,rgba(17,24,40,0.2)_17.27%,rgba(40,32,78,0.2)_33.69%,rgba(151,71,255,0.2)_99.78%)]"
        >
          {/* Glowing Diamond Effect */}
          <div className="absolute inset-0 flex items-center justify-center translate-y-[-70px]">
            {/* Outer Diamonds with Rounded Corners */}
            <div className="absolute w-[400px] h-[400px] border-[1px] border-gray-500 rotate-45 opacity-20 rounded-3xl"></div>
            <div className="absolute w-[340px] h-[340px] border-[1px] border-gray-500 rotate-45 opacity-20 rounded-3xl"></div>
            <div className="absolute w-[280px] h-[280px] border-[1px] border-gray-300 rotate-45 opacity-30 rounded-3xl"></div>
            <div className="absolute w-[220px] h-[220px] border-[1px] border-gray-200 rotate-45 opacity-30 rounded-3xl"></div>
            <div className="absolute w-[160px] h-[160px] border-[1px] border-[BP-gold] rotate-45 opacity-300 rounded-3xl"></div>
          </div>

          {/* Vibranium Logo (Properly Centered) */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-[50%] -translate-y-[100%] z-10">
            <img src={gold} alt="Sahelion Logo" className="w-32 h-32" />
          </div>

          {/* Text Content */}
          <div className="relative flex flex-col items-center text-center z-30 mt-[320px]">
            {/* Title */}
            <h2 className="text-xl md:text-2xl font-poppins text-white">
              Stable, and Backed by Reserves
            </h2>

            {/* Description */}
            <p className="font-poppins text-white text-sm mt-3 px-8 leading-relaxed">
              A stablecoin pegged 1:1 to the US dollar, backed by USDT and fiat
              reserves for stability.
            </p>
          </div>
        </div>

        {/* Secure */}
        <div
          className="relative w-[500px] h-[600px] md:w-[450px] md:h-[550px] sm:w-full sm:h-[450px] p-6 sm:p-7 rounded-[50px] text-white 
shadow-4xl border border-gray-100 overflow-hidden flex flex-col items-center justify-center 
bg-[radial-gradient(126.08%_126.08%_at_50%_50%,rgba(0,0,0,0.2)_0%,rgba(17,24,40,0.2)_17.27%,rgba(40,32,78,0.2)_33.69%,rgba(151,71,255,0.2)_99.78%)]"
        >
          {/* Background Grid Pattern (Top Half Only) */}
          <div className="absolute top-20 left-0 right-0 flex justify-center w-full">
            <div className="w-fit h-1/2 grid grid-cols-12 grid-rows-8 gap-[8px]">
              {(() => {
                // Generate random indexes for 5 yellow boxes
                let yellowIndexes = new Set();
                while (yellowIndexes.size < 10) {
                  yellowIndexes.add(Math.floor(Math.random() * 100)); // 10x10 = 100 boxes
                }

                return [...Array(72)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-6 h-6 rounded-sm ${yellowIndexes.has(i)
                        ? 'bg-yellow-400/10 shadow-yellow-400/10'
                        : 'bg-gray-300/5 shadow-gray-500/5'
                      }`}
                  ></div>
                ));
              })()}
            </div>
          </div>

          {/* Layered Shields */}
          <div className="relative flex flex-col items-center justify-center">
            {/* Biggest Shield */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="absolute w-[530px] h-[600px] text-white/20 -translate-y-12"
            >
              <path
                fill="none"
                stroke="white"
                strokeWidth="0.004"
                d="M12 2 L22 7 V13 C22 20 12 23 12 23 C12 23 2 20 2 13 V7 Z"
              />
            </svg>

            {/* 2nd Largest Shield  */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="absolute w-[430px] h-[550px] text-white/30 -translate-y-14"
            >
              <path
                fill="none"
                stroke="white"
                strokeWidth="0.005"
                d="M12 2 L22 7 V13 C22 20 12 23 12 23 C12 23 2 20 2 13 V7 Z"
              />
            </svg>

            {/* Middle Shield */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="absolute w-[330px] h-[430px] text-yellow-300/50 -translate-y-14"
            >
              <path
                fill="none"
                stroke="white"
                strokeWidth="0.006"
                d="M12 2 L22 7 V13 C22 20 12 23 12 23 C12 23 2 20 2 13 V7 Z"
              />
            </svg>
            {/* Wrapper Background to Fill Between Shields */}
            <div className="absolute inset-0 w-full h-full bg-[#111828] z-0"></div>

            {/* 2nd Smallest Shield */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              className="absolute w-[240px] h-[330px] text-yellow-300/50 -translate-y-14"
            >
              <path
                fill="#111828"
                stroke="white"
                strokeWidth="0.05"
                d="M12 2 L22 7 V13 C22 20 12 23 12 23 C12 23 2 20 2 13 V7 Z"
              />
            </svg>

            {/* Smallest Shield with Lock Inside */}
            <div className="absolute flex flex-col items-center justify-center -translate-y-14">
              {/* Smallest Shield */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                className="w-[150px] h-[240px]"
              >
                <defs>
                  <linearGradient
                    id="shieldBorder"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#FFFFFF" />
                    <stop offset="100%" stopColor="#E9B308" />
                  </linearGradient>
                </defs>
                <path
                  fill="none"
                  stroke="url(#shieldBorder)"
                  strokeWidth="0.3"
                  d="M12 2 L22 7 V13 C22 20 12 23 12 23 C12 23 2 20 2 13 V7 Z"
                />
              </svg>

              {/* Lock Icon - Centered */}
              <div className="absolute flex items-center justify-center -translate-y-0">
                <img src={lock} alt="Secure Icon" className="w-14 h-14" />
              </div>
            </div>
          </div>

          {/* Text Content */}
          <div className="absolute bottom-20 text-center text-white">
            <h2 className="text-xl md:text-2xl font-poppins text-white">
              Secure
            </h2>
            <p className="font-poppins text-white text-sm mt-3 px-2 leading-relaxed">
              Ensures security and zero volatility in the ecosystem.
            </p>
          </div>
        </div>
      </div>

      {/* Call-to-Action Button */}
      <div className="w-full py-12 px-4 md:mt-10 mt-6">
        <div className="max-w-3xl mx-auto">
          <p className="font-poppins text-white text-2xl sm:text-2xl mb-8 leading-relaxed text-center">
            Want to buy, sell, or check your Sahelion balance? Just click below
            to get started.
          </p>
          {/* <button 
            onClick={() => navigate('/dashboard-sahelion/portfolio')} 
            className="md:mt-10 mt-4 bg-yellow-500 text-black md:px-16 md:py-4 px-12 py-4 rounded-full text-lg font-semibold shadow-lg border-4 border-gray-700 hover:bg-yellow-400 transition-all duration-300"
          >
            Open Sahelion dashboard
          </button> */}
          {(environment !== "Production" && currentUserWallet) && (
            <div className='flex flex-col items-center mt-4'>
              <p>NB: IF you do not have test USDT (JBUSD with <span className='font-bold text-BP-hovered-purple'>CA: {usdtAddress}</span>) to buy SHLN, Click on the button below. 
              If you do not have Test BNB <span className='cursor-pointer underline text-blue-400 hover:text-blue-500' onClick={() => navigate(AppRoutesPaths.buyMobileMoney)}>Click Here </span> to purchase some using <span className='text-BP-gold font-bold'>Mpesa</span> option with the phone number<span className='font-bold text-BP-gold'> 708374149 from Kenya</span> </p>
              <button onClick={() => onGetTestUSDT()} className='px-4 py-2 mt-2 bg-BP-gold text-BP-opacited-white rounded-full hover:bg-BP-hovered-yellow'>Get Test USDT</button>
            </div>
          )}
          <SahelionPortfolio hideNavs={true} />
        </div>
      </div>

      <div className="flex flex-col justify-start sm:justify-center items-start w-full ">
        <div className="md:mt-15 mt-4 ml-[40px] sm:ml-32">
          <p className="font-Poppins text-[50px] sm:text-[48px] tracking-normal w-[90%] sm:w-[600px] text-gray-200 flex flex-col items-center sm:items-start text-center sm:text-left">
            <span>Transparency and</span>
            <span>Trust</span>
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 mt-16 md:grid-cols-3 gap-2 w-full max-w-[990px] text-white mx-auto md:ml-[px]">
        {/* Left Side: Smaller Cards */}
        <div className="col-span-1 flex flex-col gap-8 w-[350px] sm:w-[310px] mx-auto md:ml-[-40px] lg:ml-[-6px] mt-5">
          {/* USDT Backing */}
          <div
            className="relative bg-[#111828] rounded-[20px] border border-[#FFFFFF1A] shadow-[0_6px_35px_rgba(0,0,0,0.3)] flex items-center gap-10 p-9 overflow-hidden"
            style={{
              background:
                'linear-gradient(0deg, #111828, #111828), radial-gradient(71.82% 71.82% at 50% 55.45%, rgba(17, 24, 40, 0.1) 0%, rgba(17, 24, 40, 0.1) 53.06%, rgba(27, 22, 22, 0.1) 100%)',
            }}
          >
            {/* Inner Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-[12px]"></div>

            {/* Icon Section with Inner Glow and Outer Ring */}
            <div className="relative flex items-center justify-center">
              {/* Outer Border Ring */}
              <div className="absolute w-20 h-20 rounded-full border border-[#FFFFFF1A]"></div>

              {/* Inner Glow Ring */}
              <div className="p-4 rounded-full bg-[#111828] border border-[#FFFFFF1A] flex items-center justify-center relative overflow-hidden">
                {/* Inner Glow Effect */}
                <div className="absolute inset-x-0 bottom-0 h-14 rounded-full shadow-[inset_0_-10px_12px_rgba(255,204,0,0.3)]"></div>

                {/* Icon */}
                <img src={chart} alt="USDT" className="w-8 h-8 relative z-10" />
              </div>
            </div>

            {/* Text Content */}
            <div className="relative z-10">
              <p className="text-BP-lightbaige text-base sm:text-lg font-semibold">
                USDT Backing
              </p>
              <p className="text-3xl font-bold">${totalCryptoBacking}</p>
            </div>
          </div>

          {/* Fiat Reserves */}
          <div
            className="relative bg-[#111828] rounded-[20px] border border-[#FFFFFF1A] shadow-[0_6px_35px_rgba(0,0,0,0.3)] flex items-center gap-10 p-9 overflow-hidden"
            style={{
              background:
                'linear-gradient(0deg, #111828, #111828), radial-gradient(71.82% 71.82% at 50% 55.45%, rgba(17, 24, 40, 0.1) 0%, rgba(17, 24, 40, 0.1) 53.06%, rgba(255, 255, 255, 0.1) 100%)',
            }}
          >
            {/* Inner Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-[12px]"></div>

            {/* Icon Section with Inner Glow and Outer Ring */}
            <div className="relative flex items-center justify-center">
              {/* Outer Border Ring */}
              <div className="absolute w-20 h-20 rounded-full border border-[#FFFFFF1A]"></div>

              {/* Inner Glow Ring */}
              <div className="p-4 rounded-full bg-[#111828] border border-[#FFFFFF1A] flex items-center justify-center relative overflow-hidden">
                {/* Inner Glow Effect */}
                <div
                  className="absolute inset-x-0 bottom-0 h-14 rounded-full bg-gradient-to-b from-[#9747FF] to-transparent"
                  style={{
                    background:
                      'linear-gradient(0deg, rgba(151, 71, 255, 0.2) 12%, rgba(91, 43, 153, 0) 88%)',
                  }}
                ></div>

                {/* Icon */}
                <img src={sign} alt="USDT" className="w-9 h-9 relative z-10" />
              </div>
            </div>

            {/* Text Content */}
            <div className="relative z-10 ">
              <p className="text-BP-lightbaige text-base sm:text-lg font-semibold">
                Fiat Reserves
              </p>
              <p className="text-sm font-bold">Coming Soon</p>
            </div>
          </div>
        </div>

        {/* Right Side: Extended Chart */}
        <div className="relative bg-[#111828] border border-[#FFFFFF1A] p-4 sm:p-6 md:p-6 rounded-3xl shadow-[0_6px_35px_rgba(0,0,0,0.3)] col-span-2 overflow-hidden w-full max-w-[95%] sm:max-w-[600px] md:max-w-[1000px] lg:max-w-[1400px] xl:max-w-[1600px] mx-auto mt-6 sm:mt-0">
          {/* Inner Glow Effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 rounded-2xl"></div>

          {/* Chart Header */}
          <h3 className="text-white/90 text-md font-bold mb-6 sm:mb-8 md:mb-8 relative z-10">
            Circulating Supply
          </h3>

          {/* Chart Container */}
          <div className="flex justify-start">
            <ResponsiveContainer width="100%" height={250}>
              <AreaChart
                data={data}
                margin={{ left: -1, right: 30, top: 10, bottom: 10 }}
              >
                <XAxis
                  dataKey="name"
                  stroke="rgba(255,255,255,0.5)"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: 'white', fontSize: 12 }}
                />
                <YAxis
                  stroke="rgba(255,255,255,0.5)"
                  tick={{ fill: 'white' }}
                  domain={[0, 'dataMax']}
                  tickCount={6}
                  allowDecimals={false}
                  axisLine={false}
                  tickLine={false}
                />

                {/* Horizontal Grid Lines Only */}
                <CartesianGrid
                  stroke="rgba(255,255,255,0.1)"
                  horizontal={true}
                  vertical={false}
                />

                <Tooltip content={<CustomTooltip />} />

                <defs>
                  <linearGradient id="grad1" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#facc15" stopOpacity={1} />
                    <stop offset="100%" stopColor="#facc15" stopOpacity={0} />
                  </linearGradient>
                </defs>

                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#facc15"
                  strokeWidth={6}
                  fill="url(#grad1)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      <div className="flex flex-col justify-start sm:justify-center items-start w-full">
        <div className="mt-24 ml-[40px] sm:ml-32">
          <p className="font-Poppins text-[50px] sm:text-[48px] leading-[100%] tracking-normal w-[90%] sm:w-[600px] text-gray-200 flex flex-col items-center sm:items-start text-center sm:text-left">
            <span>Get Your Sahelion</span>
            <span>Today</span>
          </p>
        </div>
      </div>

      <div
        className="p-6 sm:p-8 md:p-8 rounded-2xl shadow-lg w-full max-w-[90%] sm:max-w-sm md:max-w-[900px] lg:max-w-[990px] mt-24 mx-auto"
        style={{
          background: `linear-gradient(0deg, rgba(122, 122, 122, 0.2), rgba(122, 122, 122, 0.2)),
                linear-gradient(46.96deg, rgba(151, 71, 255, 0) 75%, rgba(151, 71, 255, 0.2) 96.59%)`,
        }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Left Column */}
          <div className="space-y-4 rounded-2xl">
            <div className="bg-[#5050504D] bg-opacity-30 p-8 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={money}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="text-md font-semibold">
                  Decentralized Exchanges (DEXs)
                </h3>
                <p className="text-sm text-gray-400 mt-0">
                  Trade SHLN on PancakeSwap.
                </p>
                {/* <a
                  href="#"
                  className="text-yellow-400 font-semibold text-sm p-2 mt-0 inline-block"
                >
                  Buy Now ↗
                </a> */}
                <p className="text-gray-500 italic mt-1">Coming soon...</p>
              </div>
            </div>

            <div className="bg-[#5050504D] bg-opacity-30 p-7 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={mobile}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div className="flex-1">
                <h3 className="text-md font-semibold">
                  Centralized Exchanges (CEXs)
                </h3>
                <div className="text-sm text-gray-400 mt-1">
                  SHLN will be listed on major CEX platforms. Stay tuned for
                  our partner announcements.
                  <p className="text-gray-500 italic mt-1">Coming soon...</p>
                </div>
              </div>
            </div>

            <div className="bg-[#5050504D] bg-opacity-30 p-8 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={interaction}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="text-md font-semibold">
                  Decentralized Exchanges (DEXs)
                </h3>
                <p className="text-sm text-gray-400 mt-0">
                  Buy and sell SHLN directly with other users on our
                  integrated P2P platform.
                </p>
                {/* <a
                  href="#"
                  className="text-yellow-400 font-semibold mt-0 inline-block"
                >
                  Learn more →
                </a> */}
                <p className="text-gray-500 italic mt-1">Coming soon...</p>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-4">
            <div className="bg-[#5050504D] bg-opacity-30 p-6 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={w}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="text-md font-semibold">Buy with USDT</h3>
                <p className="text-sm text-gray-400 mt-0">
                  Swap your USDT for SHLN efficiently on our website.
                </p>
                <button
                  onClick={() => navigate('/dashboard-sahelion/portfolio')}
                  className="text-yellow-400 font-semibold text-sm mt-0 p-2 inline-block"
                >
                  {/* Buy Now → */}
                  Buy Now ↗
                </button>
              </div>
            </div>

            <div className="bg-[#5050504D] bg-opacity-30 p-6 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={exchange}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="text-md font-semibold">Fiat Exchange</h3>
                <div className="text-sm text-gray-400 mt-2">
                  Exchange fiat for SHLN seamlessly through our integrated
                  payment gateways.
                  <p className="text-gray-500 italic mt-2">Coming soon...</p>
                </div>
              </div>
            </div>

            <div className="bg-[#5050504D] bg-opacity-30 p-6 rounded-2xl flex items-center space-x-3 text-gray-200 w-full max-w-md min-h-32">
              <div className="w-12 h-12 bg-white flex items-center justify-center rounded-2xl flex-shrink-0">
                <img
                  src={pesa}
                  alt="DEX Icon"
                  className="w-10 h-10 object-contain"
                />
              </div>
              <div>
                <h3 className="text-md font-semibold">M-Pesa Integration</h3>
                <div className="text-sm text-gray-400 mt-2">
                  Experience easy access to SHLN with our M-Pesa integration
                  for Kenyan users.
                  <p className="text-gray-500 italic mt-2">Coming soon...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      {/* <div className="flex flex-col justify-start sm:justify-center items-start w-full">
        <div className="mt-24 ml-[40px] sm:ml-32">
          <p className="font-Poppins text-[64px] sm:text-[48px] leading-[100%] tracking-normal w-[90%] sm:w-[600px] text-gray-200 flex flex-col items-center sm:items-start text-center sm:text-left">
            <span>
              Wish to buy <span className="">$SHLN?</span>
            </span>
            <span>Here's How</span>
          </p>
        </div>
      </div> */}

      {/* <div className="text-white p-6 sm:p-8 flex justify-center items-center min-h-screen">
        <div className="max-w-xl mt-0 sm:mt-[-5px] ml-[15px] sm:ml-[-185px]">
          
          <h2 className="text-lg font-semibold text-yellow-500">
            P2P Platforms
          </h2>
          <p className="text-gray-300 mt-2">
            Buy $SHLN directly from other users on trusted P2P platforms. Find
            a seller, agree on a price, and complete the transaction securely.
          </p>

          
          <div className="mt-2 space-y-2">
            {['Binance P2P', 'LocalBitcoins', 'Paxful'].map((platform) => (
              <a
                key={platform}
                href="#"
                className="flex items-center text-purple-400 gap-2 hover:text-purple-300"
              >
                <FaExternalLinkAlt className="text-sm" />{' '}
                <span className="underline">{platform}</span>
              </a>
            ))}
          </div>

          
          <h2 className="text-lg font-semibold text-yellow-500 mt-10">
            Binance
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
            
            <div className="bg-[#7A7A7A33] p-5 rounded-xl shadow-md flex items-center gap-4 relative border border-[#23263B]">
              
              <div className="w-14 h-14 flex items-center justify-center rounded-full border-2 border-[#A0812C] relative shrink-0">
                <img
                  src={card}
                  alt="Credit/Debit Icon"
                  className="w-10 h-10 object-contain"
                />
                <div className="absolute w-full h-full rounded-full bg-[#A0812C] blur-lg opacity-5"></div>
              </div>

             
              <div>
                <h3 className="text-white font-semibold text-lg">
                  Credit/Debit Card
                </h3>
                <p className="text-gray-400 text-sm">
                  Instant purchase using your card for quick transactions.
                </p>
                <a
                  href="#"
                  className="text-yellow-400 text-sm font-medium flex items-center mt-2"
                >
                  Learn more →
                </a>
              </div>
            </div>

            
            <div className="bg-[#7A7A7A33] p-5 rounded-xl shadow-md flex items-center gap-4 relative border border-[#23263B]">
              
              <div className="w-14 h-14 flex items-center justify-center rounded-full border-2 border-purple-500 relative shrink-0">
                <img
                  src={bala}
                  alt="Credit/Debit Icon"
                  className="w-10 h-10 object-contain"
                />
                <div className="absolute inset-0 rounded-full bg-[#23263B] blur-md opacity-5"></div>
              </div>

              
              <div>
                <h3 className="text-white font-semibold text-lg">
                  Bank Transfer
                </h3>
                <p className="text-gray-400 text-sm">
                  Use bank transfer for larger amounts with secure processing
                </p>
                <a
                  href="#"
                  className="text-yellow-400 text-sm font-medium flex items-center mt-2"
                >
                  Learn more →
                </a>
              </div>
            </div>

            
            <div className="bg-[#7A7A7A33] p-5 rounded-xl shadow-md flex items-center gap-4 relative border border-[#23263B]">
              
              <div className="w-14 h-14 flex items-center justify-center rounded-full border-2 border-[#A0812C]  relative shrink-0">
                <img
                  src={cryptocurrency}
                  alt="Crypto Icon"
                  className="w-10 h-10 object-contain"
                />
                <div className="absolute inset-0 rounded-full bg-[#A0812C] blur-md opacity-5"></div>
              </div>

              
              <div className="flex-1">
                <h3 className="text-white font-semibold text-lg">
                  Crypto Swap
                </h3>
                <p className="text-gray-400 text-sm">
                  Swap your existing cryptocurrencies for $SHLN efficiently.
                </p>
                <a
                  href="#"
                  className="text-yellow-400 text-sm font-medium flex items-center mt-2"
                >
                  Learn more →
                </a>
              </div>
            </div>
          </div>
          

        </div>
        
      </div> */}
      <Footer />
    </div>
  );
};
