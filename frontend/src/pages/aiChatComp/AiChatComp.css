/* Theme variables for light and dark mode */
:root {
  --chat-bg: #fff;
  --chat-header-bg: #f5f5f5;
  --chat-body-bg: #f5f5f5;
  --chat-border: #e5e7eb;
  --chat-user-msg-bg: #fff6e0;
  --chat-user-msg-bg-gradient: none;
  --chat-bot-msg-bg: #e5e7eb;
  --chat-text: #222;
  --chat-suggestion-bg: #f5f5f5;
  --chat-suggestion-hover: #ececec;
  --chat-suggestion-text: #72519f;
  --chat-input-bg: #fff;
  --chat-input-text: #222;
  --chat-toggle-bg: #e9b308;
  --chat-toggle-hover: #d6a203;
}

.ai-chat-dark {
  --chat-bg: #121826;
  --chat-header-bg: #1F2937;
  --chat-body-bg: #1F2937;
  --chat-border: #ffffff10;
  --chat-user-msg-bg: gradient(to right, #2C5282, #58759D);
  --chat-bot-msg-bg: #29334C;
  --chat-text: #f5f5f5;
  --chat-suggestion-bg: #3730A34D;
  --chat-suggestion-hover: #3730A390;
  --chat-suggestion-text: #ffffff;
  --chat-input-bg: #29334C;
  --chat-input-text: #f5f5f5;
  --chat-toggle-bg: #e9b308;
  --chat-toggle-hover: #d6a203;
}

/* Utility classes for chat component using CSS variables */
.ai-chat-bg { background-color: var(--chat-bg) !important; }
.ai-chat-header-bg { background-color: var(--chat-header-bg) !important; }
.ai-chat-body-bg { background-color: var(--chat-body-bg) !important; }
.ai-chat-border { border-color: var(--chat-border) !important; }
.ai-chat-user-msg-bg {
  background-image: var(--chat-user-msg-bg-gradient, none);
  background-color: var(--chat-user-msg-bg) !important; /* fallback for non-gradient */
}
.ai-chat-dark .ai-chat-user-msg-bg {
  background-image: linear-gradient(to right, #2C5282, #58759D);
  background-color: unset !important;
}
.ai-chat-bot-msg-bg { background-color: var(--chat-bot-msg-bg) !important; }
.ai-chat-text { color: var(--chat-text) !important; }
.ai-chat-suggestion-bg { background-color: var(--chat-suggestion-bg) !important; }
.ai-chat-suggestion-hover:hover { background-color: var(--chat-suggestion-hover) !important; }
.ai-chat-suggestion-text { color: var(--chat-suggestion-text) !important; }
.ai-chat-input-bg { background-color: var(--chat-input-bg) !important; }
.ai-chat-input-text { color: var(--chat-input-text) !important; }
.ai-chat-toggle-bg { background-color: var(--chat-toggle-bg) !important; }
.ai-chat-toggle-hover:hover { background-color: var(--chat-toggle-hover) !important; }

/* Custom scrollbar for chat input textarea only */
.ai-chat-input-scrollbar::-webkit-scrollbar {
  width: 4px;
  background: transparent;
}
.ai-chat-input-scrollbar::-webkit-scrollbar-thumb {
  background: #bdbdbd;
  border-radius: 0;
  min-height: 20px;
}
.ai-chat-input-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.ai-chat-input-scrollbar::-webkit-scrollbar-button {
  display: none;
  height: 0;
  width: 0;
}


/* Existing chat box container styles */
.chat-box-container {
  position: absolute;
  right: 0;
  bottom: 70px;
  transform-origin: bottom right;
  transition: transform 0.3s ease, opacity 0.3s ease;
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}

.chat-box-container.open {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.chat-box-container.closed {
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}
