import React, { useState, useEffect, useRef } from 'react';
import pantherLogo from "../../assets/images/queenpanther2.png";
import aiPantherLogo from "../../assets/aiPanther.png";
import { IoChatbubbleEllipsesOutline } from "react-icons/io5";
import { TbSend } from "react-icons/tb";
import { BsFillMoonStarsFill } from "react-icons/bs";
import { ImCross } from "react-icons/im";
import { PiSunFill } from "react-icons/pi";
import './AiChatComp.css';
import httpClient from '../../components/httpClient/httpClient';

export const AiChatComp = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(true);
  const [inputValue, setInputValue] = useState("");
  const [userId, setUserId] = useState(() => localStorage.getItem('bp_user_id') || "");
  const [suggestionArray, setSuggestionArray] = useState([]);
  const [chatHistory, setChatHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const textareaRef = useRef(null);
  const chatContainerRef = useRef(null);
  const lastBotMsgRef = useRef(null);

  const toggleChat = () => setIsOpen(prev => !prev);
  const toggleTheme = () => setDarkMode(prev => !prev);

  // Auto-grow textarea height
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 90) + "px";
    }
  }, [inputValue]);

  // On mount, if userId exists in localStorage, fetch history
  useEffect(() => {
    if (userId) {
      fetchHistory(userId);
    }
    // eslint-disable-next-line
  }, [userId]);

  // Save userId to localStorage only if not already present
  const persistUserId = (id) => {
    if (!localStorage.getItem('bp_user_id')) {
      localStorage.setItem('bp_user_id', id);
    }
  };

  const fetchSuggestions = async (uid) => {
    try {
      if (!uid) return;
      const response = await httpClient.get('/rag/suggest', { params: { userId: uid } });
      if (response.status === 200 && response.data.suggestions) {
        setSuggestionArray(response.data.suggestions);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
  };

  const fetchHistory = async (uid) => {
    try {
      if (!uid) return;
      const response = await httpClient.get('/rag/history', { params: { userId: uid } });
      if (response.status === 200 && response.data.history) {
        setChatHistory(response.data.history);
      }
    } catch (error) {
      console.error('Error fetching chat history:', error);
    }
  };

  const sendPrompt = async () => {
    if (!inputValue.trim()) return;
    setChatHistory(prev => [
      ...prev,
      { role: 'user', message: inputValue }
    ]);
    setSuggestionArray([]);
    setIsLoading(true);
    const info = {
      userId: userId === "" ? "BP_User" : userId,
      message: inputValue,
    };
    setInputValue("");
    try {
      const response = await httpClient.post('/rag/ask', info);
      if (response.status === 200) {
        // Set userId in state and localStorage only if not already set
        if (!userId && response.data.userId) {
          setUserId(response.data.userId);
          persistUserId(response.data.userId);
        }
        // If history is present (first message), use it; otherwise, fetch it
        if (response.data.history) {
          setChatHistory(response.data.history);
        } else if (response.data.userId || userId) {
          fetchHistory(response.data.userId || userId);
        }
        fetchSuggestions(response.data.userId || userId);
      }
    } catch (error) {
      setChatHistory(prev => [
        ...prev,
        { role: 'assistant', message: 'Sorry, there was a problem. Please try again.' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Animated three dots for loading
  const LoadingDots = () => (
    <span className="inline-block w-8 text-center animate-pulse">
      <span className="inline-block w-1.5 h-1.5 bg-[#58759D] rounded-full mr-1"></span>
      <span className="inline-block w-1.5 h-1.5 bg-[#58759D] rounded-full mr-1"></span>
      <span className="inline-block w-1.5 h-1.5 bg-[#58759D] rounded-full mr-1"></span>
    </span>
  );

  // Parse bot message for **bold** and ordered lists
  function parseBotMessage(text) {
    if (!text) return null;
    if (Array.isArray(text)) return text.map(parseBotMessage);
    if (typeof text !== 'string') return text;

    const lines = text.split(/\r?\n/).filter(line => line.trim() !== '');

    const isNumbered = line => /^\d+\.\s+/.test(line);
    const isSublist = line => /^\s*-\s+/.test(line);

    let result = [];
    let currentList = [];
    let currentType = null;

    function flushList() {
      if (currentList.length > 0) {
        if (currentType === 'numbered') {
          result.push(
            <ol className="list-decimal ml-5" key={Math.random()}>
              {currentList.map((item, idx) => <li key={idx}>{parseBotMessage(item)}</li>)}
            </ol>
          );
        } else if (currentType === 'sublist') {
          result.push(
            <ul className="ml-8 list-disc" key={Math.random()}>
              {currentList.map((item, idx) => (
                <li key={idx}>{parseBotMessage(item.replace(/^\s*-\s*/, ''))}</li>
              ))}
            </ul>
          );
        } else {
          currentList.forEach((item, idx) => result.push(<div key={Math.random()}>{renderBold(item)}</div>));
        }
        currentList = [];
        currentType = null;
      }
    }

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (isNumbered(line)) {
        if (currentType !== 'numbered') flushList();
        currentType = 'numbered';
        currentList.push(line.replace(/^\d+\.\s*/, ''));
      } else if (isSublist(line)) {
        if (currentType !== 'sublist') flushList();
        currentType = 'sublist';
        currentList.push(line);
      } else {
        flushList();
        currentType = null;
        currentList.push(line);
        flushList();
      }
    }
    flushList();

    function renderBold(parts, key) {
      if (typeof parts !== 'string') return parts;
      const out = [];
      let lastIdx = 0;
      const boldRegex = /\*\*(.+?)\*\*/g;
      let match;
      let idx = 0;
      while ((match = boldRegex.exec(parts)) !== null) {
        if (match.index > lastIdx) {
          out.push(parts.slice(lastIdx, match.index));
        }
        out.push(<strong key={key + '-b-' + idx}>{match[1]}</strong>);
        lastIdx = match.index + match[0].length;
        idx++;
      }
      if (lastIdx < parts.length) {
        out.push(parts.slice(lastIdx));
      }
      return out.length > 1 ? <span key={key}>{out}</span> : out[0];
    }

    return result;
  }

  const renderChatHistory = (history, lastBotMsgRef) => (
    history.map((msg, idx) => {
      const time = msg.time || msg.timestamp || (msg.role === 'user' ? '03:07 AM' : '03:08 AM');
      const isLastBot = msg.role === 'assistant' && idx === history.length - 1;
      return (
        msg.role === "user" ? (
          <div key={idx} className="flex flex-col items-end">
            <div className="inline-block ai-chat-user-msg-bg ai-chat-text px-4 py-2 rounded-2xl rounded-br-none shadow text-left max-w-[80%] break-words">
              {msg.message}
            </div>
            {/* <div className="text-[10px] text-gray-400 mt-1 text-right w-[80%] pr-1">{time}</div> */}
          </div>
        ) : (
          <div key={idx} className="flex flex-col items-start" ref={isLastBot ? lastBotMsgRef : null}>
            <div className="inline-block ai-chat-bot-msg-bg ai-chat-text px-4 py-2 rounded-2xl rounded-bl-none shadow text-left max-w-[80%] break-words">
              {parseBotMessage(msg.message)}
            </div>
            {/* <div className="text-[10px] text-gray-400 mt-1 text-left w-[80%] pl-1">{time}</div> */}
          </div>
        )
      );
    })
  );

  // Scroll to bottom or to start of last bot message after chat update
  useEffect(() => {
    if (isLoading) {
      if (chatContainerRef.current) {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }
    } else {
      if (lastBotMsgRef.current && chatContainerRef.current) {
        const botMsg = lastBotMsgRef.current;
        const container = chatContainerRef.current;
        const offset = 100; // px, adjust as needed for your padding
        container.scrollTop = botMsg.offsetTop - offset;
      } else if (chatContainerRef.current) {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }
    }
  }, [chatHistory, isLoading]);

  // Scroll to bottom when chat is opened
  useEffect(() => {
    if (isOpen && chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [isOpen]);

  return (
    <>
      {/* Chat Container */}
      <div
        className={`fixed ${isOpen && window.innerWidth < 640 ? "inset-0 h-full w-full bg-BP-dark-grayish-blue" : "bottom-20 right-5"} z-50 flex flex-col items-end gap-2 ${darkMode ? 'ai-chat-dark' : 'ai-chat-light'} ${isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-0 pointer-events-none'} transition-all duration-300 origin-bottom-right`}
      >
        {/* Chat Box */}
        <div className={`p-2 rounded-3xl ${isOpen && window.innerWidth < 640 ? "h-full w-full" : ""} ai-chat-bg flex flex-col ${isOpen && window.innerWidth < 640 ? 'justify-end' : ''}`}>
          <div className={`w-80 rounded-3xl border ai-chat-border overflow-hidden ai-chat-body-bg ${isOpen && window.innerWidth < 640 ? "h-full w-full flex flex-col" : ""}`}>
            {/* Header */}
            <div className="flex items-center justify-between p-4 ai-chat-header-bg border-b ai-chat-border">
              <div className="flex items-center space-x-3">
                <div className="relative h-8 w-8 rounded-full ai-chat-bg flex items-center justify-center">
                  <img src={pantherLogo} alt="Panther" className="w-full h-full object-contain" />
                  <div className="bg-[#4CFE9D] rounded-full absolute bottom-0 right-0 w-2 h-2"></div>
                </div>
                <span className="text-sm font-semibold ai-chat-text">Support Assistance</span>
              </div>
              <div className="space-x-3">
                <button onClick={toggleTheme}>
                  {darkMode ? (
                    <PiSunFill className="w-5 h-5 ai-chat-text hover:opacity-80" />
                  ) : (
                    <BsFillMoonStarsFill className="w-5 h-5 ai-chat-text hover:opacity-80" />
                  )}
                </button>
                <button onClick={() => setIsOpen(false)} className='bg-BP-nav-gray rounded-full sm:hidden'>
                  <ImCross className='w-5 h-5 p-1' />
                </button>
              </div>
            </div>

            {/* Messages */}
            <div
              className={`p-4 space-y-4 text-sm overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 ai-chat-input-scrollbar max-sm:pb-16 ${isOpen && window.innerWidth < 640 ? 'flex-1 min-h-0 max-h-none' : 'sm:max-h-96 sm:h-96'}`}
              style={isOpen && window.innerWidth < 640 ? { minHeight: 0 } : { minHeight: 0 }}
              ref={chatContainerRef}
            >
              {chatHistory.length === 0 && !isLoading && (
                <div className="flex items-center justify-center w-full h-full">
                  <div className="mx-auto text-center flex flex-col items-center justify-center" style={{ minHeight: 180 }}>
                    <span className="text-2xl font-semibold ai-chat-text mb-2"><img src={aiPantherLogo} alt="" className="w-20 h-20" /></span>
                    <span className="text-2xl font-semibold ai-chat-text mb-2">Hi there!</span>
                    <span className="text-base ai-chat-text opacity-80">Ask anything about Black Panther Token or say hello to get started.</span>
                  </div>
                </div>
              )}
              {renderChatHistory(chatHistory, lastBotMsgRef)}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="inline-block ai-chat-bot-msg-bg ai-chat-text px-4 py-2 rounded-2xl rounded-bl-none shadow text-left max-w-[80%]">
                    <LoadingDots />
                  </div>
                </div>
              )}
              {/* Quick Suggestions */}
              {suggestionArray.length > 0 && (
                <div>
                  <p className="text-sm font-light ai-chat-text flex items-center space-x-1">Quick suggestion</p>
                  <div className="flex flex-col gap-2 mt-2">
                    {suggestionArray.map((suggestion, idx) => (
                      <div
                        onClick={() => {
                          setInputValue(suggestion);
                          textareaRef.current.focus();
                          setSuggestionArray([]);
                        }}
                        key={idx}
                        className="ai-chat-suggestion-bg ai-chat-suggestion-text ai-chat-suggestion-hover px-4 py-2 rounded-3xl shadow-lg cursor-pointer text-sm font-medium w-fit"
                      >
                        {suggestion}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div
              className={`flex space-x-2 items-center border-t ai-chat-border p-2 ai-chat-header-bg bg-inherit ${isOpen && window.innerWidth < 640 ? 'fixed left-0 right-0 bottom-0 z-50' : ''}`}
              style={isOpen && window.innerWidth < 640 ? { borderRadius: 0, width: '100%' } : {}}
            >
              <textarea
                ref={textareaRef}
                value={inputValue}
                onKeyDown={e => {
                  const isDesktop = window.innerWidth >= 640;
                  if (isDesktop) {
                    if (e.key === "Enter") {
                      if (e.ctrlKey || e.shiftKey) {
                        const { selectionStart, selectionEnd, value } = e.target;
                        const newValue = value.slice(0, selectionStart) + "\n" + value.slice(selectionEnd);
                        setInputValue(newValue);
                        setTimeout(() => {
                          e.target.selectionStart = e.target.selectionEnd = selectionStart + 1;
                        }, 0);
                        e.preventDefault();
                      } else {
                        e.preventDefault();
                        sendPrompt();
                      }
                    }
                  }
                }}
                onChange={e => setInputValue(e.target.value)}
                placeholder="Type your message...."
                className="flex-1 px-3 py-2 text-sm border-none outline-none ai-chat-input-bg ai-chat-input-text placeholder-gray-400 rounded-xl resize-none overflow-y-auto ai-chat-input-scrollbar"
                rows={1}
                style={{ maxHeight: 120, minHeight: 36 }}
              />
              <button onClick={() => { sendPrompt(); setInputValue(''); }} className="p-2 rounded-full ai-chat-toggle-bg ai-chat-toggle-hover transition">
                <TbSend className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Toggle Button */}
      {((window.innerWidth < 640 && !isOpen) || window.innerWidth >= 640) && (
        <div
          onClick={toggleChat}
          className="fixed bottom-5 right-5 h-fit w-fit p-3 bg-gradient-to-b from-[#2C5282] to-[#58759D] rounded-full cursor-pointer shadow-md z-50 transition-all duration-300 ease-in-out"
        >
          <IoChatbubbleEllipsesOutline className="w-8 h-8 text-white hover:text-BP-hovered-gray transition-all duration-300 ease-in-out" />
        </div>
      )}
    </>
  );
};