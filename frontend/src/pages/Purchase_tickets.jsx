import React, { useState, useEffect } from 'react';
import ticketwhite from "../assets/images/ticketwhite.png"
import coin from "../assets/images/coins.png"
import { ImCross } from "react-icons/im";

const PurchaseTickets = ({toShowAssignTicketModal, setNumOfTickets}) => {

  const [showTicketQtyModal, setShowTicketQtyModal] = useState(false)
  const [numOfTicketsTyped, setNumOfTicketsTyped] = useState(6)

  useEffect(() => {
    const handleShowTicketQtyModal = () => {
      setShowTicketQtyModal(true);
    };

    const element = document.querySelector('[data-testid="purchase-tickets"]');
    if (element) {
      element.addEventListener('showTicketQtyModal', handleShowTicketQtyModal);
    }

    return () => {
      if (element) {
        element.removeEventListener('showTicketQtyModal', handleShowTicketQtyModal);
      }
    };
  }, []);

  const numbers = [6,7,8,9,10,15,20,25,50,100]

  const numberClicked = (num)=>{
    toShowAssignTicketModal()
    setNumOfTickets(num)
  }

  return (
    <div className="bg-[#111828] border-[0.5px] border-white/30 rounded-3xl h-[450px] sm:h-[650px] w-[95%] mx-auto flex items-center justify-center p-4" data-testid="purchase-tickets">
      {/* Main container */}
      <div className="relative w-full max-w-[1280px] h-auto min-h-[600px] md:h-[1054px] mx-auto my-5">
        {/* Content wrapper - centers the UI elements */}
        <div className="absolute inset-0 flex items-center justify-center">
          {/* Rotated box */}
          <div className="absolute w-[250px] xs:w-[300px] sm:w-[400px] h-[250px] xs:h-[300px] sm:h-[400px] border border-white/30 bg-[#111828]/50 rounded-[10px] transform rotate-[46.51deg] z-10">
            
          {/* purple glow */}
          <div className="w-0 h-0 shadow-[0px_0px_50px_30px_#72519F]  absolute bottom-5 left-10" />
          <div className="w-0 h-0 shadow-[0px_0px_50px_30px_#72519F] absolute top-10 right-5" />
          </div>
          
          {/* Back box  */}
          <div className="absolute w-[85%] sm:w-[320px] max-w-[320px] h-[180px] sm:h-[227px] border border-white/30 bg-[#111828]/80 rounded-[30px] sm:rounded-[40px] z-20 -translate-y-24 sm:-translate-y-24"></div>
          
          {/* Middle box */}
          <div className="absolute w-[90%] sm:w-[300px] md:w-[400px] max-w-[400px] h-[180px] sm:h-[227px] border border-white/30 bg-[#72519F] rounded-[30px] sm:rounded-[40px] z-30 -translate-y-14 sm:-translate-y-16"></div>
          
          {/* Front box  */}
          <div className="absolute w-[95%] sm:w-[450px] md:w-[507px] max-w-[507px] h-[160px] sm:h-[230px] border border-white/30 bg-[#111828E5]/90 rounded-[25px] sm:rounded-[40px] z-40 -translate-y-4 sm:-translate-y-4 p-3 sm:p-5 flex flex-col justify-start">
            {/* Header */}
            <div className="flex items-center">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-[#F59E0B] rounded-full flex items-center justify-center mr-3">
                <img src={ticketwhite} alt="Ticket icon" className="w-4 h-4 sm:w-5 sm:h-5" />
              </div>
              <div>
                <h2 className="text-[#9CA3AF] text-base sm:text-xl mb-0.5 sm:mb-1">Ticket</h2>
                <p className="text-white text-xs sm:text-sm">Choose number of tickets and pick numbers manually or randomly.</p>
              </div>
            </div>

            <div className="flex justify-end mr-6">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gray-600/50 rounded-full flex items-center justify-center ">
                  <img src={coin} alt="Random icon" className="w-4 h-4 sm:w-5 sm:h-5" />
                </div>
              </div>
            
            {/* Ticket Selection */}
            <div className="mt-[-10px] sm:mt-[-15px]">
              <h3 className="text-sm sm:text-base text-white text-center mb-2 sm:mb-3">Choose number of tickets</h3>
             
              <div className="flex justify-center gap-1.5 sm:gap-3 mb-2 sm:mb-3">
                <div onClick={()=>numberClicked(1)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">1</div>
                <div onClick={()=>numberClicked(2)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">2</div>
                <div onClick={()=>numberClicked(3)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">3</div>
                <div onClick={()=>numberClicked(4)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">4</div>
                <div onClick={()=>numberClicked(5)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">5</div>
                <div onClick={()=>setShowTicketQtyModal(true)} className="w-7 h-10 sm:w-9 sm:h-12 bg-gray-600/50 rounded-xl flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">+</div>
                
                <div className="flex flex-col justify-center items-center gap-0.5">
                  <div className="flex gap-0.5">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                  <div className="flex gap-0.5">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                  <div className="flex gap-0.5">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                </div>
              </div>

              <h3 className="text-sm sm:text-base text-[#F59E0B] text-center mb-3 sm:mb-3 font-bold">1 ticket = 1 SHLN</h3>
              
              
            </div>
          </div>
          
          {/*purchase tickets*/}
          <div className="absolute w-[95%] sm:w-[450px] md:w-[507px] max-w-[507px] text-center z-50 translate-y-24 sm:translate-y-40">
            <h2 className="text-lg sm:text-2xl text-white mb-1 sm:mb-2">Purchase tickets</h2>
            <div className="text-xs sm:text-sm text-[#9CA3AF] max-w-md mx-auto flex flex-col">
                <span>Each ticket consists of 6</span>
                <span> randomly selected numbers</span>
                <span> from 01 to 49.</span>
            </div>
          </div>
        </div>
      </div>

      {showTicketQtyModal &&
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50" style={{ zIndex: 1000 }}>
                  <div className="bg-[#ffffff20] rounded-3xl lg:w-[45vw] p-5">
                  <div className="bg-[#111828] rounded-3xl p-5">
                    <div className="flex justify-between items-center space-x-3">
                      <p className="text-BP-opacited-white text-2xl">Choose number of tickets</p>
                      <button onClick={() => { setShowTicketQtyModal(false) }} className=" text-white text-lg">
                        <ImCross color="#ffffff" className='w-8 h-8 hover:bg-[#ffffff60] p-2 rounded-full' />
                      </button>
                      </div>
                    <div className="mt-5">
                      <div className="my-10 text-center">
                        <input
                          type="number"
                          className='rounded-xl h-12 mx-auto w-56 text-center text-2xl font-bold bg-[#ffffff40]'
                          onChange={(e)=>{setNumOfTicketsTyped(e.target.value)}}
                          value={numOfTicketsTyped}
                        />
                      </div>
                    <div className="grid grid-cols-5 gap-5 max-sm:gap-1">
                    {numbers.map((num, i) => (
                      <div
                        key={i}
                        className="col-span-1 text-BP-opacited-white max-sm:text-sm text-lg text-center font-bold bg-[#384152] w-full px-5 py-2 rounded-xl hover:bg-BP-gold cursor-pointer"
                        onClick={() => setNumOfTicketsTyped(num)}
                      >
                        {num}
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-center items-center py-5">
                    <button onClick={()=>{numberClicked(numOfTicketsTyped),setShowTicketQtyModal(false)}} className='bg-BP-gold hover:bg-[#7e7e7e] rounded-full px-20 py-1 text-BP-black font-semibold text-lg'>Select</button>
                  </div>
                    </div>
                  </div>
                </div>
                </div>
      }

    </div>
  );
};

export default PurchaseTickets;