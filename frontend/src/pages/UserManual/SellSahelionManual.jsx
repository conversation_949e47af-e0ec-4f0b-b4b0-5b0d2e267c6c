import React from 'react';
import sellingSahelionImage from '../../assets/UserManual/sell.png';
import sellInterfaceImage from '../../assets/UserManual/sellInterface.png';
import sellAmountImage from '../../assets/UserManual/sellAmount.png'; 
import spendingCapImage from '../../assets/UserManual/spendingCap.png';

const SellSahelionManual = () => {
  return (
    <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">
      {/* <h1 className="text-2xl font-bold text-BP-gold mb-4">
        Selling Sahelion
      </h1> */}
      
      <div className="flex justify-center mb-6">
        <img
          src={sellingSahelionImage || sellInterfaceImage}
          alt="Selling Sahelion"
          className="rounded-lg w-[90vw] max-w-2xl border border-gray-700"
        />
      </div>
      
      <p className="mb-4">
        Follow these steps to sell your Sahelion tokens through the dashboard:
      </p>
      
      <ol className="list-decimal list-inside space-y-2">
        <li>
          Click the{' '}
          <span className="bg-[#1e2a3a] text-white px-3 py-1 rounded-md">
            Sell
          </span>{' '}
          button on the dashboard
        </li>
        <li>Connect your wallet if not already connected</li>
        <li>Enter the amount of SHLN you want to sell (or click MAX to sell all)</li>
        <li>Select the cryptocurrency you want to receive (currently USDT)</li>
        <li>Review the amount you'll receive and any applicable fees</li>
        <li>Click the "Confirm sell" button to proceed with your sale</li>
        <li>Approve the transaction in your wallet when prompted</li>
      </ol>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Sell Interface:</h3>
        <p className="mb-4">When you click the Sell button, you'll see this interface:</p>
        
        <div className="flex justify-center mb-6">
          <img
            src={sellAmountImage || sellInterfaceImage}
            alt="Sell Sahelion Interface with Amount"
            className="rounded-lg w-[90vw] max-w-xl border border-gray-700"
          />
        </div>
        
        <ul className="list-disc list-inside space-y-2">
          <li>At the top, you'll see your wallet address</li>
          <li>The exchange rate is displayed: 1 SHLN = $1.00 USD</li>
          <li>You can choose between "Crypto" and "Cash" payment methods (Cash is coming soon)</li>
          <li>Enter the amount of SHLN you want to sell in the "Amount to Sell" field</li>
          <li>You can click the "MAX" button to automatically enter your entire SHLN balance</li>
          <li>Your available balance is shown below the input field (e.g., "Available: 150.0 SHLN")</li>
          <li>Select USDT from the "Receive as Crypto" dropdown</li>
          <li>The "You'll receive" section shows how much USDT you'll get and any applicable fees</li>
          <li>Click the yellow "Confirm sell" button to proceed with the sale</li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Wallet Approval Process:</h3>
        <p className="mb-4">After clicking the "Confirm sell" button, you'll need to approve the transaction in your wallet:</p>
        
        <div className="flex justify-center mb-6">
          <img
            src={spendingCapImage}
            alt="Spending Cap Request"
            className="rounded-lg w-[90vw] max-w-xl border border-gray-700"
          />
        </div>
        
        <ul className="list-disc list-inside space-y-2">
          <li>Your wallet (e.g., MetaMask) will show a "Spending cap request" popup</li>
          <li>This gives the platform permission to withdraw your SHLN tokens</li>
          <li>You'll see the exact amount you're approving (e.g., 30 SHLN)</li>
          <li>Review the details including the spender address and request source</li>
          <li>Click "Confirm" to proceed with the approval</li>
          <li>After approving, you'll need to confirm the actual sale transaction</li>
        </ul>
        
        <div className="bg-[#111827] p-4 rounded-lg mt-4 border-l-4 border-yellow-500">
          <p className="text-yellow-500 font-semibold">NB: To complete your sale, you will need to confirm two transactions in your wallet:</p>
          <ol className="list-decimal list-inside ml-6 mt-1 space-y-1 text-yellow-500">
            <li>Approve this platform to spend your SHLN tokens.</li>
            <li>Confirm the actual sale transaction.</li>
          </ol>
        </div>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Selling Options:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>
            <span className="text-BP-gold font-semibold">Sell for Crypto:</span> You can sell Sahelion for USDT
            <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
              <li>Enter the amount of SHLN you want to sell</li>
              <li>You can click "MAX" to sell your entire SHLN balance</li>
              <li>The equivalent amount of USDT will be calculated automatically (1 SHLN = $1.00 USD)</li>
              <li>A small fee (approximately 1%) may be deducted from your sale</li>
              <li>You'll need to approve two transactions in your wallet:
                <ol className="list-decimal list-inside ml-6 mt-1 space-y-1">
                  <li>Approve the platform to spend your SHLN</li>
                  <li>Confirm the actual sale transaction</li>
                </ol>
              </li>
            </ul>
          </li>
          <li>
            <span className="text-BP-gold font-semibold">Sell for Fiat (Cash):</span> This option is coming soon
            <p className="ml-6 mt-1 text-yellow-400">Note: The fiat payment option may show a "Coming Soon" message as it's still under development.</p>
          </li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">After Successful Sale:</h3>
        <p className="mb-4">Once your sale is complete:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>You'll see a success notification</li>
          <li>Your SHLN balance will be updated in the dashboard</li>
          <li>The USDT will be transferred to your wallet</li>
          <li>The transaction will typically complete in 2-5 minutes</li>
          <li>You can view your updated balance when you return to the main dashboard</li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Important Notes:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>You must connect your wallet before selling Sahelion</li>
          <li>You can only sell the amount of SHLN that you actually have in your wallet</li>
          <li>Crypto transactions typically complete in 2-5 minutes</li>
          <li>A small fee may be deducted from your sale amount</li>
          <li>Always verify transaction details before confirming</li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Switching Between Buy and Sell:</h3>
        <p className="mb-4">You can easily switch between buying and selling Sahelion:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>When on the Sell page, click the "Buy Sahelion" button at the top right to switch to buying</li>
          <li>When on the Buy page, click the "Sell Sahelion" button at the top right to switch to selling</li>
          <li>You can also return to the main dashboard by clicking the "Back" button at the top left</li>
        </ul>
      </div>
    </div>
  );
};

export default SellSahelionManual;



