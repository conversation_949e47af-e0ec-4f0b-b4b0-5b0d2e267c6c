import cnctWBtn from '../../assets/UserManual/cnctWBtn.png';
import mmCnctBtn from '../../assets/UserManual/mmCnctBtn.png';
import mmOpt from '../../assets/UserManual/mmOpt.png';
import cnctd from '../../assets/UserManual/cnctd.png';

export const MetaMaskConnectManual = () => {
  return (
    <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">

      <ol className="list-decimal list-inside space-y-5 text-base text-BP-opacited-white">

        <li>
          Navigate to any section of the platform (e.g., <span className="text-BP-gold font-semibold">Presale</span>) and click the
          <span className="text-BP-gold font-semibold"> “Connect Wallet” </span> button.
          <div className="flex justify-center mt-3">
            <img src={cnctWBtn} alt="Connect Wallet Button" className="rounded-lg w-[90vw] max-w-2xl border border-white/10 shadow" />
          </div>
        </li>

        <li>
          A pop-up will appear with multiple wallet options. Select
          <span className="text-BP-gold font-semibold"> MetaMask </span> from the list.
          <div className="flex justify-center mt-3">
            <img src={mmOpt} alt="Select MetaMask Option" className="rounded-lg w-[90vw] max-w-2xl border border-white/10 shadow" />
          </div>
        </li>

        <li>
          If you are <span className="text-BP-gold font-semibold">not logged in</span> to MetaMask, a password prompt will appear.
          Enter your password to unlock the wallet.
          <br />
          If already logged in, you will directly be shown a <span className="text-BP-gold font-semibold">Connect Request</span> interface.
          <div className="flex justify-center mt-3">
            <img src={mmCnctBtn} alt="MetaMask Connect Prompt" className="rounded-lg w-[90vw] max-w-2xl border border-white/10 shadow" />
          </div>
        </li>

        <li>
          Click the <span className="bg-[rgb(68_89_255)] text-white px-4 py-1.5 rounded-full">Connect</span> button in the MetaMask prompt.
          This authorizes the platform to interact with your wallet address.
        </li>

        <li>
          Once connected, the wallet button changes to
          <span className="text-BP-gold font-semibold"> “Disconnect”</span>. Your wallet address will also appear on the left-hand side.
          <div className="flex justify-center mt-3">
            <img src={cnctd} alt="Connected Wallet State" className="rounded-lg w-[90vw] max-w-2xl border border-white/10 shadow" />
          </div>
        </li>

      </ol>

      <p className="text-base text-BP-opacited-white italic">
        You're now ready to interact with the platform securely using MetaMask! 🔐
      </p>
    </div>
  );
};
