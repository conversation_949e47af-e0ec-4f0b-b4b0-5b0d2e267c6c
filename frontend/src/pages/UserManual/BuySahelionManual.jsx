import React from 'react';
import buyingSahelionImage from '../../assets/UserManual/buy.png';
import sahelionPageImage from '../../assets/UserManual/sahelionpage.png';
import buyInterfaceImage from '../../assets/UserManual/buyInterface.png';
import buyInterfaceWithBalanceImage from '../../assets/UserManual/buyInterfaceWithBalance.png';
import spendingCapRequestImage from '../../assets/UserManual/spendingCapRequest.png';

const BuySahelionManual = () => {
  return (
    <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">
      {/* <h1 className="text-2xl font-bold text-BP-gold mb-4">
        Buying Sahelion
      </h1> */}
      
      <div className="flex justify-center mb-6">
        <img
          src={buyingSahelionImage || sahelionPageImage}
          alt="Buying Sahelion"
          className="rounded-lg w-[90vw] max-w-2xl border border-gray-700"
        />
      </div>
      
      <p className="mb-4">
        Follow these steps to buy Sahelion tokens through the dashboard:
      </p>
      
      <ol className="list-decimal list-inside space-y-2">
        <li>
          Click the{' '}
          <span className="bg-purple-600 text-white px-3 py-1 rounded-md">
            Buy
          </span>{' '}
          button on the dashboard
        </li>{' '}
        <li>Connect your wallet if not already connected</li>
        <li>Enter the amount of USDT you want to spend</li>
        <li>You'll see the equivalent amount of SHLN you'll receive (1 SHLN = $1.00 USD)</li>
        <li>Click the "Click to Buy X SHLN" button to proceed with your purchase</li>
      </ol>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Wallet Approval Process:</h3>
        <p className="mb-4">When you click the Buy button, you'll see this interface:</p>
        
        <div className="flex justify-center mb-6">
          <img
            src={buyInterfaceWithBalanceImage}
            alt="Buy Sahelion Interface with Balance"
            className="rounded-lg w-[90vw] max-w-xl border border-gray-700"
          />
        </div>
        
        <ul className="list-disc list-inside space-y-2">
          <li>At the top, you'll see your wallet address and current SHLN balance (e.g., 150.0 SHLN)</li>
          <li>The exchange rate is displayed: 1 SHLN = $1.00 USD</li>
          <li>You can choose between "Crypto" and "Cash" payment methods (Cash is coming soon)</li>
          <li>Select USDT from the "Pay with" dropdown</li>
          <li>Enter your desired amount in the "Amount" field</li>
          <li>The "Sahelion" section shows "Enter amount to see conversion" until you enter an amount</li>
          <li>After entering an amount, the yellow-to-purple gradient button will update to show "Click to Buy X SHLN"</li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Wallet Approval Process(gas fees):</h3>
        <p className="mb-4">After clicking the "Buy" button, you'll need to approve the spending cap in your wallet:</p>
        
        <div className="flex justify-center mb-6">
          <img
            src={spendingCapRequestImage}
            alt="Spending Cap Request"
            className="rounded-lg w-[90vw] max-w-xl border border-gray-700"
          />
        </div>
        
        <ul className="list-disc list-inside space-y-2">
          <li>Your wallet will show a "Spending cap request" popup</li>
          <li>This gives the platform permission to withdraw your tokens</li>
          <li>You'll see the exact amount you're approving (e.g., 50 USDT)</li>
          <li>Review the details including the spender address and request source</li>
          <li>You'll see gas fees information in your wallet interface showing the estimated cost of the transaction</li>
          <li>The gas fees are paid in the native token of the blockchain (e.g., ETH for Ethereum, BNB for Binance Smart Chain)</li>
          <li>You can adjust gas settings in your wallet if needed (standard, fast, or custom)</li>
          <li>Click the blue "Confirm" button in the bottom right of your wallet popup to proceed with the approval</li>
          <li>After approving, you'll need to confirm a second transaction for the actual purchase</li>
        </ul>
        
        <div className="bg-[#111827] p-4 rounded-lg mt-4 border-l-4 border-yellow-500">
          <p className="text-yellow-500 font-semibold">NB: To complete your purchase, you will need to confirm two transactions in your wallet:</p>
          <ol className="list-decimal list-inside ml-6 mt-1 space-y-1 text-yellow-500">
            <li>Approve this platform to spend your selected token (e.g., USDT) - first "Confirm" button click.</li>
            <li>Confirm the actual purchase of SHLN - second "Confirm" button click after the first transaction is processed.</li>
          </ol>
          <p className="text-yellow-500 mt-2">Note: Each transaction will require gas fees to be paid in the native token of the blockchain (e.g., ETH for Ethereum, BNB for Binance Smart Chain). Make sure you have enough of the native token to cover these fees.</p>
        </div>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">After Successful Purchase:</h3>
        <p className="mb-4">Once your purchase is complete:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>You'll see a success notification</li>
          <li>Your SHLN balance will be updated in the dashboard</li>
          <li>The transaction will typically complete in 2-5 minutes</li>
          <li>You can view your updated balance at the top of the Buy interface (e.g., "Wallet: 0x6D00...aC8D (150.0 SHLN)")</li>
          <li>You can now return to the main dashboard or make additional purchases</li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Payment Methods:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>
            <span className="text-BP-gold font-semibold">Crypto:</span> You can buy Sahelion using USDT
            <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
              <li>Enter the amount of USDT you want to spend</li>
              <li>The equivalent amount of SHLN will be calculated automatically (1 SHLN = $1.00 USD)</li>
              <li>You'll need to approve two transactions in your wallet:
                <ol className="list-decimal list-inside ml-6 mt-1 space-y-1">
                  <li>Approve the platform to spend your USDT</li>
                  <li>Confirm the actual purchase of SHLN</li>
                </ol>
              </li>
            </ul>
          </li>
          <li>
            <span className="text-BP-gold font-semibold">Fiat (Cash):</span> This option is coming soon
            <p className="ml-6 mt-1 text-yellow-400">Note: The fiat payment option may show a "Coming Soon" message as it's still under development.</p>
          </li>
        </ul>
      </div>
      
      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Important Notes:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>You must connect your wallet before buying Sahelion</li>
          <li>Crypto transactions typically complete in 2-5 minutes</li>
          <li>The minimum purchase amount may vary</li>
          <li>Always verify transaction details before confirming</li>
        </ul>
      </div>

      <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Switching Between Buy and Sell:</h3>
        <p className="mb-4">You can easily switch between buying and selling Sahelion:</p>
        <ul className="list-disc list-inside space-y-2">
          <li>When on the Sell page, click the "Buy Sahelion" button at the top right to switch to buying</li>
          <li>When on the Buy page, click the "Sell Sahelion" button at the top right to switch to selling</li>
          <li>You can also return to the main dashboard by clicking the "Back" button at the top left</li>
        </ul>
      </div>
    </div>
  );
};

export default BuySahelionManual;








