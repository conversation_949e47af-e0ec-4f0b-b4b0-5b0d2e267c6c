import React, { useEffect } from 'react';
import { ArrowRightOutlined } from '@ant-design/icons';
import sahelionPageImage from '../../assets/UserManual/sahelionpage.png';
import sahelionDashboardImage from '../../assets/UserManual/saheliondashboard.png';
import connectedDashboardImage from '../../assets/UserManual/connectedDashboard.png';

const SahelionManual = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Main Sahelion Dashboard Guide
  return (
    <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">
      {/* Title */}
      {/* <h1 className="text-2xl font-bold text-BP-gold mb-4">
        How to Use the Sahelion Dashboard
      </h1> */}
      {/* Introduction */}
      <div className="bg-gray-800/30 p-4 rounded-lg">
        <p className="text-base text-BP-opacited-white leading-relaxed">
          Sahel<PERSON> is the stablecoin of the Black Panther Ecosystem, pegged 1:1
          to the US dollar and backed by USDT and fiat reserves. This guide will
          help you navigate to and use the Sahelion dashboard.
        </p>
      </div>
      
      {/* Step 1: Accessing the Dashboard */}
      <div>
        <h2 className="text-xl font-semibold text-BP-gold mb-4">
          Step 1: Access the Sahelion Dashboard
        </h2>
        <div className="bg-gray-800/30 p-4 rounded-lg mb-6">
          <h3 className="text-BP-gold font-semibold mb-2">Direct Link:</h3>
          <p>
            You can access the Sahelion dashboard directly by visiting{" "}
            <a
              href="https://www.blackpanthertkn.com/dashboard-sahelion/portfolio"
              className="text-BP-gold underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              blackpanthertkn.com/dashboard-sahelion/portfolio
            </a>
          </p>
        </div>
        <div className="flex justify-center mb-6">
          <img
            src={sahelionDashboardImage}
            alt="Sahelion Page"
            className="rounded-lg w-[90vw] max-w-2xl border border-gray-700"
          />
        </div>
        <p className="mb-4">
          On the Sahelion page, scroll down until you see the "Open Sahelion
          dashboard" button.
        </p>
        <div className="bg-gray-800/30 p-4 rounded-lg mb-6">
          <h3 className="text-BP-gold font-semibold mb-2">Button Location:</h3>
          <p>
            The "Open Sahelion dashboard" button is a yellow button located in between 
            the "Transparency and Trust" and "Get Your Sahelion Today" sections
            when you scroll down the page.
          </p>
        </div>
        <div className="flex items-center space-x-2 mb-6">
          <a 
            href="https://www.blackpanthertkn.com/dashboard-sahelion/portfolio" 
            className="bg-yellow-500 text-black px-4 py-2 text-sm sm:text-base rounded-full font-semibold hover:bg-yellow-400 transition-colors flex-shrink-0 whitespace-nowrap max-w-full overflow-hidden text-ellipsis"
            target="_blank"
            rel="noopener noreferrer"
          >
            Open Sahelion dashboard
          </a>
          <ArrowRightOutlined className="text-BP-gold" />
          <span className="text-BP-opacited-white">
            Click this button to access the dashboard
          </span>
        </div>
      </div>
      
      {/* Step 2: Sahelion Dashboard */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-BP-gold mb-4">
          Step 2: Connect Your Wallet
        </h2>
        <p className="mb-4">
          When you first access the Sahelion Portfolio Dashboard, you'll need to connect your wallet to view your balance and perform transactions.
        </p>
        
        <div className="bg-gray-800/30 p-4 rounded-lg mb-6">
          <h3 className="text-BP-gold font-semibold mb-2">To connect your wallet:</h3>
          <ol className="list-decimal list-inside space-y-2">
            <li>Click the "Connect Wallet" button at the top of the dashboard</li>
            <li>Select your wallet provider (MetaMask, Trust Wallet, etc.)</li>
            <li>Follow the prompts in your wallet to complete the connection</li>
          </ol>
        </div>
        
        <h3 className="text-lg font-semibold text-BP-gold mb-4">
          Dashboard with Connected Wallet
        </h3>
        <div className="flex justify-center mb-6">
          <img
            src={connectedDashboardImage}
            alt="Connected Sahelion Dashboard"
            className="rounded-lg w-[90vw] max-w-2xl border border-gray-700"
          />
        </div>
        <p className="mb-4">
          Once your wallet is connected, you'll see:
        </p>
        <ul className="list-disc list-inside space-y-2">
          <li>Your wallet address at the top (e.g., 0x6D00...aC8D)</li>
          <li>A "Disconnect wallet" button</li>
          <li>Your total SHLN balance displayed prominently</li>
          <li>The USD equivalent of your SHLN balance</li>
          <li>The current price change percentage (e.g., +2.3%)</li>
        </ul>
      </div>
      
      {/* Step 3: Explore the Dashboard */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-BP-gold mb-4">
          Step 3: Explore the Sahelion Portfolio Dashboard (Unconnected Wallets)
        </h2>
        <div className="flex justify-center mb-6">
          <img
            src={sahelionPageImage}
            alt="Sahelion Dashboard"
            className="rounded-lg w-[90vw] max-w-2xl border border-gray-700"
          />
        </div>
        <p className="mb-4">
          The Sahelion Portfolio Dashboard provides an overview of your Sahelion
          holdings and market information.
        </p>
        <div className="space-y-6">
          {/* Dashboard Components */}
          <div className="bg-gray-800/30 p-4 rounded-lg">
            <h3 className="text-BP-gold font-semibold mb-2">
              Dashboard Components:
            </h3>
            <ul className="list-disc list-inside space-y-3">
              <li>
                <span className="text-BP-gold font-semibold">
                  Wallet Connection:
                </span>{" "}
                At the top, you'll see your connected wallet address or a button
                to connect your wallet
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Balance Overview:
                </span>{" "}
                Your total SHLN balance and its USD equivalent
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Price Information:
                </span>{" "}
                Current price of SHLN (pegged at $1.00 USD) and price change
                percentage
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Transaction Buttons:
                </span>{" "}
                "Buy" and "Sell" buttons to initiate transactions
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Transaction History:
                </span>{" "}
                A list of your recent transactions (if any)
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Market Statistics:
                </span>{" "}
                Information about Sahelion's market performance
              </li>
            </ul>
          </div>
          
          {/* Buy and Sell Buttons */}
          <div className="bg-gray-800/30 p-4 rounded-lg">
            <h3 className="text-BP-gold font-semibold mb-2">
              Buy and Sell Buttons:
            </h3>
            <ul className="list-disc list-inside space-y-3">
              <li>
                <span className="text-BP-gold font-semibold">Buy Button:</span>{" "}
                Click this to purchase Sahelion tokens using USDT or other
                supported methods
              </li>
              <li>
                <span className="text-BP-gold font-semibold">
                  Sell Button:
                </span>{" "}
                Click this to sell your Sahelion tokens and receive USDT or other
                supported methods
              </li>
            </ul>
            <p className="mt-3">
              For detailed instructions on buying and selling Sahelion, please
              refer to the dedicated guides in this manual.
            </p>
          </div>
          
          {/* Transaction History */}
          <div className="bg-gray-800/30 p-4 rounded-lg">
            <h3 className="text-BP-gold font-semibold mb-2">
              Transaction History:
            </h3>
            <p>
              The transaction history section shows your recent Sahelion
              transactions, including:
            </p>
            <ul className="list-disc list-inside space-y-2 mt-2">
              <li>Transaction type (Buy/Sell)</li>
              <li>Amount of SHLN</li>
              <li>Date and time of transaction</li>
              <li>Transaction status (Completed/Pending)</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Step 4: Disconnect Your Wallet */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-BP-gold mb-4">
          Step 4: Disconnect Your Wallet (When Finished)
        </h2>
        <div className="bg-gray-800/30 p-4 rounded-lg">
          <p>
            When you're finished using the Sahelion dashboard, it's good practice
            to disconnect your wallet, especially if you're using a shared
            computer:
          </p>
          <ol className="list-decimal list-inside space-y-2 mt-3">
            <li>
              Click the "Disconnect wallet" button at the top of the dashboard
            </li>
            <li>
              Your wallet will be disconnected, and you'll return to the
              dashboard's initial state
            </li>
            <li>
              You can reconnect your wallet at any time by clicking the "Connect
              different address" button
            </li>
          </ol>
        </div>
      </div>
      
      {/* Troubleshooting */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-BP-gold mb-4">
          Troubleshooting
        </h2>
        <div className="bg-gray-800/30 p-4 rounded-lg">
          <h3 className="text-BP-gold font-semibold mb-2">
            Common Issues and Solutions:
          </h3>
          <ul className="list-disc list-inside space-y-3">
            <li>
              <span className="text-BP-gold font-semibold">
                Wallet Won't Connect:
              </span>
              <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
                <li>Refresh the page and try again</li>
                <li>Make sure your wallet extension is up to date</li>
                <li>
                  Check that you're on the correct network (Ethereum Mainnet)
                </li>
              </ul>
            </li>
            <li>
              <span className="text-BP-gold font-semibold">
                Transaction Failed:
              </span>
              <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
                <li>
                  Ensure you have enough ETH for gas fees (for Ethereum
                  transactions)
                </li>
                <li>
                  Check that you have sufficient balance of the token you're
                  trying to use
                </li>
                <li>Try again with a higher gas fee setting in your wallet</li>
              </ul>
            </li>
            <li>
              <span className="text-BP-gold font-semibold">
                Balance Not Showing:
              </span>
              <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
                <li>Disconnect and reconnect your wallet</li>
                <li>Refresh the page</li>
                <li>
                  Check that you're connected with the correct wallet address
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SahelionManual;
