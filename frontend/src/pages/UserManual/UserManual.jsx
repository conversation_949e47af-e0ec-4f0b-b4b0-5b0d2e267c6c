import React, { useState, useEffect, useRef } from 'react';
import WebLogo from '../../assets/images/queenpanther2.png';
import code from '../../assets/UserManual/code.png';
import email from '../../assets/UserManual/email.png';
import pp from '../../assets/UserManual/pp.png';
import signUp from '../../assets/UserManual/signUp.png';
import verify from '../../assets/UserManual/verify.png';
import resend from '../../assets/UserManual/resend.png';
import { useNavigate } from 'react-router-dom';
import { AppRoutesPaths } from '../../route/app_route';
import { CheckCircleOutlined } from '@ant-design/icons';
import LoginManual from './LoginManual';
import SahelionManual from './SahelionManual';
import BuySahelionManual from './BuySahelionManual';
import SellSahelionManual from './SellSahelionManual';
import { MetaMaskConnectManual } from './MetaMaskConnectManual';

const manualData = {

    'How to Sign Up': (
        <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">

            {/* Sign Up Link & Initial Screenshot */}
            <div className="flex items-start space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"
                    className="w-5 h-5 text-BP-gold mt-1">
                    <path strokeLinecap="round" strokeLinejoin="round"
                        d="M13.5 4.5L19.5 10.5M4.5 19.5L10.5 13.5M10.5 13.5L4.5 7.5M10.5 13.5L16.5 19.5" />
                </svg>
                <p className="text-base text-BP-opacited-white leading-relaxed">
                    Click here to open the registration page:&nbsp;
                    <a href="https://www.blackpanthertkn.com/signup"
                        className="underline text-BP-gold hover:text-BP-hovered-yellow transition"
                        rel="noopener noreferrer">
                        blackpanthertkn.com/signup
                    </a>
                </p>
            </div>

            {/* Sign Up Page Image */}
            <div className="flex justify-center">
                <img src={signUp} alt="Sign Up Page" className="rounded-lg w-[90vw] max-w-2xl" />
            </div>

            {/* Steps */}
            <ol className="list-decimal list-inside space-y-5 text-base text-BP-opacited-white">
                <li>
                    Enter your <span className="text-BP-gold font-semibold">First name</span> and <span className="text-BP-gold font-semibold">Last name</span>.
                </li>
                <li>
                    Provide a valid <span className="text-BP-gold font-semibold">Email address</span>.
                </li>
                <li>
                    Create and confirm a <span className="text-BP-gold font-semibold">strong Password</span>.
                    <ul className="list-disc list-inside ml-5 mt-2 space-y-1">
                        <li>Must be at least 8 characters long</li>
                        <li>Include at least one uppercase letter (A-Z)</li>
                        <li>Include at least one lowercase letter (a-z)</li>
                        <li>Include at least one number (0-9)</li>
                        <li>Include at least one special character (e.g., !, @, #, $)</li>
                    </ul>
                </li>
                <li>
                    Click on the <span className="text-BP-gold font-semibold">Privacy Policy</span> link and read it carefully.
                </li>

                {/* Privacy Policy Image */}
                <div className="flex justify-center">
                    <img src={pp} alt="Privacy Policy" className="rounded-lg w-[90vw] max-w-2xl" />
                </div>

                <li>
                    Check the box <span className="text-BP-gold font-semibold">"I agree to the Privacy Policy"</span>.
                </li>
                <li>
                    Click the <span className="w-fit px-5 py-1.5 rounded-full inline-flex bg-purple-600 items-center justify-center"><CheckCircleOutlined className="mr-2" /> Proceed </span> button to continue.
                </li>

                {/* Email Notification Image */}
                <li>
                    You will receive a <span className="text-BP-gold font-semibold">confirmation email</span> similar to the one shown below.
                    <div className="flex justify-center mt-3">
                        <img src={email} alt="Email Notification" className="rounded-lg w-[90vw] max-w-2xl border border-white/30" />
                    </div>
                </li>

                <li>
                    A <span className="text-BP-gold font-semibold">5-digit email verification code</span> will be sent to your inbox.
                </li>

                {/* Code Entry Image */}
                <div className="flex justify-center">
                    <img src={code} alt="Code Entry" className="rounded-lg w-[90vw] max-w-2xl" />
                </div>

                <li>
                    Enter the verification code in the input field and click the <span className="bg-BP-gold text-BP-dark-grayish-blue px-4 py-1.5 rounded-full font-semibold">Verify</span> button.
                    <div className="flex justify-center mt-3">
                        <img src={verify} alt="Verify Page" className="rounded-lg w-[90vw] max-w-2xl" />
                    </div>
                </li>

                <li>
                    If you entered the code incorrectly, or if the code expired, you can click the <span className="text-BP-gold font-semibold">Resend</span> option to receive a new verification code.
                    <div className="flex justify-center mt-3">
                        <img src={resend} alt="Resend Code" className="rounded-lg w-[90vw] max-w-2xl" />
                    </div>
                </li>
            </ol>

            {/* Final Note */}
            <p className="text-base text-BP-opacited-white italic">
                After verification, your account will be successfully created! 🎉
            </p>
        </div>
    ),

    'How to Login': (
        <>
            <LoginManual />
        </>
    ),
    
    '  Forgot Password': (
        <>
            <LoginManual forgotPasswordOnly={true} />
        </>
    ),

    'Connecting wallet through MetaMask': (
        <MetaMaskConnectManual />
    ),
    'How to Use the Sahelion Dashboard': (
        <>
            <SahelionManual />
        </>
    ),
    '  Buying Sahelion': (
        <>
            <BuySahelionManual />
        </>
    ),
    '  Selling Sahelion': (
        <>
            <SellSahelionManual />
        </>
    ),
};

export const UserManual = () => {
    const navigate = useNavigate();
    const [activeTopic, setActiveTopic] = useState('How to Sign Up');
    const [isFirstLoad, setIsFirstLoad] = useState(true);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const contentRef = useRef(null);

    // Scroll to top whenever activeTopic changes
    useEffect(() => {
        window.scrollTo(0, 0);
        if (contentRef.current) {
            contentRef.current.scrollTop = 0;
        }
    }, [activeTopic]);

    // Also scroll to top when component mounts
    useEffect(() => {
        window.scrollTo(0, 0);
    }, []);

    const handleTopicChange = (topic) => {
        setActiveTopic(topic);
        setIsFirstLoad(false);
        setIsSidebarOpen(false); // Auto close on mobile
        
        // Force scroll to top
        window.scrollTo(0, 0);
        if (contentRef.current) {
            contentRef.current.scrollTop = 0;
        }
        
        // Add a small delay to ensure the DOM has updated
        setTimeout(() => {
            window.scrollTo(0, 0);
            if (contentRef.current) {
                contentRef.current.scrollTop = 0;
            }
        }, 50);
    };

    return (
        <div className="flex h-screen" style={{ backgroundColor: '#111828' }}>
            {/* Sidebar (Mobile & Desktop) */}
            <div
                className={`fixed inset-y-0 left-0 z-30 w-64 bg-[#111828] border-r border-gray-700 p-4 transform transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} 
        md:translate-x-0 md:static md:block overflow-y-auto`}
            >
                <div className="flex space-x-2 py-3 mb-5 border-b sticky -top-5 bg-BP-dark-grayish-blue">
                    <img onClick={() => navigate(AppRoutesPaths.home)} src={WebLogo} alt="Black Panther Token" className="h-12 cursor-pointer" />
                    <h2 className="text-xl font-semibold my-auto text-white">User Manual</h2>
                </div>
                <ul className="space-y-2">
                    {Object.keys(manualData).map((topic) => {
                        const isSubtopic = topic.startsWith('  ');
                        
                        return (
                            <li key={topic}>
                                <button
                                    onClick={() => handleTopicChange(topic)}
                                    className={`w-full text-left px-3 py-2 rounded-md transition 
                                        ${activeTopic === topic ? 'bg-gray-700' : 'hover:bg-gray-800'}
                                        ${isSubtopic ? 'pl-8' : ''} text-white`}
                                >
                                    {isSubtopic && <span className="text-xs mr-2">↳</span>}
                                    {topic}
                                </button>
                            </li>
                        );
                    })}
                </ul>
            </div>

            {/* Overlay for mobile */}
            {isSidebarOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
                    onClick={() => setIsSidebarOpen(false)}
                ></div>
            )}

            {/* Main content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Topbar */}
                <div className="md:hidden p-4 border-b border-gray-700 flex items-center justify-between">
                    <img onClick={() => navigate(AppRoutesPaths.home)} src={WebLogo} alt="Black Panther Token" className="h-10" />
                    <button onClick={() => setIsSidebarOpen(true)} className="text-white focus:outline-none">
                        <svg
                            className="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                {/* Render JSX content directly */}
                <div ref={contentRef} className="flex-1 overflow-y-auto p-6 text-white">
                    <h2 className="text-2xl font-bold mb-4">{activeTopic}</h2>
                    {isFirstLoad ? (
                        <div className="flex flex-col items-center justify-center h-full text-center space-y-6">
                            <img src={WebLogo} alt="Black Panther Token" className="h-28 w-auto" />
                            <h2 className="text-3xl font-bold text-white">Welcome to the User Manual</h2>
                            <p className="text-lg text-BP-opacited-white max-w-xl">
                                This manual will guide you through everything you need to know about using the Black Panther Token platform. Select a topic from the sidebar to get started!
                            </p>
                        </div>
                    ) : (
                        manualData[activeTopic]
                    )}
                </div>
            </div>
        </div>
    );
};
