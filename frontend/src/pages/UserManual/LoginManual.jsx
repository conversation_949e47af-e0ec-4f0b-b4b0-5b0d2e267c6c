import React from 'react';
import { CheckCircleOutlined } from '@ant-design/icons';
import loginImage from '../../assets/UserManual/login.png';
import forgotPasswordImage from '../../assets/UserManual/forget.png';
import verifyCodeImage from '../../assets/UserManual/verifycode20.png';
import createPasswordImage from '../../assets/UserManual/verifycode.png';
import create from '../../assets/UserManual/createpassword.png';

const LoginManual = ({ forgotPasswordOnly = false }) => {
  if (forgotPasswordOnly) {
    return (
      <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">
        {/* Forgot Password Section */}
        {/* <h1 className="text-2xl font-bold text-BP-gold mb-4">Forgot Password</h1> */}
        
        {/* Forgot Password Image */}
        <div className="flex justify-center mb-6">
          <img src={forgotPasswordImage} alt="Forgot Password Page" className="rounded-lg w-[90vw] max-w-2xl" />
        </div>
        
        <p className="mb-4">If you've forgotten your password, follow these steps to reset it:</p>
        
        <ol className="list-decimal list-inside space-y-5 text-base text-BP-opacited-white">
          <li>
            Click the <span className="text-BP-gold font-semibold">"Forgot password?"</span> link on the login page.
          </li>
          <li>
            Enter your <span className="text-BP-gold font-semibold">Email address</span> in the field provided.
          </li>
          <li>
            Click the <span className="w-fit px-5 py-1.5 rounded-full inline-flex bg-purple-600 items-center justify-center text-white">Reset Password</span> button.
          </li>
          <li>
            A verification code will be sent to your email. You'll see a success message confirming this.
          </li>
          
          {/* Verify Code Section */}
          <li>
            You'll be presented with a verification screen where you need to enter the 5-digit code sent to your email.
            <div className="flex justify-center mt-3 mb-3">
              <img src={verifyCodeImage} alt="Verify Reset Code" className="rounded-lg w-[90vw] max-w-2xl" />
            </div>
            <ul className="list-disc list-inside ml-5 mt-2 space-y-1">
              <li>Enter the 5-digit code in the input boxes</li>
              <li>Click the <span className="bg-yellow-500 text-black px-4 py-1 rounded-md font-semibold">Verify Code</span> button</li>
              <li>If you haven't received the code, click <span className="text-yellow-400 font-semibold">Resend Code</span> to request a new one</li>
            </ul>
          </li>
          
          {/* Create New Password Section */}
          <li>
            After successful verification, you'll be directed to create a new password.
            <div className="flex justify-center mt-3 mb-3">
              <img src={createPasswordImage} alt="Create New Password" className="rounded-lg w-[90vw] max-w-2xl" />
            </div>
            <ul className="list-disc list-inside ml-5 mt-2 space-y-1">
              <li>Enter a new password that meets all the security requirements:</li>
              <ul className="list-none ml-8 mt-1 space-y-1">
                <li><span className="text-green-500">✓</span> At least 8 characters</li>
                <li><span className="text-green-500">✓</span> At least one uppercase letter</li>
                <li><span className="text-green-500">✓</span> At least one lowercase letter</li>
                <li><span className="text-green-500">✓</span> At least one number</li>
                <li><span className="text-green-500">✓</span> At least one special character</li>
              </ul>
              <li>Confirm your new password by typing it again</li>
              <li>Click the <span className="bg-purple-600 text-white px-4 py-1 rounded-md font-semibold">Change Password</span> button</li>
              <li><span className="text-red-400 font-semibold">Important:</span> Your new password must be different from your current password</li>
              <div className="flex justify-center mt-3 mb-3">
                <img src={create} alt="Create New Password" className="rounded-lg w-[90vw] max-w-2xl object-contain" />
              </div>
            </ul>
          </li>
          
          <li>
            After successfully changing your password, you'll be redirected to the login page where you can sign in with your new password.
          </li>
        </ol>
        
        <div className="bg-gray-800/30 p-4 rounded-lg mt-6">
          <h3 className="text-BP-gold font-semibold mb-2">Tips:</h3>
          <ul className="list-disc list-inside space-y-2">
            <li>If you don't receive the verification code, check your spam folder.</li>
            <li>You can request a new code by clicking the "Resend Code" button on the verification screen.</li>
            <li>Your new password must meet all the security requirements and cannot be the same as your current password.</li>
            <li>For security reasons, you'll need to complete all steps in one session. If you close your browser, you may need to restart the process.</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className="text-BP-opacited-white p-6 rounded-xl space-y-6 font-body text-base">
      {/* Login Section */}
      {/* <h1 className="text-2xl font-bold text-BP-gold mb-4">How to Login</h1> */}
      
      {/* Login Link & Instructions */}
      <div className="flex items-start space-x-2">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none"
          viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor"
          className="w-5 h-5 text-BP-gold mt-1">
          <path strokeLinecap="round" strokeLinejoin="round"
            d="M13.5 4.5L19.5 10.5M4.5 19.5L10.5 13.5M10.5 13.5L4.5 7.5M10.5 13.5L16.5 19.5" />
        </svg>
        <p className="text-base text-BP-opacited-white leading-relaxed">
          Click here to open the login page:&nbsp;
          <a href="https://www.blackpanthertkn.com/login"
            className="underline text-BP-gold hover:text-BP-hovered-yellow transition"
            rel="noopener noreferrer">
            blackpanthertkn.com/login
          </a>
        </p>
      </div>

      {/* Login Page Image */}
      <div className="flex justify-center">
        <img src={loginImage} alt="Login Page" className="rounded-lg w-[90vw] max-w-2xl" />
      </div>

      {/* Steps */}
      <ol className="list-decimal list-inside space-y-5 text-base text-BP-opacited-white">
        <li>
          Enter your <span className="text-BP-gold font-semibold">Email address</span> in the email field.
        </li>
        <li>
          Enter your <span className="text-BP-gold font-semibold">Password</span> in the password field.
          <ul className="list-disc list-inside ml-5 mt-2 space-y-1">
            <li>Click the eye icon to show/hide your password</li>
          </ul>
        </li>
        <li>
          Click the <span className="w-fit px-5 py-1.5 rounded-full inline-flex bg-purple-600 items-center justify-center text-white"><CheckCircleOutlined className="mr-2" /> Sign in </span> button to log in.
        </li>
        <li>
          If you've forgotten your password, click the <span className="text-BP-gold font-semibold">"Forgot password?"</span> link below the sign-in button.
        </li>
      </ol>

      {/* Additional Information */}
      <div className="bg-gray-800/30 p-4 rounded-lg mt-4">
        <h3 className="text-BP-gold font-semibold mb-2">Important Notes:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>After successful login, you'll be redirected to your dashboard or the page you were trying to access.</li>
          <li>If you don't have an account yet, click the <span className="text-blue-600 font-semibold">SIGN UP</span> link at the top of the login form.</li>
          <li>For security reasons, you'll be automatically logged out after a period of inactivity.</li>
        </ul>
      </div>

      {/* Troubleshooting */}
      <div className="mt-6">
        <h3 className="text-BP-gold font-semibold mb-2">Troubleshooting:</h3>
        <ul className="list-disc list-inside space-y-2">
          <li>If you can't log in, make sure your email and password are correct.</li>
          <li>Check that your account has been verified through the email verification process.</li>
          <li>If you continue to experience issues, please contact support.</li>
        </ul>
      </div>
    </div>
  );
};

export default LoginManual;
