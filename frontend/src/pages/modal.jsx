import { useState } from "react";
import QRCode from "react-qr-code";

const DepositModal = ({ walletAddress, onClose }) => {
  const [copied, setCopied] = useState(false);

  // Copy Wallet Address
  const handleCopy = () => {
    navigator.clipboard.writeText(walletAddress);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-96">
        {/* Modal Title */}
        <h2 className="text-xl font-bold text-black">Token Contract Address</h2>

        {/* Wallet Address */}
        <div className="flex items-center justify-between bg-gray-200 p-2 rounded-md mt-4">
          <p className="text-sm break-all text-black">{walletAddress}</p>
          <button onClick={handleCopy} className="ml-2 text-gls-darkgreen">
            {copied ? "✅" : "📋"}
          </button>
        </div>

        {/* QR Code */}
        <div className="flex justify-center mt-4">
          <QRCode value={walletAddress} size={150} />
        </div>

        {/* Close Button */}
        <button onClick={onClose} className="mt-4 bg-gls-darkgreen text-black px-4 py-2 rounded-md w-full">
          Close
        </button>
      </div>
    </div>
  );
};

export default DepositModal;