import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthentication } from "../components/utils/provider";
import donation from "../assets/images/donation.png";
import service from '../assets/images/service.png';
import blueprint from '../assets/images/blueprint.png';
import PantherDao from '../assets/newBPlogo.png';
import { ContributionService } from '../services/ContributionService.js';
import { FaChartPie, FaDatabase, FaChartLine } from 'react-icons/fa';

const DaoTransactions = () => {
  const [activeFilter, setActiveFilter] = useState('All');
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [cashAtHand, setCashAtHand] = useState(null); // <-- Add this state
  const filters = ['All', 'Income', 'Service payment', 'Project funding'];
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthentication();

  useEffect(() => {
    if (isAuthenticated) {
      setLoading(true);
      // Fetch both transactions and cash at hand
      Promise.all([
        ContributionService.getUserContributionHistory(),
        ContributionService.getCurrentContribution()
      ])
        .then(([historyResult, cashResult]) => {
          // Transactions
          if (historyResult.success && historyResult.data && historyResult.data.payments) {
            setTransactions(
              (historyResult.data.payments || []).map((item, idx) => ({
                id: item.id || idx,
                type: item.type || 'Income',
                description: item.description || '',
                date: item.date || '',
                amount: item.amount || 0,
                icon:
                  item.type === 'Income'
                    ? donation
                    : item.type === 'Service payment'
                    ? service
                    : item.type === 'Project funding'
                    ? blueprint
                    : donation,
              }))
            );
          } else {
            setTransactions([]);
          }
          // Cash at hand
          if (cashResult.success && cashResult.data && cashResult.data.amount) {
            setCashAtHand(cashResult.data.amount);
          } else {
            setCashAtHand(null);
          }
        })
        .catch(() => {
          setTransactions([]);
          setCashAtHand(null);
        })
        .finally(() => setLoading(false));
    } else {
      setTransactions([]);
      setCashAtHand(null);
      setLoading(false);
    }
  }, [isAuthenticated]);

  const filteredTransactions =
    activeFilter === 'All'
      ? transactions
      : transactions.filter((transaction) => transaction.type === activeFilter);

  const handleLoginToView = () => {
    navigate('/login');
  };

  return (
    <div className="bg-BP-lightbaige min-h-screen">
      {isAuthenticated ? (
        <div className="max-w-full mx-auto px-2 sm:px-4 md:px-6 py-8">
          <div className="flex justify-center mb-4">
            <img
              src={PantherDao}
              alt="DAO Transactions"
              className="h-12 sm:h-16 md:h-20 lg:h-24 w-auto"
            />
          </div>
          <div className="flex justify-center mb-8">
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-bold text-black text-center">
              DAO Transactions
            </h1>
          </div>
          <div className="mb-4 sm:mb-6"></div>
          {/* Only show Cash at hand if it exists and is > 0 */}
          {cashAtHand && Number(cashAtHand) > 0 && (
            <div className="bg-white border rounded-[20px] sm:rounded-[30px] shadow-md p-4 sm:p-8 flex  sm:flex-row justify-between items-center mb-4 z-10 relative w-full max-w-full md:w-[1064px] h-auto sm:h-[140px] mx-auto">
              <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600">Cash at hand</h2>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-black">
                ${cashAtHand}
              </p>
            </div>
          )}
          {/* Filter tabs */}
          {transactions.length > 0 && (
          <div className="overflow-x-auto scrollbar-hide mb-8 sm:mb-10 mt-6 sm:mt-10 pb-1 flex justify-center w-full">
            <div className="relative w-full max-w-full md:w-[1064px] lg:w-[1050px] h-[56px] sm:h-[70px] mx-auto hidden md:flex">
              <button
                onClick={() => setActiveFilter('All')}
                className={`rounded-[40px] text-base font-semibold transition-all ${
                  activeFilter === 'All' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                }`}
                style={{
                  width: '197px',
                  height: '70px',
                  border: activeFilter !== 'All' ? '1px solid #00000033' : undefined,
                  marginRight: '16px',
                }}
              >
                All
              </button>
              <button
                onClick={() => setActiveFilter('Income')}
                className={`rounded-[40px] text-base font-semibold transition-all ${
                  activeFilter === 'Income' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                }`}
                style={{
                  width: '197px',
                  height: '70px',
                  border: activeFilter !== 'Income' ? '1px solid #00000033' : undefined,
                  marginRight: '16px',
                  marginLeft: '16px',
                }}
              >
                Income
              </button>
              <button
                onClick={() => setActiveFilter('Service payment')}
                className={`rounded-[40px] text-base font-semibold transition-all ${
                  activeFilter === 'Service payment' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                }`}
                style={{
                  width: '274px',
                  height: '70px',
                  border: activeFilter !== 'Service payment' ? '1px solid #00000033' : undefined,
                  marginRight: '16px',
                  marginLeft: '16px',
                }}
              >
                Service payment
              </button>
              <button
                onClick={() => setActiveFilter('Project funding')}
                className={`rounded-[40px] text-base font-semibold transition-all ml-auto ${
                  activeFilter === 'Project funding' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                }`}
                style={{
                  width: '249px',
                  height: '70px',
                  border: activeFilter !== 'Project funding' ? '1px solid #00000033' : undefined,
                }}
              >
                Project funding
              </button>
            </div>
            {/* Mobile/Tablet: horizontal scrollable buttons */}
            <div className="flex md:hidden gap-1 sm:gap-2 w-full">
              {filters.map((filter) => (
                <button
                  key={filter}
                  onClick={() => setActiveFilter(filter)}
                  className={`flex-1 min-w-[70px] sm:min-w-[90px] px-1 sm:px-2 py-1 sm:py-2 rounded-[30px] text-[10px] sm:text-xs font-semibold transition-all ${
                    activeFilter === filter ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={activeFilter !== filter ? { border: '1px solid #00000033' } : {}}
                >
                  {filter}
                </button>
              ))}
            </div>
          </div>
          )}
          {/* Transactions list */}
          <div className="space-y-3 sm:space-y-4 md:space-y-6 flex flex-col items-center w-full max-w-full md:w-[1064px] lg:w-[1200px] mx-auto">
            {!loading &&
              filteredTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="bg-white border rounded-[20px] sm:rounded-[30px] shadow-md p-4 sm:p-8 flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-6 w-full max-w-full md:w-[1064px] h-auto sm:h-[140px] mx-auto"
                >
                  <div className="flex items-center gap-2 sm:gap-3 md:gap-4 mb-2 sm:mb-0">
                    <img src={transaction.icon} alt={transaction.type} className="w-7 sm:w-8 md:w-10 h-7 sm:h-8 md:h-10" />
                    <div>
                      <h3 className="text-base sm:text-lg md:text-2xl font-bold text-black">{transaction.type}</h3>
                      <p className="text-gray-600 text-xs sm:text-base md:text-lg">{transaction.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-500 text-xs sm:text-base md:text-lg">{transaction.date}</p>
                    <p className={`text-lg sm:text-xl md:text-2xl font-bold ${transaction.amount > 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {transaction.amount > 0 ? '+' : '-'}${Math.abs(transaction.amount)}
                    </p>
                  </div>
                </div>
              ))}
          </div>
          {/* Empty state if no transactions match filter */}
          {!loading && filteredTransactions.length === 0 && (
           <div className="text-center">
  {/* Icon section */}
  <div className="flex justify-center mb-6">
    <div className="bg-green-100 rounded-full flex items-center justify-center w-[140px] h-[140px] sm:w-[180px] sm:h-[180px] md:w-[200px] md:h-[200px]">
      <FaChartPie className="text-green-700 w-[48px] h-[48px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px]" />
    </div>
  </div>

  {/* Title */}
  <h1 className="font-poppins font-normal text-[24px] sm:text-[32px] md:text-[40px] leading-[1] tracking-[0] text-[#2C5282]">
    No Community Transactions
  </h1>

  {/* Description */}
  <p className="font-poppins font-light text-[16px] sm:text-[20px] md:text-[24px] leading-[1] tracking-[0] text-[#4B5563] mt-2 max-w-xs sm:max-w-lg md:max-w-xl mx-auto">
    When your community makes expenditures or receives funds, the transaction history will appear here.
  </p>

  {/* Info cards */}
  <div className="mt-8 space-y-4">
    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 bg-white p-4 sm:p-6 rounded-[16px] sm:rounded-[20px] shadow-sm text-left w-full max-w-xs sm:max-w-2xl md:w-[599px] md:h-[194px] mx-auto">
      <div className="flex items-center justify-center bg-[#4FD1C54D] rounded-[8px] sm:rounded-[10px] w-[70px] h-[70px] sm:w-[90px] sm:h-[90px] md:w-[100px] md:h-[100px] min-w-[70px] min-h-[70px] sm:min-w-[90px] sm:min-h-[90px] md:min-w-[100px] md:min-h-[100px]">
        <FaDatabase className="text-teal-700 w-[28px] h-[28px] sm:w-[36px] sm:h-[36px] md:w-[40px] md:h-[40px]" />
      </div>
      <div className="flex flex-col justify-center flex-1">
        <h2 className="font-poppins font-normal text-[20px] sm:text-[28px] md:text-[32px] leading-[1] tracking-[0] text-slate-900">
          Transparent Records
        </h2>
        <p className="font-poppins font-light text-[14px] sm:text-[18px] md:text-[24px] leading-[1] tracking-[0] text-gray-600 mt-2">
          All community spending will be recorded here
        </p>
      </div>
    </div>

    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 bg-white p-4 sm:p-6 rounded-[16px] sm:rounded-[20px] shadow-sm text-left w-full max-w-xs sm:max-w-2xl md:w-[599px] md:h-[194px] mx-auto">
      <div className="flex items-center justify-center bg-blue-100 rounded-[8px] sm:rounded-[10px] w-[70px] h-[70px] sm:w-[90px] sm:h-[90px] md:w-[100px] md:h-[100px] min-w-[70px] min-h-[70px] sm:min-w-[90px] sm:min-h-[90px] md:min-w-[100px] md:min-h-[100px]">
        <FaChartLine className="text-blue-700 w-[28px] h-[28px] sm:w-[36px] sm:h-[36px] md:w-[40px] md:h-[40px]" />
      </div>
      <div className="flex flex-col justify-center flex-1">
        <h2 className="font-poppins font-normal text-[20px] sm:text-[28px] md:text-[32px] leading-[1] tracking-[0] text-slate-900">
          Financial Overview
        </h2>
        <p className="font-poppins font-light text-[14px] sm:text-[18px] md:text-[24px] leading-[1] tracking-[0] text-gray-600 mt-2">
          Track your community's financial health
        </p>
      </div>
    </div>
  </div>
</div>
          )}
        </div>
      ) : (
        // Restricted view for non-authenticated users
        <div className="bg-BP-lightbaige min-h-screen flex flex-col items-center py-6 sm:py-8">
          <img src={PantherDao} alt="DAO Transactions" className="h-14 sm:h-20 w-auto mb-4" />
          <h1 className="text-2xl sm:text-4xl font-bold text-black mb-4">DAO Transactions</h1>
          {!loading && filteredTransactions.length === 0 && (
           <div className="text-center">
  {/* Icon section */}
  <div className="flex justify-center mb-6">
    <div className="bg-green-100 rounded-full flex items-center justify-center w-[140px] h-[140px] sm:w-[180px] sm:h-[180px] md:w-[200px] md:h-[200px]">
      <FaChartPie className="text-green-700 w-[48px] h-[48px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px]" />
    </div>
  </div>

  {/* Title */}
  <h1 className="font-poppins font-normal text-[24px] sm:text-[32px] md:text-[40px] leading-[1] tracking-[0] text-[#2C5282]">
    No Community Transactions
  </h1>

  {/* Description */}
  <p className="font-poppins font-light text-[16px] sm:text-[20px] md:text-[24px] leading-[1] tracking-[0] text-[#4B5563] mt-2 max-w-xs sm:max-w-lg md:max-w-xl mx-auto">
    When your community makes expenditures or receives funds, the transaction history will appear here.
  </p>

  {/* Info cards */}
  <div className="mt-8 space-y-4">
    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 bg-white p-4 sm:p-6 rounded-[16px] sm:rounded-[20px] shadow-sm text-left w-full max-w-xs sm:max-w-2xl md:w-[599px] md:h-[194px] mx-auto">
      <div className="flex items-center justify-center bg-[#4FD1C54D] rounded-[8px] sm:rounded-[10px] w-[70px] h-[70px] sm:w-[90px] sm:h-[90px] md:w-[100px] md:h-[100px] min-w-[70px] min-h-[70px] sm:min-w-[90px] sm:min-h-[90px] md:min-w-[100px] md:min-h-[100px]">
        <FaDatabase className="text-teal-700 w-[28px] h-[28px] sm:w-[36px] sm:h-[36px] md:w-[40px] md:h-[40px]" />
      </div>
      <div className="flex flex-col justify-center flex-1">
        <h2 className="font-poppins font-normal text-[20px] sm:text-[28px] md:text-[32px] leading-[1] tracking-[0] text-slate-900">
          Transparent Records
        </h2>
        <p className="font-poppins font-light text-[14px] sm:text-[18px] md:text-[24px] leading-[1] tracking-[0] text-gray-600 mt-2">
          All community spending will be recorded here
        </p>
      </div>
    </div>

    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 bg-white p-4 sm:p-6 rounded-[16px] sm:rounded-[20px] shadow-sm text-left w-full max-w-xs sm:max-w-2xl md:w-[599px] md:h-[194px] mx-auto">
      <div className="flex items-center justify-center bg-blue-100 rounded-[8px] sm:rounded-[10px] w-[70px] h-[70px] sm:w-[90px] sm:h-[90px] md:w-[100px] md:h-[100px] min-w-[70px] min-h-[70px] sm:min-w-[90px] sm:min-h-[90px] md:min-w-[100px] md:min-h-[100px]">
        <FaChartLine className="text-blue-700 w-[28px] h-[28px] sm:w-[36px] sm:h-[36px] md:w-[40px] md:h-[40px]" />
      </div>
      <div className="flex flex-col justify-center flex-1">
        <h2 className="font-poppins font-normal text-[20px] sm:text-[28px] md:text-[32px] leading-[1] tracking-[0] text-slate-900">
          Financial Overview
        </h2>
        <p className="font-poppins font-light text-[14px] sm:text-[18px] md:text-[24px] leading-[1] tracking-[0] text-gray-600 mt-2">
          Track your community's financial health
        </p>
      </div>
    </div>
  </div>
</div>
          )}
          {/* Cash at Hand Box */}
          {cashAtHand && Number(cashAtHand) > 0 && (
            <div className="bg-white border rounded-[20px] sm:rounded-[30px] shadow-md p-4 sm:p-8 flex  sm:flex-row justify-between items-center mb-4 z-10 relative w-full max-w-full md:w-[1064px] h-auto sm:h-[140px] mx-auto">
              <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600">Cash at hand</h2>
              <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-black">
                ${cashAtHand}
              </p>
            </div>
          )}
          {/* Filters Section */}
           {transactions.length > 0 && (
          <div className="overflow-x-auto scrollbar-hide mb-8 sm:mb-10 mt-6 sm:mt-10 pb-1 flex justify-center w-full">
            <div className="relative w-full max-w-full md:w-[1064px] lg:w-[1050px] h-[56px] sm:h-[70px] mx-auto hidden md:flex">
              <div className="flex w-full">
                <button
                  onClick={() => setActiveFilter('All')}
                  className={`rounded-[40px] text-base font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    activeFilter === 'All' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={{
                    width: '197px',
                    height: '70px',
                    border: activeFilter !== 'All' ? '1px solid #00000033' : undefined,
                    marginRight: '16px',
                  }}
                >
                  All
                </button>
                <button
                  onClick={() => setActiveFilter('Income')}
                  className={`rounded-[40px] text-base font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    activeFilter === 'Income' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={{
                    width: '197px',
                    height: '70px',
                    border: activeFilter !== 'Income' ? '1px solid #00000033' : undefined,
                    marginRight: '16px',
                    marginLeft: '16px',
                  }}
                >
                  Income
                </button>
                <button
                  onClick={() => setActiveFilter('Service payment')}
                  className={`rounded-[40px] text-base font-semibold transition-all focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    activeFilter === 'Service payment' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={{
                    width: '274px',
                    height: '70px',
                    border: activeFilter !== 'Service payment' ? '1px solid #00000033' : undefined,
                    marginRight: '30px',
                    marginLeft: '30px',
                  }}
                >
                  Service payment
                </button>
                <button
                  onClick={() => setActiveFilter('Project funding')}
                  className={`rounded-[40px] text-base font-semibold transition-all ml-auto focus:outline-none focus:ring-2 focus:ring-purple-400 ${
                    activeFilter === 'Project funding' ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={{
                    width: '249px',
                    height: '70px',
                    border: activeFilter !== 'Project funding' ? '1px solid #00000033' : undefined,
                  }}
                >
                  Project funding
                </button>
              </div>
            </div>
            {/* Mobile/Tablet: horizontal scrollable buttons */}
            <div className="flex md:hidden gap-1 sm:gap-2 w-full">
              {filters.map((filter) => (
                <button
                  key={filter}
                  onClick={() => setActiveFilter(filter)}
                  className={`flex-1 min-w-[70px] sm:min-w-[90px] px-1 sm:px-2 py-1 sm:py-2 rounded-[30px] text-[10px] sm:text-xs font-semibold transition-all ${
                    activeFilter === filter ? 'bg-purple-500 text-white' : 'bg-white text-black hover:bg-gray-100'
                  }`}
                  style={activeFilter !== filter ? { border: '1px solid #00000033' } : {}}
                >
                  {filter}
                </button>
              ))}
            </div>
          </div>
           )}
          {/* Transactions Preview */}
          {transactions.length > 0 && (
          <div className="w-full flex flex-col items-center mt-6 sm:mt-10 relative">
            {/* First transaction: fully visible */}
            <div className="bg-white border rounded-[20px] sm:rounded-[30px] shadow-md p-4 sm:p-8 flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-6 w-full max-w-full md:w-[1064px] h-auto sm:h-[140px] mx-auto">
              <div className="flex items-center gap-2 sm:gap-4">
                <img src={service} alt="Service Payment" className="w-7 sm:w-10 h-7 sm:h-10" />
                <div>
                  <h3 className="text-base sm:text-2xl font-bold text-black">Service Payment</h3>
                  <p className="text-gray-600 text-xs sm:text-lg">Payment for consulting service</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-gray-500 text-xs sm:text-lg">2025-02-25</p>
                <p className="text-lg sm:text-2xl text-red-500 font-bold">-$500</p>
              </div>
            </div>
            <div className="relative mb-4 sm:mb-6 w-full max-w-full md:w-[1064px] h-auto sm:h-[140px] mx-auto">
              <div className="bg-white border rounded-[20px] sm:rounded-[30px] shadow-md p-4 sm:p-8 flex flex-col sm:flex-row justify-between items-center opacity-70 blur-[2px] w-full h-full">
                <div className="flex items-center gap-4">
                  <img src={blueprint} alt="Project funding" className="w-10 h-10" />
                  <div>
                    <h3 className="text-2xl font-bold text-black">Project funding</h3>
                    <p className="text-gray-600 text-lg">Funds allocated to Project Alpha</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-gray-500 text-lg">2025-02-25</p>
                  <p className="text-2xl text-red-500 font-bold">-$3,500</p>
                </div>
              </div>
              {/* Overlay for the second transaction */}
              <div
                className="absolute inset-0 rounded-[30px] pointer-events-none"
                style={{
                  background: 'linear-gradient(180deg, #F8F5EDCC 60%, #F8F5ED 100%)',
                }}
              ></div>
            </div>
            {/* Overlay for the rest */}
            <div
              className="
                absolute left-1/2 -translate-x-1/2 
                top-[180px] sm:top-[220px] md:top-[304px]
                w-full max-w-full md:w-[1064px]
                h-[180px] sm:h-[220px] md:h-[320px]
                rounded-[20px] sm:rounded-[30px]
                z-20 flex flex-col items-center justify-center
                px-2 sm:px-6
              "
              style={{
                background: 'linear-gradient(180deg, #F8F5EDCC 50%, #F8F5ED 100%)',
              }}
            >
              <div className="text-center w-full">
                <p className="text-base sm:text-xl md:text-2xl font-medium font-body text-black mb-2 sm:mb-4">
                  The rest of this financial data is available to Members only
                </p>
                <button
                  onClick={handleLoginToView}
                  className="mt-2 bg-yellow-500 text-white px-4 sm:px-8 py-2 sm:py-4 rounded-full font-semibold text-base sm:text-lg shadow-md hover:bg-yellow-600"
                >
                  Login to view
                </button>
              </div>
            </div>
          </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DaoTransactions;