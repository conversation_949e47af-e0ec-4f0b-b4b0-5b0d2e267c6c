import React, { useState, useEffect, useRef } from 'react';
import HeaderNew from '../components/Header/HeaderNew';
import lotterysmall from '../assets/images/lottery.png';
import buyticketsnow from '../assets/images/buyticketsnow.png';
import milestone from '../assets/images/milestone.png';
import clockblack from '../assets/images/clockblack.png';
import ticketblack from '../assets/images/ticket.png';
import wallet from '../assets/images/wallet.png';
import giftbox from '../assets/images/giftbox.png';
import ticketYellow from '../assets/images/ticketYellow.png';
import squaresYellow from '../assets/images/squaresYellow.png';
import PurchaseTickets from './Purchase_tickets';
import { ImCross } from 'react-icons/im';
import { AiFillClockCircle } from 'react-icons/ai';
import semiCircle from '../assets/images/semiCircle.svg';
import dittedZigZagLine from '../assets/images/dittedZigZagLine.svg';
import checkMark from '../assets/images/checkMark.png';
import { BsArrowRight } from 'react-icons/bs';
import { HiOutlineRefresh } from 'react-icons/hi';
import { FaRegCalendarAlt, FaChevronDown } from 'react-icons/fa';
import { FaTrophy, FaUsers, FaTimes } from 'react-icons/fa';
import { MatchWinnersModal } from './modals/MatchWinnersModal';
import { FaSlack } from 'react-icons/fa6';
import { useNavigate } from 'react-router-dom';
import { AppRoutesPaths } from '../route/app_route';
import { useAuthentication } from '../components/utils/provider';
import award from '../assets/sahelion/award.png';
import wallet2 from '../assets/sahelion/wallet2.png';
import Footer from '../components/Footer/footer';
import { RxAccessibility } from 'react-icons/rx'
import { MdDoNotDisturbAlt } from "react-icons/md";
import { IoClose } from "react-icons/io5";
import { LotteryTktPay } from './LotteryTktPay/LotteryTktPay';
import { FaGift } from "react-icons/fa6";
import { Spin } from 'antd';
import { CiClock2 } from "react-icons/ci";
import { FiTrendingUp } from "react-icons/fi";
import ConcentricArcLoader from "../utils/ConcentricArcLoader.jsx";

export const Lottery = () => {
  const navigate = useNavigate();

  // Get wallet connection functions from useAuthentication
  const {
    connectWallet,
    currentUserWallet,
    disconnectWallet,
    getWeb3Signer,
    buyLotteryTicket,
    getRecentWinningPlayers,
    getCurrentPrizePool,
    getNextDrawDate,
    checkLotteryPaused
  } = useAuthentication();


  const inputRefs = useRef([]);

  const [showAssignTicketModal, setShowAssignTicketModal] = useState(false);
  const [showConfirmAssignModal, setShowConfirmAssignModal] = useState(false);
  const [showTicketNumsModal, setShowTicketNumsModal] = useState(false);
  const [manualMode, setManualMode] = useState(false);
  const [showLotteryTypePopup, setShowLotteryTypePopup] = useState(null); // Use `null` to track the currently open ticket index
  const [numOfTickets, setNumOfTickets] = useState(0);
  const [ticketNumbers, setTicketNumbers] = useState([]);
  const [selectedLotteryTypes, setSelectedLotteryTypes] = useState({}); // State to store selected lottery type for each ticket

  const [allTicketData, setAllTicketData] = useState([]);

  const [showWinnersModal, setShowWinnersModal] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState(null);
  const [matchWinners, setMatchWinners] = useState([]);
  const [matchWinningNumbers, setMatchWinningNumbers] = useState([]);

  const [invalidInputs, setInvalidInputs] = useState({});
  const [showTktPay, setShowTktPay] = useState(false);

  const [recentWinners, setRecentWinners] = useState({})
  const [sortedDates, setSortedDates] = useState([])
  const [purchaseIsLoading, setPurchaseIsLoading] = useState(false);

  const [currentPrizePool, setCurrentPrizePool] = useState(0);
  const [nextDrawDate, setNextDrawDate] = useState(null);
  const [lotteryPaused, setLotteryPaused] = useState(true);
  const [blockchainQueryLoading, setBlockchainQueryLoading] = useState(true)
  const [ticketPurchased, setTicketPurchased] = useState(0);

  const steps = [
    { text: 'Connect Your Wallet', icon: wallet },
    { text: 'Buy Lottery Tickets', icon: ticketblack },
    { text: 'Wait for Weekly Draw', icon: clockblack },
    { text: 'Check If You Won!', icon: giftbox },
  ];

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    if (showTktPay || showAssignTicketModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }, [showTktPay, showAssignTicketModal]);


  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };


  function calculateTimeLeft() {
    const targetDate = nextDrawDate; // Next draw time
    const now = new Date();
    const difference = targetDate - now;

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / (1000 * 60)) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    }
    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }

  const purchaseTicketsRef = React.useRef(null); // Create a ref for the PurchaseTickets component

  const scrollToPurchaseTickets = () => {
    if (purchaseTicketsRef.current) {
      purchaseTicketsRef.current.scrollIntoView({ behavior: 'smooth' }); // Smooth scroll to the component
    }
  };

  const toShowTicketQtyModal = () => {
    const purchaseTicketsElement = purchaseTicketsRef.current;
    if (purchaseTicketsElement) {
      const purchaseTicketsComponent = purchaseTicketsElement.querySelector(
        '[data-testid="purchase-tickets"]'
      );
      if (purchaseTicketsComponent) {
        purchaseTicketsComponent.dispatchEvent(
          new CustomEvent('showTicketQtyModal', { bubbles: true })
        );
      }
    }
  };

  const buyTickets = async () => {
    if (lotteryPaused) {
      alert("Lottery Is Paused")
      return;
    }
    if (!currentUserWallet) {
      alert("connect wallet first")
      return;
    }
    setPurchaseIsLoading(true);
    if (allTicketData.length > 0) {
      let tickets = allTicketData.map(ticket => ticket.ticketNumber)
      console.log("Tickets to purchase:", tickets);
      const result = await buyLotteryTicket(tickets)
      if (result.success) {
        alert("Tickets purchased successfully!");
      } else {
        alert(result.error)
      }
      setPurchaseIsLoading(false)
      setShowTicketNumsModal(false);
      setInvalidInputs(false);
      setShowConfirmAssignModal(false);
      setTicketPurchased(ticketPurchased + 1)
      navigate(AppRoutesPaths.lotteryActivity)

    } else {
      alert("Please add tickets before proceeding to purchase.");
      setPurchaseIsLoading(false);
    }
  }

  function getDrawDate(timestamp) {
    const date = new Date(timestamp * 1000); // convert to ms
    const day = date.getUTCDay(); // Sunday = 0, Monday = 1

    // How many days to add to reach next Sunday
    const daysToAdd = (1 - day) % 1;  // remember to change to: const daysToAdd = (7 - day) % 7;

    const drawDate = new Date(Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate() + daysToAdd,
      0, 0, 0 // 00:00 UTC
    ));

    return drawDate.toISOString().split('T')[0]; // "YYYY-MM-DD"
  }

  function groupRewardsByDraw(rewards) {
    const grouped = {};

    rewards.forEach(reward => {
      const timestamp = reward.timestamp.toNumber();
      const drawDate = getDrawDate(timestamp);

      if (!grouped[drawDate]) {
        grouped[drawDate] = [];
      }

      grouped[drawDate].push(reward);
    });

    return grouped;
  }

  function getLastSundayFormatted() {
    const today = new Date();
    const day = today.getDay(); // 0 = Sunday
    const diff = today.getDate() - day;
    const lastSunday = new Date(today);
    lastSunday.setDate(diff);
    lastSunday.setHours(0, 0, 0, 0);

    // Format the date as "Month day, Year"
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(lastSunday);
  }

  useEffect(() => {

    setTimeLeft(calculateTimeLeft());

  }, [nextDrawDate]);

  // Refactor generateUniqueNumbers to return an array of six two-digit numbers
  const generateUniqueNumbers = () => {
    const numbers = new Set();
    while (numbers.size < 6) {
      const randomNum = Math.floor(Math.random() * 49) + 1;
      numbers.add(randomNum < 10 ? `0${randomNum}` : `${randomNum}`);
    }
    return Array.from(numbers); // Return as an array of six two-digit numbers
  };

  useEffect(() => {
    const newTicketNumbers = Array.from({ length: numOfTickets }, () => generateUniqueNumbers());
    setTicketNumbers(newTicketNumbers);
  }, [numOfTickets]);

  useEffect(() => {
    setAllTicketData(
      Array.from({ length: numOfTickets }, (_, index) => ({
        ticket: index + 1,
        lotteryType: 'Crypto Jackpot',
        ticketNumber: ticketNumbers[index] || [], // Assign ticketNumbers if available
      }))
    );
  }, [numOfTickets, ticketNumbers]);

  useEffect(() => {
    getRecentWinningPlayers().then((data) => {
      console.log("Recent winning players data:", data);
      const groupedRewards = groupRewardsByDraw(data);
      console.log("Grouped recent Rewards by Draw Date:", groupedRewards);
      setRecentWinners(groupedRewards);
      const _sortedDates = Object.keys(groupedRewards).sort(
        (a, b) => new Date(b) - new Date(a) // Latest first
      );
      setSortedDates(_sortedDates);
      // if (data && data.length > 0) {
      //   setAllTicketData((prev) => {
      //     return prev.map((ticket, index) => ({
      //       ...ticket,
      //       winners: data[index]?.winners || [],
      //     }));
      //   });
      // }
    }).catch((error) => {
      console.error("Error fetching recent winning players:", error);
    });
  }, [currentUserWallet])

  // Refactor refreshNum to work with the new structure
  const refreshNum = (ticketIndex) => {
    const newNumber = generateUniqueNumbers();

    setTicketNumbers((prev) =>
      prev.map((numbers, index) => (index === ticketIndex ? newNumber : numbers))
    );

    setAllTicketData((prev) =>
      prev.map((ticket) =>
        ticket.ticket === ticketIndex + 1
          ? { ...ticket, ticketNumber: newNumber }
          : ticket
      )
    );
  };

  const validateUniqueNumbers = (ticketIndex, value, inputIndex) => {
    const currentTicket = ticketNumbers[ticketIndex] || [];
    return currentTicket.some((num, index) => index !== inputIndex && num === value);
  };

  // Refactor cancelTicket to handle the new structure
  const cancelTicket = (ticketIndex) => {
    setTicketNumbers((prev) => {
      const updatedTickets = [...prev];
      updatedTickets.splice(ticketIndex, 1); // Remove the ticket at the specified index
      return updatedTickets;
    });

    setAllTicketData((prev) => {
      const updatedData = prev.filter((_, index) => index !== ticketIndex); // Remove the ticket data
      return updatedData.map((ticket, index) => ({
        ...ticket,
        ticket: index + 1, // Update ticket numbers to reflect the new order
      }));
    });
  };

  const stars = [
    { top: '5%', left: '20%' },
    { top: '10%', left: '45%' },
    { top: '28%', left: '7%' },
    { top: '40%', left: '40%' },
    { top: '65%', left: '5%' },
    { bottom: '7%', left: '20%' },
    { top: '20%', right: '30%' },
    { top: '35%', right: '20%' },
    { bottom: '38%', right: '38%' },
    { bottom: '22%', right: '22%' },
    { bottom: '5%', right: '5%' },
  ];

  const [expandedDraws, setExpandedDraws] = useState({});

  const toggleDraw = (date) => {
    setExpandedDraws((prev) => ({
      ...prev,
      [date]: !prev[date],
    }));
  };

  useEffect(() => {
    const fetchCurrentPrizePool = async () => {
      try {
        const prizePool = await getCurrentPrizePool();
        console.log("Current Prize Pool:", prizePool);
        setCurrentPrizePool(prizePool);
        setBlockchainQueryLoading(false)
      } catch (error) {
        console.error("Error fetching current prize pool:", error);
        setBlockchainQueryLoading(false)
      }
    };
    fetchCurrentPrizePool();
  }, [ticketPurchased, currentUserWallet]);

  useEffect(() => {
    const fetchNextDrawDate = async () => {
      try {
        const nextDrawDate = await getNextDrawDate();
        console.log("Next Draw Date:", nextDrawDate);
        setNextDrawDate(nextDrawDate);
      } catch (error) {
        console.error("Error fetching next draw date:", error);
      }
    };
    fetchNextDrawDate();
  }, [currentUserWallet]);

  const checkIfLotteryPaused = async () => {
    try {
      const isPaused = await checkLotteryPaused();
      console.log("Is Lottery Paused:", isPaused);
      setLotteryPaused(isPaused);
    } catch (error) {
      console.error("Error checking lottery paused status:", error);
      return false;
    }
  }

  useEffect(() => {
    checkIfLotteryPaused();
  }, [currentUserWallet])


  return (
    <div className="bg-[#111828]">
      <div className="relative bg-gradient-to-br from-[#111828] from-[10%] via-[rgba(255,255,255,0.15)] via-[25%] to-[#111726] to-[40%] min-h-screen">
        <div className="my-auto shadow-[0px_-35px_50px_35px_#571C8660] w-0 h-20 absolute top-96"></div>

        <HeaderNew />

        <div className="p-[5%] md:p-[10%]">
          <div className="max-md:space-y-5 md:flex md:justify-between md:space-x-5 xl:space-x-10">
            {/* left main */}
            <div className="relative md:w-1/2 border-[0.5px] border-BP-gray-100-opc rounded-3xl bg-gradient-to-b from-BP-black from-[50%] to-[#72519F]">
              <div
                className="absolute top-0 left-0 inset-0 h-[55%] w-full rounded-t-xl"
                style={{
                  backgroundImage: `linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px)`,
                  backgroundSize: '33px 33px',
                }}
              />
              <p className="py-5 xl:pb-0 px-10 font-bold text-[6vw] md:text-[3vw]">
                Blackpanther <br /> &nbsp; Lottery
              </p>
              <p className="p-[5%] md:p-[10%] xl:pt-5 text-BP-opacited-white text-[5vw] md:text-[1.5vw]">
                Play for a chance to win big with a provably fair blockchain
                lottery.
              </p>

              <div className="relative p-5">
                <style>
                  {`
                  @keyframes move {
                    0% { transform: translate(0, 0); }
                    25% { transform: translate(3px, -3px); }
                    50% { transform: translate(-3px, 2px); }
                    75% { transform: translate(2px, 3px); }
                    100% { transform: translate(0, 0); }
                  }

                  @keyframes glow {
                    0% { box-shadow: 0px 0px 5px 1px #ffffff; }
                    50% { box-shadow: 0px 0px 15px 3px #ffffff; }
                    100% { box-shadow: 0px 0px 5px 1px #ffffff; }
                  }

                  @keyframes revolve {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                  }
                `}
                </style>

                {stars.map((star, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-white opacity-50 rounded-full"
                    style={{
                      ...star,
                      animation:
                        'move 5s infinite linear, glow 1.5s infinite alternate',
                      boxShadow: '0px 0px 10px 2px #ffffff',
                    }}
                  />
                ))}

                <div className="rounded-full border border-b-0 border-[#ffffff40] w-[75vw] h-[75vw] md:w-[25vw] md:h-[25vw] mx-auto flex items-center justify-center">
                  <div className="relative rounded-full border border-[#ffffff40] w-[50vw] h-[50vw] md:w-[17vw] md:h-[17vw] flex items-center justify-center">
                    {/* golden blur */}
                    <div className="absolute mx-auto shadow-[0px_-35px_50px_50px_#E1A80D70] w-0 rounded-full" />

                    {/* Left circular line */}
                    <div className="absolute -left-[68px] flex items-center justify-self-center">
                      <div className="h-[1px] w-16 bg-gradient-to-r from-[#ffffff20] from-[50%] to-white" />
                      <div className="w-2 h-2 bg-white rounded-full shadow-[0px_0px_10px_2px_#ffffff]" />
                    </div>

                    {/* right circular line */}
                    <div className="absolute -right-[68px] flex items-center justify-self-center">
                      <div className="w-2 h-2 bg-white rounded-full shadow-[0px_0px_10px_2px_#ffffff]" />
                      <div className="h-[1px] w-16 bg-gradient-to-l from-[#ffffff20] from-[50%] to-white" />
                    </div>

                    <div className="rounded-full bg-[#ffffff20] border border-[#ffffff40] w-[25vw] h-[25vw] md:w-[10vw] md:h-[10vw]  flex items-center justify-center">
                      <img
                        src={lotterysmall}
                        alt=""
                        className="w-[10vw] h-[10vw] md:w-[3vw] md:h-[3vw] opacity-50"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* right main */}
            <div className="space-y-5 md:w-1/2">
              <div className="p-5 rounded-3xl bg-BP-black border-[0.5px] border-BP-gray-100-opc">
                <p className="px-5 font-bold text-[10vw] md:text-[2.5vw]">
                  Current Prize Pool
                </p>
                <p className="px-5 text-BP-gold-gradient-end font-semibold text-[7vw] md:text-[3.5vw]">
                  {blockchainQueryLoading ? <ConcentricArcLoader size={60} /> : `${currentPrizePool} SHLN`}
                </p>

                <div className="xl:flex xl:items-center xl:justify-center">
                  <div className="order-2 w-[60vw] h-[60vw] md:w-[20vw] md:h-[20vw] rounded-full bg-BP-gray-50-opc border border-[#ffffff40] my-10 md:my-5 mx-auto flex items-center justify-center relative">
                    <img
                      src={semiCircle}
                      alt=""
                      className="absolute top-0 left-0 w-[30vw] h-[30vw]  md:w-[10vw] md:h-[10vw]"
                      style={{
                        animation: 'revolve 5s linear infinite',
                        transformOrigin: 'bottom right',
                      }}
                    />

                    <div className="w-[40vw] h-[40vw] md:w-[13vw] md:h-[13vw] rounded-full border-2 border-black shadow-[0px_0px_25px_10px_#E9B308] flex items-center justify-center">
                      <div className="w-fit text-center relative flex flex-col items-center justify-center pt-5">
                        {/* Furthest back (Same size as the main days) */}
                        <p className="absolute -top-5 xl:-top-8 text-BP-opacited-white font-bold text-6xl z-0 opacity-30">
                          {Math.max(timeLeft.days - 2, 0)}
                        </p>

                        {/* Middle layer (Slightly smaller) */}
                        <p className="absolute -top-0 xl:-top-1 text-BP-opacited-white font-bold text-5xl z-10 opacity-50">
                          {Math.max(timeLeft.days - 1, 0)}
                        </p>

                        {/* Topmost text (Same size as the first one) */}
                        <p className="relative text-BP-gold-gradient-end font-bold text-6xl z-20">
                          {Math.max(timeLeft.days, 0)}
                        </p>

                        {/* Days/Day Remaining text */}
                        <p className="text-[#ffffff80] text-xs">
                          {timeLeft.days <= 1 ? 'Day' : 'Days'} Remaining
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="order-1 space-y-5 xl:w-[45%]">
                    <div className="px-3 space-y-3 font-thin relative">
                      <AiFillClockCircle className="w-7 h-7 absolute -top-3 right-20" />
                      <p className="">Next Draw</p>
                      <p className="">{blockchainQueryLoading ? <ConcentricArcLoader size={50} /> : nextDrawDate ? `${nextDrawDate.toISOString()} GMT` : "..."}</p>

                    </div>
                    {/* Timer Numbers */}
                    <table className="w-[75%] mx-auto text-center border-collapse">
                      <tbody>
                        <tr className="text-white text-xl md:text-2xl font-semibold">
                          <td className="px-4">{timeLeft.days}</td>
                          <td className="px-4">{timeLeft.hours}</td>
                          <td className="px-4">{timeLeft.minutes}</td>
                          <td className="px-4">{timeLeft.seconds}</td>
                        </tr>
                        <tr className="text-white font-karla font-light text-xs md:text-[14px] text-[#FFFFFFB2]">
                          <td className="px-4 xl:px-0">Days</td>
                          <td className="px-4 xl:px-0">Hrs</td>
                          <td className="px-4 xl:px-0">Mins</td>
                          <td className="px-4 xl:px-0">Secs</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <div
                className="w-full min-h-80 p-5 rounded-[40px] relative overflow-hidden"
                style={{
                  background: `linear-gradient(0deg, #111111, #111111),
                                          linear-gradient(180deg, rgba(233, 179, 8, 0) 0%, #000000 100%)`,
                  backgroundImage: `url(${buyticketsnow})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  border: '1px solid #FFFFFF4D',
                }}
              >
                {/* Buy tickets button */}
                <div className="max-md:h-32 md:absolute bottom-[10%] left-[5%] flex items-center justify-center">
                  <button disabled={lotteryPaused} onClick={scrollToPurchaseTickets} className="w-[50vw] h-[10vw] md:w-[15vw] md:h-[3.5vw] hover:scale-150 md:hover:scale-110 transition-all duration-300 ease-out rounded-[50px] bg-[#E1AB0D] shadow-[0px_8px_4px_0px_#00000033] flex items-center justify-center">
                    {blockchainQueryLoading ? <ConcentricArcLoader size={50} /> :
                      <span className="text-black font-poppins font-medium text-[4vw] md:text-[1.5vw]">
                        {lotteryPaused ? "Lottery Paused" : "Buy tickets now"}
                      </span>}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="relative mx-auto max-w-[1200px] w-[90%] min-h-[300px] border-2 border-gray-800 py-16 rounded-[32px] bg-[#1C2431] flex flex-col items-center justify-center px-8">
          {/* Trophy Icon */}
          <div className="bg-[#2A2F45] p-8 rounded-full mt-[-24px]">
            <img
              src={award}
              alt="Prize"
              className="w-10 h-10 sm:w-16 sm:h-16"
            />
          </div>
          {/* Title */}
          <h2 className="text-2xl font-semibold p-4">Check Your Winnings</h2>
          <p className="sm:max-w-xl font-poppins text-justify md:max-w-3xl text-lg leading-relaxed p-4">
            Have you participated in the lottery within the last 180 days?{' '}
            Connect your wallet to check for unclaimed prizes and claim your
            winnings!
          </p>
          <button
            onClick={async () => {
              currentUserWallet ?
                navigate(AppRoutesPaths.lotteryActivity) :
                await connectWallet()
            }}
            className="bg-[#E1AB0D] hover:bg-[#e1a80d] w-full md:w-auto text-black font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg flex items-center justify-center gap-2 mt-10"
          >
            <img src={wallet2} alt="Prize" className="w-8 h-8" />
            <span>{currentUserWallet ? "View Your Tickets" : "Connect Wallet"}</span>
          </button>
          {currentUserWallet && <p className="text-center text-BP-opacited-white my-5">Wallet connected: {formatWalletAddress(currentUserWallet)}</p>}
        </div>



        <div className="relative mx-auto max-w-[1200px] w-full  text-white px-6 py-10 rounded-[20px] md:rounded-[40px]">
          <h1 className="text-[40px] font-karla font-bold text-BP-yellow mb-2">
            Recent Winners
          </h1>
          <p className="text-lg font-poppins font-medium text-gray-300 mb-8">
            See who won in our latest draws
          </p>
          {sortedDates.length > 0 ?
            <div className="space-y-4">

              {sortedDates.map((_date) => {
                const rewards = recentWinners[_date] || [];
                const winningNumbers = rewards.length > 0 ? rewards[0].winningNumbers.map(num => num.toNumber()) : [];
                const jackpot = rewards.filter(reward => reward.matches == 6);
                const fiveMatches = rewards.filter(reward => reward.matches == 5);
                const fourMatches = rewards.filter(reward => reward.matches == 4);
                const threeMatches = rewards.filter(reward => reward.matches == 3);

                return (
                  <div
                    className={`bg-[#1C2431] rounded-2xl overflow-hidden ${expandedDraws[_date] ? 'border-2 border-[#72519F]' : 'border-2 border-gray-800'}`}
                  >
                    <div
                      className="flex items-center justify-between p-6 hover:bg-[#232b3a] transition-colors cursor-pointer"
                      onClick={() => toggleDraw(_date)}
                    >
                      <div className="flex items-center space-x-4">
                        <FaRegCalendarAlt className="text-BP-yellow text-3xl" />
                        <span className="text-white text-xl font-poppins font-medium">
                          {_date}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`text-base px-6 py-2 rounded-full font-poppins font-medium ${jackpot.length > 0 ? "bg-BP-yellow" : "bg-[#2A3444]"} text-white`}>
                          Jackpot
                        </span>
                        <FaChevronDown
                          className={`text-gray-400 text-lg transition-transform ${expandedDraws['April-30-2025'] ? 'rotate-180' : ''}`}
                        />
                      </div>
                    </div>

                    {expandedDraws[_date] && (
                      <div className="p-6 border-t border-[#2A3444]">
                        <h3 className="text-[#E1AB0D] text-xl mb-4">
                          Winning Numbers
                        </h3>
                        <div className="flex space-x-4 mb-8">
                          {winningNumbers.map((number) => (
                            <div
                              key={number}
                              className="w-12 h-12 bg-[#571C86] rounded-full flex items-center justify-center text-white text-xl font-medium"
                            >
                              {number}
                            </div>
                          ))}
                        </div>

                        {/* Jackpot Section */}
                        {jackpot.length === 0 ? (
                          <div className="bg-[#1E2736] rounded-xl p-4 mb-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-300">Jackpot(6 matches)</span>
                              <div className="relative flex items-center justify-center">
                                {/* Outer pulsating circles */}
                                <span className="absolute w-14 h-14 bg-gray-400/30 opacity-30 rounded-full animate-ping"></span>
                                <span className="absolute w-16 h-16 bg-gray-400/30 opacity-20 rounded-full animate-ping delay-150"></span>

                                {/* Main icon */}
                                <RxAccessibility
                                  className="text-gray-400/10 bg-gray-400/20 rounded-full p-2 shadow-lg text-5xl relative z-10"
                                />
                              </div>
                            </div>
                            <span className="text-gray-400">No winner</span>
                          </div>
                        ) : (
                          <div className="bg-BP-gold-gradient-end rounded-xl p-4 mb-4"
                            onClick={() => {
                              setSelectedMatch(6);
                              setMatchWinners(jackpot);
                              setMatchWinningNumbers(winningNumbers)
                              setShowWinnersModal(true);
                            }}
                          >
                            <div className="flex justify-between items-center mb-2">
                              <div className="flex items-center space-x-2">
                                <FaTrophy className="text-white" />
                                <span className="text-white">Jackpot(6 matches)</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FaUsers className="text-white text-2xl" />
                                <span className="text-white">{jackpot.length} winners</span>
                              </div>
                            </div>
                          </div>
                        )
                        }

                        {/* Match Sections */}
                        {[
                          { matches: 5, winners: fiveMatches },
                          { matches: 4, winners: fourMatches },
                          { matches: 3, winners: threeMatches },
                        ].map((item) => (
                          <div
                            key={item.matches}
                            className="bg-[#30314c] rounded-xl p-6 mb-3 flex justify-between items-center cursor-pointer hover:bg-[#6c23a3] transition-colors"
                            onClick={() => {
                              setSelectedMatch(item.matches);
                              setMatchWinners(item.winners);
                              setMatchWinningNumbers(winningNumbers)
                              setShowWinnersModal(true);
                            }}
                          >
                            <div className="flex items-center space-x-2">
                              <FaTrophy className="text-white" />
                              <span className="text-white">
                                {item.matches} Matches
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <FaUsers className="text-white" />
                              <span className="text-white">
                                {item.winners.length} winners
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )

              })}
            </div> : <div className="flex flex-col items-center justify-center p-6 w-full max-w-4xl mx-auto">
              <h1 className="font-karla font-medium text-[32px] md:text-[40px] lg:text-[48px] leading-[100%] tracking-[0] text-[#C2C6CC] mb-4 text-center">
                No Recent Winners Yet
              </h1>
              <p className="font-poppins font-thin text-[20px]  md:text-[28px] lg:text-[32px] leading-[100%] tracking-[0] text-[#B2B8C0] mb-6 ">
                When players win our lottery, their achievements will be celebrated here in shining gold. Will you be the first champion?
              </p>
              <div className="w-full mb-6">
                <div className="flex flex-col md:flex-row justify-center gap-6 md:gap-8">
                  <div className="flex flex-col w-full max-w-[280px] h-[206px] rounded-[20px] bg-[#1F2937] items-center justify-center mx-auto mb-4 md:mb-0">
                    <FiTrendingUp className="text-yellow-300 mb-2 w-[40px] h-[40px] md:w-[50px] md:h-[50px]" />
                    <span className="font-poppins font-medium text-[18px] md:text-[24px] leading-[100%] tracking-[0] text-[#B2B8C0]">
                      Total Winners
                    </span>
                    <span className="font-karla font-bold text-[28px] md:text-[40px] leading-[100%] tracking-[0]">
                      0
                    </span>
                  </div>
                  <div className="flex flex-col w-full max-w-[280px] h-[206px] rounded-[20px] bg-[#1F2937] items-center justify-center gap-2 mx-auto mb-4 md:mb-0">
                    <FaGift className="text-yellow-300 mb-2 w-[40px] h-[40px] md:w-[50px] md:h-[50px]" />
                    <span className="font-poppins font-medium text-[18px] md:text-[24px] leading-[100%] tracking-[0] text-[#B2B8C0]">
                      Total Prizes
                    </span>
                    <span className="font-karla font-bold text-[28px] md:text-[40px] leading-[100%] tracking-[0]">
                      $0
                    </span>
                  </div>
                  <div className="flex flex-col w-full max-w-[280px] h-[206px] rounded-[20px] bg-[#1F2937] items-center justify-center gap-2 mx-auto">
                    <CiClock2 className="text-yellow-300 w-[40px] h-[40px] md:w-[50px] md:h-[50px]" />
                    <span className="font-poppins font-medium text-[18px] md:text-[24px] leading-[100%] tracking-[0] text-[#B2B8C0]">
                      Next Draw
                    </span>
                    <span className="font-karla font-bold text-[28px] md:text-[40px] leading-[100%] tracking-[0]">
                      Soon
                    </span>
                  </div>
                </div>
              </div>

              <div className="w-full border-gray-200 pt-6">
                <button onClick={scrollToPurchaseTickets} className="font-poppins font-light text-[20px] md:text-[28px] lg:text-[32px] leading-[100%] tracking-[0] text-center">
                  Participate in the next draw
                  <span className="inline-flex mb-2 items-center justify-center w-[40px] h-[40px] md:w-[50px] md:h-[50px] text-[28px] md:text-[40px] leading-[50px] align-middle">
                    →
                  </span>
                </button>
              </div>
            </div>
          }
        </div>

        <div className="flex flex-col items-center mt-12">
          <h1 className="text-white text-[32px] md:text-[64px] font-semibold md:mb-8 mb-4 leading-tight">
            About the Lottery
          </h1>

          <div className="relative mx-auto max-w-[1200px] w-[90%] min-h-[200px] md:min-h-[300px] border-2 border-gray-800 py-4 md:py-16 rounded-[24px] md:rounded-[32px] bg-[#1C2431] flex flex-col items-center justify-center px-3 md:px-8">
            <p className="sm:max-w-xl font-poppins text-justify md:max-w-4xl text-[15px] md:text-lg leading-tight md:leading-relaxed">
              The Blackpanther Lottery is a decentralized blockchain-based lottery where participants can buy tickets for a chance to win a large prize pool. The lottery operates on a provably fair mechanism, transparency and trust. Draws occur weekly and lottery players must check their numbers and claim their winnings within 6 months.
            </p>
            <button onClick={scrollToPurchaseTickets} className="w-full md:w-auto bg-BP-yellow hover:bg-BP-hovered-yellow hover:scale-95 transition-all duration-300 ease-out text-black font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg flex items-center justify-center gap-2 mt-10">
              Get started
              <FaSlack size={20} />
            </button>
          </div>
        </div>
      </div>

      <div className="relative p-[5%] md:p-[10%]">
        {/* Background Circle */}
        <div className="absolute right-0 lg:right-[12%] top-1/2 translate-y-[-50%] bg-[#72519F] shadow-[-35px_0px_15px_30px_#72519F] w-[25vw] h-[25vw] lg:w-[8vw] lg:h-[8vw] rounded-full">
          <div className="bg-[#72519F] h-full rounded-l-full w-6/12 blur-md shadow-[-20px_0px_40px_35px_#72519F]"></div>
        </div>

        {/* Main Content Box */}
        <div className="bg-[#1C24315D] rounded-3xl py-10 px-5 lg:px-10 space-y-5 relative lg:flex lg:items-center lg:min-h-[50vw] lg:w-[95%]">
          {/* Title Section */}
          <div className="space-y-5">
            <img
              src={milestone}
              alt="Milestone"
              className="w-[20vw] h-[20vw] lg:w-[8vw] lg:h-[8vw] max-lg:mx-auto"
            />
            <p className="font-semibold text-[8vw] md:text-[5vw] max-lg:text-center lg:w-[75%]">
              How it works
            </p>
          </div>

          {/* Steps Section */}
          <div className="relative space-y-5 lg:absolute lg:-right-20 xl:-right-32">
            <img
              src={dittedZigZagLine}
              alt=""
              className="absolute top-0 lg:top-[2vw] xl:top-5 lg:right-20 xl:right-32 h-[150vw] min-[450px]:h-[130vw] min-[530px]:h-[110vw] sm:h-[100vw] md:h-[110vw] lg:h-[60vw] xl:h-[59vw] min-[1340px]:h-[56vw]"
            />
            {steps.map((step, index) => (
              <div
                key={index}
                className={`relative p-[1px] lg:w-[70%] rounded-3xl bg-gradient-to-r from-BP-opacited-white to-[#7e828220]  ${index % 2 === 0 ? 'lg:ml-28' : ''
                  }`}
              >
                <div className="bg-[#1f2937] p-5 rounded-3xl min-h-36">
                  <div className="bg-[#E1AB0D] rounded-full p-2.5 w-fit h-fit">
                    <img
                      src={step.icon}
                      alt=""
                      className="w-[10vw] h-[10vw] lg:w-[3vw] lg:h-[3vw]"
                    />
                  </div>
                  <p className="text-BP-opacited-white px-[14vw] lg:px-[2.5vw] text-[5vw] lg:text-[2.5vw] -mt-5 ml-5 lg:mx-10">
                    {step.text}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div ref={purchaseTicketsRef}>
        <PurchaseTickets
          toShowAssignTicketModal={() => setShowAssignTicketModal(true)}
          setNumOfTickets={(num) => setNumOfTickets(num)}
          data-testid="purchase-tickets"
        />
      </div>

      {showAssignTicketModal && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
          style={{ zIndex: 1000 }}
        >
          <div className="bg-[#ffffff60] w-[95%] rounded-3xl p-3 md:w-[75%] lg:w-[55%] xl:w-[45%]">
            <div className="bg-BP-black rounded-3xl space-y-5 max-h-[90vh] overflow-auto">
              <div
                className="flex justify-between sticky top-0 bg-BP-black p-5 pb-3"
                style={{ zIndex: 400 }}
              >
                <div className="flex space-x-2">
                  <img src={squaresYellow} alt="" className="w-7 h-7 my-auto" />
                  <div className="h-fit my-auto ">
                    <p className="text-[4.5vw] sm:text-[3vw] lg:text-[2vw] xl:text-[1.5vw]">
                      Your tickets preview
                    </p>
                    <p className="text-[3vw] sm:text-[2vw] lg:text-[1.5vw] xl:text-[1vw] text-BP-gray-100-opc">
                      View your lottery tickets
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowAssignTicketModal(false);
                  }}
                  className="text-white text-lg"
                >
                  <ImCross
                    color="#ffffff"
                    className="w-8 h-8 hover:bg-[#ffffff60] p-2 rounded-full"
                  />
                </button>
              </div>

              {/* <p className="text-center text-xs text-BP-opacited-white px-5">
                Choose a lottery type for each of your tickets.
              </p> */}

              {Array.from({ length: numOfTickets }, (_, index) => (
                <div
                  key={index}
                  className="p-3 m-5 border-[0.5px] border-BP-gray-100-opc rounded-3xl max-sm:space-y-5 sm:flex sm:justify-between sm:px-5 mb-5"
                >
                  <div className="flex w-fit max-sm:mx-auto space-x-5">
                    <img
                      src={ticketYellow}
                      alt=""
                      className="w-7 h-6 my-auto"
                    />
                    <p className="my-auto text-xl font-bold">
                      Ticket #{index + 1}
                    </p>
                  </div>


                  {/* the dropdown for lottery type selection */}

                  {/* <div
                    onClick={
                      () =>
                        setShowLotteryTypePopup(
                          showLotteryTypePopup === index ? null : index
                        )
                    }
                    className="relative rounded-full bg-[#72519F] hover:bg-BP-purple max-sm:mx-5 px-5 flex justify-between items-center hover:cursor-pointer"
                  >
                    <p className="">
                      {selectedLotteryTypes[index] || 'Select lottery'}{' '}
                    </p>

                    <RiArrowDropDownLine
                      color="#ffffff"
                      className="w-12 h-12 my-auto"
                    />

                    {showLotteryTypePopup === index && (
                      <div
                        style={{ zIndex: 300 }}
                        className="absolute top-14 right-0 w-44 overflow-hidden bg-BP-black py-3 border-[0.5px] border-BP-gray-100-opc rounded-3xl space-y-5"
                      >
                        <div
                          className="flex space-x-3 hover:bg-[#72519F] px-3 hover:cursor-pointer"
                          onClick={() => {
                            setSelectedLotteryTypes((prev) => ({
                              ...prev,
                              [index]: 'Pick 3',
                            }));
                            setAllTicketData((prev) =>
                              prev.map((ticket) =>
                                ticket.ticket === index + 1
                                  ? {
                                      ...ticket,
                                      lotteryType: '2% of prize pool',
                                    }
                                  : ticket
                              )
                            );
                            setShowLotteryTypePopup(null);
                          }}
                        >
                          <FaHashtag
                            color="#e1a80d"
                            className="w-5 h-5 my-auto"
                          />
                          <div className="">
                            <p className="font-semibold">Match 3</p>
                            <p className="text-xs text-BP-opacited-white">
                              2% of prize pool
                            </p>
                          </div>
                        </div>
                        <div
                          className="flex space-x-3 hover:bg-[#72519F] px-3 hover:cursor-pointer"
                          onClick={() => {
                            setSelectedLotteryTypes((prev) => ({
                              ...prev,
                              [index]: 'Match 4',
                            }));
                            setAllTicketData((prev) =>
                              prev.map((ticket) =>
                                ticket.ticket === index + 1
                                  ? {
                                      ...ticket,
                                      lotteryType: '5% of prize pool',
                                    }
                                  : ticket
                              )
                            );
                            setShowLotteryTypePopup(null); 
                          }}
                        >
                          <BsStars
                            color="#e1a80d"
                            className="w-5 h-5 my-auto"
                          />
                          <div className="">
                            <p className="font-semibold">Match 4</p>
                            <p className="text-xs text-BP-opacited-white">
                              5% of prize pool
                            </p>
                          </div>
                        </div>
                        <div
                          className="flex space-x-3 hover:bg-[#72519F] px-3 hover:cursor-pointer"
                          onClick={() => {
                            setSelectedLotteryTypes((prev) => ({
                              ...prev,
                              [index]: '5 Star',
                            }));
                            setAllTicketData((prev) =>
                              prev.map((ticket) =>
                                ticket.ticket === index + 1
                                  ? {
                                      ...ticket,
                                      lotteryType: '10% of prize pool',
                                    }
                                  : ticket
                              )
                            );
                            setShowLotteryTypePopup(null); 
                          }}
                        >
                          <CiStar color="#e1a80d" className="w-5 h-5 my-auto" />
                          <div className="">
                            <p className="font-semibold">Match 5</p>
                            <p className="text-xs text-BP-opacited-white">
                              10% of prize pool
                            </p>
                          </div>
                        </div>
                        <div
                          className="flex space-x-3 hover:bg-[#72519F] px-3 hover:cursor-pointer"
                          onClick={() => {
                            setSelectedLotteryTypes((prev) => ({
                              ...prev,
                              [index]: 'Crypto jackpot',
                            }));
                            setAllTicketData((prev) =>
                              prev.map((ticket) =>
                                ticket.ticket === index + 1
                                  ? {
                                      ...ticket,
                                      lotteryType: '80% of prize pool',
                                    }
                                  : ticket
                              )
                            );
                            setShowLotteryTypePopup(null);
                          }}
                        >
                          <SiBitcoinsv
                            color="#e1a80d"
                            className="w-5 h-5 my-auto"
                          />
                          <div className="">
                            <p className="font-semibold">Crypto jackpot</p>
                            <p className="text-xs text-BP-opacited-white">
                              80% of prize pool
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                  </div> */}

                  <div className="max-sm:text-center">
                    <p className="">Crypto Jackpot</p>
                    <p className="text-[11px] text-BP-gold">'80% of prize pool'</p>
                  </div>


                </div>
              ))}

              <div
                className="sticky bottom-0 bg-BP-black w-full sm:text-center p-5"
              >
                <button onClick={() => {
                  setShowAssignTicketModal(false),
                    setShowConfirmAssignModal(true);
                }} className="bg-BP-gold hover:scale-110 transition-all duration-300 ease-out rounded-full w-full sm:w-[50%] text-BP-black py-1">
                  Confirm
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showConfirmAssignModal && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
          style={{ zIndex: 100 }}
        >
          <div className="bg-[#ffffff20] rounded-3xl max-[500px]:w-[95vw] w-[90vw] lg:w-[75vw] h-[90vh] flex justify-center items-center relative">
            <button
              onClick={() => {
                setShowConfirmAssignModal(false);
              }}
              className="absolute top-5 right-5 text-white text-lg"
            >
              <ImCross
                color="#ffffff"
                className="w-8 h-8 hover:bg-[#ffffff60] p-2 rounded-full"
              />
            </button>

            <div className="rounded-3xl bg-BP-opacited-white p-5 text-BP-black max-[500px]:w-[90vw] w-96">
              <div className="px-5">
                You can either let the system generate numbers for you (Quick
                Pick) or manually choose your own.
              </div>
              <div className="px-5 font-bold text-xl">
                Choose Selection Mode
              </div>
              <div className="flex justify-between mt-10 max-[415px]:text-xs">
                <button
                  onClick={() => {
                    setShowTicketNumsModal(true), setManualMode(false);
                  }}
                  className="rounded-full shadow-[5px_5px_20px_0px_#000000] bg-BP-gold hover:bg-yellow-400 px-5 py-1.5"
                >
                  Quick Pick (Auto)
                </button>
                <button
                  onClick={() => {
                    setShowTicketNumsModal(true), setManualMode(true);
                  }}
                  className="rounded-full shadow-[5px_5px_20px_0px_#000000] bg-[#3A82F7] hover:bg-blue-400 px-5 py-1.5 text-white"
                >
                  Manual Selection
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showTicketNumsModal && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
          style={{ zIndex: 100 }}
        >
          <div className="bg-[#ffffff20] rounded-3xl max-[500px]:w-[95vw] w-[90vw] lg:w-[75vw] h-[90vh] flex justify-center items-center relative">
            <button
              onClick={() => {
                setShowTicketNumsModal(false); setInvalidInputs(false)
              }}
              className="absolute top-5 right-5 text-white text-lg"
            >
              <ImCross
                color="#ffffff"
                className="w-8 h-8 hover:bg-[#ffffff60] p-2 rounded-full"
              />
            </button>

            <div className="rounded-3xl max-h-[80%] overflow-y-auto bg-[#111828] max-sm:p-1 p-5 space-y-3">
              <div className={`flex ${manualMode ? 'justify-between' : 'justify-end'}`}>
                {manualMode && <div className="text-center py-1.5"> <span className="text-BP-gold">Note :</span> Choose numbers from 01 to 49</div>}
                <div onClick={toShowTicketQtyModal} className="bg-gray-600/50 px-3 py-1 rounded-full flex items-center justify-center text-lg sm:text-xl font-bold cursor-pointer hover:bg-[#F59E0B]">+</div>
              </div>
              {!manualMode
                ? ticketNumbers.map((numbers, index) => (
                  <div
                    key={index}
                    className="p-5 mx-5 bg-[#20293880] rounded-3xl space-y-3"
                  >
                    <div className="flex justify-between">
                      <p className="text-BP-gold my-auto">
                        Ticket {index + 1}
                      </p>
                      <div className="space-x-1">
                        <button
                          onClick={() => refreshNum(index)}
                          className="text-white text-lg"
                        >
                          <HiOutlineRefresh
                            color="#ffffff"
                            className="w-8 h-8 bg-BP-gray-50-opc hover:bg-[#ffffff60] p-1.5 rounded-full"
                          />
                        </button>
                        <button
                          onClick={() => cancelTicket(index)}
                          className="text-white text-lg"
                        >
                          <IoClose
                            color="#ffffff"
                            className="w-8 h-8 bg-BP-gray-50-opc hover:bg-[#ffffff60] p-1.5 rounded-full"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="flex justify-between space-x-1.5">
                      {numbers.map(
                        (
                          num,
                          i // Split the array of six two-digit numbers
                        ) => (
                          <div
                            key={i}
                            className="text-BP-opacited-white text-lg font-bold bg-[#384152] w-fit p-2 rounded-3xl"
                          >
                            {num}
                          </div>
                        )
                      )}
                    </div>
                  </div>
                ))
                : ticketNumbers.map((_, index) => (
                  <div
                    key={index}
                    className="p-5 mx-5 bg-[#20293880] rounded-3xl space-y-3"
                  >
                    <div className="flex justify-between">
                      <p className="text-BP-gold my-auto">
                        Ticket {index + 1}
                      </p>
                      <button
                        onClick={() => cancelTicket(index)}
                        className="text-white text-lg"
                      >
                        <IoClose
                          color="#ffffff"
                          className="w-8 h-8 bg-BP-gray-50-opc hover:bg-[#ffffff60] p-1.5 rounded-full"
                        />
                      </button>
                    </div>

                    <div className="flex justify-between space-x-1.5">
                      {Array.from({ length: 6 }).map((_, i) => (

                        <div key={i} className='relative'>



                          {invalidInputs[`${index}-${i}`] && (
                            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 z-50">
                              {/* Tooltip container */}
                              <div className="relative bg-BP-black text-white px-3 py-2 rounded-lg shadow-lg">

                                <MdDoNotDisturbAlt className='text-red-500' />

                                {/* Triangle Arrow */}
                                <div className="absolute left-1/2 bottom-[-6px] transform -translate-x-1/2 w-3 h-3 bg-BP-black rotate-45" />
                              </div>
                            </div>
                          )}




                          <input
                            key={i}
                            type="text"
                            inputMode="numeric"
                            maxLength="2"
                            pattern="\d*"
                            ref={(el) => {
                              if (!inputRefs.current[index]) inputRefs.current[index] = [];
                              inputRefs.current[index][i] = el;
                            }}
                            className={`text-BP-opacited-white text-lg font-bold bg-[#384152] w-10 p-2 rounded-3xl text-center ${invalidInputs[`${index}-${i}`] ? 'outline-2 outline outline-red-500' : ''
                              }`}
                            onChange={(e) => {
                              let value = e.target.value.replace(/\D/g, '');
                              if (value.length > 2) value = value.slice(0, 2);

                              // Check for > 49 or duplicate numbers and mark as invalid
                              if (value && (parseInt(value) > 49 || validateUniqueNumbers(index, value, i))) {
                                setInvalidInputs((prev) => ({ ...prev, [`${index}-${i}`]: true }));
                              } else {
                                setInvalidInputs((prev) => ({ ...prev, [`${index}-${i}`]: false }));
                              }

                              // Update the ticketNumbers state
                              setTicketNumbers((prev) => {
                                const updatedTickets = [...prev];
                                const currentTicket = updatedTickets[index] || Array(6).fill('');
                                currentTicket[i] = value;
                                updatedTickets[index] = currentTicket;
                                return updatedTickets;
                              });

                              e.target.value = value;

                              if (value.length === 2 && parseInt(value) <= 49 && !validateUniqueNumbers(index, value, i) && i < 5) {
                                inputRefs.current[index][i + 1]?.focus();
                              }
                            }}
                            onBlur={(e) => {
                              let value = e.target.value;
                              if (value.length === 1) {
                                e.target.value = '0' + value;
                              }
                            }}
                          />
                        </div>
                      ))}
                    </div>

                  </div>
                ))}

              <div className="bg-BP-dark-grayish-blue sticky -bottom-5 flex justify-between max-[500px]:space-x-2.5 space-x-5 max-sm:p-2.5 max-[500px]:text-xs py-2.5">
                <div
                  onClick={() => {
                    manualMode ? (setManualMode(false)) : setManualMode(true);
                  }}
                  className="flex hover:flex-row-reverse justify-center items-center space-x-2 hover:cursor-pointer hover:text-BP-gold"
                >
                  <BsArrowRight color="#ffffff" className="w-5 h-5 ml-1" />
                  <p className="">
                    {manualMode
                      ? 'Switch to quick pick'
                      : 'Switch to manual selection'}
                  </p>
                </div>

                <button
                  className="flex justify-center items-center bg-BP-purple hover:bg-[#571c8680] rounded-full py-1 px-2 space-x-2 hover:cursor-pointer"
                  onClick={() => setShowTktPay(true)}
                  disabled={lotteryPaused || purchaseIsLoading}
                >
                  <img src={checkMark} alt="" className="" />
                  {purchaseIsLoading ? <Spin size="small" /> : <p className=""> {lotteryPaused ? "Lottery Paused" : "Purchase tickets"}</p>}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showTktPay &&

        <div
          className="fixed inset-0 flex items-center justify-center bg-BP-dark-grayish-blue overflow-y-auto"
          style={{ zIndex: 100 }}
        >
          <button
            onClick={() => {
              setShowTktPay(false);
            }}
            className="absolute top-5 left-5 text-BP-opacited-white"
          >
            &larr; Back
          </button>

          {/* Lottery Ticket Payment Component */}
          <LotteryTktPay
            allTicketData={allTicketData}
            closeNumsModal={() => setShowTicketNumsModal(false)}
            closeInvalidInputs={() => setInvalidInputs(false)}
            closeConfirmAssignModal={() => setShowConfirmAssignModal(false)}
            setTicketPurchased={() => setTicketPurchased(ticketPurchased + 1)}
            closeTktPay={() => setShowTktPay(false)}
          />
        </div>
      }

      {showWinnersModal && (
        <MatchWinnersModal
          matches={selectedMatch}
          winners={matchWinners}
          winningNumbers={matchWinningNumbers}
          onClose={() => setShowWinnersModal(false)}
        />
      )}
      <Footer />
    </div>
  );
};
