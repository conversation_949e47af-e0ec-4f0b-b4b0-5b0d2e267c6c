import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import panther from "../../assets/images/queenpanther2.png";
import bg from "../../assets/presale/Desktop - 11.png";
import { ResetPassword } from "./reset_password";
import { AuthService } from "../../services/authService";
import { AppRoutesPaths } from "../../route/app_route";

export const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      let payload = {
        email: email.trim().toLowerCase(),
      };
      const result = await AuthService.ForgotPassword(payload);
      if (result) {
        setShowModal(true);
      } else {
        setShowModal(false);
      }
    } catch (error) {
      console.error("Forgot password error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  

  return (
    <div className="flex justify-center items-center min-h-screen bg-BP-lightbaige   px-4 bg-cover bg-center py-10">
      {/* Back Button */}
      <div className="absolute top-4 left-4">
        <button
          onClick={() => navigate(AppRoutesPaths.login)}
          className="text-black text-sm mb-6 flex items-center space-x-2"
        >
          <span className="mb-2 text-lg">&larr;</span>
          <span>Back</span>
        </button>
      </div>

      <div className="w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <Link to="/">
            <img src={panther} alt="Panther Logo" className="w-14 h-14 bg-white rounded-xl" />
          </Link>
        </div>

        {/* Forgot Password Form */}
        <div className="bg-BP-opacited-white rounded-xl p-6 md:p-8 shadow-lg">
          <h2 className="text-2xl text-black font-bold text-center mb-2">Forgot Password</h2>
          <p className="text-black text-center mb-6">
            Enter your email address and we'll send you a code to reset your password.
          </p>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="relative">
                <label className="block text-gray-700 text-sm font-medium mb-1">Email address*</label>
                <div className="relative">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-black text-black border-gray-700"
                    required
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading || !email}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors mt-4 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Sending...
                  </>
                ) : (
                  "Reset Password"
                )}
              </button>
            </div>
          </form>

          {showModal && <ResetPassword onClose={() => setShowModal(false)} email={email} />}
        </div>
      </div>
    </div>
  );
};