import React, { useRef, useState, useEffect } from "react";
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { useNavigate } from "react-router-dom";
import { AppRoutesPaths } from "../../route/app_route";
import { toast } from 'react-toastify';
import { AuthService } from "../../services/authService";

export const ResetPassword = ({ onClose, email }) => {
  const [code, setCode] = useState(["", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const navigate = useNavigate();
  const modalRef = useRef();

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleChange = (index, value) => {
    if (value.length > 4) return;
    
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.querySelector(`input[name="code-${index + 1}"]`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="code-${index - 1}"]`);
      if (prevInput) {
        prevInput.focus();
        // Clear previous input
        const newCode = [...code];
        newCode[index - 1] = '';
        setCode(newCode);
      }
    }
  };

  const handleVerify = async (e) => {
    e.preventDefault();
    const verificationCode = code.join("");
    if (verificationCode.length !== 5) {
      toast.error('Please enter the complete verification code');
      return;
    }

    setIsLoading(true);
    try {
      let payload = {
        email,
        code : verificationCode,
        type: "reset"
      }
      const result = await AuthService.verifyCode(payload)
      
      if (result) {
        localStorage.setItem('reset_email', email);
        localStorage.setItem('reset_code_verified', 'true');
        onClose();
        navigate(AppRoutesPaths.changepassword);
      } else {
        setCode(["", "", "", "", ""]);
      }
    } catch (error) {
      console.error('Verification error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendLoading || countdown > 0) return;
    
    setResendLoading(true);
    try {
      const result = await AuthService.ResendResetCode({ email: email });
      
      if (result) {
        setCode(["", "", "", "", ""]);
        setCountdown(30);
      } else {
        setCode(["", "", "", "", ""]);
      }
    } catch (error) {
      console.error('Resend code error:', error);
    } finally {
      setResendLoading(false);
    }
  };
  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData("Text").trim();
    if (/^\d{5}$/.test(pasteData)) {
      const digits = pasteData.split("");
      setCode(digits);
      const lastInput = document.querySelector(`input[name="code-4"]`);
      if (lastInput) lastInput.focus();
    } else {
      toast.error("Please paste a valid 5-digit code");
    }
  };



  return (
    <div 
      ref={modalRef}
      onClick={(e) => e.target === modalRef && onClose()}
      className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 p-4"
    >
      <div className="bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 max-w-md w-full relative animate-fadeIn border border-gray-700 shadow-2xl">
        <button 
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition"
          aria-label="Close modal"
        >
          <CloseOutlined className="text-xl" />
        </button>

        <div className="text-center space-y-6">
          <h2 className="text-2xl font-bold text-white">
            Verify Reset Code
          </h2>
          
          <div className="space-y-2">
            <p className="text-gray-300">
              Please enter the verification code sent to:
            </p>
            <p className="font-medium text-yellow-400 break-all">
              {email}
            </p>
          </div>

          <form onSubmit={handleVerify} className="space-y-6">
            <div className="space-y-4">
              <div className="flex justify-center gap-2">
                {code.map((digit, index) => (
                  <input
                    key={index}
                    name={`code-${index}`}
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 bg-gray-800 text-white text-center text-xl rounded-lg border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                    value={digit}
                    onChange={(e) => handleChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onPaste={index === 0 ? handlePaste : undefined}
                    disabled={isLoading}
                  />
                ))}
              </div>
              <p className="text-gray-400 text-sm">
                Haven't received the code? Check your spam folder
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                disabled={isLoading || code.join("").length !== 5}
                className="flex-1 bg-yellow-500 text-black font-semibold px-6 py-3 rounded-lg hover:bg-yellow-400 active:bg-yellow-600 transition-all transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                    </svg>
                    Verifying...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <CheckOutlined />
                    Verify Code
                  </div>
                )}
              </button>

              <button
                type="button"
                onClick={handleResendCode}
                disabled={resendLoading || countdown > 0}
                className="text-yellow-400 hover:text-yellow-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed transition"
              >
                {resendLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                    </svg>
                    Sending...
                  </div>
                ) : countdown > 0 ? (
                  `Resend in ${countdown}s`
                ) : (
                  "Resend Code"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};