import  { useState } from "react";
import {  useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { AuthService } from "../../services/authService";
import WebLogo from '../../assets/images/queenpanther2.png';
import { Link } from "react-router-dom";

// ...imports remain the same

export const VerifyEmail = ({ userEmail, onSuccess, type }) => {
  const [code, setCode] = useState(["", "", "", "", ""]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const navigate = useNavigate();

  const handleChange = (index, value) => {
    if (!/^\d*$/.test(value)) return;
    if (value.length > 1) return;

    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    if (value && index < 4) {
      const nextInput = document.querySelector(`input[name="code-${index + 1}"]`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !code[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="code-${index - 1}"]`);
      if (prevInput) {
        prevInput.focus();
        const newCode = [...code];
        newCode[index - 1] = "";
        setCode(newCode);
      }
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData("Text").trim();
    if (/^\d{5}$/.test(pasteData)) {
      const digits = pasteData.split("");
      setCode(digits);
      const lastInput = document.querySelector(`input[name="code-4"]`);
      if (lastInput) lastInput.focus();
    } else {
      toast.error("Please paste a valid 5-digit code");
    }
  };

  const handleVerify = async () => {
    const verificationCode = code.join("");
    if (verificationCode.length !== 5) {
      toast.error("Please enter the complete verification code");
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await AuthService.verifyCode({
        email: userEmail,
        code: verificationCode,
        type
      });

      if (result) {
        onSuccess();
      } else {
        setCode(["", "", "", "", ""]);
      }
    } catch (error) {
      console.error("Verification error:", error);
      setCode(["", "", "", "", ""]);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (!userEmail) {
      toast.error("No email address found");
      return;
    }

    setIsResending(true);
    try {
      const result = await AuthService.ResendConfirmCode({ email: userEmail });

      if (result) {
        setCode(["", "", "", "", ""]);
      }
    } catch (error) {
      console.error("Resend code error:", error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen BP-lightbaige px-4 bg-cover bg-center py-10">
      <div className="bg-gray-900 p-8 rounded-lg shadow-lg text-white w-112 lg:w-1/3">
        <div className="flex justify-center mb-4">
          <div className="bg-white rounded-xl p-1 shadow-md">
            <Link to="/">
              <img src={WebLogo} alt="Panther Logo" className="w-12 h-12" />
            </Link>
          </div>
        </div>

        <h2 className="text-center text-2xl font-semibold">Verify your Email</h2>
        <p className="text-center text-gray-400 text-sm mt-2 flex items-center gap-2 justify-center">
          <span>📧</span> We have sent a confirmation code to
        </p>
        <p className="text-center text-yellow-400 font-medium">{userEmail}</p>
        <p className="text-center text-gray-400 text-sm">Enter the code below to verify your email</p>

        <div className="mt-6 flex justify-center gap-2">
          {code.map((digit, index) => (
            <input
              key={index}
              name={`code-${index}`}
              type="text"
              inputMode="numeric"
              pattern="\d*"
              maxLength="1"
              className="w-12 h-12 bg-gray-800 text-white text-center rounded-lg border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400 text-xl"
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onPaste={index === 0 ? handlePaste : undefined}
              disabled={isSubmitting}
            />
          ))}
        </div>

        <div className="flex flex-row justify-between items-center mt-6">
          <button
            onClick={handleResendCode}
            className="text-gray-400 text-sm hover:text-yellow-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isResending || isSubmitting}
          >
            {isResending ? "Sending..." : "Resend code"}
          </button>

          <button
            onClick={handleVerify}
            disabled={isSubmitting || isResending || code.join("").length !== 5}
            className="w-1/2 bg-yellow-500 hover:bg-yellow-600 text-black py-3 rounded-full font-semibold disabled:opacity-50 disabled:cursor-not-allowed transition-all"
          >
            {isSubmitting ? "Verifying..." : "Verify"}
          </button>
        </div>
      </div>
    </div>
  );
};
