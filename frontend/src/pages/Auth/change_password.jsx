import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { AppRoutesPaths } from "../../route/app_route";
import { toast } from 'react-toastify';
import panther from "../HomePage/panter.png";
import WebLogo from '../../assets/images/queenpanther2.png';
import bg from "../../assets/presale/Desktop - 11.png";
import { AuthService } from "../../services/authService";

export const ChangePassword = () => {
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: ""
  });
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [validations, setValidations] = useState({
    hasNumber: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasSpecialChar: false,
    hasMinLength: false
  });

  const navigate = useNavigate();

  useEffect(() => {
    const email = localStorage.getItem('reset_email');
    const isVerified = localStorage.getItem('reset_code_verified');

    if (!email || !isVerified) {
      toast.error('Please complete email verification first');
      navigate(AppRoutesPaths.forgotpassword);
    }
  }, []);

  const validatePassword = (password) => {
    setValidations({
      hasNumber: /\d/.test(password),
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasSpecialChar: /[@$!%*?&]/.test(password),
      hasMinLength: password.length >= 8
    });
  };

   // Check password match whenever either password changes
  useEffect(() => {
    if (formData.newPassword && formData.confirmPassword) {
      setPasswordsMatch(formData.newPassword === formData.confirmPassword);
    }
  }, [formData.newPassword, formData.confirmPassword]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    if (name === 'newPassword') {
      validatePassword(value);
    }

    if (name === 'confirmPassword' || name === 'newPassword') {
      if (formData.newPassword && formData.confirmPassword) {
        setPasswordsMatch(formData.newPassword === (name === 'confirmPassword' ? value : formData.confirmPassword));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { newPassword, confirmPassword } = formData;

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (!Object.values(validations).every(Boolean)) {
      toast.error('Please meet all password requirements');
      return;
    }

    setIsLoading(true);
    try {
      const email = localStorage.getItem('reset_email');
      let payload = {
        email,
        newPassword
      };

      const result = await AuthService.ChangePassword(payload);

      if (result) {
        localStorage.removeItem('reset_email');
        localStorage.removeItem('reset_code_verified');
        navigate(AppRoutesPaths.login);
      }
    } catch (error) {
      console.error('Change password error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-BP-lightbaige    px-4 bg-cover bg-center py-10">
      {/* Back Button */}
      <div className="absolute top-4 left-4">
        <button
          onClick={() => navigate(AppRoutesPaths.forgotpassword)}
          className="text-black text-sm mb-6 flex items-center space-x-2"
        >
          <span className="mb-2 text-lg">&larr;</span>
          <span>Back</span>
        </button>
      </div>

      <div className="w-full max-w-md mx-auto">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <Link to="/">
            <img src={WebLogo} alt="Panther Logo" className="w-14 h-14 bg-white shadow-md rounded-xl" />
          </Link>
        </div>

        {/* Change Password Form */}
        <div className="bg-BP-opacited-white rounded-xl p-6 md:p-8 shadow-lg">
          <h2 className="text-2xl text-black font-bold text-center mb-2">Create New Password</h2>
          <p className="text-black text-center mb-6">
            Please enter a new password that you haven't used before.
          </p>

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="relative">
                <label className="block text-gray-700 text-sm font-medium mb-1">New Password*</label>
                <div className="relative">
                  <input
                    type={showNewPassword ? "text" : "password"}
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleChange}
                    placeholder="Enter new password"
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-black text-black pr-10 border-gray-700"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  >
                    {showNewPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  </button>
                </div>
                <ul className="text-sm mt-2">
                  <li className={validations.hasMinLength ? "text-green-500" : "text-red-500"}>At least 8 characters</li>
                  <li className={validations.hasUpperCase ? "text-green-500" : "text-red-500"}>At least one uppercase letter</li>
                  <li className={validations.hasLowerCase ? "text-green-500" : "text-red-500"}>At least one lowercase letter</li>
                  <li className={validations.hasNumber ? "text-green-500" : "text-red-500"}>At least one number</li>
                  <li className={validations.hasSpecialChar ? "text-green-500" : "text-red-500"}>At least one special character</li>
                </ul>

              </div>

              <div className="relative">
                <label className="block text-gray-700 text-sm font-medium mb-1">Confirm Password*</label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    placeholder="Confirm new password"
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-black text-black pr-10 border-gray-700"
                    required
                  />
                  {formData.confirmPassword && !passwordsMatch && (
                    <p className="text-red-500 text-sm mt-1">Passwords do not match</p>
                  )}
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className={`absolute right-3 ${formData.confirmPassword && !passwordsMatch ? 'top-2' : 'top-1/2 transform -translate-y-1/2'} text-gray-500`}
                  >
                    {showConfirmPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading || !Object.values(validations).every(Boolean) || !formData.confirmPassword}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors mt-4 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Changing Password...
                  </>
                ) : (
                  "Change Password"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};