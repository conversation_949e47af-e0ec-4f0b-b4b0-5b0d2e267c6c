import React, { useState, useEffect } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import { toast } from "react-toastify";
import { 
  EyeOutlined, 
  EyeInvisibleOutlined, 
  MailOutlined, 
  UserOutlined, 
  CheckCircleOutlined 
} from '@ant-design/icons';
import panther from "../../assets/images/queenpanther2.png";
import bg from "../../assets/presale/Desktop - 11.png";
import { AppRoutesPaths } from "../../route/app_route";
import { AuthService } from "../../services/authService";
import { VerifyEmail } from "./Verify";
import affiliateLinkService from "../../services/affiliateLinkService";

export const Signup = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    // walletAddress: ""
  });
  const [showVerifyForm, setShowVerifyForm] = useState(false);
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(false);
  const [referralInfo, setReferralInfo] = useState(null);
  const [hasUrlReferralParams, setHasUrlReferralParams] = useState(false);
  const [referralLinkStatus, setReferralLinkStatus] = useState(null); // To track link validation status

  const navigate = useNavigate();

  // Extract referral parameters from URL on component mount and keep them persistent
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get('ref');
    const token = urlParams.get('token');
    
    if (refCode && token) {
      // Always store in localStorage when found in URL (overwrite any existing)
      localStorage.setItem('referral_code', refCode);
      localStorage.setItem('referral_token', token);
      localStorage.setItem('referral_link_used', 'true');
      
      // Set referral info for display banner
      setReferralInfo({ refCode, token });
      setHasUrlReferralParams(true); // Mark that URL has referral params
      
      // Validate the referral link to show status in banner
      validateReferralLinkForDisplay(refCode, token);
    } else {
      // No URL parameters - this is normal signup
      setHasUrlReferralParams(false);
      setReferralLinkStatus(null);
      
      // Check if we have stored referral data from previous session
      const storedRefCode = localStorage.getItem('referral_code');
      const storedToken = localStorage.getItem('referral_token');
      
      if (storedRefCode && storedToken) {
        console.log('Found existing referral data from localStorage (but no URL params):', { 
          refCode: storedRefCode, 
          token: storedToken 
        });
        // Keep referral info for backend processing but don't show banner
        setReferralInfo({ refCode: storedRefCode, token: storedToken });
      } else {
        // No referral data at all - normal signup
        setReferralInfo(null);
        
        // Clean up any partial referral data
        localStorage.removeItem('referral_code');
        localStorage.removeItem('referral_token');
        localStorage.removeItem('referral_link_used');
      }
    }
  }, []);

  // Validate referral link for display purposes (don't block signup)
  const validateReferralLinkForDisplay = async (refCode, token) => {
    try {
      const validation = await affiliateLinkService.validateAffiliateLink(refCode, token);
      
      if (!validation.valid) {
        // Clean up error message by removing the (x/y) pattern
        let cleanMessage = validation.message || 'Invalid or expired affiliate link';
        cleanMessage = cleanMessage.replace(/\s*\(\d+\/\d+\)$/, '');
        
        setReferralLinkStatus({
          valid: false,
          message: cleanMessage,
          type: 'error'
        });
      } else {
        setReferralLinkStatus({
          valid: true,
          message: `Joining through ${validation.data.referrerName}'s invite! ${validation.data.spotsRemaining} spots remaining.`,
          type: 'success',
          data: validation.data
        });
      }
    } catch (error) {
      console.error('Display validation failed:', error);
      
      let errorMessage = 'Invalid or expired affiliate link';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      // Clean up error message by removing the (x/y) pattern
      errorMessage = errorMessage.replace(/\s*\(\d+\/\d+\)$/, '');
      
      setReferralLinkStatus({
        valid: false,
        message: errorMessage,
        type: 'error'
      });
    }
  };

  // Check password match whenever either password changes
  useEffect (() => {
    if (formData.password && formData.confirmPassword) {
      setPasswordsMatch(formData.password === formData.confirmPassword);
    }
  }, [formData.password, formData.confirmPassword]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  
    // Immediately check password match when confirmPassword changes
    if (name === 'confirmPassword' || name === 'password') {
      if (formData.password && formData.confirmPassword) {
        setPasswordsMatch(formData.password === (name === 'confirmPassword' ? value : formData.confirmPassword));
      }
    }
  };
  

  const validateForm = () => {
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      toast.error("Please enter your full name");
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return false;
    }

    if (formData.password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return false;
    }

    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#@$!%*?&.()\-])[A-Za-z\d#@$!%*?&.()\-]{8,}$/;
    if (!passwordRegex.test(formData.password)) {
      toast.error("Password must contain at least one uppercase letter, one lowercase letter, one number and one special character");
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      return false;
    }

    if (!agreedToPrivacy) {
      toast.error("Please agree to the privacy policy");
      return false;
    }

    return true;
  };

  const onSignUp = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      
      // ONLY check for referral data and validate if this signup came from a referral URL
      // Don't validate if this is a normal signup (no URL parameters)
      if (hasUrlReferralParams) {
        const refCode = localStorage.getItem('referral_code');
        const token = localStorage.getItem('referral_token');
        
        if (refCode && token) {
          try {
            const validation = await affiliateLinkService.validateAffiliateLink(refCode, token);            
            if (!validation.valid) {
              // Show the specific error message from backend (cleaned up)
              let errorMessage = validation.message || 'Invalid or expired affiliate link';
              errorMessage = errorMessage.replace(/\s*\(\d+\/\d+\)$/, '');
              toast.error(errorMessage);
              setIsLoading(false);
              // DON'T clear referral data here - keep it so user sees the error consistently
              return;
            } else {
              toast.info(`Joining through ${validation.data.referrerName}'s invite! ${validation.data.spotsRemaining} spots remaining.`);
            }
          } catch (error) {
            console.error('Validation failed:', error);
            console.error('Error response data:', error.response?.data);
            
            // Extract specific error message from backend response (cleaned up)
            let errorMessage = 'Invalid or expired affiliate link';
            if (error.response?.data?.message) {
              errorMessage = error.response.data.message;
            } else if (error.message) {
              errorMessage = error.message;
            }
            
            // Clean up error message by removing the (x/y) pattern
            errorMessage = errorMessage.replace(/\s*\(\d+\/\d+\)$/, '');
            
            toast.error(errorMessage);
            setIsLoading(false);
            // DON'T clear referral data here - keep it so user sees the error consistently  
            return;
          }
        }
      }

      // Save form data (except confirmPassword) in localStorage
      const signupData = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      };
      localStorage.setItem("signup_data", JSON.stringify(signupData));

      // Send verification code
      const result = await AuthService.sendVerificationCode({ email: signupData.email });
      if (result) {
        setShowVerifyForm(true);
      } else {
        setShowVerifyForm(false);
      }
    } catch (error) {
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  const onVerifySuccess = async () => {
    setIsLoading(true);
    try {
      // Retrieve signup data from localStorage
      const signupData = JSON.parse(localStorage.getItem("signup_data"));
      if (!signupData) {
        toast.error("Signup data missing. Please start again.");
        setShowVerifyForm(false);
        return;
      }
      
      // ONLY add referral data to signup if this came from a referral URL
      let refCode = null;
      let token = null;
      
      if (hasUrlReferralParams) {
        refCode = localStorage.getItem('referral_code');
        token = localStorage.getItem('referral_token');
        
        if (refCode && token) {
          signupData.affiliateCode = refCode;
          signupData.referralToken = token;
        }
        } else {
          // Normal signup without referral
        }      const result = await AuthService.SignUpAuth(signupData);

      if (result && result.user) {
        // Clean up signup data
        localStorage.removeItem("signup_data");
        
        // ONLY process affiliate tracking if this was a referral signup
        if (hasUrlReferralParams && refCode && token && result.user._id) {
          
          try {
            const trackingResult = await affiliateLinkService.trackAffiliateSignup(refCode, token, result.user._id);
            
            if (trackingResult.success) {
              const rewards = trackingResult.data.rewardsEarned;
              let message = "Account created successfully through invite link!";
              if (rewards.bonus) {
                message += ` Bonus rewards unlocked for the referrer!`;
              }
              toast.success(message);
              
              // Verify the count was updated by fetching fresh stats
              try {
                const updatedStats = await affiliateLinkService.getAffiliateLinkStats(refCode);
              } catch (statsError) {
                console.error('Failed to fetch updated stats:', statsError);
              }
            } else {
              console.warn('Affiliate tracking returned success=false:', trackingResult);
            }
          } catch (error) {
            console.error("Failed to track affiliate signup:", error);
            console.error("Error response:", error.response?.data);
            // Don't show error to user, just log it
          }
          
          // Clean up referral data
          localStorage.removeItem('referral_code');
          localStorage.removeItem('referral_token');
          localStorage.removeItem('referral_link_used');
        } else {
          // Standard account creation - no referral tracking needed
          toast.success("Account created successfully!");
        }
        
        navigate(AppRoutesPaths.login);
      } else {
        setShowVerifyForm(false);
      }
    } catch (error) {
      console.error('Signup error:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      {showVerifyForm ? (
        <VerifyEmail userEmail={formData.email} type="signup" onSuccess={() => onVerifySuccess()} />
      ) : (
        <div className="flex justify-center items-center min-h-screen bg-BP-lightbaige   px-4 bg-cover bg-center py-10">
          
          {/* Back Button */}
          <div className="absolute top-4 left-4">
            <button
              onClick={() => navigate(-1)}
              className="text-black text-sm mb-6 flex items-center space-x-2"
            >
              <span className="mb-1 text-lg">&larr;</span>
              <span>Back</span>
            </button>
          </div>

          <div className="w-full max-w-lg mx-auto">
            {/* Logo */}
            <div className="flex justify-center mb-6">
            <Link to="/">
              <img src={panther} alt="Panther Logo" className="w-14 h-14 bg-white rounded-xl" />
            </Link>
            </div>
            
            {/* Signup Form */}
            <div className="bg-BP-opacited-white rounded-xl p-6 md:p-10 lg:p-12 shadow-lg">
              {/* Referral Link Status Banner - only show when URL has referral parameters */}
              {hasUrlReferralParams && referralInfo && referralInfo.refCode && referralInfo.token && (
                <div className={`mb-4 p-3 border rounded-lg ${
                  referralLinkStatus?.valid === false 
                    ? 'bg-red-50 border-red-200' 
                    : referralLinkStatus?.valid === true 
                      ? 'bg-blue-50 border-blue-200' 
                      : 'bg-blue-50 border-blue-200'
                }`}>
                  <div className={`flex items-center text-sm ${
                    referralLinkStatus?.valid === false 
                      ? 'text-red-500' 
                      : referralLinkStatus?.valid === true 
                        ? 'text-blue-500' 
                        : 'text-blue-500'
                  }`}>
                    <span className="mr-2">
                      {referralLinkStatus?.valid === false 
                        ? '❌' 
                        : '🔗'
                      }
                    </span>
                    <span>
                      {referralLinkStatus?.valid === false 
                        ? referralLinkStatus.message 
                        : `Signing up through referral link (Code: ${referralInfo.refCode})`
                      }
                    </span>
                  </div>
                </div>
              )}
              
              <h2 className="text-2xl text-black font-bold text-center mb-2">Create a new account</h2>
              <p className="text-black text-center mb-6">
                Already a member? <span className="text-blue-600 font-semibold cursor-pointer hover:underline" onClick={() => navigate(AppRoutesPaths.login)}>LOGIN</span>
              </p>
              
              <form onSubmit={onSignUp}>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="relative">
                      <label className="block text-gray-700 text-sm font-medium mb-1">First name*</label>
                      <div className="relative">
                        <UserOutlined className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          placeholder="Enter first name"
                          className="w-full pl-4 pr-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none text-black"
                          required
                        />
                      </div>
                    </div>
                    <div className="relative">
                      <label className="block text-gray-700 text-sm font-medium mb-1">Last name*</label>
                      <div className="relative">
                        <UserOutlined className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          placeholder="Enter last name"
                          className="w-full pl-4 pr-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                          required
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="relative">
                    <label className="block text-gray-700 text-sm font-medium mb-1">Email address*</label>
                    <div className="relative">
                      <MailOutlined className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter email address"
                        className="w-full pl-4 pr-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="relative">
                    <label className="block text-gray-700 text-sm font-medium mb-1">Password*</label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="Enter password"
                        className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 pr-10"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      >
                        {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                      </button>
                    </div>
                  </div>
                  
                  <div className="relative">
                    <label className="block text-gray-700 text-sm font-medium mb-1">Confirm Password*</label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        placeholder="Confirm password"
                        className={`w-full px-4 py-2 bg-white border ${passwordsMatch ? 'border-gray-300' : 'border-red-500'} rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 pr-10`}
                        required
                      />
                      {formData.confirmPassword && !passwordsMatch && (
                        <p className="text-red-500 text-sm mt-1">Passwords do not match</p>
                      )}
                       <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className={`absolute right-3 ${formData.confirmPassword && !passwordsMatch ? 'top-2' : 'top-1/2 transform -translate-y-1/2'} text-gray-500`}
                        >
                          {showConfirmPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                        </button>
                    </div>
                  </div>
                  
                  {/* <div>
                    <label className="block text-gray-700 text-sm font-medium mb-1">Wallet Address (Optional)</label>
                    <input
                      type="text"
                      name="walletAddress"
                      value={formData.walletAddress}
                      onChange={handleChange}
                      placeholder="Enter wallet address"
                      className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 border-gray-700"
                    />
                  </div> */}
                  
                  <div className="flex items-center">
                    <div
                      onClick={(e) => setAgreedToPrivacy(!agreedToPrivacy)}
                      className={`h-4 w-4 flex items-center justify-center border border-gray-400 rounded cursor-pointer transition-all ${
                        agreedToPrivacy ? 'border-purple-600 text-blue-600' : ''
                      }`}
                    >
                      {agreedToPrivacy && '✔'}
                    </div>
                    <label htmlFor="privacyPolicy" className="ml-2 block text-sm text-gray-700">
                      I agree to the <span onClick={() => navigate(AppRoutesPaths.privacyPolicy)} className="underline text-gray-600 text-[15px] hover:text-blue-600 transition-colors cursor-pointer">Privacy policy</span>
                    </label>
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors mt-4 flex items-center justify-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      <>
                        <CheckCircleOutlined className="mr-2" />
                        Proceed
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
};








