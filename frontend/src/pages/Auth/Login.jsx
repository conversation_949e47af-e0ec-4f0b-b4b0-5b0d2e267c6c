import React, { useState } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { EyeOutlined, EyeInvisibleOutlined, MailOutlined, CheckCircleOutlined } from '@ant-design/icons';
import panther from "../../assets/images/queenpanther2.png";
import { AppRoutesPaths } from "../../route/app_route";
import { AuthService } from "../../services/authService";
import { useAuthentication } from "../../components/utils/provider";
import { getPostLoginRoute } from "../../utils/navigation";
import { toast } from "react-toastify";
import { VerifyEmail } from "./Verify";

export const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showVerifyForm, setShowVerifyForm] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  });

  const { getCurrentUser } = useAuthentication();
  const navigate = useNavigate();
  const location = useLocation();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Save credentials to localStorage
      const loginData = {
        email: formData.email.trim().toLowerCase(),
        password: formData.password,
      };

      // 1. Check password before sending code
    const passwordValid = await AuthService.checkPassword(loginData);
    if (!passwordValid) {
      toast.error("Invalid email or password.");
      setIsLoading(false);
      return;
    }
    
      localStorage.setItem("login_data", JSON.stringify(loginData));

      // Request backend to send verification code for login
      const result = await AuthService.sendSigninCode({ email: loginData.email });
      if (result) {
        setShowVerifyForm(true);
      } else {
        setShowVerifyForm(false);
        toast.error("Failed to send verification code.");
      }
    } catch (error) {
      toast.error("Failed to send verification code.");
      setShowVerifyForm(false);
    } finally {
      setIsLoading(false);
    }
  };

  const onVerifySuccess = async () => {
    setIsLoading(true);
    try {
      // Retrieve login data from localStorage
      const loginData = JSON.parse(localStorage.getItem("login_data"));
      if (!loginData) {
        toast.error("Login data missing. Please start again.");
        setShowVerifyForm(false);
        return;
      }

      // Now actually log in
      const result = await AuthService.SignInAuth(loginData, getCurrentUser);

      if (result) {
        toast.success("Login successful! Welcome back!");
        localStorage.removeItem("login_data");
        const origin = localStorage.getItem("login_origin") || location.state?.from || "";
        const redirectTo = getPostLoginRoute(origin);
        localStorage.removeItem("login_origin");
        navigate(redirectTo, { replace: true });
      } else {
        setShowVerifyForm(false);
      }
    } catch (error) {
      toast.error("Login failed. Please check your credentials.");
      setShowVerifyForm(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {showVerifyForm ? (
        <VerifyEmail userEmail={formData.email} type="signin" onSuccess={onVerifySuccess} />
      ) : (
        <div className="flex justify-center items-center min-h-screen bg-BP-lightbaige px-4 bg-cover bg-center py-10">
          {/* Back Button */}
          <div className="absolute top-4 left-4">
            <button
              onClick={() => navigate(-1)}
              className="text-black text-sm mb-6 flex items-center space-x-2"
            >
              <span className="mb-1 text-lg">&larr;</span>
              <span>Back</span>
            </button>
          </div>

          <div className="w-full max-w-lg mx-auto">
            {/* Logo */}
            <div className="flex justify-center mb-6">
              <Link to="/">
                <img src={panther} alt="Panther Logo" className="w-14 h-14 bg-white rounded-xl" />
              </Link>
            </div>

            {/* Login Form */}
            <div className="bg-BP-opacited-white rounded-xl p-6 md:p-8 shadow-lg">
              <h2 className="text-2xl text-black font-bold text-center mb-2">Welcome Back!</h2>
              <p className="text-black text-center mb-6">
                First time here?{" "}
                <span
                  className="text-blue-600 font-semibold cursor-pointer hover:underline"
                  onClick={() => navigate(AppRoutesPaths.signup)}
                >
                  SIGN UP
                </span>
              </p>

              <form onSubmit={handleLogin}>
                <div className="space-y-4">
                  <div className="relative">
                    <label className="block text-gray-700 text-sm font-medium mb-1">Email address*</label>
                    <div className="relative">
                      <MailOutlined className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Enter email address"
                        className="w-full pl-10 pr-4 py-2 bg-white border rounded-lg focus:outline-none focus:border-black text-black border-gray-700"
                        required
                      />
                    </div>
                  </div>

                  <div className="relative">
                    <label className="block text-gray-700 text-sm font-medium mb-1">Password*</label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="Enter password"
                        className="w-full px-4 py-2 bg-white border  rounded-lg focus:outline-none focus:border-black text-black pr-10 border-gray-700"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      >
                        {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                      </button>
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors mt-4 flex items-center justify-center"
                  >
                    {isLoading ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Signing in...
                      </>
                    ) : (
                      <>
                        <CheckCircleOutlined className="mr-2" />
                        Sign in
                      </>
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={() => navigate(AppRoutesPaths.forgotpassword)}
                    className="text-gray-700 text-sm hover:text-blue-600 transition-colors"
                  >
                    Forgot password?
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
};