import React from 'react';
import HeaderNew from '../components/Header/HeaderNew';
import PANTHERQGAME from "../assets/PANTHERQGAME.mp4";
import { BsTwitterX } from "react-icons/bs";
import {
  FaLinkedin,
  FaDiscord,
  FaTelegram,
  FaTiktok,
  FaInstagram,
  FaFacebook,
  FaYoutube,
  FaChevronUp, 
  FaChevronDown,
  FaChevronLeft,
  FaChevronRight,
  
} from "react-icons/fa";
import crossbow from "../assets/game/crossbow.png";
import renewableenergy from "../assets/game/renewableenergy.png";
import carrepair from "../assets/game/car-repair.png";
import fingerprint from "../assets/game/fingerprint.png";
import man from "../assets/game/man.png";
import compass from "../assets/game/compass.png";
import coin from "../assets/game/coin.png";
import star from "../assets/game/star.png";
import runningman from "../assets/game/runningman.png";
import gameaudio from "../assets/game.mp3";
import coinaudio from "../assets/coin.mp3";




export const Game = () => {

   const handleAudio = () => {
    const audio = new Audio(gameaudio);
    audio.play();
  };

  const handleClick = () => {
    const audio = new Audio(coinaudio);
    audio.play();
  };


   const stars = [
    { top: "18%", left: "35%" },
     { top: "18%", left: "43%" },
    { top: "21%", left: "57%" },
    { top: "18%", left: "70%" },
    { top: "25%", right: "25%" },
    { top: "25%", left: "25%" },
    
  ];
  return (
    <div className="relative bg-BP-dark-grayish-blue min-h-screen overflow-hidden text-white">
      {/* Background Video */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute max-md:top-80 top-0 left-0 w-[100vw] h-60 md:h-full object-cover opacity-70"
      >
        <source src={PANTHERQGAME} type="video/mp4" />
      </video>

        {/* Overlay */}
        <div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-30 z-0" />

      {/* Social Media Links */}
          <div className="md:hidden absolute -bottom-5 left-1/3 transform -translate-x-1/4 flex flex-wrap justify-center items-center gap-6 mb-8">
        <a
          href="https://t.me/blackpanthertkn"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaTelegram className="text-3xl" />
        </a>
        <a
          href="https://x.com/bpnthrx"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <BsTwitterX className="text-3xl" />
        </a>
        <a
          href="https://discord.com/invite/wVA7dqGDu2"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaDiscord className="text-3xl" />
        </a>
        <a
          href="https://www.tiktok.com/@blackpanthertkn"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaTiktok className="text-3xl" />
        </a>
        <a
          href="https://www.facebook.com/profile.php?id=100063660784177"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaFacebook className="text-3xl" />
        </a>
        <a
          href="https://www.instagram.com/blackpanthercoin"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaInstagram className="text-3xl" />
        </a>
        <a
          href="https://www.youtube.com/@blackpanthertoken"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaYoutube className="text-3xl" />
        </a>
        <a
          href="https://www.linkedin.com/company/blackpanthertkn/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
        >
          <FaLinkedin className="text-3xl" />
        </a>
      </div>


        {/* Content */}
        <div className="relative z-10">
          <HeaderNew />
          {/* for mobile --> starts here */}
          
          <div className="md:hidden flex-col p-7 space-y-1">
            <img src={star} alt="Star Icon" className="w-8 h-8 mx-auto" />
            <p className="w-fit mx-auto text-BP-gold text-[7vw] font-semibold tracking-[0.65rem]">Coming Soon</p>
            <p className="text-BP-opacited-white font-light px-3 mx-auto">We are currently working on black panther game. We will launch it soon. Stay tuned!</p>
          </div>

          <div className="md:hidden flex flex-col items-center justify-center p-4 rounded-lg w-full mx-auto">
            <div className="flex flex-row items-stretch justify-between w-full gap-4">
            <div className="flex flex-col items-center justify-between space-y-1.5 text-white -ml-9 mt-9">
              <div className="flex flex-col items-center">
                <div className="flex flex-row items-center">
                      <h2 className="text-sm ml-10">Prizes Drawn: 0</h2>
                  </div>
                <div className='flex'>
                  <div className="flex flex-col items-center">
                  <img src={renewableenergy} alt="Energy" className="h-6 w-6" />
                  <h2 className="text-xs mt-1">10.8%</h2>
                </div>
                <div className="flex flex-col items-center">
                  <img src={carrepair} alt="Car Repair" className="h-6 w-6" />
                  <h2 className="text-xs mt-1">50%</h2>
                </div>
                </div>
              </div>

              <div
                onClick={handleClick} 
               className="flex  items-center bg-gray-500 hover:border-2 bg-opacity-20 rounded-full p-2">
                <img src={crossbow} alt="Crossbow" className="h-6 w-6 opacity-15" />
              </div>

              {/* className="bg-gray-500 hover:border-2 bg-opacity-20 rounded-full p-4 w-28 h-28 mx-auto flex items-center justify-center hover:scale-105" */}

              <div className="flex flex-row items-center ml-8">
                <img src={man} alt="Man" className="h-6 w-6" />
                <h2 className="text-[10px] text-center  leading-tight">SAHELION HUNT</h2>
              </div>

              <button className="bg-yellow-500 text-black text-xs px-3 py-1 rounded ">START</button>
          </div>

          
              {/* Center Fingerprint */}
            <div onClick={handleAudio} className="bg-black bg-opacity-60 rounded-full p-3 w-20 h-20 flex items-center justify-center relative mx-auto mt-40">
              {/* Chevron Icons */}
              <FaChevronUp className="absolute top-1 text-gray-500 text-sm" />
              <FaChevronDown className="absolute bottom-1 text-gray-500 text-sm" />
              <FaChevronLeft className="absolute left-1 text-gray-500 text-sm" />
              <FaChevronRight className="absolute right-1 text-gray-500 text-sm" />

              {/* Inner Circle */}
              <div
                onClick={handleAudio}
                className="bg-black bg-opacity-60 rounded-full p-2 w-12 h-12 flex items-center justify-center border-2 border-yellow-400 border-dashed"
              >
                <img
                  src={fingerprint}
                  alt="Fingerprint"
                  className="w-8 h-8 cursor-pointer hover:scale-105 transition-transform"
                />
              </div>
            </div>

              <div className="relative w-20 h-20 mt-20 mx-auto md:hidden">
                <div 
                  onClick={handleClick}
                className="absolute top-0 left-1/2 transform -translate-x-1/2">
                  <img src={man} alt="Man Icon" className="h-6 w-6 opacity-80" />
                </div>
                <div  onClick={handleClick} className="absolute top-1/2 left-0 transform -translate-y-1/2">
                  <img src={runningman} alt="Running Man Icon" className="h-6 w-6 opacity-80" />
                </div>
                <div  onClick={handleClick} className="absolute top-1/2 right-0 transform -translate-y-1/2">
                  <img src={compass} alt="Compass Icon" className="h-6 w-6 opacity-80" />
                </div>
                <div  onClick={handleClick} className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
                  <img src={coin} alt="Coin Icon" className="h-6 w-6 opacity-80" />
                </div>
              </div>
            </div>
          </div>



          {/* for mobile --> ends here */}


          {/* for pc --> starts here */}
          
          <div className=" max-md:hidden flex justify-between items-start w-full max-w-7xl mx-auto px-4 pt-6 gap-1 min-h-[calc(100vh-100px)]">
            {/* Left Sidebar */}
            <div className="lg:flex flex-col items-center space-y-3 w-25 mt-7 ml-0 2xl:-ml-16">
              <h2 className="text-sm font-medium">Prizes Drawn: 0</h2>

              <div className="bg-[#231134] p-1 rounded-md w-12 h-12 flex items-center justify-center">
                <img
                  src={renewableenergy}
                  alt="Energy"
                  className="h-8 w-8 object-contain"
                />
              </div>

              <h2 className="text-sm font-medium">10.8%</h2>
              <div className="bg-[#231134] p-1 rounded-md w-12 h-12 flex items-center justify-center">
                <img
                  src={carrepair}
                  alt="Car Repair"
                  className="h-8 w-8 object-contain"
                />
              </div>
              <h2 className="text-sm font-medium">50%</h2>

              <div
                onClick={handleClick}
                className="text-center w-full max-w-[160px] cursor-pointer"
              >
                <div className="bg-gray-500 hover:border-2 bg-opacity-20 rounded-full p-4 w-28 h-28 mx-auto flex items-center justify-center hover:scale-105">
                  <img
                    src={crossbow}
                    alt="Crossbow"
                    className="h-16 opacity-20"
                  />
                </div>
              </div>
              <div className="text-white text-sm flex flex-row items-center font-semibold mt-2">
                <img
                  src={man}
                  alt="Car Repair"
                  className="h-6 w-6 object-contain"
                />
                <h2 className="ml-2 text-gray-400">SAHELLION HUNT</h2>
              </div>
              <button
                onClick={handleClick}
                className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-6 px-4 rounded-lg text-sm uppercase w-full hover:scale-105"
              >
                Start
              </button>
            </div>

            {/* Main Center Content */}
            <div className="flex-grow flex flex-col items-center justify-center text-center px-2 mt-10 min-h-[calc(100vh-100px)]">
              {/* Star Icon */}
              <div className="w-10 h-10 mb-1">
                {stars.map((star, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-white opacity-50 rounded-full"
                    style={{
                      ...star,
                      animation:
                        'move 5s infinite linear, glow 1.5s infinite alternate',
                      boxShadow: '0px 0px 10px 2px #ffffff',
                    }}
                  />
                ))}

                <div className="meteor" />

                <style>
                  {`
                @keyframes meteor-move {
                  0% {
                    transform: translate(300px, -300px); /* Start from top-right */
                    opacity: 1;
                  }
                  100% {
                    transform: translate(0, 0); /* End at the center */
                    opacity: 0;
                  }
                }

                .meteor {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  width: 36px;
                  height: 36px;
                  border-radius: 50%;
                background: radial-gradient(
                    circle,
                    rgba(255, 255, 255, 0.8) 0%,
                    rgba(255, 255, 255, 0.4) 60%,
                    transparent 100%
                  );
                  box-shadow:
                    0 0 30px 10px rgba(255, 255, 255, 0.8),
                    0 20px 60px rgba(255, 255, 255, 0.4);
                  animation: meteor-move 4s linear infinite;
                }
              `}
                </style>

                <img
                  src={star}
                  alt="Star Icon"
                  className="w-8 h-8 object-contain"
                />
              </div>

              {/* Heading */}
              <h1 className="text-yellow-400 text-4xl md:text-8xl font-thin tracking-[0.63rem] flex gap-2 mb-8">
                Coming Soon
              </h1>

              {/* Subtext */}
              <p className="text-base md:text-lg text-gray-300 mb-8 max-w-md text-left">
                We are currently working on the Black Panther game. We will launch
                it soon. Stay tuned!
              </p>

              {/* Social Media Links */}
              <div className="flex flex-wrap justify-center gap-6 mb-8">
                <a
                  href="https://t.me/blackpanthertkn"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out "
                >
                  <FaTelegram className="text-3xl" />
                </a>
                <a
                  href="https://x.com/bpnthrx"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out "
                >
                  <BsTwitterX className="text-3xl" />
                </a>
                <a
                  href="https://discord.com/invite/wVA7dqGDu2"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaDiscord className="text-3xl" />
                </a>
                <a
                  href="https://www.tiktok.com/@blackpanthertkn"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaTiktok className="text-3xl" />
                </a>
                <a
                  href="https://www.facebook.com/profile.php?id=100063660784177"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaFacebook className="text-3xl" />
                </a>
                <a
                  href="https://www.instagram.com/blackpanthercoin"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaInstagram className="text-3xl" />
                </a>
                <a
                  href="https://www.youtube.com/@blackpanthertoken"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaYoutube className="text-3xl" />
                </a>
                <a
                  href="https://www.linkedin.com/company/blackpanthertkn/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-yellow-400 text-gray-300 hover:scale-150 transition-all duration-300 ease-in-out"
                >
                  <FaLinkedin className="text-3xl" />
                </a>
              </div>

              {/* Fingerprint */}
              <div className="bg-black bg-opacity-60 rounded-full p-4 w-40 h-40 flex items-center justify-center relative">
                {/* Chevron Icons */}
                <FaChevronUp className="absolute top-2 text-gray-500 text-2xl" />
                <FaChevronDown className="absolute bottom-2 text-gray-500 text-2xl" />
                <FaChevronLeft className="absolute left-2 text-gray-500 text-2xl" />
                <FaChevronRight className="absolute right-2 text-gray-500 text-2xl" />

                {/* Inner Circle */}
               <div
                  onClick={handleAudio}
                  className="bg-black bg-opacity-60 rounded-full p-4 w-24 h-24 flex items-center justify-center border-2 border-yellow-400 border-dashed"
                >
                  <img
                    src={fingerprint}
                    alt="Fingerprint"
                    className="w-16 h-16 cursor-pointer hover:scale-105 transition-transform"
                  />
                </div>
              </div>
            </div>

            {/* Right Sidebar */}
            <div className="relative w-40 h-40 mt-[6rem] mx-auto hidden md:block space-x-1">
              <div
                onClick={handleClick}
                className="absolute top-0 left-1/2 transform -translate-x-1/2 cursor-pointer hover:scale-110 hover:border  hover:rounded-full"
              >
                <div className="p-2 bg-gray-500 opacity-50 rounded-full w-10 h-10">
                  <img
                    src={man}
                    alt="Man"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <div
                onClick={handleClick}
                className="absolute top-1/2 left-0 transform -translate-y-1/2 cursor-pointer hover:scale-110 hover:border  hover:rounded-full"
              >
                <div className="p-2 bg-gray-500 opacity-50 rounded-full w-10 h-10">
                  <img
                    src={runningman}
                    alt="Running Man"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <div
                onClick={handleClick}
                className="absolute top-1/2 right-0 transform -translate-y-1/2 cursor-pointer hover:scale-110 hover:border  hover:rounded-full"
              >
                <div className="p-2 bg-gray-500 opacity-50 rounded-full w-10 h-10">
                  <img
                    src={compass}
                    alt="Compass"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
              <div
                onClick={handleClick}
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 cursor-pointer hover:scale-110 hover:border  hover:rounded-full"
              >
                <div className="p-2 bg-gray-500 opacity-50 rounded-full w-10 h-10">
                  <img
                    src={coin}
                    alt="Coin"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};
