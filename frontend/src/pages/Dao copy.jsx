import React, { useState } from 'react';
import HeaderNew from '../components/Header/HeaderNew'
import <PERSON><PERSON><PERSON> from "../assets/images/Daohello.png"
import smilingkidDao from "../assets/images/smilingkidDao.png"
import problemsolving from "../assets/images/problem-solving.png"
import join from '../assets/images/join.png'
import transparency from '../assets/images/transparency.png'
import whythismatters from '../assets/images/whythismatters.png'
import Daohelloimage from '../assets/images/daohelloimage.png'
import donation from "../assets/images/donation.png"
import cashondelivery from "../assets/images/cash-on-delivery.png"
import operation from "../assets/images/operation.png"
import deployment from "../assets/images/deployment.png"
import { Link } from 'react-router-dom';
import PantherDao from '../assets/images/PantherDao.png';
import donation2 from '../assets/images/donation2.png'

export const Dao = () => {
  const [showContributionFlow, setShowContributionFlow] = useState(false);
  const [currentPage, setCurrentPage] = useState('contribution');
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [selectedPaymentDay, setSelectedPaymentDay] = useState(null);
  const [communityName, setCommunityName] = useState('');

  const handleContributionSelect = (amount) => {
    setSelectedAmount(amount);
  };

  const handlePaymentDaySelect = (day) => {
    setSelectedPaymentDay(day);
  };

  const handleProceed = () => {
    if (selectedAmount && selectedPaymentDay) {
      setCurrentPage('community');
    }
  };

  const handleCreateCommunity = () => {
    // logic for creating a community??
    console.log('Creating community:', communityName);
    setShowContributionFlow(false);
    //  submission ??
  };

  const openContributionFlow = () => {
    setShowContributionFlow(true);
    setCurrentPage('contribution');
  };
  const [showCommunitySearch, setShowCommunitySearch] = useState(false);
  const handleShowCommunitySearch = () => {
    setShowCommunitySearch(true);
  };

  if (showContributionFlow) {
    return (
      <div className="min-h-screen bg-BP-lightbaige flex items-center justify-center p-4 sm:p-6 md:p-8">
        <div className="w-full max-w-md sm:max-w-xl md:max-w-4xl bg-BP-opacited-white rounded-xl shadow-lg p-6 sm:p-8 md:p-12 min-h-[500px] sm:min-h-[550px] md:min-h-[600px] flex flex-col">
          {currentPage === 'contribution' && (
            <div className='flex-grow'>
              <img 
                src={PantherDao} 
                alt="Logo" 
                className="mx-auto mb-4 w-16 h-16 sm:w-20 sm:h-20"
              />
    
              <div className="flex items-center justify-center mb-2">
                <img
                  src={donation2}
                  alt="donation"
                  className="w-10 h-10 sm:w-12 sm:h-12 mr-2"
                />
                <h2 className="text-xl sm:text-2xl font-bold font-title text-black">Monthly Contribution</h2>
              </div>
              <p className="text-center text-gray-600 mb-6 font-body text-sm sm:text-base">Choose your contribution</p>
    
              <div className="grid grid-cols-3 gap-2 sm:gap-3 mb-4">
                {[5, 10, 20].map((amount) => (
                  <button
                    key={amount}
                    className={`py-2 sm:py-3 w-full h-16 sm:h-20 rounded-lg text-base sm:text-lg transition-colors border border-[#50505026]  
                      ${
                        selectedAmount === amount 
                        ? 'bg-yellow-500 text-white' 
                        : 'bg-[#E5E7EB] text-gray-700 hover:bg-gray-300'
                      }`}
                    onClick={() => handleContributionSelect(amount)}
                  >
                    ${amount}
                  </button>
                ))}
              </div>
    
              {/* Custom Amount Input */}
              <input 
                type="text" 
                placeholder="Enter custom amount" 
                className="w-full p-2 sm:p-3 mb-6 border border-[#50505026] rounded-lg bg-[#E5E7EB] focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-800 text-sm sm:text-base"
              />
    
              <p className="text-center text-gray-600 mb-6 font-body text-sm sm:text-base">Pick your preferred payment day:</p>
    
              <div className="grid grid-cols-3 gap-2 sm:gap-3 mb-6">
                {['Today', '1st of the month', 'End of the month'].map((day) => (
                  <button
                    key={day}
                    className={`py-2 sm:py-3 w-full h-24 sm:h-32 rounded-lg text-sm sm:text-lg transition-colors border border-[#50505026]
                      ${
                        selectedPaymentDay === day 
                        ? 'bg-purple-500 text-white' 
                        : 'bg-[#E5E7EB] text-gray-700 hover:bg-gray-300'
                      }`}
                    onClick={() => handlePaymentDaySelect(day)}
                  >
                    {day}
                  </button>
                ))}
              </div>
    
              <button 
                className={`w-full py-3 rounded-full text-white transition-colors ${
                  selectedAmount && selectedPaymentDay
                  ? 'bg-purple-600 hover:bg-purple-700' 
                  : 'bg-gray-400 cursor-not-allowed'
                }`}
                onClick={handleProceed}
                disabled={!selectedAmount || !selectedPaymentDay}
              >
                Proceed
              </button>
            </div>
          )}
    
          {currentPage === 'community' && (
            <div className='flex-grow'>
              <img 
                src={PantherDao} 
                alt="Logo" 
                className="mx-auto mb-4 w-16 h-16 sm:w-20 sm:h-20"
              />
              <h2 className="text-xl sm:text-2xl font-bold mb-6 text-center">Choose Your Community</h2>

              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 mb-6">
                <button 
                  className="w-full sm:flex-1 bg-gray-500 text-white py-3 rounded-lg hover:bg-yellow-600 transition-colors"
                  onClick={() => setShowCommunitySearch(false)}
                >
                  + Create new community
                </button>
                <button 
                  className="w-full sm:flex-1 bg-gray-500 text-gray-700 py-3 rounded-lg hover:bg-purple-400 transition-colors"
                  onClick={handleShowCommunitySearch}
                >
                  Join an existing community
                </button>
              </div>

              {showCommunitySearch ? (
                <div className="bg-stone-50 p-6 rounded-lg mb-6">
                  <h2 className="text-xl font-medium mb-4">Search for a community</h2>
                  <div className="relative mb-4">
                    <input
                      type="text"
                      placeholder="Type to search..."
                      className="w-full border border-gray-300 rounded-lg py-3 pl-10 pr-4 text-black"
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                      </svg>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between bg-white p-4 rounded-lg mb-3">
                    <p className="font-medium text-black">Green Earth Initiative</p>
                    <button className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-8 rounded-lg transition-colors">
                      Join
                    </button>
                  </div>
                  <div className="flex items-center justify-between bg-white p-4 rounded-lg mb-3">
                    <p className="font-medium text-black">Tech Innovators DAO</p>
                    <button className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-8 rounded-lg transition-colors">
                      Join
                    </button>
                  </div>
                  <div className="flex items-center justify-between bg-white p-4 rounded-lg mb-3">
                    <p className="font-medium text-black">Digital Creators Hub</p>
                    <button className="bg-purple-500 hover:bg-purple-600 text-white py-2 px-8 rounded-lg transition-colors">
                      Join
                    </button>
                  </div>
                </div>
              ) : (
                <input 
                  type="text" 
                  placeholder="Enter community name" 
                  className="w-full p-3 mb-6 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-black"
                  value={communityName}
                  onChange={(e) => setCommunityName(e.target.value)}
                />
              )}

              <div className="flex justify-between items-center">
                <button 
                  className="text-gray-600 hover:text-gray-800 text-sm sm:text-base"
                  onClick={() => setCurrentPage('contribution')}
                >
                  ← Back
                </button>
                <button 
                  className={`py-3 px-6 rounded-lg text-white transition-colors ${
                    communityName || showCommunitySearch
                    ? 'bg-purple-600 hover:bg-purple-700' 
                    : 'bg-gray-400 cursor-not-allowed'
                  }`}
                  onClick={handleCreateCommunity}
                  disabled={!communityName && !showCommunitySearch}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen w-full overflow-hidden">
      <div className="absolute inset-0 z-0">
        <img 
          src={Daohello}
          alt="Background" 
          className="w-full h-full object-cover"
        />
      </div>

      <div className="relative z-10 flex flex-col min-h-screen">
        <HeaderNew />

        <div className="flex-grow flex flex-col p-6 md:p-12 lg:p-16">
          <div className="flex-grow">
            <div className="max-w-2xl">
              <h1 className="text-4xl md:text-6xl font-bold font-title mb-4">
                Together, We Heal.
              </h1>
              <h1 className="text-4xl md:text-6xl font-bold font-title mb-6">
                Together, We
              </h1>
              <h1 className="text-4xl md:text-6xl font-bold font-title mb-6">
                Empower.
              </h1>
              <p className="text-lg md:text-xl max-w-xl font-body">
                Every heartbeat matters. Every choice transforms lives. Your contribution funds 
                groundbreaking research, life-saving treatments, and vital healthcare for 
                communities affected by sickle cell anemia, snake bites, and neglected 
                tropical diseases.
              </p>
            </div>
          </div>

          <div className="flex flex-col md:hidden w-full mt-8 space-y-6">
            <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-12 h-12 bg-transparent border border-yellow-500 rounded-full">
                <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              </div>
              <div>
                <h3 className="text-3xl font-bold">3,112</h3> 
                <p className="text-sm font-title">Active members</p>
              </div>
            </div>

            <div className="w-full">
              <div className="w-full h-64 rounded-xl overflow-hidden">
                <img 
                  src={smilingkidDao}
                  alt="Healthcare provider with child" 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            <div className="bg-[#FFFFFFCC]/100 text-black p-4 rounded-2xl w-full">
              <div className="text-left mb-2">
                <h3 className="text-3xl font-bold font-title">$500,000+</h3>
                <p className="text-sm font-body">deployed to life-saving health initiatives</p>
              </div>
              <button 
                onClick={openContributionFlow} 
                className="px-6 py-2 bg-BP-yellow hover:bg-BP-hovered-yellow text-black rounded-full transition duration-300 w-full md:w-auto"
              >
                Join community
              </button>
            </div>

            {/* <button 
              onClick={openContributionFlow} 
              className="px-6 py-2 bg-yellow-500 hover:bg-yellow-600 text-black rounded-full transition duration-300 w-full md:w-auto"
            >
              Join community
            </button> */}
          </div>

          <div className="hidden md:block">
            <div className="mt-10 ml-44">
              <Link to="/login">
              <button className="px-8 py-2 bg-gray-800/50 hover:bg-gray-700/70 text-white rounded-full border border-gray-600 transition duration-200  hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                Sign in
              </button></Link>
              
            </div>

            <div className="flex items-center justify-between w-full relative z-20 mt-auto">
              <div className="flex items-center space-x-4">
              <div className="flex items-center justify-center w-12 h-12 bg-transparent border border-yellow-500 rounded-full relative">
                <div className="w-4 h-4 bg-yellow-500 rounded-full absolute animate-pulseglow"></div>
              </div>
                <div>
                  <h3 className="text-3xl font-bold">3,112</h3>
                  <p className="text-sm">Active members</p>
                </div>
              </div>

              <div className="bg-[#FFFFFFCC]/100 text-black p-4 rounded-2xl flex items-center mr-96 mt-12">
                <div className="text-left mr-4">
                  <h3 className="text-3xl font-bold">$500,000+</h3>
                  <p className="text-sm max-w-xs">deployed to life-saving health initiatives</p>
                </div>
                <button className="px-6 py-2 bg-yellow-500 hover:bg-yellow-600 text-black rounded-full transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                  Join community
                </button>
              </div>
            </div>

            <div className="absolute right-80 top-[450px] transform -translate-y-1/2 z-10">
              <div className="w-[450px] h-[520px] rounded-2xl overflow-hidden">
                <img 
                  src={smilingkidDao}
                  alt="Healthcare provider with child" 
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
          
          <div className="absolute top-36 right-80 w-12 h-12 bg-[#9747FF80] rounded-full opacity-80 hidden md:block"></div>
          <div className="absolute top-36 left-8 w-12 h-12 bg-[#E9B30880] rounded-full opacity-80 hidden md:block"></div>
          {/* <div className="absolute bottom-14 right-1/3 w-16 h-16 bg-[#E1AB0D] rounded-full opacity-80 hidden md:block"></div> */}
          <div className="absolute top-14 right-4 w-12 h-12 bg-[#E1AB0D] rounded-full opacity-80 md:hidden"></div>
        </div>

        <div className="bg-[#F9F5EB] py-16 px-6 md:px-16 lg:px-24">
          <div className="max-w-3xl mx-auto text-[#111111]">
            <h2 className="text-4xl md:text-5xl font-bold font-title mb-4 text-center ">How it works</h2>
            <p className="text-lg mb-12 text-center font-body">Be part of the movement. Your contribution funds life-saving health initiatives.</p>
            
            <div className="flex flex-col gap-6">
              <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
                <div className="flex flex-col md:flex-row md:items-start gap-4">
                  <div className="bg-[#111827] w-20 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                    <img src={join} alt="Sign Up" className="w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Sign Up & Contribute</h3>
                    <p className="font-body">
                      Become a member by signing up and contributing a minimum of $5. Your contribution goes directly into the DAO's charity wallet, funding life-changing health initiatives like sickle cell treatment, snake bite response, and research on tropical diseases.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
                <div className="flex flex-col md:flex-row md:items-start gap-4">
                  <div className="bg-[#111827] w-20 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                    <img src={problemsolving} alt="Vote" className="w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Vote on Impactful Projects</h3>
                    <p className="font-body">
                      As a member, you help decide where the funds go. Vote on medical research, community health programs, and hospital development projects that matter most to you. Your voice shapes real-world impact.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-3xl p-6" style={{ boxShadow: '2px 2px 10px 0px #0000001A, -2px 0px 10px 0px #0000001A' }}>
                <div className="flex flex-col md:flex-row md:items-start gap-4">
                  <div className="bg-[#111827] w-20 h-16 p-3 rounded-xl flex items-center justify-center self-center mt-6">
                    <img src={transparency} alt="Transparency" className="w-8 h-8" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold font-title mb-2 text-center md:text-left">Full Transparency & Financial Access</h3>
                    <p className="font-body">
                      We believe in absolute transparency. Get real-time access to financial records, spending reports, and project updates. See exactly how your contributions are being used to create change.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white py-16 px-6 md:px-16 lg:px-24">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="w-full md:w-1/2 relative text-white p-8 md:p-12 rounded-2xl flex flex-col justify-center overflow-hidden">
                <img 
                  src={Daohelloimage} 
                  alt="" 
                  className="absolute inset-0 w-full h-full object-cover z-0"
                />
                
                <div className="relative z-20">
                  <h2 className="text-5xl md:text-6xl font-bold font-title mb-6">Why this matters</h2>
                  <p className="text-lg leading-relaxed font-body">
                    Millions suffer from sickle cell anemia, snake bites, and neglected tropical diseases. 
                    Research funding is scarce, and treatments are inaccessible. Our DAO ensures that people 
                    affected receive the care they deserve.
                  </p>
                </div>
              </div>
              
              <div className="w-full md:w-1/2 relative rounded-2xl overflow-hidden">
                <img
                  src={whythismatters}
                  alt="Healthcare provider with patients"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent flex items-end justify-center p-8">
                  <Link to="/signup">
                    <button className="px-12 py-4 bg-white text-black rounded-full text-lg font-medium hover:bg-gray-100 transition duration-300 shadow-lg">
                      Get started
                    </button>
                  </Link>
                  
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* TRANSPARENCY & IMPACT SECTION */}
        {/* <h2 className="text-4xl text-black md:text-5xl font-bold mb-12 text-left" >Transparency & Impact</h2> */}

        <div className="bg-[#111828] w-full md:w-[90%] text-white py-16 px-6 md:px-16 lg:px-24 rounded-lg  mx-auto">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold font-title mb-12 text-left">Transparency & Impact</h2>
            <div className="flex flex-col">
              <div className="flex flex-col md:flex-row justify-between gap-6 mb-12">
                <div className="flex gap-4 items-center">
                  <div className="w-0.5 h-16 bg-yellow-500"></div>
                  <p className="text-lg font-body">Live updates on fund distribution and project progress</p>
                </div>
                
                <div className="flex gap-4 items-center">
                  <div className="w-0.5 h-16 bg-yellow-500"></div> 
                  <p className="text-lg font-body">100% of funds go to research and healthcare projects</p>
                </div>
                
                <div className="flex gap-4 items-center">
                  <div className="w-0.5 h-16 bg-yellow-500"></div>
                  <p className="text-lg font-body">Community-driven decision-making</p>
                </div>
              </div>
              
              <div className="flex justify-center mb-12">
                <Link to="/daotransactions">
                  <button className="px-8 py-4 bg-BP-yellow hover:bg-BP-hovered-yellow text-black rounded-full transition duration-200 text-lg font-medium hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                    View transactions
                </button>
                </Link>
                
              </div>
              
              <div className="space-y-4">
                <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                <div className="flex items-center mr-2">
                <div className="flex items-center justify-center w-8 h-8 bg-transparent border border-yellow-500 rounded-full relative">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full absolute animate-pulseglow"></div>
                </div>
              </div>
                  <div className="text-2xl font-bold font-title mr-4">3,112</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Active members</div>
                </div>
                
                <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                  <div className="flex items-center mr-2">
                    <img src={donation} alt="Contributions icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4">$ 34,2355</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly contributions</div>
                </div>
                
                <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                  <div className="flex items-center mr-2">
                    <img src={cashondelivery} alt="Cash icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4">$ 4,094</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash on hand</div>
                </div>
                
                <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                  <div className="flex items-center mr-2">
                    <img src={operation} alt="Overheads icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold mr-4 font-title">$ 2,000</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Monthly overheads</div>
                </div>
                
                <div className="bg-[#7A7A7A80] rounded-lg p-5 flex items-center">
                  <div className="flex items-center mr-2">
                    <img src={deployment} alt="Deployed cash icon" className="w-8 h-8 rounded-full" />
                  </div>
                  <div className="text-2xl font-bold font-title mr-4">$ 600,2355</div>
                  <div className="border-l-2 border-yellow-500 font-body pl-4 ml-auto">Cash deployed</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-[#F8F4E9] py-16 px-6 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-medium font-body mb-4 text-center italic text-black">
              Be the heartbeat of change. Join us in funding life saving solutions.
            </h2>
            
            <div className="mt-10">
              <button 
                onClick={openContributionFlow} 
                className="inline-block px-12 py-4 bg-BP-yellow text-black rounded-full text-xl font-medium transition duration-200 hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short"
              >
                Join Now
              </button>
            </div>
            
            <div className="mt-6">
              <p className="text-gray-700 font-body">or already have an account ?</p>
              <a href="/login" className="inline-block mt-2 text-xl font-medium font-body text-black border-b border-[#002B36] hover:border-b-2 transition duration-200  hover:scale-105 hover:-translate-y-1 transform hover:animate-bounce-short">
                Sign in
              </a> 
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};