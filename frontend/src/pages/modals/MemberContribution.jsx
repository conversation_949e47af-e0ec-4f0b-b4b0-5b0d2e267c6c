import React, { useRef } from 'react';
import { IoClose } from 'react-icons/io5';
import { FiCalendar } from 'react-icons/fi';
import { FaDownload } from "react-icons/fa6";
import { FaSearch } from "react-icons/fa";

export const MemberContribution = ({ onClose }) => {
  const fromRef = useRef(null);
  const toRef = useRef(null);

  // Example data for members who contributed
  const members = [
    {
      name: "<PERSON> Mugao",
      role: "Admin",
      compliance: "90%",
      actions: "View History"
    },
    {
      name: "Divine Jicks",
      role: "Admin",
      compliance: "80%",
      actions: "View History"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Member",
      compliance: "50%",
      actions: "View History"
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 rounded-2xl flex items-center justify-center z-50 p-4">
      <div className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-2xl sm:max-w-2xl md:max-w-5xl transition-all min-h-[600px] md:min-h-[700px] flex flex-col gap-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-3">
          <h2 className="font-karla font-semibold text-[36px] leading-[100%] tracking-[0] text-[#2c5282]">
            Members Who Contributed
          </h2>
          <button
            className="text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <IoClose className="w-6 h-6" />
          </button>
        </div>

         <div className="relative mb-6 text-gray-700">
                <input
                  type="text"
                  placeholder="Search members..."
                  className="w-full py-3 pl-10 pr-4 bg-gray-100 rounded-xl focus:outline-none text-sm"
                />
                <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>

        <div
          className="overflow-x-auto md:overflow-x-visible rounded-xl max-w-6xl w-full"
          style={{ boxShadow: "0px 4px 24px 0px rgba(0,0,0,0.10)" }}
        >
          <table className="min-w-[600px] md:min-w-full text-left border-collapse bg-white overflow-hidden">
            <thead className="bg-gray-100">
              <tr className="text-gray-700 text-sm font-semibold">
                <th className="px-4 py-3">Name</th>
                <th className="px-4 py-3">Role</th>
                <th className="px-4 py-3">Compliance rate</th>
                <th className="px-4 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {members.map((item, idx) => (
                <tr key={idx} className="border-t text-sm text-gray-800">
                  <td className="px-6 py-6">{item.name}</td>
                  <td className="px-4 py-6">{item.role}</td>
                  <td className="px-4 py-6">{item.compliance}</td>
                  <td className="px-4 py-6">
                    <button className="text-yellow-500  decoration-2 hover:opacity-80">
                      {item.actions}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};