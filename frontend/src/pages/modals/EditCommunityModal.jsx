import React, { useState } from 'react';
import { IoClose } from 'react-icons/io5';
import { CommunityService } from '../../services/CommunityService';
import { toast } from 'react-toastify';

export const EditCommunityModal = ({ onClose, currentName, currentDescription, communityId, onSaved }) => {
  const [name, setName] = useState(currentName || '');
  const [description, setDescription] = useState(currentDescription || '');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await CommunityService.editCommunity(communityId, name, description);
      toast.success('Community updated successfully!');
      if (onSaved) onSaved({ name, description });
      onClose();
    } catch (error) {
      toast.error(error?.response?.data?.error || 'Failed to update community');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FDFCF9] p-6 rounded-3xl shadow-xl w-full max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-6xl max-h-screen overflow-y-auto relative">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-600">Edit Community</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <IoClose className="w-8 h-8" />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label className="block text-gray-600 mb-2">Community Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-3 rounded-3xl text-gray-700 font-bold bg-white focus:outline-none focus:border-gray-400 border border-gray-200 min-h-[80px]"
              placeholder="Enter community name"
              disabled={loading}
            />
          </div>

          <div className="mb-8">
            <label className="block text-gray-600 mb-2">Description</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-4 py-3 rounded-3xl text-gray-600 bg-white focus:outline-none focus:border-gray-400 border border-gray-200 min-h-[120px]"
              placeholder="Enter community description"
              disabled={loading}
            />
          </div>

          <div className="flex justify-end gap-12">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2.5 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2.5 rounded-lg bg-purple-600 text-white hover:bg-purple-700"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
