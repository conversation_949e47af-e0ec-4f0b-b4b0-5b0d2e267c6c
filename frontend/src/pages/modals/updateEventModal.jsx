import React, { useState, useRef, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';
import { FiCalendar, FiClock } from 'react-icons/fi';
import { EventService } from '../../services/eventService';

export const UpdateEventModal = ({ onClose, onEventUpdated, event }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: '',
    time: '',
    duration: '60',
    meetingLink: ''
  });
  const [loading, setLoading] = useState(false);
  const dateInputRef = useRef(null);

  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title || '',
        description: event.description || '',
        date: event.date ? event.date.split('T')[0] : '',
        time: event.time || '',
        duration: event.duration ? String(event.duration) : '60',
        meetingLink: event.meetingLink || ''
      });
    }
  }, [event]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    const updated = await EventService.updateEvent(event.id || event._id, formData);
    setLoading(false);
    if (updated) {
      if (onEventUpdated) onEventUpdated(updated);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 rounded-2xl flex items-center justify-center z-50 p-4">
      <form
        onSubmit={handleSubmit}
        className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-2xl sm:max-w-2xl md:max-w-5xl transition-all min-h-[600px] md:min-h-[700px] flex flex-col justify-between"
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-[#2d5383]">Update event</h2>
          <button
            className="text-gray-400 hover:text-gray-600"
            onClick={onClose}
            type="button"
          >
            <IoClose className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <div className="space-y-4">
          {/* Event Title */}
          <div>
            <label className="block text-sm font-medium text-[#2d5383] mb-1">
              Event Title <span className="text-[#2d5383]">*</span>
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter title"
            className="w-full px-3 py-4 border text-black border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-[#2d5383] mb-1">
              Description <span className="text-[#2d5383]">*</span>
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter description"
              className="w-full px-4 py-6 border text-black border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
              rows={3}
              required
            />
          </div>

          {/* Date , Time, duration should be in a Row */}
          <div className="grid grid-cols-3 gap-4">
            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-[#2d5383] mb-1">
                Date <span className="text-[#2d5383]">*</span>
              </label>
              <div className="relative">
                <FiCalendar
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer"
                  onClick={() => dateInputRef.current.showPicker()}
                />
                <input
                  ref={dateInputRef}
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  placeholder="mm/dd/yyyy"
                  className="w-full pl-10 pr-3 py-2 text-black border rounded-xl border-gray-300  focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
                  required
                />
              </div>
            </div>

            {/* Time */}
            <div>
              <label className="block text-sm font-medium text-[#2d5383] mb-1">
                Time <span className="text-[#2d5383]">*</span>
              </label>
              <div className="relative">
                <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  name="time"
                  value={formData.time}
                  onChange={handleChange}
                  placeholder="04:40 PM"
                  className="w-full pl-10 pr-3 py-2 text-black border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
                  required
                />
              </div>
            </div>

            {/* Duration */}
            <div>
              <label className="block text-sm font-medium text-[#2d5383] mb-1">
                Duration (min) <span className="text-[#2d5383]">*</span>
              </label>
              <input
                type="number"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                className="w-full px-3 py-2 border text-black border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
                required
              />
            </div>
          </div>

          {/* Meeting Link */}
          <div>
            <label className="block text-sm font-medium text-[#2d5383] mb-1">
              Meeting Link (zoom/Meet) <span className="text-[#2d5383]">*</span>
            </label>
            <input
              type="url"
              name="meetingLink"
              value={formData.meetingLink}
              onChange={handleChange}
              placeholder="https://zoom.us/..."
              className="w-full px-3 py-4 border text-black border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#7f8690] bg-white placeholder-[#7f8690]"
              required
            />
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="mt-8 flex justify-end gap-4">
          <button
            type="button"
            onClick={onClose}
            className="w-[250px] h-[80px] border border-[#2C5282] text-[#2C5282] font-medium rounded-[20px] hover:bg-gray-100 transition"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="w-[290px] h-[80px] border border-[#2C5282] bg-[#2c5282] text-white font-medium rounded-[20px] transition"
          >
            {loading ? "Updating..." : "Update Event"}
          </button>
        </div>
      </form>
    </div>
  );
};