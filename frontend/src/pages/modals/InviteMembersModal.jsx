import React, { useState, useEffect } from 'react';
import { IoMail, IoLink, IoClose } from 'react-icons/io5';
import { IoIosArrowDown } from 'react-icons/io';
import { toast } from 'react-toastify';
import { SocialService } from '../../services/SocialService';

export const InviteMembersModal = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState('email');
  const [emails, setEmails] = useState('');
  const [message, setMessage] = useState('');
  const [expiration, setExpiration] = useState('1 day');
  const [maxUsers, setMaxUsers] = useState('5');
  const [loading, setLoading] = useState(false);
  const [editableLink, setEditableLink] = useState();
  const [linkLoading, setLinkLoading] = useState(false);
  const [showExpirationDropdown, setShowExpirationDropdown] = useState(false);
  const [baseUrl, setBaseUrl] = useState('');
  const [isLinkGenerated, setIsLinkGenerated] = useState(false);
  const [generatedLinkId, setgeneratedLinkId] = useState('');

  const expirationOptions = ['1 day', '7 days', '30 days'];

  const handleCopy = async () => {
    if (!editableLink) {
      toast.error('Please enter or generate a link first');
      return;
    }
    try {
      await navigator.clipboard.writeText(editableLink);
      toast.success('Link copied!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const handleSendInvitations = async () => {
    setLoading(true);
    const emailList = emails
      .split(',')
      .map((e) => e.trim())
      .filter(Boolean);

    if (emailList.length === 0) {
      toast.error('Please enter at least one email address.');
      setLoading(false);
      return;
    }

    if (!generatedLinkId) {
      toast.error('No invite link generated.');
      setLoading(false);
      return;
    }

    try {
      await SocialService.inviteFriendLink(emailList, message, generatedLinkId, expiration);
      setEmails('');
      setMessage('');
      onClose();
    } catch (err) {
      toast.error(err.message || 'Failed to send invitations.');
    }
    setLoading(false);
  };

  const handleGenerateLink = async () => {
  setLinkLoading(true);
  try {
    const { inviteLink, baseUrl, linkId } = await SocialService.generateShareLink(
      expiration,
      maxUsers,
      editableLink,
      emails?.split(',')[0]?.trim(),
      message
    );

    setEditableLink(inviteLink);
    setBaseUrl(baseUrl);
    setIsLinkGenerated(true);
    setgeneratedLinkId(linkId);

    // 👉 Show the 'link' tab temporarily
    setActiveTab('link');

    // ⏳ Wait a bit before going back to 'email' tab
    setTimeout(() => {
      setActiveTab('email');
    }, 2500); // Switch back after 2.5 seconds (adjust if needed)
    
  } catch (err) {
    toast.error(err.message || 'Failed to generate invite link.');
  }
  setLinkLoading(false);
};



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-3xl shadow-lg w-full max-w-6xl mx-4 p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-700">Invite members</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <IoClose className="w-8 h-8" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex gap-6 mb-6">
          <button
            className={`pb-2 flex items-center gap-2 ${
              activeTab === 'email'
                ? 'text-purple-600 border-b-2 border-purple-600'
                : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('email')}
          >
            <IoMail className="text-xl" />
            Email
          </button>
          <button
            className={`pb-2 flex items-center gap-2 ${
              activeTab === 'link'
                ? 'text-purple-600 border-b-2 border-purple-600'
                : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('link')}
          >
            <IoLink className="text-xl" />
            Share Link
          </button>
        </div>

        {activeTab === 'email' && (
          <>
            <div className="mb-6">
              <label className="block text-gray-600 mb-2">
                Email Addresses (comma separated)
              </label>
              <input
                type="text"
                value={emails}
                onChange={(e) => setEmails(e.target.value)}
                placeholder="<EMAIL>,<EMAIL>"
                className="w-full px-4 py-3 rounded-2xl border border-gray-200 text-gray-600 bg-white focus:outline-none focus:border-gray-400 min-h-[80px]"
              />
            </div>

            <div className="mb-6">
              <label className="block text-gray-600 mb-2">
                Personal Message (optional)
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full px-4 py-3 rounded-2xl border border-gray-200 text-gray-600 bg-white focus:outline-none focus:border-gray-400 min-h-[120px]"
              />
            </div>
          </>
        )}

        {activeTab === 'link' && (
          <div className="mb-6">
            <label className="block text-gray-700 mb-6 font-karla">
              Invitation Link
            </label>
            <div className="flex gap-2 mb-2 items-center">
              <div className="relative w-full">
                <input
                  type="text"
                  value={editableLink}
                  onChange={(e) => setEditableLink(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl text-gray-600 bg-white border border-purple-400 pr-28"
                />
              </div>
              <button
                onClick={handleCopy}
                className="px-6 py-2 bg-purple-600 text-white rounded-xl hover:bg-purple-700"
              >
                Copy
              </button>
            </div>

            <div className="px-6 mt-8 space-y-4 w-full max-w-md mx-auto">
              <h3 className="text-sm font-karla text-gray-800">
                Link settings
              </h3>

              {/* Custom Expiration Dropdown */}
              <div className="flex justify-between items-center relative">
                <label className="text-sm text-gray-600">Expiration</label>
                <div className="relative w-24">
                  <button
                    type="button"
                    onClick={() => setShowExpirationDropdown((v) => !v)}
                    className="flex items-center justify-between bg-white text-gray-700 border border-gray-300 px-3 py-2 rounded-md w-full text-sm focus:outline-none focus:border-gray-400"
                  >
                    <span>{expiration}</span>
                    <IoIosArrowDown className="w-4 h-4 ml-2" />
                  </button>
                  {showExpirationDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg">
                      {expirationOptions.map((opt) => (
                        <div
                          key={opt}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm text-black"
                          onClick={() => {
                            setExpiration(opt);
                            setShowExpirationDropdown(false);
                          }}
                        >
                          {opt}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center relative">
                <label className="text-sm text-gray-600">Max users</label>
                <div className="relative w-24 flex items-center justify-center">
                  <input
                    type="number"
                    value={maxUsers}
                    min={1}
                    onChange={(e) => setMaxUsers(e.target.value)}
                    className="px-3 py-2 rounded-md border border-gray-300 bg-white text-sm text-black w-full text-center focus:outline-none focus:border-gray-400"
                    placeholder="5"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end gap-6">
          <button
            onClick={onClose}
            className="px-6 py-2.5 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50"
          >
            Cancel
          </button>
          {activeTab === 'email' ? (
            <button
              onClick={handleSendInvitations}
              disabled={loading || !emails.trim()}
              className="px-6 py-2.5 rounded-lg bg-purple-600 text-white hover:bg-purple-700 disabled:bg-purple-300"
            >
              {loading ? 'Sending...' : 'Send Invitations'}
            </button>
          ) : (
            <button
              className="px-6 py-2.5 rounded-lg bg-purple-600 text-white hover:bg-purple-700 disabled:bg-purple-300"
              onClick={handleGenerateLink}
              disabled={linkLoading}
            >
              {linkLoading ? 'Generating...' : 'Generate Link'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
