import React, { useRef } from 'react';
import { IoClose } from 'react-icons/io5';
import { FiCalendar } from 'react-icons/fi';
import { FaDownload } from "react-icons/fa6";
import { FaSearch } from "react-icons/fa";

export const TotalOverheads = ({ onClose }) => {
  const fromRef = useRef(null);
  const toRef = useRef(null);

  // Example data for overheads
  const overheads = [
    {
      description: "Marketing Event",
      amount: "$20000",
      date: "7/7/2025",
      proof: "View Receipt"
    },
    {
      description: "Email Services",
      amount: "$30000",
      date: "4/6/2025",
      proof: "0xAF8...A9B0"
    },
    {
      description: "Legal Consultation",
      amount: "$34500",
      date: "12/12/2024",
      proof: "View Receipt"
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 rounded-2xl flex items-center justify-center z-50 p-4">
      <div className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-2xl sm:max-w-2xl md:max-w-5xl transition-all min-h-[600px] md:min-h-[700px] flex flex-col gap-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-3">
         <h2 className="font-karla font-semibold text-2xl sm:text-3xl md:text-4xl lg:text-[36px] leading-[100%] tracking-[0] text-[#2c5282]">
            Paid Services
          </h2>
          <button
            className="text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <IoClose className="w-6 h-6" />
          </button>
        </div>

        <div className="w-full flex flex-col gap-2 sm:gap-0">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 mb-2">
            {/* Search Bar */}
            <div className="flex items-center w-full lg:w-[391px] h-[58px] bg-white border border-gray-300 rounded-[12px] px-4">
              <FaSearch className="text-gray-400 w-5 h-5 mr-3" />
              <input
                type="text"
                placeholder="Search"
                className="flex-1 bg-transparent outline-none font-karla text-lg text-[#000000] placeholder-gray-400"
                style={{ height: "40px" }}
              />
            </div>
            {/* Date Filters */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center">
              <label className="font-karla font-medium text-lg text-[#000000]">From</label>
              <div className="relative ml-0 sm:ml-2 mt-2 sm:mt-0">
                <input
                  ref={fromRef}
                  type="date"
                  className="border border-gray-300 bg-white text-[#000000] rounded-[12px] px-4 py-2 text-base font-karla pr-10 w-[180px] sm:w-auto"
                />
                <FiCalendar
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-black cursor-pointer w-5 h-5"
                  onClick={() => fromRef.current && fromRef.current.showPicker && fromRef.current.showPicker()}
                />
              </div>
              <label className="font-karla font-medium text-lg text-[#000000] ml-0 sm:ml-4 mt-2 sm:mt-0">To</label>
              <div className="relative ml-0 sm:ml-2 mt-2 sm:mt-0">
                <input
                  ref={toRef}
                  type="date"
                  className="border border-gray-300 bg-white text-[#000000] rounded-[12px] px-4 py-2 text-base font-karla pr-10 w-[180px] sm:w-auto"
                />
                <FiCalendar
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-black cursor-pointer w-5 h-5"
                  onClick={() => toRef.current && toRef.current.showPicker && toRef.current.showPicker()}
                />
              </div>
            </div>
          </div>

          <div className="hidden md:block" style={{ height: '24px' }}></div>

          <div className="flex flex-col sm:flex-row sm:justify-end sm:items-center my-2 gap-2">
            <button
              className="flex items-center justify-center gap-3 bg-[#a855f7] text-white w-full sm:w-[314px] h-[56px] rounded-[12px] transition hover:bg-[#9333ea]"
            >
              <FaDownload className="w-8 h-8 text-white" />
              <span className="font-poppins font-normal text-[20px] sm:text-[24px] leading-[100%] tracking-[0]">
                Download Report
              </span>
            </button>
          </div>
        </div>

        <div
          className="overflow-x-auto md:overflow-x-visible rounded-xl max-w-6xl w-full"
          style={{ boxShadow: "0px 4px 24px 0px rgba(0,0,0,0.10)" }}
        >
          <table className="min-w-[600px] md:min-w-full text-left border-collapse bg-white overflow-hidden">
            <thead className="bg-gray-100">
              <tr className="text-gray-700 text-sm font-semibold">
                <th className="px-4 py-3">Service Description</th>
                <th className="px-4 py-3">Amount</th>
                <th className="px-4 py-3">Date</th>
                <th className="px-4 py-3">Transaction Proof</th>
              </tr>
            </thead>
            <tbody>
              {overheads.map((item, idx) => (
                <tr key={idx} className="border-t text-sm text-gray-800">
                  <td className="px-6 py-6">{item.description}</td>
                  <td className="px-4 py-6">{item.amount}</td>
                  <td className="px-4 py-6">{item.date}</td>
                  <td className="px-4 py-6 text-yellow-600">{item.proof}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};