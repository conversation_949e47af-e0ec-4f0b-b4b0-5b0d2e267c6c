import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>oader, <PERSON>Copy, FiX, FiCheck } from 'react-icons/fi';
import { LuDollarSign } from "react-icons/lu";
import { IoCallOutline } from "react-icons/io5";

const PaymentProcessingModal = ({ 
    isOpen, 
    onClose, 
    paymentData = {},
    walletAddress = '',
    isBlocking = true // New prop to prevent clicking anywhere when processing
}) => {
    if (!isOpen) return null;

    const [copiedWallet, setCopiedWallet] = useState(false);
    const [processingStep, setProcessingStep] = useState(1);

    const {
        amount = '0',
        finalAmount = null,
        bnbAmount = '0',
        phoneNumber = '',
        transactionId = '',
        rate = 500,
        fees = null,
        currency = null,
        localCurrency = null,
        country = '',
        chargeAmount = null,
        chargeCurrency = 'USD'
    } = paymentData;

    // Animate processing steps
    useEffect(() => {
        if (!isOpen) return;
        
        const steps = [1, 2, 3];
        let currentIndex = 0;
        
        const interval = setInterval(() => {
            currentIndex = (currentIndex + 1) % steps.length;
            setProcessingStep(steps[currentIndex]);
        }, 2000);

        return () => clearInterval(interval);
    }, [isOpen]);

    const copyToClipboard = (text, type = 'general') => {
        navigator.clipboard.writeText(text).then(() => {
            if (type === 'wallet') {
                setCopiedWallet(true);
                setTimeout(() => setCopiedWallet(false), 2000);
            }
        }).catch(err => {
            console.error('Failed to copy:', err);
        });
    };

    // Custom animations
    const modalStyles = `
        @keyframes processingFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes processingSlideUp {
            from { 
                opacity: 0; 
                transform: translateY(20px) scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }
        @keyframes processingSpinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @keyframes processingPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        @keyframes processingDots {
            0%, 20% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }
        .animate-processing-fadeIn {
            animation: processingFadeIn 0.3s ease-out;
        }
        .animate-processing-slideUp {
            animation: processingSlideUp 0.4s ease-out;
        }
        .animate-processing-spinner {
            animation: processingSpinner 1s linear infinite;
        }
        .animate-processing-pulse {
            animation: processingPulse 1.5s ease-in-out infinite;
        }
        .animate-processing-dots-1 {
            animation: processingDots 1.5s ease-in-out infinite;
            animation-delay: 0s;
        }
        .animate-processing-dots-2 {
            animation: processingDots 1.5s ease-in-out infinite;
            animation-delay: 0.3s;
        }
        .animate-processing-dots-3 {
            animation: processingDots 1.5s ease-in-out infinite;
            animation-delay: 0.6s;
        }
    `;

    const getProcessingStepText = () => {
        switch (processingStep) {
            case 1:
                return "Initiating payment request...";
            case 2:
                return "Connecting to payment provider...";
            case 3:
                return "Processing transaction...";
            default:
                return "Processing payment...";
        }
    };

    const handleOverlayClick = (e) => {
        // Only allow closing if not blocking and clicking the overlay
        if (!isBlocking && e.target === e.currentTarget) {
            onClose();
        }
    };

    return (
        <>
            <style>{modalStyles}</style>
            <div 
                className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 animate-processing-fadeIn p-2 sm:p-4"
                onClick={handleOverlayClick}
            >
                <div 
                    className="bg-slate-800 border border-slate-600 rounded-xl sm:rounded-2xl max-w-sm sm:max-w-md w-full mx-2 sm:mx-4 shadow-2xl transform animate-processing-slideUp overflow-hidden max-h-[95vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header */}
                    <div className="relative bg-gradient-to-r from-blue-500/10 to-indigo-500/10 p-4 sm:p-6 border-b border-slate-600">
                        {!isBlocking && (
                            <button
                                onClick={onClose}
                                className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-white transition-colors"
                            >
                                <FiX className="w-5 h-5 sm:w-6 sm:h-6" />
                            </button>
                        )}
                        
                        {/* Processing icon */}
                        <div className="flex justify-center mb-3 sm:mb-4">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-500/20 rounded-full flex items-center justify-center animate-processing-pulse">
                                <FiLoader className="w-10 h-10 sm:w-12 sm:h-12 text-blue-400 animate-processing-spinner" />
                            </div>
                        </div>
                        
                        <div className="text-center">
                            <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Processing Payment</h2>
                            <p className="text-gray-300 text-sm sm:text-base">
                                {getProcessingStepText()}
                                <span className="inline-flex ml-1">
                                    <span className="animate-processing-dots-1">.</span>
                                    <span className="animate-processing-dots-2">.</span>
                                    <span className="animate-processing-dots-3">.</span>
                                </span>
                            </p>
                            <p className="text-gray-400 text-xs sm:text-sm mt-2">
                                Please do not close this window
                            </p>
                        </div>
                    </div>

                    {/* Payment details */}
                    <div className="p-4 sm:p-6 space-y-3 sm:space-y-4">
                        {/* Transaction Summary */}
                        <div className="bg-slate-700/30 border border-slate-600 rounded-lg p-3 sm:p-4">
                            <div className="text-center">
                                <div className="text-gray-400 text-sm mb-2">
                                    Transaction Summary:
                                </div>
                                <div className="text-teal-400 font-bold text-2xl mb-2">
                                    ${finalAmount?.toFixed(2) || amount}
                                </div>
                                {currency?.success && fees?.finalAmountLocal && (
                                    <div className="text-gray-300 text-sm">
                                        ({currency.currencyInfo?.code}{fees.finalAmountLocal.toFixed(2)})
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* BNB Amount */}
                        <div className="bg-slate-700/40 rounded-lg p-3 sm:p-4">
                            <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3">
                                <LuDollarSign className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
                                <span className="text-xs sm:text-sm font-medium text-gray-300">BNB Details</span>
                            </div>
                            
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-lg sm:text-xl font-bold text-white">BNB to Receive:</span>
                                <span className="text-lg sm:text-xl text-green-400 font-semibold">{bnbAmount} BNB</span>
                            </div>
                            
                            <div className="text-xs text-gray-400 pt-2 border-t border-slate-600">
                                <div>Rate: 1 BNB = ${typeof rate === 'number' ? rate.toLocaleString() : rate}</div>
                                {currency?.exchangeRate && (
                                    <div className="mt-1">
                                        Exchange Rate: 1 USD = {currency.exchangeRate.rate.toFixed(4)} {currency.exchangeRate.toCurrency}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Phone number used */}
                        {phoneNumber && (
                            <div className="flex items-center justify-between p-2 sm:p-3 bg-slate-700/20 rounded-lg">
                                <div className="flex items-center gap-2 sm:gap-3">
                                    <IoCallOutline className="w-4 h-4 sm:w-5 sm:h-5 text-teal-400" />
                                    <div>
                                        <div className="text-xs sm:text-sm text-gray-400">Phone Number</div>
                                        <div className="text-sm sm:text-base text-white font-medium break-all">{phoneNumber}</div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Wallet address */}
                        <div className="p-2 sm:p-3 bg-slate-700/20 rounded-lg">
                            <div className="flex items-center gap-2 sm:gap-3 mb-2">
                                <div className="w-4 h-4 sm:w-5 sm:h-5 bg-teal-400 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-bold text-black">W</span>
                                </div>
                                <span className="text-xs sm:text-sm text-gray-400">Receiving Wallet</span>
                            </div>
                            <div className="flex items-center justify-between gap-2">
                                <span className="text-white font-mono text-xs sm:text-sm break-all">
                                    {walletAddress.slice(0, 6)}...{walletAddress.slice(-6)}
                                </span>
                                <button
                                    onClick={() => copyToClipboard(walletAddress, 'wallet')}
                                    className={`transition-colors flex-shrink-0 ${copiedWallet ? 'text-green-400' : 'text-teal-400 hover:text-teal-300'}`}
                                    title={copiedWallet ? "Copied!" : "Copy wallet address"}
                                >
                                    {copiedWallet ? (
                                        <FiCheck className="w-3 h-3 sm:w-4 sm:h-4" />
                                    ) : (
                                        <FiCopy className="w-3 h-3 sm:w-4 sm:h-4" />
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Processing status */}
                        <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-orange-500/10 border border-orange-500/30 rounded-lg">
                            <div className="flex items-start gap-2 sm:gap-3">
                                <div className="w-4 h-4 sm:w-5 sm:h-5 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-xs font-bold text-white">!</span>
                                </div>
                                <div>
                                    <h4 className="text-orange-400 font-semibold text-xs sm:text-sm mb-1">Processing in Progress</h4>
                                    <p className="text-orange-300 text-xs sm:text-sm">
                                        Your payment is being processed. This may take a few moments. Please wait and don't refresh or close this window.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default PaymentProcessingModal;
