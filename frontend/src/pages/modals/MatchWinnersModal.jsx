import React from 'react';
import { FaTimes, FaTrophy } from 'react-icons/fa';
import {ethers} from 'ethers';

export const MatchWinnersModal = ({ matches, winners, winningNumbers, onClose }) => {
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="absolute inset-0 bg-black/50" onClick={onClose}></div>
      <div className="bg-[#2A3444] rounded-2xl p-8 relative z-10 w-[90%] max-w-[500px]">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-white text-xl font-medium">{matches} Matches Winners</h3>
          <button onClick={onClose} className="text-white hover:text-gray-300">
            <FaTimes />
          </button>
        </div>
        
        {winners.map((winner, index) => (
          <div key={index} className={`${matches == 6 ? "bg-BP-gold-gradient-end":"bg-[#1C2431]"}  rounded-xl p-4 mb-3`}>
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <FaTrophy className="text-white" />
                <span className="text-white font-mono">{formatWalletAddress(winner.player)}</span>
              </div>
              <span className="text-white">$ {ethers.utils.formatEther(winner.reward)}</span>
            </div>
            <div className="mt-2">
              <p className={`${matches == 6 ? "text-gray-100":"text-gray-400"}  text-sm mb-2`}>Matched numbers:</p>
              <div className="flex space-x-2">
                {winner.numbers.map((number, idx) => (
                  <div
                    key={idx}
                    className={`${winningNumbers.includes(number.toNumber()) ? "bg-[#72519F]":""} w-8 h-8 rounded-full flex items-center justify-center text-white font-medium`}
                  >
                    {number.toNumber()}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
