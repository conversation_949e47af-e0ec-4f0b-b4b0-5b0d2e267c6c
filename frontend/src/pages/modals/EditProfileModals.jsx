import React, { useState, useEffect, useMemo } from 'react';
import { UserService } from '../../services/userService';
import { toast } from 'react-toastify';
import {
  EyeInvisibleOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { Select } from 'antd';
import { getCountryOptions } from '../../utils/countryUtils';
import { AuthService } from '../../services/authService';
import { VerifyEmail } from '../Auth/Verify';

export const EditFirstNameModal = ({
  onClose,
  currentFirstName,
  getCurrentUser,
}) => {
  const [fullName, setFullName] = useState(currentFirstName || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!fullName.trim()) {
      toast.error('First name cannot be empty');
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        firstName: fullName.trim(),
      };

      const success = await UserService.UpdateUserDetails(
        payload,
        getCurrentUser
      );
      if (success) {
        setIsLoading(false); // Set loading to false before closing
        onClose(); // Close the modal
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to update first name');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[400px]">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Edit your firstname
        </h2>
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">First name</label>
          <input
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            className="w-full p-3 bg-white text-gray-700 border rounded-xl"
            disabled={isLoading}
          />
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-xl border text-gray-600 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-6 py-2 rounded-xl bg-purple-500 text-white hover:bg-purple-600"
          >
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

export const EditLastNameModal = ({
  onClose,
  currentLastName,
  getCurrentUser,
}) => {
  const [lastName, setLastName] = useState(currentLastName || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    if (!lastName.trim()) {
      toast.error('Last name cannot be empty');
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        lastName: lastName.trim(),
      };

      const success = await UserService.UpdateUserDetails(
        payload,
        getCurrentUser
      );
      if (success) {
        setIsLoading(false);
        onClose();
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to update last name');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[400px]">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Edit your lastname
        </h2>
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Last name</label>
          <input
            type="text"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            className="w-full p-3 bg-white text-gray-700 border rounded-xl"
            disabled={isLoading}
          />
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-xl border text-gray-600 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-6 py-2 rounded-xl bg-purple-500 text-white hover:bg-purple-600"
          >
            {isLoading ? 'Saving...' : 'Save changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export const EditEmailModal = ({ onClose, currentEmail, getCurrentUser }) => {
  const [email, setEmail] = useState(currentEmail || '');
  const [isLoading, setIsLoading] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [code, setCode] = useState(["", "", "", "", ""]);
  const [verifyLoading, setVerifyLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleChange = (index, value) => {
    // Handle pasting
    if (value.length > 1) {
      const pastedValue = value.replace(/\D/g, '').slice(0, 5).split('');
      const newCode = [...code];
      
      // Fill in the exact digits from the pasted value
      pastedValue.forEach((digit, i) => {
        if (i < 5) newCode[i] = digit;
      });
      
      setCode(newCode);

      // Focus on the next empty input or the last input
      const nextEmptyIndex = newCode.findIndex(digit => !digit);
      const inputToFocus = document.querySelector(
        `input[name="code-${nextEmptyIndex > -1 ? nextEmptyIndex : 4}"]`
      );
      if (inputToFocus) inputToFocus.focus();
      return;
    }

    // Handle single digit input
    if (!/^\d*$/.test(value)) return;
    
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Auto-focus next input
    if (value && index < 4) {
      const nextInput = document.querySelector(`input[name="code-${index + 1}"]`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      const prevInput = document.querySelector(`input[name="code-${index - 1}"]`);
      if (prevInput) {
        prevInput.focus();
        const newCode = [...code];
        newCode[index - 1] = '';
        setCode(newCode);
      }
    }
  };

  const handleSendVerification = async () => {
    if (!email.trim()) {
      toast.error('Email cannot be empty');
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    try {
      const result = await AuthService.sendVerificationCode({
        email: email.trim(),
      });
      if (result) {
        setShowVerification(true);
        setCountdown(30);
      }
    } catch (error) {
      toast.error('Failed to send verification code');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerify = async (e) => {
    e.preventDefault();
    const verificationCode = code.join("");
    if (verificationCode.length !== 5) {
      toast.error('Please enter the complete verification code');
      return;
    }

    setVerifyLoading(true);
    try {
      const result = await AuthService.verifyCode({
        email: email.trim(),
        code: verificationCode
      });
      
      if (result) {
        await handleVerificationSuccess();
      } else {
        setCode(["", "", "", "", ""]);
      }
    } catch (error) {
      toast.error('Verification failed');
    } finally {
      setVerifyLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendLoading || countdown > 0) return;
    
    setResendLoading(true);
    try {
      const result = await AuthService.sendVerificationCode({
        email: email.trim(),
      });
      if (result) {
        setCode(["", "", "", "", ""]);
        setCountdown(30);
      }
    } catch (error) {
      toast.error('Failed to resend code');
    } finally {
      setResendLoading(false);
    }
  };

  const handleVerificationSuccess = async () => {
    try {
      const payload = {
        email: email.trim(),
      };

      const success = await UserService.UpdateUserDetails(
        payload,
        getCurrentUser
      );
      if (success) {
        toast.success('Email updated successfully');
        onClose();
      }
    } catch (error) {
      toast.error('Failed to update email');
    }
  };

  if (showVerification) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex justify-center items-center z-50 p-4">
        <div className="bg-gradient-to-br from-gray-900 to-black rounded-2xl p-8 max-w-md w-full relative animate-fadeIn border border-gray-700 shadow-2xl">
          <button 
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-white transition"
          >
            <CloseOutlined className="text-xl" />
          </button>

          <div className="text-center space-y-6">
            <h2 className="text-2xl font-bold text-white">
              Verify Reset Code
            </h2>
            
            <div className="space-y-2">
              <p className="text-gray-300">
                Please enter the verification code sent to:
              </p>
              <p className="font-medium text-yellow-400 break-all">
                {email}
              </p>
            </div>

            <form onSubmit={handleVerify} className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-center gap-2">
                  {code.map((digit, index) => (
                    <input
                      key={index}
                      name={`code-${index}`}
                      type="text"
                      maxLength="1"
                      className="w-12 h-12 bg-gray-800 text-white text-center text-xl rounded-lg border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                      value={digit}
                      onChange={(e) => handleChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(e, index)}
                      disabled={verifyLoading}
                    />
                  ))}
                </div>
                <p className="text-gray-400 text-sm">
                  Haven't received the code? Check your spam folder
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  disabled={verifyLoading || code.join("").length !== 5}
                  className="flex-1 bg-yellow-500 text-black font-semibold px-6 py-3 rounded-lg hover:bg-yellow-400 active:bg-yellow-600 transition-all transform active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                >
                  {verifyLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                      </svg>
                      Verifying...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <CheckOutlined />
                      Verify Code
                    </div>
                  )}
                </button>

                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={resendLoading || countdown > 0}
                  className="text-yellow-400 hover:text-yellow-300 font-medium disabled:opacity-50 disabled:cursor-not-allowed transition"
                >
                  {resendLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                      </svg>
                      Sending...
                    </div>
                  ) : countdown > 0 ? (
                    `Resend in ${countdown}s`
                  ) : (
                    "Resend Code"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[400px]">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Change email address
        </h2>
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">New email address</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-3 border bg-white text-gray-700 rounded-xl mb-4"
            disabled={isLoading}
          />
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <p className="text-BP-hovered-yellow flex items-center gap-2">
              <InfoCircleOutlined className="text-BP-gold" />
              You will need to verify your new email address.
            </p>
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-xl border text-gray-600 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSendVerification}
            disabled={isLoading}
            className="px-6 py-2 rounded-xl bg-purple-500 text-white hover:bg-purple-600"
          >
            {isLoading ? 'Sending...' : 'Proceed'}
          </button>
        </div>
      </div>
    </div>
  );
};

export const EditCountryModal = ({
  onClose,
  currentCountry,
  getCurrentUser,
}) => {
  const [country, setCountry] = useState(currentCountry === 'N/A' ? '' : currentCountry || '');
  const [isLoading, setIsLoading] = useState(false);
  const [countryError, setCountryError] = useState('');

  const countryOptions = useMemo(() => getCountryOptions(), []);

  const handleCountryChange = (value) => {
    setCountry(value);
    setCountryError('');
  };

  const validateField = (field, value) => {
    if (field === 'country' && !value) {
      setCountryError('Please select a country');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateField('country', country)) {
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        country: country.trim(),
      };

      const success = await UserService.UpdateUserDetails(
        payload,
        getCurrentUser
      );
      if (success) {
        setIsLoading(false);
        onClose();
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to update country');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[400px]">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Edit your country
        </h2>
        <div className="mb-6">
          <label className="block text-gray-600 mb-2">Country</label>
          <Select
            showSearch
            placeholder="Select your country"
            value={country === 'N/A' ? undefined : country} 
            onChange={(value) => {
              handleCountryChange(value);
              validateField('country', value);
            }}
            onBlur={() => validateField('country', country)}
            options={countryOptions}
            filterOption={(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
              option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            disabled={isLoading}
            defaultActiveFirstOption={false}
            className={`
              w-full h-12
              [&_.ant-select-selector]:h-12
              [&_.ant-select-selector]:rounded-xl
              [&_.ant-select-selector]:border
              [&_.ant-select-selector]:border-gray-200
              [&_.ant-select-selector]:px-3
              [&_.ant-select-selector]:flex
              [&_.ant-select-selector]:items-center
              [&_.ant-select-selector]:bg-white
              [&_.ant-select-selection-item]:leading-[48px]
              [&_.ant-select-selection-placeholder]:leading-[48px]
              [&_.ant-select-selection-placeholder]:text-gray-400
              [&_.ant-select-arrow]:text-gray-400
              [&_.ant-select-arrow]:right-3
              hover:[&_.ant-select-selector]:border-BP-purple
              focus:[&_.ant-select-selector]:border-BP-purple
              focus:[&_.ant-select-selector]:ring-2
              focus:[&_.ant-select-selector]:ring-purple-100
              ${countryError ? 'border-red-500' : 'border-gray-200'}
            `}
            dropdownClassName="
              rounded-xl
              shadow-lg
              [&_.ant-select-item]:px-4
              [&_.ant-select-item]:py-2
              [&_.ant-select-item-option-selected]:bg-purple-50
              [&_.ant-select-item-option-selected]:text-BP-purple
              [&_.ant-select-item-option-active]:bg-gray-50
            "
          />
          {countryError && (
            <p className="text-red-500 text-xs mt-1">{countryError}</p>
          )}
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-xl border text-gray-600 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-6 py-2 rounded-xl bg-purple-500 text-white hover:bg-purple-600"
          >
            {isLoading ? 'Saving...' : 'Save changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export const EditPasswordModal = ({ onClose, getCurrentUser }) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [validations, setValidations] = useState({
    hasNumber: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasSpecialChar: false,
    hasMinLength: false,
  });

  const validatePassword = (password) => {
    setValidations({
      hasNumber: /\d/.test(password),
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasSpecialChar: /[@$!%*#?&]/.test(password),
      hasMinLength: password.length >= 8,
    });
  };

  const handleNewPasswordChange = (e) => {
    const value = e.target.value;
    setNewPassword(value);
    validatePassword(value);
  };

  const validateFields = () => {
    // Trim all fields to check for empty strings
    const trimmedCurrentPassword = currentPassword.trim();
    const trimmedNewPassword = newPassword.trim();
    const trimmedConfirmPassword = confirmPassword.trim();

    // Check for empty fields
    if (
      !trimmedCurrentPassword ||
      !trimmedNewPassword ||
      !trimmedConfirmPassword
    ) {
      toast.error('All fields are required');
      return false;
    }

    // Validate password match
    if (trimmedNewPassword !== trimmedConfirmPassword) {
      toast.error('New passwords do not match');
      return false;
    }

    // Validate password requirements
    if (!Object.values(validations).every(Boolean)) {
      toast.error('Password must meet all requirements');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateFields()) {
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        currentPassword: currentPassword.trim(),
        newPassword: newPassword.trim(),
        confirmPassword: confirmPassword.trim(),
      };

      const success = await UserService.ChangePasswordWithVerification(payload);
      if (success) {
        toast.success('Password updated successfully');
        onClose();
      }
    } catch (error) {
      console.error('Password update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRequirementsText = () => {
    const requirements = [];

    if (!validations.hasMinLength) requirements.push('8 characters');
    if (!validations.hasUpperCase) requirements.push('upperCase');
    if (!validations.hasLowerCase) requirements.push('lowerCase');
    if (!validations.hasNumber) requirements.push('numbers');
    if (!validations.hasSpecialChar) requirements.push('symbols');

    if (requirements.length === 0) {
      return 'Password meets all requirements';
    }

    return `Password must contain ${requirements.join(', ')}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[400px]">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          Change password
        </h2>
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-gray-600 mb-2">Current password</label>
            <div className="relative">
              <input
                type={showCurrentPassword ? 'text' : 'password'}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="w-full p-3 border bg-white text-gray-700 rounded-xl pr-10"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showCurrentPassword ? (
                  <EyeInvisibleOutlined />
                ) : (
                  <EyeOutlined />
                )}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-gray-600 mb-2">New password</label>
            <div className="relative">
              <input
                type={showNewPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={handleNewPasswordChange}
                className="w-full p-3 border bg-white text-gray-700 rounded-xl pr-10"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showNewPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-gray-600 mb-2">
              Confirm new password
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full p-3 border bg-white text-gray-700 rounded-xl pr-10"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showConfirmPassword ? (
                  <EyeInvisibleOutlined />
                ) : (
                  <EyeOutlined />
                )}
              </button>
            </div>
          </div>
          <div
            className={`bg-yellow-50 border-l-4 ${
              Object.values(validations).every(Boolean)
                ? 'border-green-400'
                : 'border-yellow-400'
            } p-4 mt-4`}
          >
            <p
              className={`flex items-center gap-2 ${
                Object.values(validations).every(Boolean)
                  ? 'text-green-600'
                  : 'text-BP-hovered-yellow'
              }`}
            >
              {Object.values(validations).every(Boolean) ? (
                <CheckCircleOutlined />
              ) : (
                <InfoCircleOutlined />
              )}
              {getRequirementsText()}
            </p>
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-xl border text-gray-600 hover:bg-gray-50"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="px-6 py-2 rounded-xl bg-purple-500 text-white hover:bg-purple-600"
          >
            {isLoading ? 'Changing...' : 'Change password'}
          </button>
        </div>
      </div>
    </div>
  );
};
