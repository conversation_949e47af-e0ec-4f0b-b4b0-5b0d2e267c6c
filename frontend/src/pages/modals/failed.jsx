import React, { useEffect } from 'react';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';

const MySwal = withReactContent(Swal);

const FailedAlert = ({ message, onClose }) => {
  useEffect(() => {
    MySwal.fire({
      icon: 'error',
      title: 'Failed!',
      text: message,
      confirmButtonText:'OK',
      customClass: {
         confirmButton:"w-28 h-10 px-5 py-2 rounded-full bg-enk-shadegreen hover:bg-white hover:text-enk-darkgreen hover:border-enk-shadegreen flex justify-center items-center text-white text-sm font-medium font-['Inter'] transition-all duration-300 ease-in-out"
      },
    }).then((result) => {
      if (result.isConfirmed) {
        onClose(); 
      }
    });
  }, [message,onClose]); 

  return null;
};

export default FailedAlert;