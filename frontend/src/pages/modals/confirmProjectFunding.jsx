import React, { useState } from 'react';
import { IoMdInformationCircle } from "react-icons/io";
import { IoClose } from 'react-icons/io5';
import { LogProject } from './LogProject';

export const ConfirmProjectFunding = ({ onClose }) => {
  const [showLog, setShowLog] = useState(false);

  if (showLog) {
    return <LogProject onClose={onClose} />;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-xl mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="font-semibold text-xl text-gray-800">Confirm Service Funding</h2>
          <button onClick={onClose}><IoClose className="text-2xl text-gray-500" /></button>
        </div>
        <p className="text-gray-700 text-sm mb-4">
          Are you sure you want to log a payment of $100 for a marketing event? This action cannot be undone
        </p>
        <div className="bg-[#fbf2d6] p-4 mb-6 relative overflow-hidden">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-[#E1AB0D]"></div>
          <div className="flex gap-3 pl-2">
            <IoMdInformationCircle  className="text-[#E1AB0D] text-xl flex-shrink-0 mt-2" />
            <p className="text-[#E1AB0D]  text-sm">
              This action will immediately reflect on the dashboards of all community members.
            </p>
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 rounded-md bg-[#A855F7] text-white"
            onClick={() => setShowLog(true)}
          >
            Log Project
          </button>
        </div>
      </div>
    </div>
  );
};