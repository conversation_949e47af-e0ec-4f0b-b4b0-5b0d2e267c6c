import React from 'react';
import { IoInformationCircle } from 'react-icons/io5';

export const PrivacyConfirmationModal = ({ onClose, onConfirm }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl mx-4 max-h-screen overflow-y-auto">
        <h2 className="font-semibold text-xl text-gray-800 mb-2">
          Confirm Privacy Change
        </h2>
        <p className="text-gray-700 text-sm mb-4">
          Are you sure you want to make this community private? Only invited
          members will be able to join.
        </p>
        {/* Yellow Info Box */}
        <div className="bg-[#FFF9E7] p-4 mb-4 relative overflow-hidden">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-yellow-400"></div>
          <div className="flex gap-3 pl-2">
            <IoInformationCircle className="text-yellow-400 text-xl flex-shrink-0 mt-1" />
            <p className="text-yellow-600 text-sm">
              Existing members will not be affected by this change.
            </p>
          </div>
        </div>
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="bg-[#9747FF] text-white px-4 py-2 rounded-lg"
          >
            Confirm change
          </button>
        </div>
      </div>
    </div>
  );
};
