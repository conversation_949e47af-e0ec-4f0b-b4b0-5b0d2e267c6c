import React, { useState } from 'react';
import { IoClose } from 'react-icons/io5';
import { ConfirmProjectFunding } from "./confirmProjectFunding.jsx";
import { ConfirmServiceFunding } from "./confirmServiceFunding..jsx";

export const LogDisbursement = ({ onClose }) => {
  const [type, setType] = useState('project');
  const [label, setLabel] = useState('');
  const [amount, setAmount] = useState('');
  const [transaction, setTransaction]= useState('');
  const [showConfirm, setShowConfirm] = useState(false);
  const [loading, setLoading] = useState(false);

  const onSubmit = async () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShowConfirm(true);
    }, 500);
  };

  // Handle closing confirm modal
  const handleConfirmClose = () => {
    setShowConfirm(false);
    onClose();
  };

  return (
    <>
      <div className={`fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 ${showConfirm ? 'hidden' : ''}`}>
        <div className="bg-white rounded-3xl shadow-lg p-6 w-full max-w-3xl mx-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="font-semibold text-xl text-[#2c5282]">Log Disbursement</h2>
            <button onClick={onClose}><IoClose className="text-2xl text-gray-500" /></button>
          </div>
          <h2 className="font-semibold text-xl text-[#2c5282] mb-4">Disbursement Type</h2>

          <div className="flex flex-row gap-6 mb-6">
            <div className="flex items-center">
              <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                <input
                  type="radio"
                  name="type"
                  value="project"
                  checked={type === 'project'}
                  onChange={() => setType('project')}
                  className="appearance-none w-3 h-3 sm:w-2 sm:h-2 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                />
              </div>
              <label className="text-[#2c5282] text-sm sm:text-base">
                Project Funding
              </label>
            </div>
            <div className="flex items-center">
              <div className="mr-2 flex items-center justify-center p-1 rounded-full border border-purple-600 w-fit h-fit">
                <input
                  type="radio"
                  name="type"
                  value="service"
                  checked={type === 'service'}
                  onChange={() => setType('service')}
                  className="appearance-none w-3 h-3 sm:w-2 sm:h-2 rounded-full checked:bg-purple-600 focus:outline-none transition-colors duration-200"
                />
              </div>
              <label className="text-[#2c5282] text-sm sm:text-base">
                Service Funding
              </label>
            </div>
          </div>

          {type === 'project' ? (
            <div className="mb-6 flex flex-col gap-4">
              <div>
                <label className="block mb-1 font-medium text-[#2c5282]">Project Name</label>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="Sickle Cell Campaign"
                  value={label}
                  onChange={e => setLabel(e.target.value)}
                />
              </div>
              <div>
                <label className="block mb-1 font-medium text-[#2c5282]">Amount Deployed</label>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="Amount Deployed"
                  value={amount}
                  onChange={e => setAmount(e.target.value)}
                  type="number"
                  min="0"
                />
              </div>
             <div className="flex flex-col gap-6 mt-6 mb-6">
              <div>
                <h2 className="block mb-1 font-medium text-[#2c5282]">Proof of Transaction</h2>
                <p className='text-[#2C5282]  mt-5 mb-6 ml-5'>Transaction Hash</p>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="Enter Transaction Hash"
                  value={transaction}
                  onChange={e => setTransaction(e.target.value)}
                  type="text"
                />
              </div>

 
            <div className="flex items-center my-4">
              <hr className="flex-grow border-t border-[#F5C21A]" />
              <span className="mx-4 text-[#F5C21A] font-semibold">OR</span>
              <hr className="flex-grow border-t border-[#F5C21A]" />
            </div>

            <div>
              <h2 className="block mb-1 font-medium text-[#2c5282]">Transaction Receipt</h2>
              <button className='w-[200px] h-[56px] rounded-[20px] border border-purple-600 text-gray-700 text-lg font-semibold transition-opacity duration-200'>
                <span className='font-poppins font-normal text-[20px] leading-[100%] tracking-[0] text-purple-600'>
                  Upload Receipt
                </span>
              </button>
            </div>
          </div>


            </div>
          ) : (
            <div className="mb-6 flex flex-col gap-4">
              <div>
                <label className="block mb-1 font-medium text-[#2c5282]">Service Description</label>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="Marketing Event"
                  value={label}
                  onChange={e => setLabel(e.target.value)}
                />
              </div>
              <div>
                <label className="block mb-1 font-medium text-[#2c5282]">Amount Paid</label>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="$200"
                  value={amount}
                  onChange={e => setAmount(e.target.value)}
                  type="number"
                  min="0"
                />
              </div>
                <div className="flex flex-col gap-6 mt-6 mb-6">
              <div>
                <h2 className="block mb-1 font-medium text-[#2c5282]">Proof of Transaction</h2>
                <p className='text-[#2C5282]  mt-5 mb-6 ml-5'>Transaction Hash</p>
                <input
                  className="w-full border p-5 rounded-xl bg-white text-black"
                  placeholder="Enter Transaction Hash"
                  value={transaction}
                  onChange={e => setTransaction(e.target.value)}
                  type="text"
                />
              </div>

 
            <div className="flex items-center my-4">
              <hr className="flex-grow border-t border-[#F5C21A]" />
              <span className="mx-4 text-[#F5C21A] font-semibold">OR</span>
              <hr className="flex-grow border-t border-[#F5C21A]" />
            </div>

            <div>
              <h2 className="block mb-1 font-medium text-[#2c5282]">Transaction Receipt</h2>
             <button className='w-[200px] h-[56px] rounded-[20px] border border-purple-600 text-gray-700 text-lg font-semibold transition-opacity duration-200'>
              <span className='font-poppins font-normal text-[20px] leading-[100%] tracking-[0] text-purple-600'>
                Upload Receipt
              </span>
            </button>
            </div>
          </div>
            </div>
          )}

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="w-[172px] h-[74px] rounded-[20px] border border-gray-300 text-gray-700 text-lg font-semibold transition-opacity duration-200"
              style={{ marginTop: '16px' }}
            >
              Cancel
            </button>
            <button
              onClick={onSubmit}
              disabled={!label.trim() || !amount.trim() || loading}
              className={`w-[300px] h-[74px] rounded-[20px] bg-[#9333EA] text-white px-6 py-4 text-lg font-semibold border border-gray-300 transition-opacity duration-200 ${(!label.trim() || !amount.trim() || loading) ? 'opacity-50 cursor-not-allowed' : ''}`}
              style={{ marginTop: '16px' }}
            >
              {loading ? 'Submitting...' : 'Log Distribution'}
            </button>
          </div>
        </div>
      </div>
      {showConfirm && (
        type === 'project'
          ? <ConfirmProjectFunding onClose={handleConfirmClose} />
          : <ConfirmServiceFunding onClose={handleConfirmClose} />
      )}
    </>
  );
};