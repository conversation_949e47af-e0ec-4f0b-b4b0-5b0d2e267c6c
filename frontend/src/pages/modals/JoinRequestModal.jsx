import React from 'react';
import { IoClose } from 'react-icons/io5';

export const JoinRequestModal = ({ onClose }) => {
  const joinRequests = [
    { name: '<PERSON>', email: '<EMAIL>' },
    { name: '<PERSON>', email: 'mi<PERSON><PERSON>@example.com' },
    { name: '<PERSON>', email: '<EMAIL>' },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#FDFCF9] p-6 rounded-3xl shadow-xl w-full max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-6xl max-h-screen overflow-y-auto relative">
        {/* Close Button */}
        <button
          className="absolute top-4 right-6 text-gray-400 text-xl hover:text-gray-600"
          onClick={onClose}
        >
          <IoClose className="w-8 h-8" />
        </button>

        <h2 className="text-2xl font-semibold text-gray-800 mb-6">Join Requests</h2>

        <div className="space-y-4">
          {joinRequests.map((user, index) => (
            <div
              key={index}
              className="p-4 bg-white rounded-2xl border border-gray-200 flex flex-col sm:flex-row justify-between sm:items-center"
            >
              <div>
                <p className="font-semibold text-gray-900">{user.name}</p>
                <p className="text-sm text-gray-600 mt-2">{user.email}</p>
                <p className="text-xs text-gray-500 mt-2">Requested on: 2024-06-15</p>
              </div>
              <div className="flex gap-3 mt-4 sm:mt-0 sm:flex-shrink-0 flex-wrap">
                <button className="bg-green-100 text-green-600 px-5 py-2 rounded-lg font-medium hover:bg-green-200 transition">
                  ✔ Approve
                </button>
                <button className="bg-red-100 text-red-600 px-5 py-2 rounded-lg font-medium hover:bg-red-200 transition">
                  ✖ Reject
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-between items-center px-1">
          <button className="text-gray-600 font-medium hover:text-yellow-300 px-3 py-1 rounded transition duration-200">
            Approve All
          </button>
          <button
            className="bg-[#FDFCF9] border border-yellow-300 text-gray-700 font-medium px-6 py-2 rounded-xl shadow-sm hover:bg-gray-100 transition"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
