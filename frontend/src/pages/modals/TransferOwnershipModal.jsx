import React, { useState } from 'react';
import { RiVipCrownFill } from 'react-icons/ri';
import { IoClose } from 'react-icons/io5';

export const TransferOwnershipModal = ({
  onClose,
  members = [],
  onTransfer,
  loading = false,
}) => {
  const [selectedMember, setSelectedMember] = useState('');

  const handleTransfer = () => {
    if (selectedMember && onTransfer) {
      onTransfer(selectedMember);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl mx-4 max-h-screen overflow-y-auto">
        <div className="p-6">
          <h2 className="font-semibold text-xl text-gray-800 mb-6">
            Transfer Community Ownership
          </h2>

          <div className="mb-6">
            <label className="block text-gray-600 text-sm mb-2">
              Select New Owner
            </label>
            <div className="relative">
              <select
                className="w-full appearance-none bg-gray-50 border border-gray-200 text-gray-700 py-3 px-4 rounded-2xl focus:outline-none"
                value={selectedMember}
                onChange={e => setSelectedMember(e.target.value)}
              >
                <option value="" disabled>
                  Select a member
                </option>
                {members.map(member => (
                  <option key={member._id} value={member._id}>
                    {member.firstName} {member.lastName} ({member.email})
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Yellow Info Box */}
          <div className="bg-[#FFF9E7] p-4 mb-4 relative overflow-hidden">
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-yellow-400"></div>
            <div className="flex gap-3 pl-2">
              <RiVipCrownFill className="text-yellow-400 text-xl flex-shrink-0 mt-4" />

              <p className="text-yellow-600 text-sm mt-2">
                <span className="font-medium text-yellow-600 p-2">
                  Important
                </span>
                Transferring ownership will make you a regular admin. The new
                owner will have full control over the community.
              </p>
            </div>
          </div>

          {/* Red Warning Box */}
          <div className="bg-[#FFEFEF]  p-4 mb-6 relative overflow-hidden">
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500"></div>
            <div className="flex gap-3 pl-2">
              <IoClose className="text-red-500 text-xl flex-shrink-0 mt-2" />
              <p className="text-red-500 text-sm">
                This action cannot be undone. Only transfer to trusted member
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-6 py-2.5 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              className="px-6 py-2.5 rounded-lg bg-[#bbabd3] text-white hover:bg-[#8A3FFF] transition disabled:opacity-60"
              onClick={handleTransfer}
              disabled={!selectedMember || loading}
            >
              {loading ? "Transferring..." : "Transfer ownership"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
