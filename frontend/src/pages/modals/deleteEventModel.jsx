import React from 'react';
import { IoClose } from 'react-icons/io5';

export const DeleteEventModal = ({ onClose, confirmText, setConfirmText, onDelete }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl mx-4">
        <h2 className="font-semibold text-xl text-gray-800 mb-2">Delete Event</h2>
        <p className="text-gray-700 text-sm mb-4">
          Are you sure you want to permanently delete the <strong>Event</strong>? This action cannot be undone.
        </p>
        {/* Red Warning Box */}
        <div className="bg-[#FFEFEF]  p-4 mb-6 relative overflow-hidden">
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500"></div>
            <div className="flex gap-3 pl-2">
              <IoClose className="text-red-500 text-xl flex-shrink-0 mt-2" />
              <p className="text-red-500 text-sm">
                All data will be deleted.
              </p>
            </div>
          </div>
        <p className="text-sm text-gray-700 mb-2">Type "confirm" to proceed:</p>
        <input
          type="text"
          className="w-full border p-2 text-gray-700 rounded-md mb-4 bg-white"
          value={confirmText}
          onChange={(e) => setConfirmText(e.target.value)}
        />
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
          >
            Cancel
          </button>
          <button
            disabled={confirmText !== 'confirm'}
            onClick={confirmText === 'confirm' ? onDelete : undefined}
            className={`px-4 py-2 rounded-md ${
              confirmText === 'confirm'
                ? 'bg-red-600 text-white'
                : 'bg-red-200 text-red-400 cursor-not-allowed'
            }`}
          >
            Delete event
          </button>
        </div>
      </div>
    </div>
  );
};




