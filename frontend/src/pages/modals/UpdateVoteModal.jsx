import React, { useState } from 'react';
import { IoClose } from 'react-icons/io5';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

export const UpdateVoteModal = ({ vote, onClose, onSave }) => {
  const [voteTitle, setVoteTitle] = useState(vote.voteTitle || "");
  const [totalFund, setTotalFund] = useState(vote.totalFund || 0);
  const [fundingDistribution, setFundingDistribution] = useState(vote.fundingDistribution || "total");
  const [numberOfWinningProjects, setNumberOfWinningProjects] = useState(vote.numberOfWinningProjects || 1);
  const [newStartDate, setNewStartDate] = useState(new Date(vote.startDate));
  const [newEndDate, setNewEndDate] = useState(new Date(vote.endDate));
  const today = new Date();

  const isPending = vote.status === "pending";
  const isMulti = vote.votingType === "multi";
  const isRanked = vote.votingType === "ranked";

  // Only allow editing dates if pending
  const canEditDates = isPending;

  // Only allow editing fundingDistribution/numberOfWinningProjects for multi
  // For active: only allow editing title, fund, and (for multi) fundingDistribution, numberOfWinningProjects

  const handleSave = () => {
    const payload = {
      voteTitle,
      totalFund: Number(totalFund),
    };
    if (isMulti) {
      payload.fundingDistribution = fundingDistribution;
      payload.numberOfWinningProjects = Number(numberOfWinningProjects);
    }
    if (canEditDates) {
      payload.startDate = newStartDate.toISOString();
      payload.endDate = newEndDate.toISOString();
    }
    onSave(payload);
  };

  const isSaveDisabled = () => {
    // Basic validation
    if (!voteTitle || totalFund < 0) return true;
    if (isMulti && (!numberOfWinningProjects || numberOfWinningProjects < 1)) return true;
    if (canEditDates) {
      if (!newStartDate || !newEndDate) return true;
      if (newEndDate <= newStartDate) return true;
      // 14 day max
      if ((newEndDate - newStartDate) > 14 * 24 * 60 * 60 * 1000) return true;
    }
    return false;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="font-semibold text-xl text-gray-800">Edit Vote Details</h2>
          <button 
            onClick={onClose} 
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close modal"
          >
            <IoClose className="text-2xl" />
          </button>
        </div>
        
        <form className="mb-6" onSubmit={e => { e.preventDefault(); handleSave(); }}>
          <div className="flex flex-col gap-4 mb-4">
            <div>
              <label className="block text-gray-600 mb-1">Vote Title:</label>
              <input
                type="text"
                value={voteTitle}
                onChange={e => setVoteTitle(e.target.value)}
                maxLength={100}
                className="w-full border p-2 rounded-md bg-white text-black"
                required
              />
            </div>
            <div>
              <label className="block text-gray-600 mb-1">Total Fund:</label>
              <input
                type="number"
                value={totalFund}
                onChange={e => setTotalFund(e.target.value)}
                min={0}
                className="w-full border p-2 rounded-md bg-white text-black"
                required
              />
            </div>
            {isMulti && (
              <>
                <div>
                  <label className="block text-gray-600 mb-1">Funding Distribution:</label>
                  <select
                    value={fundingDistribution}
                    onChange={e => setFundingDistribution(e.target.value)}
                    className="w-full border p-2 rounded-md bg-white text-black"
                  >
                    <option value="total">Total</option>
                    <option value="per_project">Per Project</option>
                  </select>
                </div>
                <div>
                  <label className="block text-gray-600 mb-1">Number of Winning Projects:</label>
                  <input
                    type="number"
                    value={numberOfWinningProjects}
                    onChange={e => setNumberOfWinningProjects(e.target.value)}
                    min={1}
                    className="w-full border p-2 rounded-md bg-white text-black"
                    required
                  />
                </div>
              </>
            )}
            {canEditDates && (
              <>
                <div>
                  <label htmlFor="newStartDate" className="block text-gray-600 mb-1">
                    Start date:
                  </label>
                  <DatePicker
                    id="newStartDate"
                    selected={newStartDate}
                    onChange={setNewStartDate}
                    minDate={today}
                    dateFormat="yyyy-MM-dd"
                    className="w-full border p-2 rounded-md bg-white text-black"
                    popperClassName="z-[99999]"
                    wrapperClassName="w-full"
                  />
                </div>
                <div>
                  <label htmlFor="newEndDate" className="block text-gray-600 mb-1">
                    End date:
                  </label>
                  <DatePicker
                    id="newEndDate"
                    selected={newEndDate}
                    onChange={setNewEndDate}
                    minDate={newStartDate}
                    dateFormat="yyyy-MM-dd"
                    className="w-full border p-2 rounded-md bg-white text-black"
                    popperClassName="z-[99999]"
                    wrapperClassName="w-full"
                  />
                </div>
              </>
            )}
          </div>
          <div className="flex justify-end gap-3 mt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSaveDisabled()}
              className={`px-4 py-2 rounded-md text-white transition-colors ${
                isSaveDisabled()
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-purple-600 hover:bg-purple-700'
              }`}
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};