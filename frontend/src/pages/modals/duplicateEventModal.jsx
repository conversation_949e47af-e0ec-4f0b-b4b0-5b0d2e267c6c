import React from 'react';
import { IoClose } from 'react-icons/io5';

export const DuplicateEventModal = ({ onClose, confirmText, setConfirmText, onDuplicate }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl mx-4">
        <h2 className="font-semibold text-xl text-gray-800 mb-2">Duplicate Event</h2>
        <p className="text-gray-700 text-sm mb-4">
          Are you sure you want to duplicate this <strong>Event</strong>? This will create a new event with the same details.
        </p>
        <div className="bg-[#FFFBEF] p-4 mb-6 relative overflow-hidden">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-yellow-500"></div>
          <div className="flex gap-3 pl-2">
            <IoClose className="text-yellow-500 text-xl flex-shrink-0 mt-2" />
            <p className="text-yellow-700 text-sm">
              You can edit the duplicated event after creation.
            </p>
          </div>
        </div>
        <p className="text-sm text-gray-700 mb-2">Type "confirm" to proceed:</p>
        <input
          type="text"
          className="w-full border p-2 text-gray-700 rounded-md mb-4 bg-white"
          value={confirmText}
          onChange={(e) => setConfirmText(e.target.value)}
        />
        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
          >
            Cancel
          </button>
          <button
            disabled={confirmText !== 'confirm'}
            onClick={confirmText === 'confirm' ? onDuplicate : undefined}
            className={`px-4 py-2 rounded-md ${
              confirmText === 'confirm'
                ? 'bg-yellow-600 text-white'
                : 'bg-yellow-200 text-yellow-400 cursor-not-allowed'
            }`}
          >
            Duplicate event
          </button>
        </div>
      </div>
    </div>
  );
};