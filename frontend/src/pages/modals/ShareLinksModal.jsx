import React, { useState, useEffect } from 'react';
import affiliateService from "../../services/affiliateService";
import affiliateLinkService from "../../services/affiliateLinkService";

// Import social media icons
import discordIcon from "../../assets/links/discord.png";
import facebookIcon from "../../assets/links/facebook.png";
import instagramIcon from "../../assets/links/instagram.png";
import linkedinIcon from "../../assets/links/linkedin.png";
import twitterIcon from "../../assets/links/twitter.png";

const ShareLinksModal = ({ isOpen, onClose, communityId = null, baseUrl = "http://localhost:5173/black_panther_dao"}) => {
  const [linkCopied, setLinkCopied] = useState(false);
  const [affiliateLink, setAffiliateLink] = useState("");
  const [loading, setLoading] = useState(false);
  const [regenerating, setRegenerating] = useState(false);
  const [error, setError] = useState("");
  const [hasAffiliateCode, setHasAffiliateCode] = useState(false);
  const [linkStatus, setLinkStatus] = useState(null); // To track if link is expired/full
  const [canRegenerate, setCanRegenerate] = useState(false);
  const [successMessage, setSuccessMessage] = useState(""); // Add success message state

  useEffect(() => {
    if (isOpen) {
      generateOrGetAffiliateLink();
    }
  }, [isOpen, communityId]);

  // Validate affiliate link status
  const validateLinkStatus = async (link) => {
    try {
      // Extract ref and token from the link
      const url = new URL(link);
      const ref = url.searchParams.get('ref');
      const token = url.searchParams.get('token');
      
      if (!ref || !token) return;
      
      const validation = await affiliateLinkService.validateAffiliateLink(ref, token);
      
      if (!validation.valid) {
        setLinkStatus({
          valid: false,
          message: validation.message,
          canRegenerate: validation.canRegenerate || false
        });
        setCanRegenerate(validation.canRegenerate || false);
      } else {
        setLinkStatus({
          valid: true,
          data: validation.data
        });
        setCanRegenerate(false);
      }
    } catch (error) {
      console.error('Error validating link:', error);
      // Check if error response indicates we can regenerate
      if (error.response?.data?.canRegenerate) {
        setLinkStatus({
          valid: false,
          message: error.message || 'Link validation failed',
          canRegenerate: true
        });
        setCanRegenerate(true);
      }
    }
  };

  const generateOrGetAffiliateLink = async () => {
    try {
      setLoading(true);
      setError("");
      setLinkStatus(null);
      setCanRegenerate(false);
      
      let response;
      if (communityId) {
        // Generate community affiliate code
        response = await affiliateService.generateCommunityAffiliateCode(communityId);
        setAffiliateLink(response.affiliateLink);
      } else {
        // Generate individual affiliate code
        response = await affiliateService.generateAffiliateCode();
        setAffiliateLink(response.affiliateLink);
      }
      
      setHasAffiliateCode(true);
      
      // Validate the generated link
      await validateLinkStatus(response.affiliateLink);
      
    } catch (err) {
      if (err.response?.status === 400 && err.response?.data?.message?.includes("already has")) {
        // User already has a code, try to get existing info
        try {
          if (communityId) {
            const info = await affiliateService.getCommunityAffiliateInfo(communityId);
            setAffiliateLink(info.affiliateLink);
            await validateLinkStatus(info.affiliateLink);
          } else {
            const info = await affiliateService.getUserAffiliateInfo();
            setAffiliateLink(info.affiliateLink);
            await validateLinkStatus(info.affiliateLink);
          }
          setHasAffiliateCode(true);
        } catch (infoErr) {
          setError("Failed to get affiliate information");
        }
      } else {
        setError(err.response?.data?.message || "Failed to generate affiliate link");
      }
    } finally {
      setLoading(false);
    }
  };

  const regenerateAffiliateLink = async () => {
    try {
      setRegenerating(true);
      setError("");
      setSuccessMessage("");
      setLinkStatus(null);
      
      let response;
      if (communityId) {
        response = await affiliateService.regenerateCommunityAffiliateCode(communityId);
      } else {
        response = await affiliateService.regenerateAffiliateCode();
      }
      
      console.log('🔄 Regenerate response:', response);
      
      // Handle the response structure from your backend
      const newAffiliateLink = response.data?.affiliateLink || response.affiliateLink;
      
      if (newAffiliateLink) {
        setAffiliateLink(newAffiliateLink);
        setCanRegenerate(false);
        
        // Show success message
        setSuccessMessage("✅ New affiliate link generated successfully!");
        setTimeout(() => setSuccessMessage(""), 5000); // Clear after 5 seconds
        
        // Reset link status to show success
        setLinkStatus({
          valid: true,
          data: {
            spotsRemaining: response.data?.maxUsers || response.maxUsers || 50,
            usersSignedUp: response.data?.currentUsers || response.currentUsers || 0,
            maxUsers: response.data?.maxUsers || response.maxUsers || 50
          }
        });
        
        // Optionally validate the new link after a short delay
        setTimeout(async () => {
          await validateLinkStatus(newAffiliateLink);
        }, 1000);
      } else {
        throw new Error('No affiliate link received from server');
      }
      
    } catch (err) {
      console.error('❌ Regenerate error:', err);
      setError(err.response?.data?.message || err.message || "Failed to regenerate affiliate link");
    } finally {
      setRegenerating(false);
    }
  };

  if (!isOpen) return null;

  const shareText = communityId 
    ? "Hey! Check out this amazing DAO community I'm part of. Join us and let's build something great together!" 
    : "Hey! Check out Black Panther Token! Join me in this exciting crypto community!";
  
  const currentUrl = affiliateLink || (communityId 
    ? `${baseUrl}/dao/${communityId}/join` 
    : `${baseUrl}`);
  
  const copyToClipboard = async () => {
    try {
      const shareContent = `${shareText}\n\n${currentUrl}`;
      await navigator.clipboard.writeText(shareContent);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = `${shareText}\n\n${currentUrl}`;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setLinkCopied(true);
        setTimeout(() => setLinkCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy failed: ', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  const shareToFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}&quote=${encodeURIComponent(shareText)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
  };

  const shareToTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(currentUrl)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
  };

  const shareToLinkedIn = () => {
    // LinkedIn's newer sharing API approach
    const linkedInUrl = `https://www.linkedin.com/feed/?shareActive=true&text=${encodeURIComponent(shareText + '\n\n' + currentUrl)}`;
    window.open(linkedInUrl, '_blank', 'width=600,height=400');
  };

  const shareToInstagram = () => {
    // Instagram doesn't have a direct URL sharing API, so we copy both text and link to clipboard and redirect
    const shareContent = `${shareText}\n\n${currentUrl}`;
    navigator.clipboard.writeText(shareContent).then(() => {
      // Redirect to Instagram after copying
      window.open('https://www.instagram.com/', '_blank');
    }).catch(() => {
      copyToClipboard();
      window.open('https://www.instagram.com/', '_blank');
    });
  };

  const shareToDiscord = () => {
    // Discord doesn't have a direct web sharing API, but we can copy content and redirect
    const shareContent = `${shareText}\n${currentUrl}`;
    navigator.clipboard.writeText(shareContent).then(() => {
      // Redirect to Discord after copying
      window.open('https://discord.com/', '_blank');
    }).catch(() => {
      copyToClipboard();
      window.open('https://discord.com/', '_blank');
    });
  };

  const shareToTelegram = () => {
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(shareText)}`;
    window.open(telegramUrl, '_blank', 'width=600,height=400');
  };

  const shareToTikTok = () => {
    // TikTok doesn't have a direct URL sharing API, so we copy both text and link to clipboard and redirect
    const shareContent = `${shareText}\n\n${currentUrl}`;
    navigator.clipboard.writeText(shareContent).then(() => {
      // Redirect to TikTok after copying
      window.open('https://www.tiktok.com/', '_blank');
    }).catch(() => {
      copyToClipboard();
      window.open('https://www.tiktok.com/', '_blank');
    });
  };

  const shareToYouTube = () => {
    // YouTube doesn't have a direct sharing API for external links, so we copy both text and link and redirect
    const shareContent = `${shareText}\n\n${currentUrl}`;
    navigator.clipboard.writeText(shareContent).then(() => {
      // Redirect to YouTube after copying
      window.open('https://www.youtube.com/', '_blank');
    }).catch(() => {
      copyToClipboard();
      window.open('https://www.youtube.com/', '_blank');
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-3xl p-8 shadow-2xl w-full max-w-2xl mx-4 relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Title */}
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-800 mb-2">
            Share {communityId ? 'Community' : 'Referral'} Link
          </h3>
          <p className="text-gray-600 text-sm">
            {communityId 
              ? "Share this community and earn rewards when people join!" 
              : "Invite friends and earn affiliate rewards!"
            }
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Generating your affiliate link...
            </div>
          </div>
        )}

        {/* Regenerating State */}
        {regenerating && (
          <div className="text-center mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-green-50 text-green-600 rounded-lg">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Regenerating your affiliate link...
            </div>
          </div>
        )}

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-green-700 text-sm font-medium">{successMessage}</span>
            </div>
          </div>
        )}

        {/* Link Status Warning */}
        {linkStatus && !linkStatus.valid && (
          <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <span className="text-orange-700 text-sm font-medium">Link Status Issue</span>
                  <p className="text-orange-600 text-sm mt-1">{linkStatus.message}</p>
                </div>
              </div>
              {!regenerating && (linkStatus && !linkStatus.valid && canRegenerate)&& (
                <button
                  onClick={regenerateAffiliateLink}
                  disabled={regenerating}
                  className="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 text-white text-sm font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-4 focus:ring-emerald-300 active:scale-95"
                  title="Generate a fresh new link to replace the expired/full one"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                  </svg>
                  {regenerating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Regenerating...
                    </>
                  ) : (
                    'Regenerate Link'
                  )}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Link Success Status */}
        {linkStatus && linkStatus.valid && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <span className="text-green-700 text-sm font-medium">Link Active</span>
                <p className="text-green-600 text-sm mt-1">
                  {linkStatus.data.spotsRemaining} spots remaining ({linkStatus.data.usersSignedUp}/{linkStatus.data.maxUsers} used)
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-700 text-sm">{error}</span>
            </div>
            <button
              onClick={generateOrGetAffiliateLink}
              className="mt-3 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        )}

        {/* Share your link section */}
        {hasAffiliateCode && !loading && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-3">
              <label className="block text-gray-700 text-sm font-medium">
                Your {communityId ? 'community' : 'referral'} link
              </label>
              
            </div>
            
            {/* Link Display */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
              <p className="text-sm text-gray-600 break-all">
                {currentUrl}
              </p>
            </div>
            
            {/* Copy Link Button */}
            <button
              onClick={copyToClipboard}
              disabled={!currentUrl || regenerating}
              className={`w-full py-4 px-6 rounded-2xl transition-all duration-200 flex items-center justify-center gap-3 ${
                linkCopied 
                  ? 'bg-white border-2 border-purple-500 text-purple-600 shadow-lg' 
                  : 'bg-gray-50 hover:bg-gray-100 text-purple-600 border border-gray-200 shadow-md disabled:opacity-50 disabled:cursor-not-allowed'
              }`}
            >
              {linkCopied ? (
                <>
                  <svg className="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium">Link Copied</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <span className="font-medium">Copy Link</span>
                </>
              )}
            </button>
          </div>
        )}

        {/* Share to section */}
        {hasAffiliateCode && !loading && (
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-medium mb-4">
              Share to social media
            </label>
            
            {/* Social Media Buttons */}
            <div className="flex flex-wrap justify-center gap-4">
            {/* Twitter/X */}
            <button
              onClick={shareToTwitter}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors shadow-md"
              title="Twitter"
            >
              <img src={twitterIcon} alt="Twitter" className="w-6 h-6" />
            </button>

            {/* Discord */}
            <button
              onClick={shareToDiscord}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors shadow-md"
              title="Discord"
            >
              <img src={discordIcon} alt="Discord" className="w-6 h-6" />
            </button>

            {/* Facebook */}
            <button
              onClick={shareToFacebook}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors shadow-md"
              title="Facebook"
            >
              <img src={facebookIcon} alt="Facebook" className="w-6 h-6" />
            </button>

            {/* Instagram */}
            <button
              onClick={shareToInstagram}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors shadow-md"
              title="Instagram"
            >
              <img src={instagramIcon} alt="Instagram" className="w-6 h-6" />
            </button>

            {/* LinkedIn */}
            <button
              onClick={shareToLinkedIn}
              className="flex items-center justify-center w-16 h-16 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors shadow-md"
              title="LinkedIn"
            >
              <img src={linkedinIcon} alt="LinkedIn" className="w-6 h-6" />
            </button>

            {/* Telegram - Using text fallback since icon not available */}
            {/* <button
              onClick={shareToTelegram}
              className="flex items-center justify-center w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full transition-colors shadow-md text-white font-bold text-sm"
              title="Telegram"
            >
              TG
            </button> */}

            {/* TikTok - Using text fallback since icon not available */}
            {/* <button
              onClick={shareToTikTok}
              className="flex items-center justify-center w-12 h-12 bg-black hover:bg-gray-800 rounded-full transition-colors shadow-md text-white font-bold text-sm"
              title="TikTok"
            >
              TT
            </button> */}

            {/* YouTube - Using text fallback since icon not available */}
            {/* <button
              onClick={shareToYouTube}
              className="flex items-center justify-center w-12 h-12 bg-red-500 hover:bg-red-600 rounded-full transition-colors shadow-md text-white font-bold text-sm"
              title="YouTube"
            >
              YT
            </button> */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShareLinksModal;
