import React, { useRef } from 'react';
import { IoClose } from 'react-icons/io5';
import { FaSearch } from "react-icons/fa";
import { IoNotifications } from "react-icons/io5";

export const PendingContribution = ({ onClose }) => {
  const fromRef = useRef(null);
  const toRef = useRef(null);

  // Example data for pending contributions
  const members = [
    {
      name: "<PERSON> Mugao",
      role: "Admin",
      compliance: "90%",
      last: "10/10/2024",
      actions: "View History"
    },
    {
      name: "Divine Jicks",
      role: "Admin",
      compliance: "70%",
      last: "1/3/2025",
      actions: "View History"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Member",
      compliance: "50%",
      last: "1/8/2020",
      actions: "View History"
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 rounded-2xl flex items-center justify-center z-50 p-4">
      <div className="bg-white p-8 rounded-2xl shadow-2xl w-full max-w-2xl sm:max-w-2xl md:max-w-5xl transition-all min-h-[600px] md:min-h-[700px] flex flex-col gap-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-3">
          <h2 className="font-karla font-semibold text-[36px] leading-[100%] tracking-[0] text-[#2c5282]">
            Pending Contributions
          </h2>
          <button
            className="text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <IoClose className="w-6 h-6" />
          </button>
        </div>

        <div className="w-full flex flex-col gap-2 sm:gap-0">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 mb-2">
            {/* Search Bar */}
            <div className="flex items-center w-full lg:w-[391px] h-[58px] bg-white border border-gray-300 rounded-[12px] px-4">
              <FaSearch className="text-gray-400 w-5 h-5 mr-3" />
              <input
                type="text"
                placeholder="Search"
                className="flex-1 bg-transparent outline-none font-karla text-lg text-[#000000] placeholder-gray-400"
                style={{ height: "40px" }}
              />
            </div>
            {/* Send Reminder Button */}
            <button
              className="flex items-center justify-center gap-3 bg-[#f5c21a] text-white w-full lg:w-[314px] h-[56px] rounded-[20px] font-poppins font-semibold text-[20px] transition hover:opacity-90 mt-2 lg:mt-0"
              style={{ minWidth: "0" }}
            >
              <IoNotifications className="w-6 h-6  text-white" />
              Send Reminder
            </button>
          </div>
        </div>

        <div
          className="overflow-x-auto md:overflow-x-visible rounded-xl max-w-6xl w-full"
          style={{ boxShadow: "0px 4px 24px 0px rgba(0,0,0,0.10)" }}
        >
          <table className="min-w-[600px] md:min-w-full text-left border-collapse bg-white overflow-hidden">
            <thead className="bg-gray-100">
              <tr className="text-gray-700 text-sm font-semibold">
                <th className="px-4 py-3">Name</th>
                <th className="px-4 py-3">Role</th>
                <th className="px-4 py-3">Compliance rate</th>
                <th className="px-4 py-3">Last Contribution</th>
                <th className="px-4 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {members.map((item, idx) => (
                <tr key={idx} className="border-t text-sm text-gray-800">
                  <td className="px-6 py-6">{item.name}</td>
                  <td className="px-4 py-6">{item.role}</td>
                  <td className="px-4 py-6">{item.compliance}</td>
                  <td className="px-4 py-6">{item.last}</td>
                  <td className="px-4 py-6">
                    <button className="text-yellow-500 decoration-2 hover:opacity-80">
                      {item.actions}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};