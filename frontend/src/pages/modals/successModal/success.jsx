import React, { useEffect } from 'react';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import './success.css'; // Import CSS for z-index styling

const MySwal = withReactContent(Swal);

const SuccessAlert = ({ message,onClose }) => {
  useEffect(() => {
    if(message){
    MySwal.fire({
      icon: 'success',
      title: 'Success!',
      text: message,
      confirmButtonText:'OK',
      customClass: { 
        confirmButton:"w-28 h-10 px-5 py-2 rounded-full bg-[#1B454B] hover:bg-white hover:text-enk-darkgreen hover:border-enk-shadegreen flex justify-center items-center text-white text-sm font-medium font-['Inter'] transition-all duration-300 ease-in-out",
        popup: 'swal-highest-zindex'
      },
      didOpen: () => {
        const icon = document.querySelector('.swal2-success .swal2-success-ring');
        const lineTip = document.querySelector('.swal2-success .swal2-success-line-tip');
        const lineLong = document.querySelector('.swal2-success .swal2-success-line-long');
        
        if (icon && lineTip && lineLong) {
          icon.style.borderColor = '#1B454B';
          lineTip.style.backgroundColor = '#1B454B';
          lineLong.style.backgroundColor = '#1B454B';
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        onClose();
      }
    });
  }
  }, [message, onClose]);

  return null;
};

export default SuccessAlert;
