import React, { useState } from 'react';
import { FiCheckCircle, FiCopy, FiExternalLink, FiX, FiCheck } from 'react-icons/fi';
import { LuDollarSign } from "react-icons/lu";
import { IoCallOutline } from "react-icons/io5";

const PaymentSuccessModal = ({ 
    isOpen, 
    onClose, 
    paymentData = {},
    walletAddress = '',
    transactionHash=''
}) => {
    if (!isOpen) return null;

    const [copiedWallet, setCopiedWallet] = useState(false);
    const [copiedTransaction, setCopiedTransaction] = useState(false);

    const {
        amount = '0',
        finalAmount = null,
        bnbAmount = '0',
        phoneNumber = '',
        transactionId = '',
        rate = 500, // This will now come from the dynamic API call
        fees = null,
        // New currency conversion data
        currency = null,
        localCurrency = null,
        country = '',
        chargeAmount = null,
        chargeCurrency = 'USD'
    } = paymentData;

    const copyToClipboard = (text, type = 'general') => {
        navigator.clipboard.writeText(text).then(() => {
            // Set the appropriate copied state
            if (type === 'wallet') {
                setCopiedWallet(true);
                setTimeout(() => setCopiedWallet(false), 2000); // Reset after 2 seconds
            } else if (type === 'transaction') {
                setCopiedTransaction(true);
                setTimeout(() => setCopiedTransaction(false), 2000); // Reset after 2 seconds
            }
        }).catch(err => {
            console.error('Failed to copy:', err);
        });
    };

    const handleViewOnBlockchain = () => {
        const environment = import.meta.env.VITE_ENVIRONMENT;
        if(environment === 'Production') {
            window.open(`https://bscscan.com/tx/${transactionHash}`, '_blank');
        } else {
            window.open(`https://testnet.bscscan.com/tx/${transactionHash}`, '_blank');
        }
    };

    // Custom animations
    const modalStyles = `
        @keyframes successFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes successSlideUp {
            from { 
                opacity: 0; 
                transform: translateY(20px) scale(0.95); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }
        @keyframes checkAnimation {
            0% { transform: scale(0) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }
        @keyframes celebrationPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .animate-success-fadeIn {
            animation: successFadeIn 0.3s ease-out;
        }
        .animate-success-slideUp {
            animation: successSlideUp 0.4s ease-out;
        }
        .animate-check {
            animation: checkAnimation 0.6s ease-out;
        }
        .animate-celebration {
            animation: celebrationPulse 2s ease-in-out infinite;
        }
        @keyframes copySuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        .animate-copy-success {
            animation: copySuccess 0.3s ease-out;
        }
    `;

    return (
        <>
            <style>{modalStyles}</style>
            <div 
                className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 animate-success-fadeIn p-2 sm:p-4"
                onClick={onClose}
            >
                <div 
                    className="bg-slate-800 border border-slate-600 rounded-xl sm:rounded-2xl max-w-sm sm:max-w-md w-full mx-2 sm:mx-4 shadow-2xl transform animate-success-slideUp overflow-hidden max-h-[95vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                >
                    {/* Header with close button */}
                    <div className="relative bg-gradient-to-r from-green-500/10 to-teal-500/10 p-4 sm:p-6 border-b border-slate-600">
                        <button
                            onClick={onClose}
                            className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-white transition-colors"
                        >
                            <FiX className="w-5 h-5 sm:w-6 sm:h-6" />
                        </button>
                        
                        {/* Success icon */}
                        <div className="flex justify-center mb-3 sm:mb-4">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-500/20 rounded-full flex items-center justify-center animate-celebration">
                                <FiCheckCircle className="w-10 h-10 sm:w-12 sm:h-12 text-green-400 animate-check" />
                            </div>
                        </div>
                        
                        <div className="text-center">
                            <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Payment Successful! 🎉</h2>
                            <p className="text-gray-300 text-sm sm:text-base">
                                Your BNB will be sent to your wallet within 5 minutes
                                {currency?.converted && fees?.finalAmountLocal && (
                                    <span className="block mt-1">
                                        Paid: {currency.converted.symbol}{fees.finalAmountLocal.toFixed(2)} ({currency.converted.currency})
                                    </span>
                                )}
                            </p>
                        </div>
                    </div>

                    {/* Payment details */}
                    <div className="p-4 sm:p-6 space-y-3 sm:space-y-4">
                        {/* Local Currency Amount Display (if available) */}
                        {currency?.converted && fees?.finalAmountLocal && (
                            <div className="bg-slate-700/30 border border-slate-600 rounded-lg p-3 sm:p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <span className="text-gray-400 text-sm">
                                            Amount in {currency.converted.currency}:
                                        </span>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-teal-400 font-semibold text-xl">
                                            {currency.converted.symbol}{fees.finalAmountLocal.toFixed(2)}
                                        </div>
                                        {currency.exchangeRate && (
                                            <div className="text-gray-500 text-xs">
                                                Rate: 1 USD = {currency.exchangeRate.rate.toFixed(4)} {currency.exchangeRate.toCurrency}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Amount paid */}
                        <div className="bg-slate-700/40 rounded-lg p-3 sm:p-4">
                            <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3">
                                <LuDollarSign className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
                                <span className="text-xs sm:text-sm font-medium text-gray-300">Payment Summary</span>
                            </div>
                            
                            {/* BNB received */}
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-lg sm:text-xl font-bold text-white">BNB Received:</span>
                                <span className="text-lg sm:text-xl text-green-400 font-semibold">{bnbAmount} BNB</span>
                            </div>
                            
                            {/* Amount breakdown */}
                            <div className="space-y-1 text-sm">
                                <div className="flex justify-between text-gray-300">
                                    <span>BNB Value:</span>
                                    <div className="text-right">
                                        {currency?.converted ? (
                                            <>
                                                <span>{currency.converted.formatted}</span>
                                                <div className="text-xs text-gray-400">
                                                    ≈ ${amount}
                                                </div>
                                            </>
                                        ) : (
                                            <span>${amount}</span>
                                        )}
                                    </div>
                                </div>
                                
                                {fees && (
                                    <>
                                        <div className="flex justify-between text-gray-300">
                                            <span>Transaction Fee:</span>
                                            <div className="text-right">
                                                {currency?.converted && fees.transactionFeeLocal ? (
                                                    <>
                                                        <span>{currency.converted.symbol}{fees.transactionFeeLocal.toFixed(2)}</span>
                                                        <div className="text-xs text-gray-400">
                                                            ≈ ${fees.transactionFee?.toFixed(2) || '0.00'}
                                                        </div>
                                                    </>
                                                ) : (
                                                    <span>${fees.transactionFee?.toFixed(2) || '0.00'}</span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="border-t border-slate-600 my-1 pt-1">
                                            <div className="flex justify-between font-semibold text-white">
                                                <span>Total Paid:</span>
                                                <div className="text-right">
                                                    {currency?.converted && fees.finalAmountLocal ? (
                                                        <>
                                                            <span>{currency.converted.symbol}{fees.finalAmountLocal.toFixed(2)}</span>
                                                            <div className="text-sm text-gray-400">
                                                                ≈ ${finalAmount?.toFixed(2) || (parseFloat(amount) + (fees.transactionFee || 0)).toFixed(2)}
                                                            </div>
                                                        </>
                                                    ) : (
                                                        <span>${finalAmount?.toFixed(2) || amount}</span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                )}
                                
                                <div className="text-xs text-gray-400 mt-2 pt-2 border-t border-slate-600">
                                    <div>Rate: 1 BNB = ${typeof rate === 'number' ? rate.toLocaleString() : rate}</div>
                                    {currency?.exchangeRate && (
                                        <div className="mt-1">
                                            Exchange Rate: 1 USD = {currency.exchangeRate.rate.toFixed(4)} {currency.exchangeRate.toCurrency}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Phone number used */}
                        {phoneNumber && (
                            <div className="flex items-center justify-between p-2 sm:p-3 bg-slate-700/20 rounded-lg">
                                <div className="flex items-center gap-2 sm:gap-3">
                                    <IoCallOutline className="w-4 h-4 sm:w-5 sm:h-5 text-teal-400" />
                                    <div>
                                        <div className="text-xs sm:text-sm text-gray-400">Phone Number</div>
                                        <div className="text-sm sm:text-base text-white font-medium break-all">{phoneNumber}</div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Wallet address */}
                        <div className="p-2 sm:p-3 bg-slate-700/20 rounded-lg">
                            <div className="flex items-center gap-2 sm:gap-3 mb-2">
                                <div className="w-4 h-4 sm:w-5 sm:h-5 bg-teal-400 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-bold text-black">W</span>
                                </div>
                                <span className="text-xs sm:text-sm text-gray-400">Receiving Wallet</span>
                            </div>
                            <div className="flex items-center justify-between gap-2">
                                <span className="text-white font-mono text-xs sm:text-sm break-all">
                                    {walletAddress.slice(0, 6)}...{walletAddress.slice(-6)}
                                </span>
                                <button
                                    onClick={() => copyToClipboard(walletAddress, 'wallet')}
                                    className={`transition-colors flex-shrink-0 ${copiedWallet ? 'text-green-400 animate-copy-success' : 'text-teal-400 hover:text-teal-300'}`}
                                    title={copiedWallet ? "Copied!" : "Copy wallet address"}
                                >
                                    {copiedWallet ? (
                                        <FiCheck className="w-3 h-3 sm:w-4 sm:h-4" />
                                    ) : (
                                        <FiCopy className="w-3 h-3 sm:w-4 sm:h-4" />
                                    )}
                                </button>
                            </div>
                        </div>

                        {/* Transaction ID if available */}
                        {transactionId && (
                            <div className="p-2 sm:p-3 bg-slate-700/20 rounded-lg">
                                <div className="flex items-center justify-between gap-2">
                                    <div className="flex-1 min-w-0">
                                        <div className="text-xs sm:text-sm text-gray-400">Transaction ID</div>
                                        <div className="text-white font-mono text-xs sm:text-sm break-all">
                                            {transactionId.length > 20 ? 
                                                `${transactionId.slice(0, 8)}...${transactionId.slice(-8)}` : 
                                                transactionId
                                            }
                                        </div>
                                    </div>
                                    <button
                                        onClick={() => copyToClipboard(transactionId, 'transaction')}
                                        className={`transition-colors flex-shrink-0 ${copiedTransaction ? 'text-green-400 animate-copy-success' : 'text-teal-400 hover:text-teal-300'}`}
                                        title={copiedTransaction ? "Copied!" : "Copy transaction ID"}
                                    >
                                        {copiedTransaction ? (
                                            <FiCheck className="w-3 h-3 sm:w-4 sm:h-4" />
                                        ) : (
                                            <FiCopy className="w-3 h-3 sm:w-4 sm:h-4" />
                                        )}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Action buttons */}
                        <div className="space-y-2 sm:space-y-3 pt-3 sm:pt-4">
                            <button
                                onClick={handleViewOnBlockchain}
                                className="w-full bg-teal-500/20 hover:bg-teal-500/30 border border-teal-500/50 text-teal-400 font-semibold py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm sm:text-base"
                            >
                                <FiExternalLink className="w-3 h-3 sm:w-4 sm:h-4" />
                                View on Blockchain
                            </button>
                            
                            <button
                                onClick={onClose}
                                className="w-full bg-slate-600 hover:bg-slate-500 text-white font-semibold py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg transition-colors text-sm sm:text-base"
                            >
                                Go Back
                            </button>
                        </div>

                        {/* Important note */}
                        <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                            <div className="flex items-start gap-2 sm:gap-3">
                                <div className="w-4 h-4 sm:w-5 sm:h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-xs font-bold text-white">i</span>
                                </div>
                                <div>
                                    <h4 className="text-blue-400 font-semibold text-xs sm:text-sm mb-1">What happens next?</h4>
                                    <p className="text-blue-300 text-xs sm:text-sm">
                                        Your BNB tokens will appear in your wallet within 5 minutes. You'll receive a notification once the transfer is complete.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default PaymentSuccessModal;
