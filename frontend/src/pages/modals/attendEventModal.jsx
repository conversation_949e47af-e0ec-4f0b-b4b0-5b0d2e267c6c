import React from 'react';
import { IoClose } from 'react-icons/io5';

export const AttendEventModal = ({
  onClose,
  email,
  setEmail,
  name,
  setName,
  onSubmit,
  loading
}) => (
  <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
    <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md mx-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="font-semibold text-xl text-gray-800">Attend Event</h2>
        <button onClick={onClose}><IoClose className="text-2xl text-gray-500" /></button>
      </div>
      <input
        className="w-full border p-2 mb-4 rounded bg-white text-black"
        placeholder="Your Name"
        value={name}
        onChange={e => setName(e.target.value)}
        type="text"
      />
      <input
        className="w-full border p-2 mb-4 rounded bg-white text-black"
        placeholder="Your Email"
        value={email}
        onChange={e => setEmail(e.target.value)}
        type="email"
      />
      <div className="flex justify-end gap-3">
        <button
          onClick={onClose}
          className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
        >
          Cancel
        </button>
        <button
          onClick={onSubmit}
          disabled={!name.trim() || !email.trim() || loading}
          className={`px-4 py-2 rounded-md bg-[#9333EA] text-white ${(!name.trim() || !email.trim() || loading) ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {loading ? 'Submitting...' : 'Attend'}
        </button>
      </div>
    </div>
  </div>
);