import React, { useEffect } from 'react';
import { HiOutlineCog6Tooth } from 'react-icons/hi2';

const ComingSoonModal = ({ isOpen, onClose }) => {
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    // Modal styles for animations
    const modalStyles = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        .animate-fadeIn {
            animation: fadeIn 0.2s ease-out;
        }
        .animate-scaleIn {
            animation: scaleIn 0.2s ease-out;
        }
    `;

    return (
        <>
            <style>{modalStyles}</style>
            <div 
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn p-2 sm:p-4"
                onClick={onClose}
            >
                <div 
                    className="bg-slate-800 border border-slate-600 rounded-lg sm:rounded-xl p-4 sm:p-6 max-w-xs sm:max-w-sm mx-2 sm:mx-4 shadow-2xl transform animate-scaleIn"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="text-center">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                            <HiOutlineCog6Tooth className="w-5 h-5 sm:w-6 sm:h-6 text-black animate-spin" style={{animation: 'spin 3s linear infinite'}} />
                        </div>
                        
                        <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">Coming Soon</h3>
                        
                        <div className="bg-slate-700/50 rounded-lg p-2.5 sm:p-3 mb-3 sm:mb-4">
                            <p className="text-gray-300 mb-2 leading-relaxed text-xs sm:text-sm">
                                Lines of code. Endless ideas. One mission: to bring you something extraordinary.
                            </p>
                            
                            <p className="text-yellow-400 font-bold text-sm sm:text-base">
                                Coming soon.
                            </p>
                        </div>
                        
                        <button
                            onClick={onClose}
                            className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-semibold py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg transition-all duration-200 shadow-lg transform hover:scale-105 text-sm sm:text-base"
                        >
                            Got it
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default ComingSoonModal;
