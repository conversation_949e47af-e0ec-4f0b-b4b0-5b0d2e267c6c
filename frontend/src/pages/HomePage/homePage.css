@keyframes marquee {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
  }
  
  .marquee {
    animation: marquee 30s linear infinite;
  }
  /* @media only screen and (min-width: 390px) {
    
    #animated {
      margin-top: 18em;
    }
  } */

  /* @keyframes move {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0);
    }
  }
  
  .animate-move {
    animation: move 2s ease-in-out infinite;
  } */
  

  

  