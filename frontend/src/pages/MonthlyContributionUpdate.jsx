import { useState, useEffect } from 'react';
import WebLogo from '../assets/newBPlogo.png';
import donation from '../assets/images/donation2.png';
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import { AppRoutesPaths } from '../route/app_route';
import { useAuthentication } from '../components/utils/provider';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';

import SuccessAlert from "../pages/modals/successModal/success";
import FailedAlert from "../pages/modals/failed";

import { motion } from 'framer-motion';
import { FiDollarSign, FiCreditCard, FiRepeat} from 'react-icons/fi';
import { ContributionService } from '../services/ContributionService';
import { Tooltip } from 'antd';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

// Payment Form Component
const PaymentForm = ({ amount, onSuccess, subscriptionId, onError }) => {
    const stripe = useStripe();
    const elements = useElements();
    const [errorMessage, setErrorMessage] = useState(null);
    const [processing, setProcessing] = useState(false);
    const [isStripeLoading, setIsStripeLoading] = useState(true);
    const navigate = useNavigate();

    // Check if Stripe is ready
    useEffect(() => {
        const checkStripeReady = async () => {
            try {
                if (stripe && elements) {
                    // Wait for elements to be fully loaded
                    await elements.fetchUpdates();
                    setIsStripeLoading(false);
                }
            } catch (error) {
                console.error('Error loading Stripe:', error);
                setErrorMessage('Error loading payment form. Please try again.');
            }
        };

        checkStripeReady();
    }, [stripe, elements]);

    const handleSubmit = async (event) => {
        event.preventDefault();
        if (processing || !stripe || !elements) return;

        setProcessing(true);
        setErrorMessage(null);

        try {
            const { error: submitError, setupIntent } = await stripe.confirmSetup({
                elements,
                confirmParams: {
                    return_url: `${window.location.origin}/monthly-contribution-update?status=success&subscriptionId=${subscriptionId}&amount=${amount}`,
                },
            });

            if (submitError) {
                setErrorMessage(submitError.message);
                onError(submitError);
            } else if (setupIntent) {
                localStorage.setItem('stripe_payment_ref', setupIntent.id);
                localStorage.setItem('stripe_payment_amount', amount); // Store amount in localStorage
                onSuccess(setupIntent.id);
            }
        } catch (error) {
            setErrorMessage(error.message);
            onError(error);
        } finally {
            setProcessing(false);
        }
    };

    if (isStripeLoading) {
        return (
            <div className="w-full flex flex-col items-center justify-center py-8 space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
                <p className="text-gray-600 font-medium">Loading payment form...</p>
            </div>
        );
    }

    return (
        <form onSubmit={handleSubmit} className="w-full space-y-4">
            <div className="relative">
                <PaymentElement />
                {errorMessage && (
                    <div className="text-red-500 text-sm mt-2">{errorMessage}</div>
                )}
            </div>
            <button
                type="submit"
                disabled={!stripe || processing}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${processing
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                    }`}
            >
                {processing ? (
                    <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Processing...
                    </div>
                ) : (
                    'Pay Now'
                )}
            </button>
        </form>
    );
};

export const MonthlyContributionUpdate = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [paymentMethod, setPaymentMethod] = useState('fiat');
    const [amountOption, setAmountOption] = useState('10');
    const [customAmount, setCustomAmount] = useState('');
    const [convertedAmount, setConvertedAmount] = useState('');
    const [walletAddress, setWalletAddress] = useState('');
    const [isWalletConnected, setIsWalletConnected] = useState(false);
    const [clientSecret, setClientSecret] = useState(null);
    const [errorMessage, setErrorMessage] = useState('');
    const [subscriptionId, setSubscriptionId] = useState(null);
    const { currentUser, currentUserWallet} = useAuthentication();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [paymentReference, setPaymentReference] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalType, setModalType] = useState(null); // 'success' or 'failed'
    const [todayDate, setTodayDate] = useState('');
    const [today, setToday] = useState('');
    const [showSuccessCard, setShowSuccessCard] = useState(false);
    const [amountError, setAmountError] = useState('');
    const [currentContribution, setCurrentContribution] = useState(null);
    const [loadingContribution, setLoadingContribution] = useState(true);
    const [processedAmount, setProcessedAmount] = useState(null); // Store the actual processed amount

    // Check URL parameters on component mount
    useEffect(() => {
        const status = searchParams.get('status');
        const allParams = {};
        for (let [key, value] of searchParams.entries()) {
            allParams[key] = value;
        }
        console.log('All URL parameters:', allParams);
        console.log('URL status:', status, 'showSuccessCard:', showSuccessCard);
        
        if (status === 'success' && !showSuccessCard) {
            const storedSetupIntent = searchParams.get('setup_intent') || localStorage.getItem('stripe_payment_ref');
            const paramSubscriptionId = searchParams.get('subscriptionId');
            const urlAmount = searchParams.get('amount');
            const storedAmount = localStorage.getItem('stripe_payment_amount');
            
            
            // Set the processed amount from URL or localStorage
            let amountToUse = null;
            if (urlAmount) {
                setProcessedAmount(urlAmount);
                amountToUse = urlAmount;
            } else if (storedAmount) {
                setProcessedAmount(storedAmount);
                amountToUse = storedAmount;
            } else {
                console.log('No amount found in URL or localStorage, keeping current processedAmount:', processedAmount);
            }

            if (paymentMethod === 'fiat' && storedSetupIntent && currentUser?._id) {
                setSubscriptionId(paramSubscriptionId);
                setPaymentReference(storedSetupIntent);
                // Pass the amount directly to avoid race condition
                activateContribution('stripe', paramSubscriptionId, storedSetupIntent, amountToUse);
            }
        } else if (status === 'failed' && !isModalOpen) {
            const error = searchParams.get('error');
            setErrorMessage(error || 'Payment failed. Please try again.');
            setModalType('failed');
            setIsModalOpen(true);
            const newUrl = window.location.pathname;
            window.history.replaceState({}, '', newUrl);
        }
    }, [searchParams, paymentMethod, currentUser, showSuccessCard]);

    useEffect(() => {
        if (amountOption === 'custom') {
            setConvertedAmount(customAmount || '0');
        } else {
            setConvertedAmount(amountOption);
        }
    }, [customAmount, amountOption]);

    useEffect(() => {
        function getOrdinalDate() {
            const date = new Date();
            const day = date.getDate();

            const getOrdinal = (n) => {
                const s = ["th", "st", "nd", "rd"];
                const v = n % 100;
                return n + (s[(v - 20) % 10] || s[v] || s[0]);
            };

            setTodayDate(date)
            return getOrdinal(day);
        }

        setToday(getOrdinalDate());
    }, []);

    useEffect(() => {
        // Check if the user has a connected wallet
        if (currentUserWallet) {
            setIsWalletConnected(true);
            setWalletAddress(currentUserWallet);
        } else {
            setIsWalletConnected(false);
            setWalletAddress('');
        }
        setIsLoading(false);

    }, [currentUserWallet]);

    // Fetch current contribution on component mount
    useEffect(() => {
        const fetchCurrentContribution = async () => {
            if (!currentUser?._id) return;
            
            setLoadingContribution(true);
            try {
                console.log('Fetching current contribution for update page...');
                const result = await ContributionService.getCurrentContribution();
                console.log('Current contribution result:', result);
                
                if (result.success && result.data) {
                    setCurrentContribution(result.data);
                    // Set payment method based on current contribution
                    if (result.data.currency === 'USD') {
                        setPaymentMethod('fiat');
                    } else {
                        setPaymentMethod('crypto');
                    }
                } else {
                    console.log('No current contribution found');
                    setCurrentContribution(null);
                }
            } catch (error) {
                console.error('Error fetching current contribution:', error);
                setCurrentContribution(null);
            } finally {
                setLoadingContribution(false);
            }
        };

        fetchCurrentContribution();
    }, [currentUser]);

   

    const activateContribution = async (method, subscriptionId = null, _ref = null, overrideAmount = null) => {
        if (showSuccessCard) return;
        setIsLoading(true);
        
        try {
            // Use overrideAmount if provided, otherwise use processedAmount, otherwise calculate from current state
            const finalAmount = overrideAmount || processedAmount || (amountOption === 'custom' ? customAmount : amountOption);
            console.log('Activating contribution with amount:', finalAmount, 'overrideAmount:', overrideAmount, 'processedAmount:', processedAmount, 'amountOption:', amountOption, 'customAmount:', customAmount);
            
            // Update processedAmount to the final amount being used
            setProcessedAmount(finalAmount);
            
            const contributionData = {
                userId: currentUser._id,
                amount: finalAmount,
                currency: method === 'stripe' ? 'USD' : 'SHLN',
                paymentMethod: method,
                setupIntentId: _ref,
                subscriptionId: subscriptionId
            };

            console.log('Contribution data being sent:', contributionData);
            const result = await ContributionService.activateContribution(contributionData);

            if (result.success) {
                localStorage.removeItem('stripe_payment_ref');
                localStorage.removeItem('stripe_payment_amount'); // Clean up stored amount
                setShowSuccessCard(true);
                setClientSecret(null);

                const newUrl = window.location.pathname;
                window.history.replaceState({}, '', newUrl);

                return result.data;
            } else {
                throw new Error(result.message || 'Failed to activate contribution');
            }
        } catch (error) {
            console.error('Activation error:', error);
            setModalType('failed');
            setIsModalOpen(true);
            setErrorMessage(error.message || 'Failed to activate contribution');
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async () => {
        if (isSubmitting || isLoading) return;

        setIsSubmitting(true);
        setIsLoading(true);

        try {
            if (paymentMethod === 'fiat') {
                const finalAmount = amountOption === 'custom' ? customAmount : amountOption;
                console.log('HandleSubmit - sending amount:', finalAmount, 'amountOption:', amountOption, 'customAmount:', customAmount);
                
                // Store the processed amount
                setProcessedAmount(finalAmount);
                
                const paymentData = {
                    amount: finalAmount,
                    userId: currentUser?._id,
                };

                console.log('Payment data being sent:', paymentData);
                const result = await ContributionService.generatePaymentIntent(paymentData);

                if (result.success) {
                    setClientSecret(result.data.clientSecret);
                    setSubscriptionId(result.data.subscriptionId);
                } else {
                    throw new Error(result.message || 'Failed to generate payment intent');
                }
            } else {
                if (!isWalletConnected) {
                    setErrorMessage('Please connect your wallet first');
                    setModalType('failed');
                    setIsModalOpen(true);
                    return;
                }

                const verificationData = {
                    userId: currentUser?._id,
                    transactionHash: walletAddress
                };

                const result = await ContributionService.verifyCryptoPayment(verificationData);

                if (result.success) {
                    setModalType('success');
                    setIsModalOpen(true);
                } else {
                    throw new Error(result.message || 'Crypto payment verification failed');
                }
            }
        } catch (error) {
            console.error('Payment error:', error);
            const errorMsg = error.message || 'Payment processing failed. Please try again.';
            setErrorMessage(errorMsg);
            setModalType('failed');
            setIsModalOpen(true);
        } finally {
            setIsLoading(false);
            setIsSubmitting(false);
        }
    };

    const handlePaymentSuccess = (paymentRef) => {
        setPaymentReference(paymentRef);
        setModalType('success');
        setIsModalOpen(true);
        setIsLoading(false);
    };

    const handlePaymentError = (error) => {
        console.error('Payment error:', error);
        const errorMsg = error.message || 'Payment failed. Please try again.';
        setErrorMessage(errorMsg);
        setModalType('failed');
        setIsModalOpen(true);
        setIsLoading(false);
    };

    

    return (
        <div className="min-h-screen flex-col items-center justify-center bg-[#F8F5ED] md:p-[5%] space-y-10 text-BP-black">
            <div className="flex justify-center mb-6 bg-white w-fit mx-auto p-5 rounded-xl shadow-xl border-2">
                <Link to="/">
                    <img src={WebLogo} alt="Logo" className="h-10" />
                </Link>

            </div>

            <div className="bg-white rounded-2xl shadow-md md:p-[3vw] p-[4vw] w-[96%] md:w-[45vw] text-center mx-auto space-y-6 border">
                {showSuccessCard ? (
                    <div className="flex flex-col items-center justify-center py-8 space-y-6">
                        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                            <IoMdCheckmarkCircleOutline className="w-12 h-12 text-green-600" />
                        </div>
                        <h3 className="text-2xl font-semibold text-gray-800">Payment Successful!</h3>
                        <p className="text-gray-600">Your monthly contribution has been set up successfully.</p>
                        <div className="w-full max-w-md bg-gray-50 rounded-lg p-6 space-y-4">
                            <div className="flex justify-between">
                                <span className="text-gray-600">Amount:</span>
                                <span className="font-medium">${(() => {
                                    const displayAmount = processedAmount || (amountOption === 'custom' ? customAmount : amountOption);
                                    console.log('Success card displaying amount:', displayAmount, 'processedAmount:', processedAmount, 'amountOption:', amountOption, 'customAmount:', customAmount);
                                    return displayAmount;
                                })()}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Payment Method:</span>
                                <span className="font-medium">{paymentMethod === 'fiat' ? 'Cash (USD)' : 'Crypto (SHLN)'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Next Payment:</span>
                                <span className="font-medium">{today} of every month</span>
                            </div>
                        </div>
                        <button
                            onClick={() => {
                                setShowSuccessCard(false);
                                // Force refresh by adding a timestamp to the URL
                                const dashboardPath = AppRoutesPaths.dashboard?.root || '/';
                                navigate(`${dashboardPath}?refreshed=${Date.now()}`);
                            }}
                            className="mt-4 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                        >
                            Return to Dashboard
                        </button>
                    </div>
                ) : (
                    <>
                        <div className="text-2xl font-semibold mb-2 flex items-center gap-2">
                            <img src={donation} alt="" className="w-10 h-10" />
                            <h3 className="font-title text-[5.6vw] md:text-[2vw] font-semibold borde">
                                Monthly Contribution Update
                            </h3>
                        </div>

                        {/* Current Contribution Display */}
                        {loadingContribution ? (
                            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                                <div className="animate-pulse flex space-x-4">
                                    <div className="rounded-full bg-gray-300 h-10 w-10"></div>
                                    <div className="flex-1 space-y-2 py-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                                        <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                                    </div>
                                </div>
                            </div>
                        ) : currentContribution ? (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <div className="flex items-center mb-2">
                                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                                        <FiDollarSign className="w-4 h-4 text-white" />
                                    </div>
                                    <h4 className="font-semibold text-gray-800">Current Monthly Contribution</h4>
                                </div>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="text-gray-600">Amount:</span>
                                        <span className="font-medium ml-2">
                                            {currentContribution.currency === 'USD' 
                                                ? `$${currentContribution.amount}` 
                                                : `${currentContribution.amount} SHLN`}
                                        </span>
                                    </div>
                                    <div>
                                        <span className="text-gray-600">Status:</span>
                                        <span className={`font-medium ml-2 ${
                                            currentContribution.status === 'active' ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                            {currentContribution.status}
                                        </span>
                                    </div>
                                    <div>
                                        <span className="text-gray-600">Method:</span>
                                        <span className="font-medium ml-2">
                                            {currentContribution.currency === 'USD' ? 'Cash (USD)' : 'Crypto (SHLN)'}
                                        </span>
                                    </div>
                                    {currentContribution.nextPaymentDate && (
                                        <div>
                                            <span className="text-gray-600">Next Payment:</span>
                                            <span className="font-medium ml-2">
                                                {new Date(currentContribution.nextPaymentDate).toLocaleDateString()}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                                        <FiDollarSign className="w-4 h-4 text-white" />
                                    </div>
                                    <p className="text-gray-700">
                                        No active contribution found. Set up your first monthly contribution below.
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Payment Method Selection */}
                        <div className=''>
                            <h2 className="text-lg font-normal text-[#4B5563] flex items-center font-body">
                                <FiCreditCard className="mr-2 text-[#4B5563]" />
                                Payment Method
                            </h2>
                            <div className="py-3 flex flex-row justify-between md:w-[75%] w-full">
                                <motion.button
                                    whileHover={{ scale: 1.03 }}
                                    whileTap={{ scale: 0.98 }}
                                    type="button"
                                    onClick={() => setPaymentMethod('fiat')}
                                    className={`py-3 px-4 rounded-xl border flex justify-center items-center ${paymentMethod === 'fiat' ? 'border-[#9747FF] bg-indigo-50' : 'border-gray-200 hover:border-gray-300'}`}
                                >
                                    <FiDollarSign className={`w-5 h-5 ${paymentMethod === "fiat" ? 'text-[#9747FF]' : "text-[#4B5563]"}`} />
                                    <span className={`text-[#4B5563] ${paymentMethod === "fiat" ? 'text-[#9747FF]' : "text-[#4B5563]"}`}>Cash(USD)</span>
                                </motion.button>

                                <motion.button
                                    whileHover={{ scale: 1.03 }}
                                    whileTap={{ scale: 0.98 }}
                                    type="button"
                                    onClick={() => setPaymentMethod('crypto')}
                                    className={`py-3 px-4 rounded-xl border flex justify-center items-center ${paymentMethod === 'crypto' ? 'border-[#9747FF] bg-indigo-50' : 'border-gray-200 hover:border-gray-300'}`}
                                >
                                    <FiDollarSign className={`w-5 h-5 ${paymentMethod === "crypto" ? 'text-[#9747FF]' : "text-[#4B5563]"}`} />
                                    <span className={`text-[#4B5563] ${paymentMethod === "crypto" ? 'text-[#9747FF]' : "text-[#4B5563]"}`}>Crypto(SHLN)</span>
                                </motion.button>
                            </div>

                            
                        </div>

                        {/* Payment Form or Amount Selection */}
                        {clientSecret && paymentMethod === 'fiat' ? (
                            <Elements stripe={stripePromise} options={{ clientSecret }}>
                                <PaymentForm
                                    amount={amountOption === 'custom' ? customAmount : amountOption}
                                    onSuccess={handlePaymentSuccess}
                                    onError={handlePaymentError}
                                    subscriptionId={subscriptionId}
                                />
                            </Elements>
                        ) : (
                            <>
                                {/* Amount Selection */}
                                <div className="">
                                    <p className="text-[#4B5563] text-start font-body font-normal">Choose your contribution</p>
                                    <div className='flex flex-row justify-between mt-4'>
                                        {['5', '10', '20'].map((option) => (
                                            <motion.button
                                                key={option}
                                                whileHover={{ scale: 1.03 }}
                                                whileTap={{ scale: 0.98 }}
                                                type="button"
                                                onClick={() => {
                                                    setAmountOption(option);
                                                    setCustomAmount(option); // Also set the custom amount so it shows in the input
                                                    setAmountError('');
                                                }}
                                                className={`py-4 px-[2] md:w-[11vw] w-[28vw] md:rounded-[1.2vw] rounded-[3vw] border-2 transition-all bg-[#505050]/10 ${amountOption === option ? 'border-[#F5C21A] bg-[#F5C21A]' : 'border-gray-200 hover:border-gray-300'}`}
                                            >
                                                <div className="flex flex-col items-center">
                                                    <span className={`text-xl font-bold ${amountOption === option ? 'text-white' : 'text-gray-700'}`}>
                                                        {paymentMethod === 'fiat' ? `$${option}` : `${option} SHLN`}
                                                    </span>
                                                    <span className="text-xs text-gray-500 mt-1">
                                                        {paymentMethod === 'fiat' ? `${option} SHLN` : `$${option}`}
                                                    </span>
                                                </div>
                                            </motion.button>
                                        ))}
                                    </div>

                                    {/* Custom Amount Input */}
                                    {(paymentMethod === "fiat") ? (
                                        <>
                                            <h2 className="text-lg  mt-4 font-normal text-[#4B5563] flex items-center font-body text-[1rem]">
                                                Custom input
                                            </h2>
                                            <input
                                                onChange={(e) => {
                                                    const value = e.target.value.replace(/[^0-9.]/g, '');
                                                    const decimalCount = value.split('.').length - 1;
                                                    if (decimalCount <= 1) {
                                                        const numValue = parseFloat(value);
                                                        setCustomAmount(value); // Always update customAmount
                                                        if (value === '' || isNaN(numValue)) {
                                                            setAmountOption('custom');
                                                            setAmountError('');
                                                        } else if (numValue < 5) {
                                                            setAmountOption('custom');
                                                            setAmountError('Minimum amount is $5');
                                                        } else {
                                                            setAmountOption('custom');
                                                            setAmountError('');
                                                        }
                                                    }
                                                }}
                                                type="text"
                                                inputMode="decimal"
                                                className={`border w-full md:h-[4vw] h-[15vw] md:rounded-[1vw] rounded-[3vw] text-center font-body font-light bg-[#505050]/10 focus:outline focus:outline-purple-400 focus:outline-1 ${amountError ? 'border-red-500' : 'border-[#E5E7EB]'}`}
                                                placeholder='Enter custom amount (min $5)'
                                                value={customAmount || (amountOption !== 'custom' ? amountOption : '')}
                                            />
                                            {amountError && (
                                                <p className="text-red-500 text-sm mt-1 text-left">{amountError}</p>
                                            )}
                                        </>
                                    ) : (
                                        <div className="md:mt-[3vw] mt-[8vw]">
                                            <h2 className="text-lg font-normal text-[#4B5563] flex items-center font-body text-[1rem]">
                                                <FiRepeat className="mr-2 text-[#9747FF]" />
                                                Custom input
                                            </h2>
                                            <div className='flex flex-col py-2 gap-[2vw]'>
                                                <div className="relative">
                                                    <input
                                                        type="text"
                                                        inputMode="decimal"
                                                        onChange={(e) => {
                                                            const value = e.target.value.replace(/[^0-9]/g, '');
                                                            const numValue = parseInt(value);
                                                            setCustomAmount(value); // Always update customAmount
                                                            if (value === '' || isNaN(numValue)) {
                                                                setAmountOption('custom');
                                                                setAmountError('');
                                                            } else if (numValue < 5) {
                                                                setAmountOption('custom');
                                                                setAmountError('Minimum amount is 5 SHLN');
                                                            } else {
                                                                setAmountOption('custom');
                                                                setAmountError('');
                                                            }
                                                        }}
                                                        value={customAmount || (amountOption !== 'custom' ? amountOption : '')}
                                                        placeholder="Enter amount (min 5 SHLN)"
                                                        className={`w-full md:pl-[30%] pl-[40%] pr-[2vw] md:py-[1vw] py-[3vw] border rounded-lg focus:outline focus:outline-purple-400 focus:outline-1 ${amountError ? 'border-red-500' : 'border-gray-200'}`}
                                                    />
                                                    <div className="absolute left-3 top-3.5 text-gray-500">
                                                        SHLN
                                                    </div>
                                                </div>
                                                {amountError && (
                                                    <p className="text-red-500 text-sm text-left">{amountError}</p>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    <button
                                        onClick={handleSubmit}
                                        disabled={amountError || isSubmitting || isLoading || (() => {
                                            const finalAmount = amountOption === 'custom' ? customAmount : amountOption;
                                            return !finalAmount || parseFloat(finalAmount) < 5;
                                        })()}
                                        className={`w-full md:h-[4vw] h-[12vw] mt-4 md:rounded-[1vw] rounded-[3vw] font-body text-[1.2rem] shadow-[0px_10px_15px_rgba(0,0,0,0.1)] transition-colors ${
                                            amountError || isSubmitting || isLoading || (() => {
                                                const finalAmount = amountOption === 'custom' ? customAmount : amountOption;
                                                return !finalAmount || parseFloat(finalAmount) < 5;
                                            })()
                                                ? 'bg-gray-400 cursor-not-allowed text-gray-200'
                                                : 'bg-[#9747FF] text-white hover:bg-[#7A2FDB]'
                                        }`}
                                    >
                                        {isSubmitting || isLoading 
                                            ? 'Processing...' 
                                            : currentContribution 
                                                ? 'Update Contribution' 
                                                : 'Set Up Contribution'
                                        }
                                    </button>
                                </div>


                            </>
                        )}
                    </>
                )}
            </div>

            {/* Modals */}
            {isModalOpen && modalType === 'success' && (
                <SuccessAlert 
                    message="Payment processed successfully!" 
                    onClose={() => {
                        setIsModalOpen(false);
                        const dashboardPath = AppRoutesPaths.dashboard?.root || '/';
                        navigate(`${dashboardPath}?refreshed=${Date.now()}`);
                    }} 
                />
            )}
            
            {isModalOpen && modalType === 'failed' && (
                <FailedAlert 
                    message={errorMessage || "Payment failed. Please try again."} 
                    onClose={() => setIsModalOpen(false)} 
                />
            )}
        </div>
    );
}; 