import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import HeaderNew from '../components/Header/HeaderNew';
import bg from '../assets/presale/Desktop - 10.png';
import { IoLogIn } from 'react-icons/io5';
import addUser from "../assets/presale/add-user.png";
import login from "../assets/presale/login.png"
import star from "../assets/presale/star.png"
import tag from "../assets/presale/price-tag.png"
import { AppRoutesPaths } from '../route/app_route';
import { Spin } from 'antd';
import Footer from '../components/Footer/footer';
import { FaArrowRight } from 'react-icons/fa';
import { bpnthrToken } from '../constants/constants';
import { WalletModal } from './Presale/walletModal';
import { useAuthentication } from '../components/utils/provider';
import DepositModal from './modal';


export const PresaleAuth = () => {
  const navigate = useNavigate()
  const [usdInputValue, setUsdInputValue] = useState('');
  const [bpnthrInputValue, setBpnthrInputValue] = useState('');
  const price = 0.0001; // Price of 1 BPNTHRQ in USD
  const [loadingUSD, setLoadingUSD] = useState(false);
  const [loadingPanther, setLoadingPanther] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

   const contractAddress = bpnthrToken || "Please contact Admin";
   const { currentUser } = useAuthentication();



  const onSetUSDInput = (val) => {
    if (val < 0) return;
    setLoadingPanther(true); // Start loading for BPNTHRQ calculation
    setUsdInputValue(val);
    const tokens = val / price; // Calculate tokens
    setTimeout(() => {
      setBpnthrInputValue(tokens.toFixed(8)); // Set tokens with 8 decimal places for precision
      setLoadingPanther(false); // Stop loading
    }, 500); // Simulate delay for calculation
  };



  // Function to calculate USD based on BPNTHRQ input
  const onSetBpnthrInput = (val) => {
    if (val < 0) return;
    setLoadingUSD(true); // Start loading for USD calculation
    setBpnthrInputValue(val);
    const usd = val * price; // Calculate USD
    setTimeout(() => {
      setUsdInputValue(usd.toFixed(8)); // Set USD with 8 decimal places for precision
      setLoadingUSD(false); // Stop loading
    }, 500); // Simulate delay for calculation
  };

  return (
    <div
      className="min-h-screen text-white flex flex-col bg-[#111828] bg-cover bg-center"

    >
      <HeaderNew />

      <div className="max-w-7xl mx-auto px-6 py-20 flex flex-col mt-12 lg:flex-row items-center justify-between gap-10">

        {/* Left Section */}
        <div className="relative lg:w-1/2 flex flex-col justify-center h-full mb-10 lg:mb-20 text-left">
          <h2 className="text-4xl sm:text-3xl lg:text-6xl font-medium leading-tight mb-6 sm:mb-10 lg:mb-20  md:w-full">
            Black Panther <br />
            Token <span className="text-yellow-400">(BPNTHRQ)</span> <br />
            is Live
          </h2>

          {/* three line */}
          <div className="absolute -bottom-16 -right-4 sm:-right-28 lg:-bottom-20 lg:right-0 flex  space-x-0.5 sm:space-x-6">
            <div className="max-sm:mt-28 ">
              <div className="ml-[2px] h-28 sm:h-56 w-[1px] bg-gradient-to-b from-[#42424099] to-white" />
              <div className="w-1 h-1 bg-white rounded-full" />
            </div>
            <div className="mt-10 max-md:mt-10">
              <div className="ml-[2px] h-56 w-[1px] bg-gradient-to-b from-[#42424099] to-white md:h-50" />
              <div className="w-1 h-1 bg-white rounded-full" />
            </div>
            <div className="mt-20">
              <div className="ml-[2px] h-56 w-[1px] bg-gradient-to-b from-[#42424099] to-white" />
              <div className="w-1 h-1 bg-white rounded-full" />
            </div>
          </div>

        </div>

        {/* right sec */}
        <div className="lg:w-1/2 md:w-2/3 sm:w-3/4 w-full max-w-md flex flex-col justify-center h-full">
          <div className="bg-[#302f29] p-8 rounded-3xl shadow-2xl text-center flex flex-col justify-between h-full w-full">
            <h3 className="text-lg font-semibold text-white">
              JOIN THE BLACK PANTHER (BPNTHRQ) HOLDERS
            </h3>
         <div className="text-gray-300 text-sm mt-3">
              Please go to your Wallet Address and CUSTOM IMPORT the BPNTHRQ ticker by copying the Contract Address: 
              <div className="w-full mt-2"> {/* Full width container */}
                <div
                  className="w-full max-w-xs mx-auto bg-[#727069] p-2 rounded-lg text-gray-300 text-sm cursor-pointer hover:bg-[#828079] transition-colors text-center"
                  onClick={() => setIsModalOpen(true)}
                >
                  {contractAddress.substring(0, 6)}...{contractAddress.substring(contractAddress.length - 4)}
                </div>
              </div>
              inside your wallet in order to view the number of tokens you have bought.
            </div>

            {/* <p className="">Private Sale - June 4th - 10th</p>
            <p className="">Public Presale - June 12th - 18th</p> */}
            <p className='mt-2'>Live on PancakeSwap</p>

             {isModalOpen && (
                            <DepositModal
                                walletAddress={contractAddress}
                                onClose={() => setIsModalOpen(false)}
                            />
                        )}

            {/* <h4 className="text-yellow-400 text-lg font-semibold mt-6">
              What is Pinksale Private and Presale?
            </h4>

            <p className="text-gray-400 text-sm">Pinksale Private and Presale are token sale phases where early
              investors buy tokens before public launch, often at discounted prices.</p> */}
            {/* <div className="mt-4 flex flex-col gap-4 w-full">
              <div className="relative w-full">
                <input
                  type="number"
                  placeholder="USD"
                  value={usdInputValue}
                  onChange={(e) => onSetUSDInput(e.target.value)}
                  className="w-full p-4 rounded-full bg-[#9a9994] text-black placeholder-black border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400 text-center leading-tight"
                  disabled={loadingUSD} // Disable input while loading
                />
                {loadingUSD && (
                  <div className="absolute inset-0 flex items-center justify-center bg-[#9a9994]/60 rounded-full">
                    <Spin size="small" />
                  </div>
                )}
              </div>
              <div className="relative w-full">
                <input
                  type="number"
                  placeholder="$ BPNTHRQ"
                  value={bpnthrInputValue}
                  onChange={(e) => onSetBpnthrInput(e.target.value)}
                  className="w-full p-4 rounded-full bg-[#9a9994] text-black placeholder-black border border-gray-700 focus:outline-none focus:ring-2 focus:ring-yellow-400 text-center leading-tight"
                  disabled={loadingPanther} // Disable input while loading
                />
                {loadingPanther && (
                  <div className="absolute inset-0 flex items-center justify-center bg-[#9a9994]/60 rounded-full">
                    <Spin size="small" />
                  </div>
                )}
              </div>
            </div> */}
          </div>

          {/* <div className="flex items-center justify-center mt-6"> */}
            {/* <button onClick={() => navigate(AppRoutesPaths.presale)} className="flex items-center gap-2 bg-purple-600 hover:bg-purple-500 px-6 py-3 rounded-full text-white font-extrabold">
                Proceed To Pinksale <FaArrowRight className="w-5 h-5" />
              </button> */}
            
          {/* </div> */}

          {/* <div className="text-center mt-8">
            <div className='my-4'>
              <p>NB: Creating an account is <span className='text-purple-600'>OPTIONAL</span></p>
            </div>
            <div className="flex gap-4 justify-center">
              <button onClick={() => navigate(AppRoutesPaths.signup)} className="flex items-center gap-2 bg-purple-600 hover:bg-purple-600 px-6 py-3 rounded-full text-white font-extrabold">
                Register <img src={addUser} alt="Add User" className="w-5 h-5" />
              </button>
              <p className='text-center mt-3'>OR</p>
              <button onClick={() => {
                // Store origin before navigating to login
                localStorage.setItem("login_origin", AppRoutesPaths.presaleAuth);
                navigate(AppRoutesPaths.login);
              }}
                className="flex items-center gap-2 bg-BP-yellow hover:bg-BP-hovered-yellow  px-6 py-3 rounded-full text-white font-extrabold">
                Login <img src={login} alt="Add User" className="w-5 h-5" />
              </button>
            </div>
            <p className="mt-2">To proceed with buying the tokens</p>
          </div> */}
        </div>
      </div>

      {/* Bottom Row: Two Cards Side by Side - Responsive */}
      <div className="w-full flex justify-center px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-col md:flex-row gap-6 sm:gap-8 lg:gap-10 max-w-5xl w-full">
          
          {/* Buy BNB Card */}
          <div className="flex-1 w-full max-w-sm mx-auto md:mx-0 md:max-w-none">
            <div className="bg-[#302f29] p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl shadow-2xl text-center h-full flex flex-col justify-between min-h-[180px] sm:min-h-[200px]">
              <div>
                <h4 className="text-base sm:text-lg lg:text-xl font-semibold text-white mb-3 sm:mb-4 leading-tight">
                  To Buy BNB with Mobile Money proceed here 
                </h4>
              </div>
              <button 
                onClick={() => navigate('/buy-mobile-money')}
                className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-500 px-4 sm:px-6 py-2.5 sm:py-3 rounded-full text-white hover:text-white font-extrabold transition-colors text-sm sm:text-base w-full sm:w-auto"
              >
                Proceed To Mobile Money<FaArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>

          {/* Buy BPNTHRQ Card */}
          <div className="flex-1 w-full max-w-sm mx-auto md:mx-0 md:max-w-none">
            <div className="bg-[#302f29] p-4 sm:p-6 lg:p-8 rounded-2xl sm:rounded-3xl shadow-2xl text-center h-full flex flex-col justify-between min-h-[180px] sm:min-h-[200px]">
              <div>
                <h4 className="text-base sm:text-lg lg:text-xl font-semibold text-white mb-3 sm:mb-4 leading-tight">
                  To buy BPNTHRQ proceed here, but make sure you have BNB
                </h4>
              </div>

              <a
                href="https://pancakeswap.finance/swap?chain=bsc&outputCurrency=0x8f02135c35686D3100Fac67d2A996c34bd5882e4&inputCurrency=BNB&chainOut=bsc"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-500 px-4 sm:px-6 py-2.5 sm:py-3 rounded-full text-white hover:text-white font-extrabold transition-colors text-sm sm:text-base w-full sm:w-auto"
              >
                Proceed To pancakeswap <FaArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-14 flex flex-col items-center relative w-fit mx-[25%]">

        <div className="flex flex-col items-center gap-2 font-body font-thin">
          <span className="text-sm">• Get BPNTHRQ on</span>
          <span className="text-sm">pancakeswap</span>
        </div>

        <div className="relative w-60 h-14 rounded-tl-full border-t border-white" >
          <img src={tag} alt="" className="absolute right-8 -top-5 w-9 h-9 p-2 rounded-full bg-BP-gray-100-opc" />
        </div>

      </div>


      <Footer />
    </div>
  );
};