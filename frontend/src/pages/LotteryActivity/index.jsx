import React, { useState, useEffect } from 'react';
import { FaTrophy, FaRegCalendarAlt } from 'react-icons/fa';
import { IoTimeOutline } from 'react-icons/io5';
import { IoMdRefresh } from 'react-icons/io';
import { MdKeyboardArrowRight, MdArrowBack } from 'react-icons/md';
import { FiRefreshCw } from 'react-icons/fi';
import { BsCheckCircle } from 'react-icons/bs';
import { useAuthentication } from '../../components/utils/provider';
import { useNavigate } from 'react-router-dom';
import { AppRoutesPaths } from '../../route/app_route';
import Won from './Won';
import Upcoming from './Upcoming';
import Completed from './Completed';
import award from '../../assets/sahelion/award.png';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import { ethers } from 'ethers';
import { useAccount } from 'wagmi';
import { setupKzg } from 'viem';
import { Spin } from 'antd';
import ConcentricArcLoader from "../../utils/ConcentricArcLoader.jsx";

const LotteryActivityPage = () => {
  const navigate = useNavigate();
  const [selectedDraw, setSelectedDraw] = useState(null);
  const [selectedDrawDate, setSelectedDrawDate] = useState(null);
  const [viewUpcoming, setViewUpcoming] = useState(false);
  const [playerUpcomingTickets, setPlayerUpcomingTickets] = useState([]);
  const [loadingTickets, setLoadingTickets] = useState(true)
  const [viewCompleted, setViewCompleted] = useState(false);
  const [rewards, setRewards] = useState({});
  const [sortedDates, setSortedDates] = useState([]);

  const { isConnected, address } = useAccount();

  // Get wallet connection functions from useAuthentication
  const {
    connectWallet,
    currentUserWallet,
    disconnectWallet,
    getLotteryPlayerRewards,
    getPlayerUpcomingTickets,
  } = useAuthentication();

  const lotteryData = [
    {
      date: 'May 27, 2025',
      tickets: 3,
      status: 'upcoming',
    },
    {
      date: 'May 7, 2025',
      tickets: 3,
      status: 'won',
      unclaimedTickets: 2,
      winningNumbers: [17, 22, 36, 11, 16, 25],
      yourNumbers: [40, 23, 26, 10, 20, 12],
      matched: '4/6',
      prize: '20,000,000 PNTHR',
      expiresIn: '2 months',
      matchedNumbers: [40, 26, 20, 12], // Add this array to track matched numbers
    },
    {
      date: 'April 15, 2025',
      tickets: 3,
      status: 'completed',
      bestMatch: '2/6',
    },
  ];

  // Format wallet address for display
  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  const onWalletConnect = async () => {
    await connectWallet();
  }

  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (currentUserWallet) {
      await disconnectWallet();
    } else {
      await connectWallet();
    }
  };

  function getDrawDate(timestamp) {
    const date = new Date(timestamp * 1000); // convert to ms
    const day = date.getUTCDay(); // Sunday = 0, Monday = 1

    // How many days to add to reach next Sunday
    const daysToAdd = (1 - day) % 1;  // remember to change to: const daysToAdd = (7 - day) % 7;

    const drawDate = new Date(Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate() + daysToAdd,
      0, 0, 0 // 00:00 UTC
    ));

    return drawDate.toISOString().split('T')[0]; // "YYYY-MM-DD"
  }

  function groupRewardsByDraw(rewards) {
    const grouped = {};

    rewards.forEach(reward => {
      const timestamp = reward.timestamp.toNumber();
      const drawDate = getDrawDate(timestamp);

      if (!grouped[drawDate]) {
        grouped[drawDate] = [];
      }

      grouped[drawDate].push(reward);
    });

    return grouped;
  }

  function getLastSundayFormatted() {
    const today = new Date();
    const day = today.getDay(); // 0 = Sunday
    const diff = today.getDate() - day;
    const lastSunday = new Date(today);
    lastSunday.setDate(diff);
    lastSunday.setHours(0, 0, 0, 0);

    // Format the date as "Month day, Year"
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(lastSunday);
  }

  const fetchLotteryPlayerRewards = async () => {
    try {
      const rewards = await getLotteryPlayerRewards();
      console.log("Player Rewards:", rewards);
      const groupedRewards = groupRewardsByDraw(rewards);
      console.log("Grouped Rewards by Draw Date:", groupedRewards);
      setRewards(groupedRewards);
      const _sortedDates = Object.keys(groupedRewards).sort(
        (a, b) => new Date(b) - new Date(a) // Latest first
      );
      setSortedDates(_sortedDates);
      setLoadingTickets(false)
      return groupedRewards;
    } catch (error) {
      console.error("Error fetching player rewards:", error);
    }
  }

  useEffect(() => {
    getPlayerUpcomingTickets().then((tickets) => {
      console.log("Upcoming Tickets:", tickets);
      setPlayerUpcomingTickets(tickets);
    }).catch((error) => {
      console.error("Error fetching upcoming tickets:", error);
    });
  }, [currentUserWallet])

  useEffect(() => {
    fetchLotteryPlayerRewards()
  }, [currentUserWallet])

  if (viewUpcoming) {
    return (
      <Upcoming
        upcomingTickets={playerUpcomingTickets}
        setSelectedDraw={() => setViewUpcoming(false)}
        lastSunday={getLastSundayFormatted()}
      />
    );
  }

  if (viewCompleted) {
    return (
      <Completed
        selectedDraw={lotteryData[2]}
        setSelectedDraw={() => setViewCompleted(false)}
      />
    );
  }

  if (selectedDraw) {
    return (
      <Won
        selectedDraw={selectedDraw}
        setSelectedDraw={setSelectedDraw}
        fetchLotteryPlayerRewards={fetchLotteryPlayerRewards}
        selectedDrawDate={selectedDrawDate} />
    );
  }
  return (
    <>
      <div className="min-h-screen bg-[#111828]">
        <HeaderNew />
        <div className="flex justify-center">
          <div className="lg:w-[70%] px-4 py-8">
            <div className="rounded-[32px] p-8 mt-[-50px]">
              <button
                onClick={() => navigate(AppRoutesPaths.lottery)}
                className="flex items-center gap-2 text-gray-400 mb-8 hover:text-white"
              >
                <MdArrowBack />
                Back to Lottery
              </button>

              <div className="flex items-center gap-3 mb-2">
                <img
                  src={award}
                  alt="Prize"
                  className="w-10 h-10 sm:w-16 sm:h-16"
                />
                <h2 className="text-[32px] font-bold text-white">
                  Your Lottery Activity
                </h2>
              </div>

              <p className="text-gray-400 mb-6">
                View your upcoming draws and past results
              </p>
              {
                isConnected ?
                  currentUserWallet ?
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                      <span className="text-gray-400 text-sm sm:text-base">
                        Wallet: {formatWalletAddress(currentUserWallet)}
                      </span>
                      <button className="flex items-center gap-2 bg-[#1E2736] text-yellow-400 px-4 py-2 rounded-full hover:bg-[#252f42] transition-colors" onClick={handleConnectWallet}>
                        <FiRefreshCw className="text-lg sm:text-xl" />
                        Disconnect Wallet
                      </button>
                    </div> : ""
                  : ""
              }

              {
                !isConnected ?
                  <button onClick={onWalletConnect} className='bg-[#E1AB0D] hover:bg-[#e1a80d] w-full md:w-auto text-black font-medium text-[14px] md:text-[16px] px-4 md:px-16 py-3 md:py-4 rounded-lg flex items-center justify-center gap-2 mt-10'>
                    Connect Wallet
                  </button> :
                  loadingTickets? <ConcentricArcLoader size = {60}/> :currentUserWallet ? 
                  <div>
                    <div className="space-y-4m mt-8">
                      {/* Upcoming Card */}
                      <div
                        className="bg-[#1E2736]/60 hover:bg-[#252f42] rounded-2xl p-4 sm:p-10 mb-4 md:w-full w-[360px] sm:max-w-none transition-colors cursor-pointer"
                        onClick={() => setViewUpcoming(true)}
                      >
                        <div className="flex flex-col gap-4 sm:gap-0">
                          <div className="flex items-center justify-between">
                            <h3 className="text-white text-[22px] sm:text-[28px] font-medium leading-none">
                              {getLastSundayFormatted()}
                            </h3>
                            <div className="flex items-center gap-2 bg-[#333732]/90 px-4 sm:px-8 py-2 rounded-full">
                              <IoTimeOutline className="text-yellow-400 text-lg" />
                              <span className="text-yellow-400 text-sm sm:text-base">
                                Upcoming
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <p className="text-[#8A93A7]">Participated with {playerUpcomingTickets.length} tickets</p>
                            <MdKeyboardArrowRight className="text-[#8A93A7] text-2xl" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4m mt-8">
                      {sortedDates.map(drawDate => {
                        const drawRewards = rewards[drawDate];
                        const totalTickets = drawRewards.length;
                        const unclaimedWins = drawRewards.filter(
                          ticket => ticket.matches > 2 && ticket.claimed === false
                        );
                        const anyWins = drawRewards.some(ticket => ticket.matches > 2);
                        const isUpcoming = new Date(drawDate) > new Date();

                        return (
                          <div
                            className="bg-[#1a1e34]/60 rounded-2xl p-4 sm:p-10 hover:bg-[#252f42] transition-colors cursor-pointer mb-4 md:w-full w-[360px] sm:max-w-none"
                            onClick={() => {
                              setSelectedDrawDate(drawDate);
                              setSelectedDraw(drawRewards)
                            }}
                          >
                            <div className="flex flex-col gap-4 sm:gap-0">
                              <div className="flex items-center justify-between">
                                <h3 className="text-white text-[22px] sm:text-[28px] font-medium leading-none">
                                  {new Date(drawDate).toLocaleDateString(undefined, {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                  })}
                                </h3>

                                {isUpcoming ? (
                                  <div className="flex items-center gap-2 bg-[#333732]/90 px-4 sm:px-8 py-2 rounded-full">
                                    <IoTimeOutline className="text-yellow-400 text-lg" />
                                    <span className="text-yellow-400 text-sm sm:text-base">
                                      Upcoming
                                    </span>
                                  </div>
                                ) : anyWins ? (
                                  <div className="flex items-center gap-2 bg-[#72519F] px-4 sm:px-6 py-2 rounded-full">
                                    <FaTrophy className="text-white text-lg" />
                                    <span className="text-white text-sm sm:text-base">
                                      You won
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-2 bg-[#374151] px-4 sm:px-6 py-2 rounded-full">
                                    <BsCheckCircle className="text-[#8A93A7] text-lg" />
                                    <span className="text-[#8A93A7] text-sm sm:text-base">
                                      Completed
                                    </span>
                                  </div>
                                )}
                              </div>
                              <div className="w-full border-b border-[#8A93A7]/30">
                                <div className="flex items-center justify-between py-2">
                                  <p className="text-[#8A93A7]">
                                    Participated with {totalTickets} tickets
                                  </p>
                                  <MdKeyboardArrowRight className="text-[#8A93A7] text-2xl" />
                                </div>
                              </div>
                              {unclaimedWins.length > 0 && (
                                <p className="text-yellow-400 text-[15px]">
                                  {unclaimedWins.length} unclaimed winning tickets
                                </p>
                              )}

                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div> : ""
              }
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default LotteryActivityPage;



