import React, {useState} from 'react';
import { FaRegCalendarAlt, FaTrophy } from 'react-icons/fa';
import { FiRefreshCw } from 'react-icons/fi';
import { MdArrowBack } from 'react-icons/md';
import { useAuthentication } from '../../components/utils/provider';
import profit from '../../assets/sahelion/profit.png';
import jackpot from '../../assets/sahelion/jackpot.png';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import { ethers } from "ethers";

const Won = ({ selectedDraw, setSelectedDraw, fetchLotteryPlayerRewards, selectedDrawDate }) => {
  const { currentUserWallet, claimReward } = useAuthentication();
  console.log(selectedDraw[0])
  const winningNumbers = selectedDraw[0].winningNumbers.map(num => num.toNumber());
  const [claiming, setClaiming] = useState(false);
  const [draws, setDraws] = useState(selectedDraw);

  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  const onClaimreward = (drawId) =>{
    setClaiming(true);
    claimReward(drawId)
      .then(() => {
        fetchLotteryPlayerRewards().then((rewards) => {
          console.log("Reward claimed successfully");
          setDraws(rewards[selectedDrawDate]);
          setClaiming(false);
        }).catch((error) => {
          console.error("Error fetching player rewards after claiming:", error);
        });
        
      })
      .catch((error) => {
        console.error("Error claiming reward:", error);
        setClaiming(false);
      });
  }

  const expirationPeriod = 180 * 24 * 60 * 60; // 180 days in seconds
  const now = Math.floor(Date.now() / 1000); 

  function getDaysUntilExpiration(rewardTimestamp) {
  
    const expirationTime = rewardTimestamp + expirationPeriod;
    const remainingSeconds = expirationTime - now;
  
    if (remainingSeconds <= 0) {
      return 0; // already expired
    }
  
    const remainingDays = Math.floor(remainingSeconds / (60 * 60 * 24));
    return remainingDays;
  }
  

  return (
    <div className="min-h-screen bg-[#111828]">
      <HeaderNew />
      <div className="flex justify-center">
        <div className="lg:w-[70%] px-4 py-8">
          <div className="rounded-[32px] p-4 sm:p-8 mt-[-50px]">
            <button
              onClick={() => setSelectedDraw(null)}
              className="flex items-center gap-2 text-gray-400 mb-6 sm:mb-8 hover:text-white"
            >
              <MdArrowBack />
              Back to Activity
            </button>

            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 mb-6 sm:mb-8">
              <div className="flex items-center gap-4">
                <img
                  src={profit}
                  alt="Prize"
                  className="w-14 h-14 sm:w-16 sm:h-16"
                />
                <h1 className="text-[28px] sm:text-3xl font-bold text-white">
                  Your Prize Portfolio
                </h1>
              </div>
              <span className="bg-[#333732]/90 text-yellow-400 px-4 py-2 rounded-full text-sm w-fit">
                {draws.filter(draw=>draw.claimed ===false && draw.matches.toNumber() >= 3).length} Unclaimed Tickets
              </span>
            </div>

            {/* Wallet Info */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
              <span className="text-gray-400 text-sm sm:text-base">
                Wallet: {currentUserWallet ? formatWalletAddress(currentUserWallet) : 'Not connected'}
              </span>
              
            </div>

            {/* Winning Numbers */}
            <div className="mt-6 sm:mt-4 overflow-x-auto">
              <div className="space-y-4 sm:space-y-0 sm:flex sm:gap-4 min-w-max">
                <p className="text-base sm:text-lg mt-2 text-white whitespace-nowrap">
                  Winning Numbers:
                </p>
                <div className="flex gap-4">
                  {winningNumbers.map((number, index) => (
                    <div
                      key={index}
                      className="w-10 h-10 sm:w-12 sm:h-12 bg-[#72519F] rounded-full flex items-center justify-center text-white font-medium"
                    >
                      {number}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Your Tickets Section */}

            <div className="mt-8 flex flex-col items-center sm:items-stretch">
              <h2 className="text-lg sm:text-xl mb-4 text-white border-b border-[#8A93A7] pb-2 w-full">
                Your tickets
              </h2>

              {
                draws.map((draw, index) => {
                  const matches = draw.matches.toNumber();
                  const claimed = draw.claimed;
                  const timestamp = draw.timestamp.toNumber();
                  const formattedReward = ethers.utils.formatEther(draw.reward);
                  const daysRemaining = getDaysUntilExpiration(timestamp);
                  const drawDate = new Date(timestamp * 1000).toLocaleDateString(undefined, {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  });
                  return (
                    <>
                      {matches === 6 && !claimed ? (
                        <div className="bg-[#1f2937] rounded-xl p-4 sm:p-6 mb-4 md:w-full w-[360px] sm:max-w-none">
                          {/* Date and Expires Section */}
                          <div className="flex flex-row sm:flex-row items-center justify-between gap-3 sm:gap-0 mb-4">
                            <div className="flex items-center gap-2 text-white">
                              <span className="text-yellow-400">
                                <FaRegCalendarAlt className="text-BP-yellow text-2xl" />
                              </span>
                              <span className="text-sm sm:text-base">
                              {drawDate}
                              </span>
                            </div>
                            <span className="bg-[#333732]/90 text-yellow-400 px-4 py-2 rounded-full text-sm w-fit">
                              {daysRemaining < 0 ? "Expired": `Expires in ${daysRemaining} days`}
                            </span>
                          </div>

                          <div className="flex gap-2 sm:gap-4 mb-4 sm:mb-6 overflow-x-auto pb-2">
                            {draw.numbers.map((number, index) => {
                              const num = number.toNumber();
                              return (
                                <div
                                  key={index}
                                  className="w-10 h-10 sm:w-12 sm:h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white font-medium flex-shrink-0 text-sm sm:text-base"
                                >
                                  {num}
                                </div>
                              )
                            })}
                          </div>

                          <div className="flex justify-between items-center mb-3 sm:mb-4">
                            <div className="flex flex-col items-center gap-1">
                              <span className="text-gray-400 text-xs sm:text-sm">
                                Jackpot
                              </span>
                              <img
                                src={jackpot}
                                alt="Prize"
                                className="w-6 h-6 sm:w-10 sm:h-10"
                              />
                            </div>
                            <div className="flex flex-col items-center">
                              <span className="text-gray-400 text-xs sm:text-sm">
                                Prize
                              </span>
                              <span className="text-white text-sm sm:text-base">
                                {formattedReward} $SHLN
                              </span>
                            </div>
                          </div>

                          <button className="w-full bg-[#72519F] text-white py-3 sm:py-4 rounded-xl hover:bg-[#6c23a3] transition-colors text-sm sm:text-base" onClick={() => claimReward(draw.id.toNumber())}>
                            <FaTrophy className="inline-block mr-2" />
                            Claim now
                          </button>
                        </div>
                      ) : matches > 2 && !claimed ? (
                        <div className="bg-[#1f2937] rounded-xl p-4 sm:p-6 mb-4 md:w-full w-[360px] sm:max-w-none">
                          {/* Date and Expires Section */}
                          <div className="flex flex-row sm:flex-row items-center justify-between gap-3 sm:gap-0 mb-4">
                            <div className="flex items-center gap-2 text-white">
                              <span className="text-yellow-400">
                                <FaRegCalendarAlt className="text-BP-yellow text-2xl" />
                              </span>
                              <span className="text-sm sm:text-base">
                              {drawDate}
                              </span>
                            </div>
                            <span className="bg-[#333732]/90 text-yellow-400 px-4 py-2 rounded-full text-sm w-fit">
                            {daysRemaining < 0 ? "Expired": `Expires in ${daysRemaining} days`}
                            </span>
                          </div>

                          {/* Numbers Section */}
                          <div className="flex justify-center sm:justify-start gap-2 sm:gap-4 mb-6 overflow-x-auto pb-2">
                            {draw.numbers.map((number, index) => {
                              const num = number.toNumber();
                              return (
                                <div
                                  key={index}
                                  className={`w-10 h-10 sm:w-12 sm:h-12 ${winningNumbers.includes(num)
                                    ? 'bg-[#72519F]'
                                    : ''
                                    } rounded-full flex items-center justify-center text-white font-medium flex-shrink-0`}
                                >
                                  {num}
                                </div>
                              )
                            })}
                          </div>

                          {/* Matched and Prize Section */}
                          <div className="flex flex-row sm:flex-row justify-between items-center mb-4 w-full">
                            <span className="text-gray-400 text-sm sm:text-base">
                              Matched {matches}/6
                            </span>
                            <div className="flex flex-col items-center">
                              <span className="text-gray-400 text-xs sm:text-sm">
                                Prize
                              </span>
                              <span className="text-white text-sm sm:text-base">
                                {formattedReward} $SHLN
                              </span>
                            </div>
                          </div>

                          <button disabled={claiming} className="w-full bg-[#72519F] text-white py-3 sm:py-4 rounded-xl hover:bg-[#6c23a3] transition-colors text-sm sm:text-base" onClick={() => {onClaimreward(draw.id.toNumber());}} >
                            Claim now
                          </button>
                        </div>
                      ) : matches <= 2 ? (
                        <div className="bg-[#1f2937] rounded-xl p-4 sm:p-6 mb-4 md:w-full w-[360px] sm:max-w-none">
                          <div className="flex flex-row sm:flex-row items-center justify-between gap-3 sm:gap-0 mb-4">
                            <div className="flex items-center gap-2 text-white">
                              <span className="text-yellow-400">
                                <FaRegCalendarAlt className="text-gray-400 text-xl sm:text-2xl" />
                              </span>
                              <span className="text-sm sm:text-base">
                              {drawDate}
                              </span>
                            </div>
                            <span className="text-gray-400 text-sm sm:text-base">
                              Not a winner
                            </span>
                          </div>

                          <div className="flex gap-2 sm:gap-4 mb-4 sm:mb-6 overflow-x-auto pb-2">
                            {draw.numbers.map((number, index) => {
                              const num = number.toNumber();
                              return (
                                <div
                                  key={index}
                                  className={`w-10 h-10 sm:w-12 sm:h-12 ${winningNumbers.includes(num) ? 'bg-[#72519F]' : ''
                                    } rounded-full flex items-center justify-center text-white font-medium flex-shrink-0 text-sm sm:text-base`}
                                >
                                  {num}
                                </div>
                              )
                            })}
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-gray-400 text-sm sm:text-base">
                              Matched {matches}/6
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="w-full">
                          <h2 className="text-lg sm:text-xl mb-4 text-white border-b border-[#8A93A7] pb-2">
                            Claimed tickets
                          </h2>
                          <div className="bg-[#1f2937] rounded-xl p-4 sm:p-6 mb-4 md:w-full w-[360px] sm:max-w-none">
                            <div className="flex flex-row sm:flex-row items-center justify-between gap-3 sm:gap-0 mb-4">
                              <div className="flex items-center gap-2 text-white">
                                <span className="text-yellow-400">
                                  <FaRegCalendarAlt className="text-gray-400 text-xl sm:text-2xl" />
                                </span>
                                <span className="text-sm sm:text-base">
                                  {drawDate}
                                </span>
                              </div>
                              <span className="text-green-500 text-sm sm:text-base">
                                Claimed
                              </span>
                            </div>

                            <div className="flex justify-center sm:justify-start gap-2 sm:gap-4 mb-6 overflow-x-auto pb-2">
                              {draw.numbers.map((number, index) => {
                                const num = number.toNumber();
                                return (
                                  <div
                                    key={index}
                                    className="w-10 h-10 sm:w-12 sm:h-12 bg-[#72519F] rounded-full flex items-center justify-center text-white font-medium flex-shrink-0 text-sm sm:text-base"
                                  >
                                    {num}
                                  </div>
                                )
                              })}
                            </div>

                            {/* Matched and Prize Section */}
                            <div className="flex flex-row sm:flex-row justify-between items-center mb-4 w-full">
                              <span className="text-gray-400 text-sm sm:text-base">
                                Matched {matches}/6
                              </span>
                              <div className="flex flex-col items-center">
                                <span className="text-gray-400 text-xs sm:text-sm">
                                  Prize
                                </span>
                                <span className="text-white text-sm sm:text-base">
                                  {formattedReward} $SHLN
                                </span>
                              </div>
                            </div>

                            <button className="w-full bg-[#72519F] text-white py-3 sm:py-4 rounded-xl opacity-50 cursor-not-allowed text-sm sm:text-base">
                              Claim now
                            </button>
                          </div>
                        </div>
                      )}
                    </>

                  )
                })
              }

            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Won;

