import React from 'react';
import { FiRefreshCw } from "react-icons/fi";
import { MdArrowBack } from 'react-icons/md';
import profit from '../../assets/sahelion/profit.png';
import calendardis from '../../assets/sahelion/calendardis.png';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import { useAuthentication } from '../../components/utils/provider';

const Completed = ({ selectedDraw, setSelectedDraw }) => {
    // Get wallet connection functions from useAuthentication
    const {
      connectWallet,
      currentUserWallet,
      disconnectWallet,
      getWeb3Signer
    } = useAuthentication();
    
  const winningNumbers = [17, 22, 36, 11, 16, 25];
  const tickets = [
    {
      numbers: [20, 13, 17, 10, 22, 32],
      matched: '2/6',
      matchedNumbers: [17, 22],
    },
    {
      numbers: [17, 13, 36, 14, 23, 13],
      matched: '1/6',
      matchedNumbers: [36],
    },
    {
      numbers: [17, 13, 36, 14, 23, 13],
      matched: '1/6',
      matchedNumbers: [36],
    },
    {
      numbers: [17, 13, 36, 14, 23, 13],
      matched: '1/6',
      matchedNumbers: [36],
    },
  ];

  return (
    <div className="min-h-screen bg-[#111828]">
      <HeaderNew />
      <div className="flex justify-center">
        <div className="md:w-[70%] w-[450px] md:min-w-[600px] px-4 py-8">
          <div className="rounded-[32px] p-8 mt-[-50px]">
            <button
              onClick={() => setSelectedDraw(null)}
              className="flex items-center gap-2 text-gray-400 mb-8 hover:text-white"
            >
              <MdArrowBack />
              Back to Activity
            </button>

            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={profit}
                  alt="Prize"
                  className="w-10 h-10 sm:w-16 sm:h-16"
                />
                <h2 className="text-[32px] font-bold text-white">
                  Your Prize Portfolio
                </h2>
              </div>

              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                <span className="text-gray-400 text-sm sm:text-base">
                  Wallet: 0x742d...3a1b
                </span>
                <button className="flex items-center gap-2 bg-[#1E2736] text-yellow-400 px-4 py-2 rounded-full hover:bg-[#252f42] transition-colors">
                  <FiRefreshCw className="text-lg sm:text-xl" />
                  Connect different address
                </button>
              </div>
            </div>
            {/* Winning Numbers */}
            <div className="mb-6 sm:mb-4 overflow-x-auto">
              <div className="space-y-4 sm:space-y-0 sm:flex sm:gap-4 min-w-max">
                <p className="text-base sm:text-lg mt-2 text-white whitespace-nowrap">
                  Winning Numbers:
                </p>

                <div className="flex gap-4">
                  {winningNumbers.map((number, idx) => (
                    <div
                      key={idx}
                      className="w-10 h-10 sm:w-12 sm:h-12 bg-[#72519F] rounded-full flex items-center justify-center text-white font-medium"
                    >
                      {number}
                    </div>
                  ))}
                </div>
              </div>

              <h3 className="text-white text-xl mt-6">Your tickets</h3>
              <div className="space-y-4 mt-4">
                {tickets.map((ticket, index) => (
                  <div key={index} className="bg-[#1f2937]/60 rounded-2xl p-6">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center gap-2 text-gray-400">
                      <img src={calendardis} alt="Prize" className="w-4 h-4 sm:w-5 sm:h-5" />
                        <span className="text-sm">15 April 2025</span>
                      </div>
                      <span className="text-gray-400 text-sm">Not a winner</span>
                    </div>

                    <div className="flex gap-6 mb-4">
                      {ticket.numbers.map((number, idx) => (
                        <div
                          key={idx}
                          className={`w-10 h-10 rounded-full ${
                            ticket.matchedNumbers.includes(number)
                              ? 'bg-[#252f42]'
                              : 'bg-transparent'
                          } flex items-center justify-center text-white`}
                        >
                          {number}
                        </div>
                      ))}
                    </div>

                    <div className="text-gray-400">
                      <span>Matched {ticket.matched}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Completed;


