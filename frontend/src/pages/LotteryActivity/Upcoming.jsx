import React from 'react';
import { MdArrowBack } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { FiRefreshCw } from 'react-icons/fi';
import profit from '../../assets/sahelion/profit.png';
import calendardis from '../../assets/sahelion/calendardis.png';
import HeaderNew from '../../components/Header/HeaderNew';
import Footer from '../../components/Footer/footer';
import { useAuthentication } from '../../components/utils/provider';

const Upcoming = ({ upcomingTickets, setSelectedDraw, lastSunday }) => {
  const { currentUserWallet } = useAuthentication();

  const formatWalletAddress = (address) => {
    if (!address) return "Not connected";
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  return (
    <div className="min-h-screen bg-[#111828]">
      <HeaderNew />
      <div className="flex justify-center">
        <div className="md:w-[70%] w-[450px] md:min-w-[600px] px-4 py-8">
          <div className="rounded-[32px] p-8 mt-[-50px]">
            <button
              onClick={() => setSelectedDraw(null)}
              className="flex items-center gap-2 text-gray-400 mb-8 hover:text-white"
            >
              <MdArrowBack />
              Back to Activity
            </button>

            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <img
                  src={profit}
                  alt="Prize"
                  className="w-10 h-10 sm:w-16 sm:h-16"
                />
                <h2 className="text-[32px] font-bold text-white">
                  Your Prize Portfolio
                </h2>
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                <span className="text-gray-400 text-sm sm:text-base">
                  Wallet: {formatWalletAddress(currentUserWallet)}
                </span>
          
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-white text-xl mb-4">Your tickets</h3>
              {upcomingTickets.map((ticket, index) => {
                return(
                <div key={index} className="bg-[#1f2937]/60 rounded-2xl p-6">
                  <div className="flex items-center justify-between text-gray-400 mb-4 w-full">
                    <div className="flex items-center gap-2 text-gray-400">
                      <img
                        src={calendardis}
                        alt="Prize"
                        className="w-4 h-4 sm:w-5 sm:h-5"
                      />
                      <span className="text-sm">{lastSunday}</span>
                    </div>
                    <span className="w-5 h-5 rounded-full border border-yellow-400 flex items-center justify-center">
                      <span className="text-yellow-400 text-xs">i</span>
                    </span>
                  </div>
                  <div className="flex gap-6">
                    {ticket.numbers.map((number, idx) => {
                      return(
                      <div
                        key={idx}
                        className="w-10 h-10 flex items-center justify-center text-white"
                      >
                        {number.toNumber()}
                      </div>
                    )})}
                  </div>
                </div>
              )})}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Upcoming;

