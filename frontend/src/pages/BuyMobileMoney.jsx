import { mpesaSTK, mpesaStatus, getBNBValue, getListOfTransactions } from "../services/paymentService";

import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import Balance from '../assets/buymoney/balance-card-hero.png';
import Momo from '../assets/buymoney/momo.png';
import Orange from '../assets/buymoney/orange.png';
import Img3 from '../assets/buymoney/images.png';
import Unnamed from '../assets/buymoney/unnamed.png';
import Walletbr from '../assets/buymoney/walletbr.png';
import { FiCheckCircle } from 'react-icons/fi';
import { FiLink } from 'react-icons/fi';
import { LuDollarSign } from "react-icons/lu";
import { IoCallOutline } from "react-icons/io5";
import { HiOutlineCog6Tooth } from 'react-icons/hi2';
import { HiOutlineInformationCircle } from 'react-icons/hi2';
import { FiChevronDown, FiRefreshCw } from 'react-icons/fi';
import { useAuthentication } from '../components/utils/provider';
import HeaderNew from '../components/Header/HeaderNew';
import Footer from '../components/Footer/footer';
import { getCountryOptions } from '../utils/countryUtils';
import ComingSoonModal from './modals/ComingSoonModal';
import PaymentSuccessModal from './modals/PaymentSuccessModal';
import PaymentProcessingModal from './modals/PaymentProcessingModal';
import { convertUSDToLocalCurrency, formatCurrencyAmount, getCurrencyInfo } from '../utils/currencyUtils';
import CurrencyService from '../services/currencyService';

// Function to format phone number for M-Pesa
const formatPhoneForMpesa = (phoneNumber, countryCode) => {
    let cleanPhone = phoneNumber.replace(/\D/g, '');
    let cleanCountryCode = countryCode.replace(/\D/g, '');

    // For Kenya specifically (254), ensure proper formatting
    if (cleanCountryCode === '254') {
        if (cleanPhone.startsWith('0')) {
            cleanPhone = cleanPhone.substring(1);
        }

        // If phone starts with 254, don't add it again
        if (cleanPhone.startsWith('254')) {
            return cleanPhone;
        }

        // Add Kenya country code
        return `254${cleanPhone}`;
    }

    // For other countries, just combine cleanly
    return `${cleanCountryCode}${cleanPhone}`;
};

// Function to validate phone number for M-Pesa
const validatePhoneForMpesa = (phoneNumber, countryCode) => {
    const formatted = formatPhoneForMpesa(phoneNumber, countryCode);

    // For Kenya, ensure it's exactly 12 digits and starts with 254
    if (countryCode.includes('254')) {
        return formatted.length === 12 && formatted.startsWith('254');
    }

    // For other countries, basic validation
    return formatted.length >= 10 && formatted.length <= 15;
};

export const BuyMobileMoney = () => {
    const [selectedProvider, setSelectedProvider] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [amount, setAmount] = useState('');
    const [bnbAmount, setBnbAmount] = useState('0.000000');
    const [localCurrencyAmount, setLocalCurrencyAmount] = useState('');
    const [currencyConversion, setCurrencyConversion] = useState(null);
    const [isConvertingCurrency, setIsConvertingCurrency] = useState(false);
    const [walletAddress, setWalletAddress] = useState('');
    const [isWalletConnected, setIsWalletConnected] = useState(false);
    const [selectedCountry, setSelectedCountry] = useState(null);
    const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
    const [countrySearchTerm, setCountrySearchTerm] = useState('');
    const [isCheckingWallet, setIsCheckingWallet] = useState(true); // Add loading state
    const [bnbRate, setBnbRate] = useState(500); // Dynamic BNB rate
    const [isLoadingRate, setIsLoadingRate] = useState(true); // Loading state for rate
    const [validationErrors, setValidationErrors] = useState({
        provider: false,
        phoneNumber: false,
        amount: false
    });

    const [mpesaSessionID, setmpesaSessionID] = useState('')
    const [isProcessingPayment, setIsProcessingPayment] = useState(false)
    const pollingIntervalRef = useRef(null);
    const [showValidationModal, setShowValidationModal] = useState(false);
    const [validationMessage, setValidationMessage] = useState('');
    const [showComingSoonModal, setShowComingSoonModal] = useState(false);
    const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);
    const [paymentSuccessData, setPaymentSuccessData] = useState({});
    const [successTransactionHash, setSuccessTransactionHash] = useState('');
    const [bnbTransactionHistory, setBnbTransactionHistory] = useState([]);
    const [selectedTxn, setSelectedTxn] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    const { currentUserWallet, connectWallet, disconnectWallet } = useAuthentication();

    // Transaction fee configuration
    // const transactionFeePercentage = 5;
    const countryOptions = getCountryOptions();

    // Function to stop polling
    const stopPolling = () => {
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
        }
        setIsProcessingPayment(false);
    };

    const getCountriesWithKenyaFirst = () => {
        const kenya = countryOptions.find(country => country.value === 'Kenya');
        const otherCountries = countryOptions.filter(country => country.value !== 'Kenya');
        return kenya ? [kenya, ...otherCountries] : countryOptions;
    };

    const sortedCountryOptions = getCountriesWithKenyaFirst();

    const filteredCountryOptions = sortedCountryOptions.filter(country =>
        country.value.toLowerCase().includes(countrySearchTerm.toLowerCase()) ||
        country.valueSec.includes(countrySearchTerm)
    );

    const providers = [
        { id: 'mpesa', name: 'M-pesa', icon: Unnamed },
        { id: 'airtel', name: 'Airtel', icon: Img3 },
        { id: 'momo', name: 'Momo', icon: Momo },
        { id: 'orange', name: 'Orange', icon: Orange },
        { id: 'creditcards', name: 'Credit cards', icon: Balance }
    ];

    useEffect(() => {
        // Initialize wallet connection check
        const initializeWallet = async () => {
            setIsCheckingWallet(true);
            if (currentUserWallet) {
                setIsWalletConnected(true);
                setWalletAddress(currentUserWallet);
                localStorage.setItem('connectedWallet', currentUserWallet);
            } else {
                setIsWalletConnected(false);
                setWalletAddress('');
                const savedWallet = localStorage.getItem('connectedWallet');
                if (savedWallet) {
                    localStorage.removeItem('connectedWallet');
                }
            }

            setIsCheckingWallet(false);
        };

        initializeWallet();
    }, [currentUserWallet]);

    // Set default country to Kenya
    useEffect(() => {
        if (sortedCountryOptions.length > 0 && !selectedCountry) {
            setSelectedCountry(sortedCountryOptions[0]);
        }
    }, [sortedCountryOptions, selectedCountry]);

    // Convert currency when country changes
    useEffect(() => {
        const convertCurrencyOnCountryChange = async () => {
            if (selectedCountry && amount && parseFloat(amount) > 0) {
                setIsConvertingCurrency(true);
                try {
                    const conversion = await convertUSDToLocalCurrency(amount, selectedCountry.value);
                    setCurrencyConversion(conversion);
                    setLocalCurrencyAmount(conversion.formattedAmount);
                } catch (error) {
                    console.error('Currency conversion failed:', error);
                    // Fallback to showing USD amount
                    const currencyInfo = getCurrencyInfo(selectedCountry.value);
                    setLocalCurrencyAmount(`${currencyInfo.symbol}${amount}`);
                } finally {
                    setIsConvertingCurrency(false);
                }
            } else {
                setLocalCurrencyAmount('');
                setCurrencyConversion(null);
            }
        };

        convertCurrencyOnCountryChange();
    }, [selectedCountry, amount]);

    // Fetch BNB rate on component mount
    useEffect(() => {
        const fetchBNBRate = async () => {
            setIsLoadingRate(true);
            try {
                const rate = await getBNBValue();
                setBnbRate(rate);
            } catch (error) {
                console.error("Failed to fetch BNB rate:", error);
                // Keep default rate of 500 if fetch fails
                setBnbRate(500);
                toast.error("Failed to fetch current BNB rate. Using default rate.");
            } finally {
                setIsLoadingRate(false);
            }
        };

        fetchBNBRate();
    }, []);

    const refreshBNBRate = async () => {
        setIsLoadingRate(true);
        try {
            const rate = await getBNBValue();
            setBnbRate(rate);
            toast.success("BNB rate updated successfully!");

            // Recalculate BNB amount if there's an amount entered
            if (amount && !isNaN(amount)) {
                const bnb = parseFloat(amount) / rate;
                setBnbAmount(bnb.toFixed(6));
            }
        } catch (error) {
            console.error("Failed to refresh BNB rate:", error);
            toast.error("Failed to refresh BNB rate. Please try again.");
        } finally {
            setIsLoadingRate(false);
        }
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (isCountryDropdownOpen && !event.target.closest('.country-dropdown')) {
                setIsCountryDropdownOpen(false);
                setCountrySearchTerm('');
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isCountryDropdownOpen]);

    // Cleanup polling interval on component unmount
    useEffect(() => {
        return () => {
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = null;
            }
        };
    }, []);

    // Handle ESC key to close modal
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape') {
                if (showValidationModal) {
                    setShowValidationModal(false);
                }
                if (showComingSoonModal) {
                    setShowComingSoonModal(false);
                }
                if (showPaymentSuccessModal) {
                    handlePaymentSuccessClose();
                }
            }
        };

        document.addEventListener('keydown', handleEscKey);
        return () => {
            document.removeEventListener('keydown', handleEscKey);
        };
    }, [showValidationModal, showComingSoonModal, showPaymentSuccessModal]);

    // Handle page visibility changes (when user comes back to the tab)
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden && !currentUserWallet) {
                // When page becomes visible and no wallet is connected, just check localStorage
                const savedWallet = localStorage.getItem('connectedWallet');
                if (savedWallet) {
                    // Clean up localStorage if wallet is not actually connected
                    localStorage.removeItem('connectedWallet');
                }
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [currentUserWallet]);

    useEffect(() => {
        const GetListOfTransactions = async () => {
            const response = await getListOfTransactions()
            setBnbTransactionHistory(response);
        }

        GetListOfTransactions();
    }, [successTransactionHash])

    const handleRowClick = (txn) => {
        console.log("Selected transaction:", txn);
        setSelectedTxn(txn);
        setIsModalOpen(true);
    };

    // Handle wallet connection
    const handleConnectWallet = async () => {
        if (currentUserWallet) {
            // Disconnect wallet
            await disconnectWallet();
            localStorage.removeItem('connectedWallet');
            setIsWalletConnected(false);
            setWalletAddress('');
        } else {
            // Connect wallet
            try {
                await connectWallet();
                // The useEffect will handle setting the state once currentUserWallet updates
            } catch (error) {
                console.error('Failed to connect wallet:', error);
            }
        }
    };

    const handleAmountChange = async (e) => {
        const value = e.target.value;
        setAmount(value);

        if (value && !isNaN(value)) {
            const bnb = parseFloat(value) / bnbRate;
            setBnbAmount(bnb.toFixed(6));

            // Convert to local currency if country is selected and amount is valid
            if (selectedCountry && parseFloat(value) > 0) {
                setIsConvertingCurrency(true);
                try {
                    const conversion = await convertUSDToLocalCurrency(value, selectedCountry.value);
                    setCurrencyConversion(conversion);
                    setLocalCurrencyAmount(conversion.formattedAmount);
                } catch (error) {
                    console.error('Currency conversion failed:', error);
                    // Fallback to showing USD amount
                    const currencyInfo = getCurrencyInfo(selectedCountry.value);
                    setLocalCurrencyAmount(`${currencyInfo.symbol}${value}`);
                } finally {
                    setIsConvertingCurrency(false);
                }
            } else {
                setLocalCurrencyAmount('');
                setCurrencyConversion(null);
            }
        } else {
            setBnbAmount('0.000000');
            setLocalCurrencyAmount('');
            setCurrencyConversion(null);
        }

        // Clear amount validation error when valid amount is entered
        if (value && parseFloat(value) > 0 && validationErrors.amount) {
            setValidationErrors(prev => ({ ...prev, amount: false }));
        }
    };

    // Calculate transaction fees
    const calculateFees = () => {
        if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
            return {
                transactionFee: 0,
                totalFees: 0,
                finalAmount: 0,
                transactionFeeLocal: 0,
                totalFeesLocal: 0,
                finalAmountLocal: 0
            };
        }

        const amountValue = parseFloat(amount);

        // Tiered fee structure
        let transactionFee;
        let transactionFeePercentage;
        
        if (amountValue >= 1 && amountValue <= 4) {
            transactionFee = 0.25; // Fixed calculation for amounts $1-$4
        } else if (amountValue >= 5) {
            transactionFee = (5 * amountValue) / 100; // 5% for amounts $5 and above
            transactionFeePercentage = 5; // 5% for amounts $5 and above
        } else {
            transactionFee = 0; // No fee for amounts below $1
            transactionFeePercentage = 0; // No fee for amounts below $1
        }

        // USD calculations
        const totalFees = transactionFee;
        const finalAmount = amountValue + totalFees; // No rounding for USD

        // Local currency calculations (if conversion is available)
        let transactionFeeLocal = 0;
        let totalFeesLocal = 0;
        let finalAmountLocal = 0;

        if (currencyConversion && currencyConversion.success) {
            const localAmount = currencyConversion.convertedAmount;
            // Convert the USD fee to local currency (not apply fee logic to local amount)
            transactionFeeLocal = transactionFee * currencyConversion.rate;
            totalFeesLocal = transactionFeeLocal;
            finalAmountLocal = localAmount + totalFeesLocal; // No rounding for local currency
        }

        return {
            transactionFee: transactionFee,
            totalFees: totalFees,
            finalAmount: finalAmount,
            transactionFeeLocal: transactionFeeLocal,
            totalFeesLocal: totalFeesLocal,
            finalAmountLocal: finalAmountLocal,
            feePercentage: transactionFeePercentage
        };
    };

    const feeCalculation = calculateFees();

    const handleProviderSelect = (providerId) => {
        // Only M-pesa is available, show coming soon modal for others
        if (providerId !== 'mpesa') {
            setShowComingSoonModal(true);
            return;
        }

        // Allow selecting and unselecting M-pesa
        setSelectedProvider(selectedProvider === providerId ? '' : providerId);
        // Clear provider validation error when M-pesa is selected
        if (providerId && validationErrors.provider) {
            setValidationErrors(prev => ({ ...prev, provider: false }));
        }
    };

    const handleBuyBNB = async () => {
        if (!isWalletConnected) {
            handleConnectWallet();
            return;
        }

        // Validate required fields
        const isValidPhone = selectedCountry
            ? validatePhoneForMpesa(phoneNumber, selectedCountry.valueSec)
            : phoneNumber.trim().length > 0;

        const errors = {
            provider: !selectedProvider,
            phoneNumber: !phoneNumber.trim() || !isValidPhone,
            amount: !amount || amount.trim() === '' || parseFloat(amount) <= 0
        };

        setValidationErrors(errors);

        // If there are any errors, don't proceed
        if (errors.provider || errors.phoneNumber || errors.amount) {
            let message = '';

            if (errors.provider) {
                message = ' You have not selected a payment provider. Please choose M-pesa, Airtel, Momo, Orange, or Credit cards to continue.';
            } else if (errors.phoneNumber && phoneNumber.trim() && !isValidPhone) {
                message = 'Please enter a valid phone number. Use format like: 712345678';
            } else if (errors.phoneNumber) {
                message = 'Please enter your phone number to proceed with the payment.';
            } else if (errors.amount) {
                message = 'Please enter a valid amount greater than $0.00 to continue.';
            }

            setValidationMessage(message);
            setShowValidationModal(true);
            return;
        }

        const formattedPhone = selectedCountry
            ? formatPhoneForMpesa(phoneNumber, selectedCountry.valueSec)
            : phoneNumber;

        // Calculate final amount including fees
        const finalPaymentAmount = currencyConversion?.success ? feeCalculation.finalAmountLocal : feeCalculation.finalAmount;

        // Prepare base payload
        const payload = {
            phone: formattedPhone,
            amount: parseFloat(finalPaymentAmount.toFixed(2)), // Use final amount including fees in local currency
            bnbAmount: parseFloat(parseFloat(amount).toFixed(8)), // Original BNB amount requested
            walletAddress: walletAddress,
            provider: selectedProvider,
            country: selectedCountry.value,
            countryCode: selectedCountry.valueSec,
            // Enhanced currency data
            currency: {
                original: {
                    value: parseFloat(amount),
                    currency: 'USD',
                    formatted: `$${parseFloat(amount).toFixed(2)}`
                },
                converted: currencyConversion?.success ? {
                    value: currencyConversion.convertedAmount,
                    currency: currencyConversion.currencyInfo.code,
                    symbol: currencyConversion.currencyInfo.symbol,
                    formatted: currencyConversion.formattedAmount
                } : null,
                exchangeRate: currencyConversion?.success ? {
                    rate: currencyConversion.rate,
                    fromCurrency: 'USD',
                    toCurrency: currencyConversion.currencyInfo.code,
                    timestamp: currencyConversion.timestamp
                } : null,
                conversionSuccess: currencyConversion?.success || false
            },
            // Amount to charge (in local currency if conversion succeeded, otherwise USD)
            chargeAmount: currencyConversion?.success ? feeCalculation.finalAmountLocal : feeCalculation.finalAmount,
            chargeCurrency: currencyConversion?.success ? currencyConversion.currencyInfo.code : 'USD',
            // Legacy fields for existing backend compatibility
            localCurrency: {
                amount: currencyConversion?.convertedAmount || parseFloat(amount),
                currencyCode: currencyConversion?.currencyInfo?.code || 'USD',
                currencySymbol: currencyConversion?.currencyInfo?.symbol || '$',
                formattedAmount: localCurrencyAmount || `$${amount}`,
                exchangeRate: currencyConversion?.rate || 1,
                isConverted: currencyConversion?.success || false
            },
            fees: {
                transactionFee: feeCalculation.transactionFee,
                totalFees: feeCalculation.totalFees,
                // Local currency fees
                transactionFeeLocal: feeCalculation.transactionFeeLocal,
                totalFeesLocal: feeCalculation.totalFeesLocal,
                // Send the appropriate fee amounts based on currency
                chargeTransactionFee: currencyConversion?.success ? feeCalculation.transactionFeeLocal : feeCalculation.transactionFee,
                chargeTotalFees: currencyConversion?.success ? feeCalculation.totalFeesLocal : feeCalculation.totalFees,
                feePercentage: feeCalculation.feePercentage
            }
        };

        setIsProcessingPayment(true); // Start processing payment

        mpesaSTK(payload)
            .then((data) => {
                const { sessionID, errorMessage, message } = data;

                if (sessionID) {

                    // Clear any existing polling interval
                    if (pollingIntervalRef.current) {
                        clearInterval(pollingIntervalRef.current);
                        pollingIntervalRef.current = null;
                    }

                    // Polling for the status with timeout and retry limits
                    let pollCount = 0;
                    const maxPolls = 90; // Maximum 30 polls (3 minute at 2-second intervals)

                    pollingIntervalRef.current = setInterval(async () => {
                        pollCount++;

                        // Stop polling after maximum attempts
                        if (pollCount > maxPolls) {
                            clearInterval(pollingIntervalRef.current);
                            pollingIntervalRef.current = null;
                            setIsProcessingPayment(false);
                            toast.warning("Payment status check timed out. Please check your M-Pesa messages. If you have been deducted but did not receive BNB please contact support");
                            return;
                        }

                        try {
                            const statusData = await mpesaStatus(sessionID);
                            // console.log(`Payment status check (${pollCount}/${maxPolls}):`, statusData);

                            if (statusData?.status === 'pending' || statusData?.bnbStatus === 'pending') {
                                // Continue polling
                            } else if (statusData?.status === 'success') {
                                // Prepare success data
                                const successData = {
                                    amount: amount, // Original BNB amount
                                    finalAmount: finalPaymentAmount, // Total amount paid including fees
                                    bnbAmount: bnbAmount,
                                    phoneNumber: formattedPhone,
                                    transactionId: sessionID,
                                    rate: bnbRate,
                                    country: selectedCountry.value,
                                    countryCode: selectedCountry.valueSec,
                                    // Enhanced currency data
                                    currency: currencyConversion?.success ? {
                                        original: {
                                            value: parseFloat(amount),
                                            currency: 'USD',
                                            formatted: `$${parseFloat(amount).toFixed(2)}`
                                        },
                                        converted: {
                                            value: currencyConversion.convertedAmount,
                                            currency: currencyConversion.currencyInfo.code,
                                            symbol: currencyConversion.currencyInfo.symbol,
                                            formatted: currencyConversion.formattedAmount
                                        },
                                        exchangeRate: {
                                            rate: currencyConversion.rate,
                                            fromCurrency: 'USD',
                                            toCurrency: currencyConversion.currencyInfo.code,
                                            timestamp: currencyConversion.timestamp
                                        },
                                        conversionSuccess: true
                                    } : null,
                                    // Legacy compatibility
                                    localCurrency: currencyConversion?.success ? {
                                        amount: currencyConversion.convertedAmount,
                                        currencyCode: currencyConversion.currencyInfo.code,
                                        currencySymbol: currencyConversion.currencyInfo.symbol,
                                        formattedAmount: currencyConversion.formattedAmount,
                                        exchangeRate: currencyConversion.rate,
                                        isConverted: true
                                    } : null,
                                    chargeAmount: currencyConversion?.success ? feeCalculation.finalAmountLocal : feeCalculation.finalAmount,
                                    chargeCurrency: currencyConversion?.success ? currencyConversion.currencyInfo.code : 'USD',
                                    fees: {
                                        transactionFee: feeCalculation.transactionFee,
                                        totalFees: feeCalculation.totalFees,
                                        transactionFeeLocal: feeCalculation.transactionFeeLocal,
                                        totalFeesLocal: feeCalculation.totalFeesLocal,
                                        finalAmountLocal: feeCalculation.finalAmountLocal,
                                        transactionFeePercentage: feeCalculation.feePercentage
                                    }
                                };

                                console.log("Payment success data:", statusData);

                                setPaymentSuccessData(successData);
                                setSuccessTransactionHash(statusData.transactionHash);
                                setShowPaymentSuccessModal(true);

                                clearInterval(pollingIntervalRef.current);
                                pollingIntervalRef.current = null;
                                setIsProcessingPayment(false);
                            } else if (statusData?.status === 'cancelled' || statusData?.status === 'canceled') {
                                // Handle cancelled payment silently - user cancelled the transaction
                                clearInterval(pollingIntervalRef.current);
                                pollingIntervalRef.current = null;
                                setIsProcessingPayment(false);
                            } else if (statusData?.status && statusData.status.startsWith('failed')) {
                                toast.error(`Payment Failed: ${statusData?.status || 'Unknown error'}`);
                                clearInterval(pollingIntervalRef.current);
                                pollingIntervalRef.current = null;
                                setIsProcessingPayment(false);
                            } else {
                                // Unknown status, continue polling but log it
                                console.warn("Unknown payment status:", statusData?.status);
                            }
                        } catch (error) {
                            console.error("Polling error:", error.message || error);

                            // On error, stop after 3 consecutive failures
                            if (pollCount >= 3) {
                                toast.error("Error checking payment status. Please check your M-Pesa messages.");
                                clearInterval(pollingIntervalRef.current);
                                pollingIntervalRef.current = null;
                                setIsProcessingPayment(false);
                            }
                        }
                    }, 2000);

                } else {
                    // Handle the error with a popup
                    toast.error(errorMessage || message || "Failed to initiate payment");
                    setIsProcessingPayment(false); // Stop processing payment
                }
            })
            .catch((error) => {
                console.error("M-Pesa STK Error:", error);
                toast.error(error.errorMessage || "Failed to send payment request.");
                setIsProcessingPayment(false); // Stop processing payment
            });


        // TODO: Implement actual buy BNB logic here
        const fullPhoneNumber = selectedCountry
            ? formatPhoneForMpesa(phoneNumber, selectedCountry.valueSec)
            : phoneNumber;


    };

    // Add custom animations
    const modalStyles = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-6px); }
            60% { transform: translateY(-3px); }
        }
        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(45, 212, 191, 0.5); }
            50% { box-shadow: 0 0 20px rgba(45, 212, 191, 0.8), 0 0 30px rgba(45, 212, 191, 0.6); }
        }
        .animate-fadeIn {
            animation: fadeIn 0.2s ease-out;
        }
        .animate-scaleIn {
            animation: scaleIn 0.2s ease-out;
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        .animate-bounce-delay {
            animation: bounce 1.4s infinite;
        }
        .animate-glow {
            animation: glow 2s ease-in-out infinite;
        }
    `;

    // Validation Modal Component
    const ValidationModal = () => {
        if (!showValidationModal) return null;

        return (
            <>
                <style>{modalStyles}</style>
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn p-2 sm:p-4"
                    onClick={() => setShowValidationModal(false)}
                >
                    <div
                        className="bg-slate-800 border border-slate-600 rounded-lg sm:rounded-xl p-4 sm:p-6 max-w-xs sm:max-w-md mx-2 sm:mx-4 shadow-2xl transform animate-scaleIn"
                        onClick={(e) => e.stopPropagation()}
                    >
                        <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
                            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-red-500/20 rounded-full flex items-center justify-center">
                                {validationMessage.includes('provider') ? (
                                    <HiOutlineCog6Tooth className="w-4 h-4 sm:w-6 sm:h-6 text-red-400" />
                                ) : validationMessage.includes('phone') ? (
                                    <IoCallOutline className="w-4 h-4 sm:w-6 sm:h-6 text-red-400" />
                                ) : (
                                    <LuDollarSign className="w-4 h-4 sm:w-6 sm:h-6 text-red-400" />
                                )}
                            </div>
                            <h3 className="text-lg sm:text-xl font-semibold text-white">Required Field Missing</h3>
                        </div>

                        <p className="text-gray-300 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
                            {validationMessage}
                        </p>

                        <button
                            onClick={() => setShowValidationModal(false)}
                            className="w-full bg-teal-400 hover:bg-teal-500 text-white font-semibold py-2.5 sm:py-3 px-3 sm:px-4 rounded-lg transition-colors text-sm sm:text-base"
                        >
                            Got it
                        </button>
                    </div>
                </div>
            </>
        );
    };

    // Handle payment success modal close
    const handlePaymentSuccessClose = () => {
        setShowPaymentSuccessModal(false);
        // Reset form fields for new transaction
        setAmount('');
        setPhoneNumber('');
        setSelectedProvider('');
        setBnbAmount('0.000000');
        setLocalCurrencyAmount('');
        setCurrencyConversion(null);
        setValidationErrors({
            provider: false,
            phoneNumber: false,
            amount: false
        });
    };

    // Provider availability by country
    const getProviderAvailability = (countryName) => {
        const supportedProviders = {
            // East Africa - M-Pesa supported
            'Kenya': ['mpesa'],
            'Tanzania': ['mpesa'],
            'Uganda': ['mpesa'],
            'Rwanda': ['mpesa'],
            'Ethiopia': ['mpesa'],
            'Ghana': ['mpesa'],
            'Lesotho': ['mpesa'],
            'Democratic Republic of the Congo': ['mpesa'],
            'Mozambique': ['mpesa'],
            'Egypt': ['mpesa'],

            // West/Central Africa - Orange Money, MTN MoMo more common
            'Cameroon': ['orange', 'momo'], // Orange Money and MTN Mobile Money
            'Senegal': ['orange', 'momo'],
            'Côte d\'Ivoire': ['orange', 'momo'],
            'Mali': ['orange', 'momo'],
            'Burkina Faso': ['orange', 'momo'],
            'Niger': ['orange', 'momo'],
            'Guinea': ['orange', 'momo'],
            'Benin': ['orange', 'momo'],
            'Togo': ['orange', 'momo'],
            'Central African Republic': ['orange', 'momo'],
            'Chad': ['orange', 'momo'],
            'Gabon': ['orange', 'momo'],
            'Equatorial Guinea': ['orange', 'momo'],
            'Republic of the Congo': ['orange', 'momo'],
            'Madagascar': ['orange', 'momo'],

            // West Africa - Additional providers
            'Nigeria': ['momo'], // MTN MoMo, but also others
            'Sierra Leone': ['orange', 'momo'],
            'Liberia': ['orange', 'momo'],
            'Mauritania': ['orange', 'momo'],
            'The Gambia': ['orange', 'momo'],

            // Southern Africa
            'South Africa': ['momo'], // MTN and others
            'Botswana': ['orange', 'momo'],
            'Namibia': ['momo'],
            'Zambia': ['momo'],
            'Zimbabwe': ['momo'],
            'Malawi': ['airtel', 'momo'],

            // Countries with credit card support (global)
            'United States': ['creditcards'],
            'United Kingdom': ['creditcards'],
            'Canada': ['creditcards'],
            'Australia': ['creditcards'],
            'Germany': ['creditcards'],
            'France': ['creditcards'],
            'Italy': ['creditcards'],
            'Spain': ['creditcards'],
            'Netherlands': ['creditcards'],
            'Belgium': ['creditcards'],
            'Austria': ['creditcards'],
            'Ireland': ['creditcards'],
            'Portugal': ['creditcards'],
            'Finland': ['creditcards'],
            'Greece': ['creditcards'],
            'Japan': ['creditcards'],
            'Singapore': ['creditcards'],
            'South Korea': ['creditcards'],
        };

        return supportedProviders[countryName] || ['creditcards']; // Default to credit cards if country not specified
    };

    // Check if a provider is supported in the selected country
    const isProviderSupportedInCountry = (providerId, countryName) => {
        const supportedProviders = getProviderAvailability(countryName);
        return supportedProviders.includes(providerId);
    };

    return (
        <div className="min-h-screen bg-slate-900 text-white">
            {/* Add the styles to the page */}
            <style>{modalStyles}</style>
            <HeaderNew />
            <div className="p-2 sm:p-4 pt-8 sm:pt-12">
                {/* Logo Section */}
                {/* <div className="flex justify-center pt-8 pb-4">
                    <div className="w-fit mx-auto p-2 rounded-xl shadow-xl border border-slate-600">
                        <Link to="/">
                            <img src={queenpanther} alt="Logo" className="h-20 w-20" />
                        </Link>
                    </div>
                </div> */}
                <div className="max-w-5xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-8 w-full">


                    {/* Left Panel - Buy BNB with Mobile Money */}
                    <div className="bg-slate-800 border border-slate-600 rounded-xl sm:rounded-2xl p-4 sm:p-8 w-full max-w-2xl mx-auto">
                        <div className="mb-6 sm:mb-8">
                            <h1 className="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">Buy BNB with Mobile Money</h1>
                            <p className="text-gray-400 text-sm sm:text-base">Get BNB instantly sent to your wallet</p>
                        </div>

                        {/* Connect Wallet Button */}
                        {isCheckingWallet ? (
                            <button
                                disabled
                                className="bg-slate-600 w-full text-gray-300 font-medium text-[16px] sm:text-[18px] px-4 sm:px-6 py-3 sm:py-4 rounded-lg flex items-center justify-center gap-3 mt-2"
                            >
                                <div className="w-5 h-5 border-2 border-gray-300/30 border-t-gray-300 rounded-full animate-spin"></div>
                                Checking wallet...
                            </button>
                        ) : !isWalletConnected ? (
                            <button
                                onClick={handleConnectWallet}
                                className="bg-[#E1AB0D] hover:bg-[#e1a80d] w-full text-black font-medium text-[18px] sm:text-[24px] px-4 sm:px-6 py-3 sm:py-4 rounded-lg flex items-center justify-start gap-6 sm:gap-14 mt-2 transition-colors"
                            >
                                <img src={Walletbr} alt="Wallet" className="w-6 h-6 sm:w-8 sm:h-8" />
                                Connect Wallet
                            </button>
                        ) : (
                            <button
                                onClick={handleConnectWallet}
                                className="w-full mt-2 px-4 sm:px-6 py-3 sm:py-4 rounded-lg bg-[#4FD1C5]/10 border border-[#4FD1C5] flex items-center text-[#4FD1C5] text-[16px] sm:text-[18px] font-medium hover:bg-[#4FD1C5]/20 transition-colors"
                            >
                                <FiLink className="h-5 w-5 text-[#4FD1C5] mr-3" />
                                Connected: {walletAddress.slice(0, 6)}...{walletAddress.slice(-4)} (Click to disconnect)
                            </button>
                        )}


                        {/* Select Provider */}
                        <div className="mb-4 sm:mb-6">
                            <div className="flex items-center gap-2 mt-3 sm:mt-4">
                                <HiOutlineCog6Tooth className="w-4 h-4 text-yellow-500" />
                                <h3 className="text-lg sm:text-xl font-semibold">
                                    Select Provider
                                    {validationErrors.provider && <span className="text-red-500 ml-1">*</span>}
                                </h3>
                            </div>

                            <div className="grid grid-cols-2 mt-3 sm:mt-4 gap-2 sm:gap-4">
                                {providers.slice(0, 4).map((provider) => {
                                    const isAvailable = provider.id === 'mpesa';
                                    return (
                                        <button
                                            key={provider.id}
                                            onClick={() => handleProviderSelect(provider.id)}
                                            className={`p-3 sm:p-5 rounded-lg sm:rounded-xl border-2 transition-all duration-200 relative ${selectedProvider === provider.id
                                                ? 'border-teal-400 bg-slate-700 shadow-lg'
                                                : isAvailable
                                                    ? 'border-slate-600 bg-slate-800 hover:border-slate-500'
                                                    : 'border-slate-600 bg-slate-800 opacity-60 hover:border-slate-500'
                                                }`}
                                        >
                                            <div className="flex flex-col items-center gap-1 sm:gap-2">
                                                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-[12px] sm:rounded-[16px] flex items-center justify-center">
                                                    <img src={provider.icon} alt={provider.name} className="w-8 h-8 sm:w-10 sm:h-10 object-contain" />
                                                </div>
                                                <span className="font-medium text-xs sm:text-sm">{provider.name}</span>
                                                {/* {!isAvailable && (
                                                    <span className="text-yellow-400 text-[10px] sm:text-xs font-medium">Coming Soon</span>
                                                )} */}
                                            </div>
                                        </button>
                                    );
                                })}
                            </div>

                            {/* Second row for Orange and Credit cards */}
                            <div className="grid grid-cols-2 gap-2 sm:gap-4 mt-2 sm:mt-4">
                                {providers.slice(4).map((provider) => {
                                    const isAvailable = provider.id === 'mpesa';
                                    return (
                                        <button
                                            key={provider.id}
                                            onClick={() => handleProviderSelect(provider.id)}
                                            className={`p-3 sm:p-5 rounded-lg sm:rounded-xl border-2 transition-all duration-200 relative ${selectedProvider === provider.id
                                                ? 'border-teal-400 bg-slate-700 shadow-lg'
                                                : isAvailable
                                                    ? 'border-slate-600 bg-slate-800 hover:border-slate-500'
                                                    : 'border-slate-600 bg-slate-800 opacity-60 hover:border-slate-500'
                                                }`}
                                        >
                                            <div className="flex flex-col items-center gap-1 sm:gap-2">
                                                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-md sm:rounded-lg flex items-center justify-center">
                                                    <img src={provider.icon} alt={provider.name} className="w-8 h-8 sm:w-10 sm:h-10 object-contain" />
                                                </div>
                                                <span className="font-medium text-xs sm:text-sm">{provider.name}</span>
                                                {/* {!isAvailable && (
                                                    <span className="text-yellow-400 text-[10px] sm:text-xs font-medium">Coming Soon</span>
                                                )} */}
                                            </div>
                                        </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Phone Number */}
                        <div className="mb-6">
                            <div className="flex items-center gap-2 mb-3">
                                <IoCallOutline className="w-5 h-5 sm:w-6 sm:h-6 text-BP-yellow" />
                                <label className="text-lg sm:text-xl font-semibold">
                                    Phone number
                                    {validationErrors.phoneNumber && <span className="text-red-500 ml-1">*</span>}
                                </label>
                            </div>
                            <div className="flex gap-2">
                                {/* Country Code Dropdown */}
                                <div className="relative country-dropdown">
                                    <button
                                        type="button"
                                        onClick={() => setIsCountryDropdownOpen(!isCountryDropdownOpen)}
                                        className={`bg-slate-700/40 border rounded-lg px-2 sm:px-3 py-3 sm:py-4 text-white focus:outline-none text-sm sm:text-lg flex items-center gap-1 sm:gap-2 min-w-[90px] sm:min-w-[120px] ${validationErrors.phoneNumber ? 'border-red-500' : 'border-slate-600'
                                            }`}
                                    >
                                        <span className="text-xs sm:text-sm">
                                            {selectedCountry ? selectedCountry.valueSec : '+254'}
                                        </span>
                                        <FiChevronDown className={`w-3 h-3 sm:w-4 sm:h-4 transition-transform ${isCountryDropdownOpen ? 'rotate-180' : ''}`} />
                                    </button>

                                    {isCountryDropdownOpen && (
                                        <div className="absolute top-full left-0 mt-1 w-72 sm:w-80 bg-slate-700 border border-slate-600 rounded-lg shadow-lg z-50 max-w-[calc(100vw-2rem)]">
                                            {/* Search Input */}
                                            <div className="p-2 sm:p-3 border-b border-slate-600">
                                                <input
                                                    type="text"
                                                    placeholder="Search countries..."
                                                    value={countrySearchTerm}
                                                    onChange={(e) => setCountrySearchTerm(e.target.value)}
                                                    className="w-full bg-slate-600 border border-slate-500 rounded px-2 sm:px-3 py-1.5 sm:py-2 text-white text-xs sm:text-sm focus:outline-none focus:border-slate-400"
                                                    onClick={(e) => e.stopPropagation()}
                                                />
                                            </div>

                                            {/* Country List */}
                                            <div className="max-h-40 sm:max-h-48 overflow-y-auto">
                                                {filteredCountryOptions.length > 0 ? (
                                                    filteredCountryOptions.map((country, index) => (
                                                        <button
                                                            key={index}
                                                            type="button"
                                                            onClick={() => {
                                                                setSelectedCountry(country);
                                                                setIsCountryDropdownOpen(false);
                                                                setCountrySearchTerm('');
                                                            }}
                                                            className="w-full text-left px-3 sm:px-4 py-2 sm:py-3 hover:bg-slate-600 text-white text-xs sm:text-sm flex justify-between items-center"
                                                        >
                                                            <span className="truncate pr-2">{country.value}</span>
                                                            <span className="text-gray-400 flex-shrink-0">{country.valueSec}</span>
                                                        </button>
                                                    ))
                                                ) : (
                                                    <div className="px-3 sm:px-4 py-2 sm:py-3 text-gray-400 text-xs sm:text-sm">
                                                        No countries found
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Phone Number Input */}
                                <input
                                    type="tel"
                                    value={phoneNumber}
                                    onChange={(e) => {
                                        setPhoneNumber(e.target.value);
                                        // Clear phone number validation error when user starts typing
                                        if (e.target.value.trim() && validationErrors.phoneNumber) {
                                            setValidationErrors(prev => ({ ...prev, phoneNumber: false }));
                                        }
                                    }}
                                    placeholder="e.g 712345678(without 0)"
                                    className={`flex-1 bg-slate-700/40 border rounded-lg px-3 sm:px-4 text-white placeholder-gray-400 focus:outline-none text-sm sm:text-lg ${validationErrors.phoneNumber ? 'border-red-500 focus:border-red-500' : 'border-slate-600 focus:border-slate-400'
                                        }`}
                                />
                            </div>
                            {/* {selectedCountry?.valueSec === '+254' && (
                                <p className="text-sm text-gray-400 mt-2">
                                </p>
                            )} */}
                        </div>

                        {/* Amount */}
                        <div className="mb-6">
                            <div className="flex items-center gap-2 mb-3">
                                <LuDollarSign className="w-6 h-6 text-BP-yellow" />
                                <label className="text-xl font-semibold">
                                    Amount (USD)
                                    {validationErrors.amount && <span className="text-red-500 ml-1">*</span>}
                                </label>
                            </div>
                            <input
                                type="number"
                                min="0"
                                value={amount}
                                onChange={handleAmountChange}
                                placeholder="0.00"
                                className={`w-full bg-slate-700/40 border rounded-lg px-4 py-4 text-white placeholder-gray-400 focus:outline-none text-lg ${validationErrors.amount ? 'border-red-500 focus:border-red-500' : 'border-slate-600 focus:border-slate-400'
                                    }`}
                            />

                            {/* Local Currency Amount Display */}
                            {selectedCountry && selectedCountry.value !== 'United States' && (
                                <div className="mt-3 p-3 bg-slate-700/30 border border-slate-600 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <span className="text-gray-400 text-sm">
                                                Amount in {getCurrencyInfo(selectedCountry.value).code}:
                                            </span>
                                            {isConvertingCurrency && (
                                                <div className="w-4 h-4 border-2 border-gray-400/30 border-t-gray-400 rounded-full animate-spin"></div>
                                            )}
                                        </div>
                                        <div className="text-right">
                                            <div className="text-teal-400 font-semibold text-lg">
                                                {localCurrencyAmount || (amount && parseFloat(amount) > 0 ? 'Converting...' : '--')}
                                            </div>
                                            {currencyConversion && currencyConversion.success && (
                                                <div className="text-gray-500 text-xs">
                                                    Rate: 1 USD = {currencyConversion.rate.toFixed(4)} {currencyConversion.currencyInfo.code}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    {currencyConversion && !currencyConversion.success && (
                                        <div className="text-yellow-400 text-xs mt-1">
                                            ⚠️ Currency conversion unavailable - showing USD amount
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>

                        {/* You receive */}
                        <div className="mb-6">
                            <div className="flex justify-between items-center bg-slate-700 rounded-lg p-5">
                                <div>
                                    <div className="text-gray-400 font-semibold text-lg">You receive</div>
                                    <div className="text-teal-400 text-2xl font-bold">{bnbAmount} BNB</div>
                                    {localCurrencyAmount && currencyConversion?.success && (
                                        <div className="text-gray-400 text-sm mt-1">
                                            Worth: {localCurrencyAmount}
                                        </div>
                                    )}
                                </div>
                                <div className="text-right">
                                    <div className="text-gray-400 text-lg flex items-center gap-2 justify-end">
                                        Rate
                                        <button
                                            onClick={refreshBNBRate}
                                            disabled={isLoadingRate}
                                            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
                                            title="Refresh BNB rate"
                                        >
                                            <FiRefreshCw className={`w-4 h-4 ${isLoadingRate ? 'animate-spin' : ''}`} />
                                        </button>
                                    </div>
                                    <div className="text-white font-semibold text-lg">
                                        {isLoadingRate ? (
                                            <span className="animate-pulse">Loading...</span>
                                        ) : (
                                            `1 BNB = $${bnbRate?.toLocaleString()}`
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Transaction Fees */}
                        {amount && parseFloat(amount) > 0 && (
                            <div className="mb-6">
                                <div className="bg-slate-700/50 border border-slate-600 rounded-lg p-4">
                                    <h3 className="text-lg font-semibold text-gray-300 mb-3 flex items-center gap-2">
                                        <HiOutlineInformationCircle className="w-5 h-5 text-blue-400" />
                                        Transaction Breakdown
                                    </h3>

                                    <div className="space-y-2">
                                        {/* BNB Amount */}
                                        <div className="flex justify-between items-center text-sm">
                                            <span className="text-gray-400">BNB Amount:</span>
                                            <div className="text-right">
                                                <span className="text-white font-medium">${parseFloat(amount).toFixed(2)}</span>
                                                {localCurrencyAmount && currencyConversion?.success && (
                                                    <div className="text-gray-500 text-xs">
                                                        ≈ {formatCurrencyAmount(currencyConversion.convertedAmount, currencyConversion.currencyInfo.code)}
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Transaction Fee */}
                                        <div className="flex justify-between items-center text-sm">
                                            <span className="text-gray-400">Transaction Fee:</span>
                                            <div className="text-right">
                                                <span className="text-orange-400 font-medium">${feeCalculation.transactionFee.toFixed(2)}</span>
                                                {localCurrencyAmount && currencyConversion?.success && (
                                                    <div className="text-gray-500 text-xs">
                                                        ≈ {formatCurrencyAmount(feeCalculation.transactionFeeLocal, currencyConversion.currencyInfo.code)}
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Divider */}
                                        <div className="border-t border-slate-600 my-2"></div>
                                        {/* Total to Pay */}
                                        <div className="flex justify-between items-center">
                                            <span className="text-white font-semibold">Total to Pay:</span>
                                            <div className="text-right">
                                                <span className="text-teal-400 font-bold text-lg">${feeCalculation.finalAmount.toFixed(2)}</span>
                                                {localCurrencyAmount && currencyConversion?.success && (
                                                    <div className="text-teal-300 text-sm">
                                                        ≈ {formatCurrencyAmount(feeCalculation.finalAmountLocal, currencyConversion.currencyInfo.code)}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Fee Info */}
                                    <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-md">
                                        <p className="text-blue-300 text-xs">
                                            💡 Transaction fees help maintain our service and cover blockchain costs.
                                            {currencyConversion?.success && (
                                                <span className="block mt-1">
                                                    💱 Exchange rate: 1 USD = {currencyConversion.rate.toFixed(4)} {currencyConversion.currencyInfo.code}
                                                </span>
                                            )}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Buy Button */}
                        <button
                            onClick={handleBuyBNB}
                            className={`w-full font-semibold py-4 px-6 rounded-lg transition-all duration-300 text-lg flex items-center justify-center gap-3 relative overflow-hidden ${isProcessingPayment
                                ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white cursor-not-allowed animate-glow'
                                : !isWalletConnected
                                    ? 'bg-slate-500 hover:bg-slate-500 text-white'
                                    : 'bg-teal-400 hover:bg-teal-500 text-white transform hover:scale-[1.02] shadow-lg hover:shadow-xl'
                                }`}
                            disabled={isProcessingPayment} // Disable button while processing
                        >
                            {/* Processing loader */}
                            {isProcessingPayment && (
                                <>
                                    {/* Background shimmer effect */}
                                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 animate-pulse"></div>

                                    {/* Loader elements */}
                                    <div className="flex items-center gap-3 z-10">
                                        {/* Main spinning circle */}
                                        <div className="relative">
                                            <div className="w-6 h-6 border-3 border-white/20 border-t-white rounded-full animate-spin"></div>
                                            <div className="absolute inset-0 w-6 h-6 border-3 border-transparent border-r-white/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '0.8s' }}></div>
                                        </div>

                                        {/* Animated dots */}
                                        <div className="flex gap-1">
                                            <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay" style={{ animationDelay: '0ms' }}></div>
                                            <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay" style={{ animationDelay: '200ms' }}></div>
                                            <div className="w-2 h-2 bg-white rounded-full animate-bounce-delay" style={{ animationDelay: '400ms' }}></div>
                                        </div>
                                    </div>
                                </>
                            )}

                            {/* Button text */}
                            <span className={`${isProcessingPayment ? 'animate-pulse' : ''} z-10 relative`}>
                                {isProcessingPayment ? 'Processing payment...' : !isWalletConnected ? 'Connect your wallet' : 'Buy BNB now'}
                            </span>

                            {/* Success indicator (hidden by default) */}
                            {!isProcessingPayment && isWalletConnected && (
                                <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    💎
                                </div>
                            )}
                        </button>
                    </div>

                    {/* Right Panel - How it works */}
                    <div className="bg-slate-800 border border-slate-600 rounded-2xl p-8 w-full max-w-2xl mx-auto flex flex-col">
                        <h2 className="text-2xl font-bold text-yellow-500 mb-8">How it works</h2>
                        <h3 className="text-xl font-semibold mb-8">Simple Steps to Get BNB</h3>

                        <div className="space-y-8 mb-2">
                            {/* Step 1 */}
                            <div className="flex gap-4">
                                <div className="w-8 h-8  rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <FiCheckCircle className="w-6 h-6 text-BP-yellow" />
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2 text-lg">Connect your wallet</h4>
                                    <p className="text-gray-400">We'll verify it can receive BNB</p>
                                </div>
                            </div>

                            {/* Step 2 */}
                            <div className="flex gap-4">
                                <div className="w-8 h-8  rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <FiCheckCircle className="w-6 h-6 text-BP-yellow" />
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2 text-lg">Select your mobile money provider</h4>
                                    <p className="text-gray-400">Choose from our trusted partners</p>
                                </div>
                            </div>

                            {/* Step 3 */}
                            <div className="flex gap-4">
                                <div className="w-8 h-8  rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <FiCheckCircle className="w-6 h-6 text-BP-yellow" />
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2 text-lg">Enter your phone number</h4>
                                    <p className="text-gray-400">Must be registered with your provider</p>
                                </div>
                            </div>

                            {/* Step 4 */}
                            <div className="flex gap-4">
                                <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <FiCheckCircle className="w-6 h-6 text-BP-yellow" />
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2 text-lg">Enter amount in USD</h4>
                                    <p className="text-gray-400">Amounts above our limit will be handled via Transak.</p>
                                </div>
                            </div>

                            {/* Step 5 */}
                            <div className="flex gap-4">
                                <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <FiCheckCircle className="w-6 h-6 text-BP-yellow" />
                                </div>
                                <div>
                                    <h4 className="font-semibold mb-2 text-lg">Confirm and complete payment</h4>
                                    <p className="text-gray-400">You'll receive BNB in your wallet within 5 minutes</p>
                                </div>
                            </div>
                        </div>

                        {/* Important Information */}
                        <div className="mt-4 p-4 bg-slate-800/30 border border-slate-600/50 rounded-lg">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0">
                                    <HiOutlineInformationCircle className="w-6 h-6 text-teal-400" />
                                </div>
                                <div>
                                    <h4 className="text-gray-400 font-semibold mb-1">Important Information</h4>
                                    <p className="text-[#6e6029] text-sm">
                                        Your wallet must support BEP-20 tokens. We'll verify this automatically when you connect.
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Transaction History Section */}
                        <div className="mt-8">
                            <div className="flex items-center gap-3 mb-6">
                                <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                                    <FiCheckCircle className="w-5 h-5 text-yellow-500" />
                                </div>
                                <h2 className="text-2xl font-bold text-yellow-500">Transaction History</h2>
                            </div>

                            {bnbTransactionHistory.length > 0 ? (
                                <div className="bg-slate-700/30 border border-slate-600 rounded-xl overflow-hidden">
                                    {/* Desktop Table View */}
                                    <div className="hidden md:block">
                                        {/* Fixed header */}
                                        <div className="bg-slate-700/50 border-b border-slate-600">
                                            <div className="grid grid-cols-6 gap-8 px-4 py-3">
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">#</div>
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">Sender</div>
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">Initiator</div>
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">Recipient</div>
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">Amount</div>
                                                <div className="text-left text-xs font-semibold text-gray-300 uppercase tracking-wider">Date</div>
                                            </div>
                                        </div>
                                        
                                        {/* Scrollable body with single vertical scrollbar only */}
                                        <div className="max-h-[320px] overflow-y-auto overflow-x-hidden white-scrollbar bg-transparent">
                                            <div className="divide-y divide-slate-600">
                                                {bnbTransactionHistory.map((txn, index) => (
                                                    <div 
                                                        key={index} 
                                                        onClick={() => handleRowClick(txn)} 
                                                        className="grid grid-cols-6 gap-4 px-4 py-3 hover:bg-slate-600/30 cursor-pointer transition-colors duration-200 group"
                                                    >
                                                        <div className="flex items-center">
                                                            <span className="inline-flex items-center justify-center w-6 h-6 bg-teal-500/20 text-teal-400 rounded-full text-xs font-semibold">
                                                                {txn.index}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center gap-1 min-w-0">
                                                            <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                                                            <span className="text-white font-mono text-xs truncate" title={txn.sender}>
                                                                {txn.sender.slice(0, 6)}...{txn.sender.slice(-4)}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center gap-1 min-w-0">
                                                            <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></div>
                                                            <span className="text-white font-mono text-xs truncate" title={txn.initiator}>
                                                                {txn.initiator.slice(0, 6)}...{txn.initiator.slice(-4)}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center gap-1 min-w-0">
                                                            <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                                                            <span className="text-white font-mono text-xs truncate" title={txn.recipient}>
                                                                {txn.recipient.slice(0, 6)}...{txn.recipient.slice(-4)}
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center gap-1 min-w-0">
                                                            <span className="text-teal-400 font-bold text-sm truncate">{txn.amount}</span>
                                                            <span className="text-gray-400 text-xs flex-shrink-0">BNB</span>
                                                        </div>
                                                        <div className="flex items-center min-w-0">
                                                            <span className="text-gray-300 text-xs truncate">{txn.date}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Mobile Card View */}
                                    <div className="md:hidden max-h-[400px] overflow-y-auto space-y-3 p-4">
                                        {bnbTransactionHistory.map((txn, index) => (
                                            <div 
                                                key={index}
                                                onClick={() => handleRowClick(txn)}
                                                className="bg-slate-800/50 border border-slate-600 rounded-lg p-4 cursor-pointer hover:bg-slate-700/30 transition-colors duration-200"
                                            >
                                                <div className="mb-3">
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <span className="inline-flex items-center justify-center w-6 h-6 bg-teal-500/20 text-teal-400 rounded-full text-xs font-semibold">
                                                            {txn.index}
                                                        </span>
                                                        <span className="text-gray-400 text-sm">Transaction #{txn.index}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 justify-center bg-slate-700/30 rounded-lg py-2">
                                                        <span className="text-teal-400 font-bold text-xl">{txn.amount}</span>
                                                        <span className="text-gray-400 text-sm">BNB</span>
                                                    </div>
                                                </div>
                                                
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                                        <span className="text-gray-400 text-xs w-16">From:</span>
                                                        <span className="text-white font-mono text-xs" title={txn.sender}>
                                                            {txn.sender.slice(0, 8)}...{txn.sender.slice(-6)}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                                                        <span className="text-gray-400 text-xs w-16">To:</span>
                                                        <span className="text-white font-mono text-xs" title={txn.recipient}>
                                                            {txn.recipient.slice(0, 8)}...{txn.recipient.slice(-6)}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                        <span className="text-gray-400 text-xs w-16">Date:</span>
                                                        <span className="text-gray-300 text-xs">{txn.date}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {/* Scroll Indicator for Long Lists */}
                                    {bnbTransactionHistory.length > 5 && (
                                        <div className="bg-slate-700/50 border-t border-slate-600 px-4 py-2">
                                            <p className="text-gray-400 text-xs text-center">
                                                Showing {bnbTransactionHistory.length} transactions • Click any row for details
                                            </p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="bg-slate-700/20 border border-slate-600 rounded-xl p-8 text-center">
                                    <div className="w-16 h-16 bg-gray-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <FiCheckCircle className="w-8 h-8 text-gray-500" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-300 mb-2">No Transactions Yet</h3>
                                    <p className="text-gray-400 text-sm">
                                        Your transaction history will appear here once you complete your first BNB purchase.
                                    </p>
                                </div>
                            )}
                        </div>

                    </div>
                </div>
            </div>

            {isModalOpen && selectedTxn && (
                <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn">
                    <div className="bg-slate-800 border border-slate-600 rounded-xl shadow-2xl max-w-md w-full relative animate-scaleIn overflow-hidden">
                        {/* Header */}
                        <div className="bg-gradient-to-r from-yellow-500/20 to-teal-500/20 border-b border-slate-600 p-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <div className="w-8 h-8 bg-teal-500/20 rounded-lg flex items-center justify-center">
                                        <FiCheckCircle className="w-4 h-4 text-teal-400" />
                                    </div>
                                    <div>
                                        <h2 className="text-lg font-bold text-white">Transaction Details</h2>
                                        <p className="text-gray-400 text-xs">#{selectedTxn.index}</p>
                                    </div>
                                </div>
                                <button
                                    onClick={() => setIsModalOpen(false)}
                                    className="w-6 h-6 rounded-md bg-slate-700/50 hover:bg-slate-600 border border-slate-600 flex items-center justify-center text-gray-400 hover:text-white transition-all duration-200"
                                >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="p-4 space-y-3">
                            {/* Amount */}
                            <div className="bg-slate-700/30 border border-slate-600 rounded-lg p-3 text-center">
                                <div className="text-gray-400 text-xs mb-1">Amount</div>
                                <div className="text-xl font-bold text-teal-400">{selectedTxn.amount} BNB</div>
                            </div>

                            {/* Addresses */}
                            <div className="space-y-2 text-sm">
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-green-400 rounded-full flex-shrink-0"></div>
                                    <span className="text-gray-400 font-medium w-16">Sender:</span>
                                    <span className="text-white font-mono text-xs break-all">{selectedTxn.sender}</span>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></div>
                                    <span className="text-gray-400 font-medium w-16">Initiator:</span>
                                    <span className="text-white font-mono text-xs break-all">{selectedTxn.initiator}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                                    <span className="text-gray-400 font-medium w-16">Recipient:</span>
                                    <span className="text-white font-mono text-xs break-all">{selectedTxn.recipient}</span>
                                </div>
                            </div>

                            {/* Metadata */}
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div className="bg-slate-700/20 border border-slate-600/50 rounded-md p-2">
                                    <div className="text-gray-400 text-xs font-medium">Timestamp</div>
                                    <div className="text-white text-xs font-mono">{selectedTxn.timestamp}</div>
                                </div>
                                <div className="bg-slate-700/20 border border-slate-600/50 rounded-md p-2">
                                    <div className="text-gray-400 text-xs font-medium">Date</div>
                                    <div className="text-white text-xs">{selectedTxn.date}</div>
                                </div>
                            </div>

                            {/* Close button */}
                            <div className="pt-2 border-t border-slate-600">
                                <button
                                    onClick={() => setIsModalOpen(false)}
                                    className="w-full bg-slate-700 hover:bg-slate-600 border border-slate-600 text-white font-medium py-2 px-3 rounded-lg transition-colors duration-200 text-sm"
                                >
                                    Close Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <Footer />
            <ValidationModal />
            <ComingSoonModal
                isOpen={showComingSoonModal}
                onClose={() => setShowComingSoonModal(false)}
            />
            <PaymentProcessingModal
                isOpen={isProcessingPayment}
                onClose={() => setIsProcessingPayment(false)}
                paymentData={{
                    amount: amount,
                    finalAmount: feeCalculation?.finalAmount,
                    bnbAmount: bnbAmount,
                    phoneNumber: phoneNumber,
                    rate: bnbRate,
                    fees: feeCalculation,
                    currency: currencyConversion
                }}
                walletAddress={walletAddress}
                isBlocking={true}
            />
            <PaymentSuccessModal
                isOpen={showPaymentSuccessModal}
                onClose={handlePaymentSuccessClose}
                paymentData={paymentSuccessData}
                walletAddress={walletAddress}
                transactionHash={successTransactionHash}
            />
        </div>
    );
};