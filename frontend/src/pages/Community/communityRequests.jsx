import React, { useState, useEffect, useRef } from "react";
import { Input, <PERSON>, Button } from "antd";
import { FiSearch, FiClock, FiCheckCircle, FiPlus, FiX, FiUser, FiUsers, FiDollarSign } from "react-icons/fi";
import { FaUsersLine } from "react-icons/fa6";
import { MdOutlinePendingActions } from "react-icons/md";
import { MdOutlineChatBubbleOutline } from "react-icons/md";
import { GoMail } from "react-icons/go";
import { CommunityService } from "../../services/CommunityService";
import { toast } from "react-toastify";
import capitalizeFirst from "../../utils/capitalizeFirst"
import { RiQuestionnaireFill } from "react-icons/ri";

const statusColor = {
  pending: "#E9B308",
  approved: "#4FD1C5",
  rejected: "#FF4D4F",
};

const statusIcon = {
  pending: <FiClock style={{ color: "#E9B308", marginRight: 6, fontSize: 18 }} />,
  approved: <FiCheckCircle style={{ color: "#4FD1C5", marginRight: 6, fontSize: 18 }} />,
  rejected: <MdOutlinePendingActions style={{ color: "#FF4D4F", marginRight: 6, fontSize: 18 }} />,
};

export const CommunityRequests = () => {
  const [allCommunityInfo, setAllCommunityInfo] = useState([]);
  const [selected, setSelected] = useState(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isLgScreen, setIsLgScreen] = useState(window.innerWidth >= 1024);
  const reviewCardRef = useRef(null);

  useEffect(() => {
    const handleResize = () => setIsLgScreen(window.innerWidth >= 1024);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    fetchAllCommunities();
  }, []);

  useEffect(() => {
    if (selected && reviewCardRef.current) {
      reviewCardRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [selected]);

  const fetchAllCommunities = async () => {
    const response = await CommunityService.getAllCommunityInfoToApprove();
    if (response?.status === 200) {
      // console.log("Fetched communities now:", response.data.communities);
      setAllCommunityInfo(response.data.communities);
    }
  };

  const handleApprove = async (id) => {
    try {
      await CommunityService.approveCommunity(id);
      fetchAllCommunities();
    } catch (e) {
      console.error(e);
    }
  };


  const handleReject = async (id, reason) => {
    if (!reason) {
      toast.error("Please provide a rejection reason.");
      return;
    }
    try {
      await CommunityService.rejectCommunity(id, reason);
      setRejectionReason("");
      fetchAllCommunities();
    } catch (e) {
      console.error(e);
    }
  };

  // Filter communities based on search query
  const filteredCommunities = allCommunityInfo.filter((c) => {
    const query = searchQuery.trim().toLowerCase();
    if (!query) return true;
    return (
      c.communityName?.toLowerCase().includes(query) ||
      c.category?.toLowerCase().includes(query) ||
      c.communityAdmin?.firstName?.toLowerCase().includes(query) ||
      c.communityAdmin?.lastName?.toLowerCase().includes(query) ||
      c.communityAdmin?.country?.toLowerCase().includes(query) ||
      c.communityAdmin?.email?.toLowerCase().includes(query)
    );
  });

  return (
    <div className="min-h-screen lg:p-8 font-sans">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-[2rem] font-medium text-[#2C5282] mb-1">
            DAO Submissions Review
          </h1>
          <div className="text-[#7b8ba1] text-base font-normal">
            Review and approve new community proposals
          </div>
        </div>
        <div className="mt-4 md:mt-0">
          <div className="flex items-center bg-white border border-[#e6e6e6] rounded-[12px] px-4 py-2 w-72">
            <FiSearch className="text-[#b0b7c3] mr-2 text-lg" />
            <input
              type="text"
              placeholder="Search submissions"
              className="bg-transparent outline-none border-none w-full text-[#2C5282] placeholder-[#b0b7c3] text-base"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Stats (Hardcoded) */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card className="rounded-xl text-center shadow-sm border-0 bg-white">
          <div className="text-[#7b8ba1] text-sm mb-1">Total submission</div>
          <div className="text-2xl font-semibold text-[#2C5282]">{allCommunityInfo.length}</div>
        </Card>
        <Card className="rounded-xl text-center shadow-sm border-0 bg-white">
          <div className="text-[#7b8ba1] text-sm mb-1">Pending review</div>
          <div className="text-2xl font-semibold text-[#2C5282]">
            {allCommunityInfo.filter(c => c.communityAcceptanceStatus === "pending").length}
          </div>
        </Card>
        <Card className="rounded-xl text-center shadow-sm border-0 bg-white">
          <div className="text-[#7b8ba1] text-sm mb-1">Approved</div>
          <div className="text-2xl font-semibold text-[#2C5282]">
            {allCommunityInfo.filter(c => c.communityAcceptanceStatus === "approved").length}
          </div>
        </Card>
        <Card className="rounded-xl text-center shadow-sm border-0 bg-white">
          <div className="text-[#7b8ba1] text-sm mb-1">Rejected</div>
          <div className="text-2xl font-semibold text-[#2C5282]">
            {allCommunityInfo.filter(c => c.communityAcceptanceStatus === "rejected").length}
          </div>
        </Card>
      </div>

      {/* Responsive Main Content */}
      {isLgScreen ? (
        <div className={`grid grid-cols-1 ${selected ? "lg:grid-cols-2" : "lg:grid-cols-3"} gap-6`}>
          {/* Submissions List */}
          <Card className={`rounded-xl shadow-md border-0 h-fit bg-white ${selected ? "col-span-1" : "col-span-2"} p-0`}>
            <div className="border-b px-6 py-4">
              <span className="text-lg font-semibold text-[#2C5282]">Recent Submissions</span>
            </div>
            <div>
              {filteredCommunities.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <FiSearch className="text-4xl text-gray-400 mb-2" />
                  <div className="text-gray-400 text-lg font-medium">No submissions found</div>
                  <div className="text-gray-400 text-sm">Try a different search term.</div>
                </div>
              ) : (
                filteredCommunities.map((s, idx) => (
                  <div
                    key={s._id}
                    className={`flex items-center px-6 py-4 ${idx !== allCommunityInfo.length - 1 ? "border-b" : ""} hover:bg-[#4FD1C533] transition cursor-pointer`}
                    onClick={() => setSelected(s)}
                    style={{
                      background: selected && selected._id === s._id ? "#4FD1C533" : "",
                    }}
                  >
                    {/* Logo or Icon */}
                    <div className="mr-4">
                      {s.logo ? (
                        <img
                          src={encodeURI(s.logo)}
                          alt={s.communityName}
                          crossOrigin="anonymous"
                          className="h-12 w-12 rounded-lg object-cover border-2 border-[#e6f0fa] shadow"
                        />

                      ) : (
                        <div className="h-12 w-12 rounded-lg bg-[#e6e8ec] flex items-center justify-center text-3xl text-[#b0b7c3]">
                          <FaUsersLine />
                        </div>
                      )}
                    </div>
                    {/* Info */}
                    <div className="flex-1">
                      <div className="font-semibold text-[#2C5282]">{s.communityName}</div>
                      <div className="text-xs text-[#7b8ba1]">{capitalizeFirst(s.category)}</div>
                    </div>
                    {/* Status */}
                    <div className="flex items-center mr-6">
                      <span
                        className="flex items-center text-base"
                        style={{ color: statusColor[s.communityAcceptanceStatus] }}
                      >
                        {statusIcon[s.communityAcceptanceStatus]}
                        {capitalizeFirst(s.communityAcceptanceStatus)}
                      </span>
                    </div>
                    {/* Date */}
                    <div className="text-xs text-[#b0b7c3]">
                      {new Date(s.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))
              )}
            </div>
          </Card>
          {/* Details Preview or Selector Prompt */}
          {!selected ? (
            <Card className="rounded-xl shadow-md border-0 bg-white h-fit flex flex-col items-center justify-center text-center min-h-[340px]">
              <div className="flex flex-col items-center justify-center h-full">
                <div className="bg-[#f3f4f6] rounded-full h-16 w-16 flex items-center justify-center mb-4 text-3xl text-[#b0b7c3]">
                  <FiPlus />
                </div>
                <div className="text-[#2C5282] font-semibold text-lg mb-1">Select a submission</div>
                <div className="text-[#7b8ba1] text-sm">
                  Choose a DAO submission from the list to review details and take action
                </div>
              </div>
            </Card>
          ) : (
            <Card
              ref={reviewCardRef}
              className="rounded-xl shadow-md border-0 bg-white p-0 min-h-[340px] h-fit relative flex flex-col"
            >
              <div className="border-b-2">
                <p className="text-[#4B5563] font-semibold text-lg mb-3">Review Submission</p>
                <button
                  className="absolute top-4 right-4 text-[#b0b7c3] hover:text-[#2C5282] text-xl"
                  onClick={() => setSelected(null)}
                  aria-label="Close"
                >
                  <FiX />
                </button>
              </div>
              <div className="flex items-center gap-3 px-6 pt-6 pb-2">
                {selected.logo ? (
                  <img
                    src={encodeURI(selected.logo)}
                    alt={selected.communityName}
                    crossOrigin="anonymous"
                    className="h-12 w-12 rounded-lg object-cover border-2 border-[#e6f0fa] shadow"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-lg bg-[#e6e8ec] flex items-center justify-center text-3xl text-[#b0b7c3]">
                    <FaUsersLine />
                  </div>
                )}
                <div>
                  <div className="font-semibold text-lg text-[#2C5282]">{selected.communityName}</div>
                  <div className="text-[#7b8ba1] text-sm">{capitalizeFirst(selected.category)}</div>
                </div>
                <div className="ml-auto flex items-center gap-2">
                  <span
                    className="flex items-center text-base"
                    style={{ color: statusColor[selected.communityAcceptanceStatus] }}
                  >
                    {statusIcon[selected.communityAcceptanceStatus]}
                    {capitalizeFirst(selected.communityAcceptanceStatus)}
                  </span>
                </div>
              </div>

              {/* Description */}
              <div className="px-6 mt-2">
                {selected.communityAcceptanceStatus === "rejected" &&
                  <div className="rounded-lg p-3 mb-3" style={{ background: "#fff0f0" }}>
                    <div className="text-[#FF4D4F] font-medium mb-1 flex items-center gap-2">
                      <RiQuestionnaireFill className="text-[#FF4D4F] text-lg" />
                      Rejection reason
                    </div>
                    <div className="text-[#7b8ba1] text-sm">{selected.rejectionReason}</div>
                  </div>
                }
                <div className="rounded-lg p-3 mb-3" style={{ background: "rgba(79, 209, 197, 0.1)" }}>
                  <div className="text-[#2C5282] font-medium mb-1 flex items-center gap-2">
                    <MdOutlineChatBubbleOutline className="text-[#2C5282] text-lg" />
                    Description
                  </div>
                  <div className="text-[#7b8ba1] text-sm">{selected.communityDescription}</div>
                </div>
                <div className="rounded-lg p-3 mb-3" style={{ background: "#F8F5ED" }}>
                  <div className="text-[#2C5282] font-medium mb-1 flex items-center gap-2">
                    <GoMail className="text-[#2C5282] text-lg" />
                    Detailed purpose
                  </div>
                  <div className="text-[#7b8ba1] text-sm">{selected.communityDetailedDescription}</div>
                </div>
              </div>

              {/* Governance & Contribution */}
              <div className="flex gap-4 px-6 mb-6">
                <div className="flex-1 rounded-[16px] p-4" style={{ background: "#F5F5F5" }}>
                  <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                    <FiUsers /> Governance
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#7b8ba1] text-sm font-medium">Visibility:</span>
                    <span className="text-[#2C5282] text-sm font-medium">{capitalizeFirst(selected.visibility)}</span>
                  </div>
                </div>
                <div className="flex-1 rounded-[16px] p-4" style={{ background: "#F5F5F5" }}>
                  <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                    <FiDollarSign /> Contribution
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#7b8ba1] text-sm font-medium">Type:</span>
                    <span className="text-[#2C5282] text-sm font-medium">{capitalizeFirst(selected.contributionType)}</span>
                  </div>
                  <div className={`${selected.contributionType === "custom" ? "mt-0.5" : "flex items-center gap-2"}`}>
                    <div className="text-[#7b8ba1] text-sm font-medium">Amount:</div>
                    <div className="text-[#2C5282] text-sm font-medium">
                      {
                        selected.contributionType === "custom"
                          ? selected.contributionAmounts.map((amount, index) => (
                            <span key={index}>
                              ${amount}
                              {index < selected.contributionAmounts.length - 1 && (
                                <span className="text-[#7b8ba1] font-normal"> or </span>
                              )}
                            </span>
                          ))
                          : `$${selected.contributionAmounts[0]}`
                      }
                    </div>
                  </div>
                </div>
              </div>

              {/* Submitter Info */}
              <div className="rounded-[16px] mx-6 p-4 mb-3" style={{ background: "#F5F5F5" }}>
                <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                  <FiUser /> Submitter info
                </div>
                <div className="flex flex-col gap-1 text-sm">
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Name:</span>
                    <span className="text-[#2C5282] font-medium">
                      {selected.communityAdmin.firstName} {selected.communityAdmin.lastName}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Country:</span>
                    <span className="text-[#2C5282] font-medium">{capitalizeFirst(selected.communityAdmin.country) || "N/A"}</span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Email:</span>
                    <span className="text-[#2C5282] font-medium">{selected.communityAdmin.email}</span>
                  </div>
                </div>
              </div>

              {/* Review Actions */}
              <div className="px-6 pb-6 mt-auto">
                <div className="text-[#2C5282] font-medium mb-2">Review Actions</div>
                <div className="mb-3 text-BP-nav-gray">Rejection reason (if applicable)</div>
                <div className="mb-3">
                  <Input.TextArea
                    rows={6}
                    placeholder="Provide a clear feedback if rejecting..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      background: "#f7f9fa",
                      borderRadius: 8,
                      border: "1px solid #e6e6e6",
                      fontSize: 14,
                    }}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    type="default"
                    danger
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      borderRadius: 8,
                      border: "none",
                      background: "#fff0f0",
                      color: "#FF4D4F",
                      fontWeight: 500,
                      width: "100%",
                    }}
                    onClick={() => {
                      handleReject(selected._id, rejectionReason);
                      setSelected(null);
                    }}
                  >
                    &#10005; Reject Submission
                  </Button>
                  <Button
                    type="primary"
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      borderRadius: 8,
                      background: "#38b2ac",
                      border: "none",
                      fontWeight: 500,
                      width: "100%",
                    }}
                    onClick={() => {
                      handleApprove(selected._id);
                      setSelected(null);
                    }}
                  >
                    &#10003; Approve Submission
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      ) : (
        // Small screens: show only one card at a time
        <div className="flex flex-col gap-6">
          {!selected ? (
            <>
              <Card className="rounded-xl shadow-md border-0 h-fit bg-white p-0">
                <div className="border-b sm:px-6 py-4">
                  <span className="text-lg font-semibold text-[#2C5282]">Recent Submissions</span>
                </div>
                <div>
                  {filteredCommunities.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <FiSearch className="text-4xl text-gray-400 mb-2" />
                      <div className="text-gray-400 text-lg font-medium">No submissions found</div>
                      <div className="text-gray-400 text-sm">Try a different search term.</div>
                    </div>
                  ) : (
                    filteredCommunities.map((s, idx) => (
                      <div
                        key={s._id}
                        className={`sm:flex max-sm:space-y-5 items-center justify-between sm:px-6 py-4 ${idx !== allCommunityInfo.length - 1 ? "border-b" : ""} hover:bg-[#4FD1C533] transition cursor-pointer`}
                        onClick={() => setSelected(s)}
                        style={{
                          background: selected && selected._id === s._id ? "#4FD1C533" : "",
                        }}
                      >
                        <div className="flex items-center">

                          {/* Logo or Icon */}
                          <div className="mr-4">
                            {s.logo ? (
                              <img
                                src={encodeURI(s.logo)}
                                alt={s.communityName}
                                crossOrigin="anonymous"
                                className="h-12 w-12 rounded-lg object-cover border-2 border-[#e6f0fa] shadow"
                              />

                            ) : (
                              <div className="h-12 w-12 rounded-lg bg-[#e6e8ec] flex items-center justify-center text-3xl text-[#b0b7c3]">
                                <FaUsersLine />
                              </div>
                            )}
                          </div>
                          {/* Info */}
                          <div className="flex-1">
                            <div className="font-semibold text-[#2C5282]">{s.communityName}</div>
                            <div className="text-xs text-[#7b8ba1]">{capitalizeFirst(s.category)}</div>
                          </div>

                        </div>

                        <div className="flex justify-between">

                          {/* Status */}
                          <div className="flex items-center mr-6">
                            <span
                              className="flex items-center text-base"
                              style={{ color: statusColor[s.communityAcceptanceStatus] }}
                            >
                              {statusIcon[s.communityAcceptanceStatus]}
                              {capitalizeFirst(s.communityAcceptanceStatus)}
                            </span>
                          </div>
                          {/* Date */}
                          <div className="text-xs my-auto text-[#b0b7c3]">
                            {new Date(s.createdAt).toLocaleDateString()}
                          </div>

                        </div>

                      </div>
                    ))
                  )}
                </div>
              </Card>
              <Card className="rounded-xl shadow-md border-0 bg-white h-fit flex flex-col items-center justify-center text-center min-h-[340px]">
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="bg-[#f3f4f6] rounded-full h-16 w-16 flex items-center justify-center mb-4 text-3xl text-[#b0b7c3]">
                    <FiPlus />
                  </div>
                  <div className="text-[#2C5282] font-semibold text-lg mb-1">Select a submission</div>
                  <div className="text-[#7b8ba1] text-sm">
                    Choose a DAO submission from the list to review details and take action
                  </div>
                </div>
              </Card>
            </>
          ) : (
            <Card
              ref={reviewCardRef}
              className="rounded-xl shadow-md border-0 bg-white p-0 min-h-[340px] h-fit relative flex flex-col"
            >
              <div className="border-b-2">
                <p className="text-[#4B5563] font-semibold text-lg mb-3">Review Submission</p>
                <button
                  className="absolute top-4 right-4 text-[#b0b7c3] hover:text-[#2C5282] text-xl"
                  onClick={() => setSelected(null)}
                  aria-label="Close"
                >
                  <FiX />
                </button>
              </div>
              <div className="flex items-center gap-3 sm:px-6 pt-6 pb-2">
                {selected.logo ? (
                  <img
                    src={encodeURI(selected.logo)}
                    alt={selected.communityName}
                    crossOrigin="anonymous"
                    className="h-12 w-12 rounded-lg object-cover border-2 border-[#e6f0fa] shadow"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-lg bg-[#e6e8ec] flex items-center justify-center text-3xl text-[#b0b7c3]">
                    <FaUsersLine />
                  </div>
                )}
                <div>
                  <div className="font-semibold text-lg text-[#2C5282]">{selected.communityName}</div>
                  <div className="text-[#7b8ba1] text-sm">{capitalizeFirst(selected.category)}</div>
                </div>
                <div className="ml-auto flex items-center gap-2">
                  <span
                    className="flex items-center text-base"
                    style={{ color: statusColor[selected.communityAcceptanceStatus] }}
                  >
                    {statusIcon[selected.communityAcceptanceStatus]}
                    {capitalizeFirst(selected.communityAcceptanceStatus)}
                  </span>
                </div>
              </div>

              {/* Description */}
              <div className="sm:px-6 mt-2">
                {selected.communityAcceptanceStatus === "rejected" &&
                  <div className="rounded-lg p-3 mb-3" style={{ background: "#fff0f0" }}>
                    <div className="text-[#FF4D4F] font-medium mb-1 flex items-center gap-2">
                      <RiQuestionnaireFill className="text-[#FF4D4F] text-lg" />
                      Rejection reason
                    </div>
                    <div className="text-[#7b8ba1] text-sm">{selected.rejectionReason}</div>
                  </div>
                }
                <div className="rounded-lg p-3 mb-3" style={{ background: "rgba(79, 209, 197, 0.1)" }}>
                  <div className="text-[#2C5282] font-medium mb-1 flex items-center gap-2">
                    <MdOutlineChatBubbleOutline className="text-[#2C5282] text-lg" />
                    Description
                  </div>
                  <div className="text-[#7b8ba1] text-sm">{selected.communityDescription}</div>
                </div>
                <div className="rounded-lg p-3 mb-3" style={{ background: "#F8F5ED" }}>
                  <div className="text-[#2C5282] font-medium mb-1 flex items-center gap-2">
                    <GoMail className="text-[#2C5282] text-lg" />
                    Detailed purpose
                  </div>
                  <div className="text-[#7b8ba1] text-sm">{selected.communityDetailedDescription}</div>
                </div>
              </div>

              {/* Governance & Contribution */}
              <div className="sm:flex max-sm:space-y-3 gap-4 sm:px-6 mb-6">
                <div className="flex-1 rounded-[16px] p-4" style={{ background: "#F5F5F5" }}>
                  <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                    <FiUsers /> Governance
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#7b8ba1] text-sm font-medium">Visibility:</span>
                    <span className="text-[#2C5282] text-sm font-medium">{capitalizeFirst(selected.visibility)}</span>
                  </div>
                </div>
                <div className="flex-1 rounded-[16px] p-4" style={{ background: "#F5F5F5" }}>
                  <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                    <FiDollarSign /> Contribution
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-[#7b8ba1] text-sm font-medium">Type:</span>
                    <span className="text-[#2C5282] text-sm font-medium">{capitalizeFirst(selected.contributionType)}</span>
                  </div>
                  <div className={`${selected.contributionType === "custom" ? "mt-0.5" : "flex items-center gap-2"}`}>
                    <div className="text-[#7b8ba1] text-sm font-medium">Amount:</div>
                    <div className="text-[#2C5282] text-sm font-medium">
                      {
                        selected.contributionType === "custom"
                          ? selected.contributionAmounts.map((amount, index) => (
                            <span key={index}>
                              ${amount}
                              {index < selected.contributionAmounts.length - 1 && (
                                <span className="text-[#7b8ba1] font-normal"> or </span>
                              )}
                            </span>
                          ))
                          : `$${selected.contributionAmounts[0]}`
                      }
                    </div>
                  </div>
                </div>
              </div>

              {/* Submitter Info */}
              <div className="rounded-[16px] sm:mx-6 p-4 mb-3" style={{ background: "#F5F5F5" }}>
                <div className="flex items-center gap-2 mb-2 text-[#2C5282] font-medium text-base">
                  <FiUser /> Submitter info
                </div>
                <div className="flex flex-col gap-1 text-sm">
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Name:</span>
                    <span className="text-[#2C5282] font-medium">
                      {selected.communityAdmin.firstName} {selected.communityAdmin.lastName}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Country:</span>
                    <span className="text-[#2C5282] font-medium">{capitalizeFirst(selected.communityAdmin.country) || "N/A"}</span>
                  </div>
                  <div className="flex gap-2">
                    <span className="text-[#7b8ba1] font-medium min-w-[70px]">Email:</span>
                    <span className="text-[#2C5282] font-medium">{selected.communityAdmin.email}</span>
                  </div>
                </div>
              </div>

              {/* Review Actions */}
              <div className="sm:px-6 pb-6 mt-auto">
                <div className="text-[#2C5282] font-medium mb-2">Review Actions</div>
                <div className="mb-3 text-BP-nav-gray">Rejection reason (if applicable)</div>
                <div className="mb-3">
                  <Input.TextArea
                    rows={6}
                    placeholder="Provide a clear feedback if rejecting..."
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      background: "#f7f9fa",
                      borderRadius: 8,
                      border: "1px solid #e6e6e6",
                      fontSize: 14,
                    }}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    type="default"
                    danger
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      borderRadius: 8,
                      border: "none",
                      background: "#fff0f0",
                      color: "#FF4D4F",
                      fontWeight: 500,
                      width: "100%",
                    }}
                    onClick={() => {
                      handleReject(selected._id, rejectionReason);
                      setSelected(null);
                    }}
                  >
                    &#10005; Reject Submission
                  </Button>
                  <Button
                    type="primary"
                    disabled={selected.communityAcceptanceStatus !== "pending"}
                    style={{
                      borderRadius: 8,
                      background: "#38b2ac",
                      border: "none",
                      fontWeight: 500,
                      width: "100%",
                    }}
                    onClick={() => {
                      handleApprove(selected._id);
                      setSelected(null);
                    }}
                  >
                    &#10003; Approve Submission
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};
