import React, {useState, useEffect} from 'react';
import HeaderNew from '../../components/Header/HeaderNew';
import { useAuthentication } from '../../components/utils/provider';
import { CommunityService } from '../../services/CommunityService';
import { toast } from "react-toastify";
import { IoIosAddCircleOutline } from "react-icons/io";
import { RxLockClosed } from "react-icons/rx";
import { IoCheckmarkSharp } from "react-icons/io5";
import { LuUsers } from "react-icons/lu";
import { FiCopy } from "react-icons/fi";
import { MdNavigateNext } from "react-icons/md";
import { TbWorld } from "react-icons/tb";
import { LuPen } from "react-icons/lu";
import { GoAlertFill } from "react-icons/go";
import { useNavigate } from 'react-router-dom';

export const CreateNewCommunity = () => {

    const {
        connectWallet,
        currentUserWallet,
        bPnthrBalance
      } = useAuthentication();

    const navigate = useNavigate();

    const [copied, setCopied] = useState(false);

    const [showConnectWalletUI, setShowConnectWalletUI] = useState(true);
    const [showDaoSetup, setShowDaoSetup] = useState(false);
    const [showContributionSettings, setShowContributionSettings] = useState(false);
    const [showConfirmDaoDetails, setShowConfirmDaoDetails] = useState(false);
    const [showAboutCommunity, setShowAboutCommunity] = useState(false);
    const [showSuccessfulSubmit, setShowSuccessfulSubmit] = useState(false);
    
    // DAO form fields state for BE starts

    const [communityName, setCommunityName] = useState('');
    const [description, setDescription] = useState('');
    const [category, setCategory] = useState('');
    const [visibility, setVisibility] = useState('public');
    const [logo, setLogo] = useState(null);
    
    const [contributionType, setContributionType] = useState('fixed'); // 'fixed', 'minimum', 'custom'
    const [contributionAmounts, setContributionAmounts] = useState(['']);

    // Contribution wallet editing state
    const [isEditingWallet, setIsEditingWallet] = useState(false);
    const [customContributionWallet, setCustomContributionWallet] = useState('');

    const [aboutDescription, setAboutDescription] = useState('');

    // DAO form fields state for BE ends


    // Format wallet address for display
    const formatWalletAddress = (address) => {
        if (!address) return "Not connected";
        return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    };

    // When contributionType changes, reset contributionAmounts accordingly
    useEffect(() => {
        if (contributionType === 'custom') {
            setContributionAmounts(['', '', '']);
        } else {
            setContributionAmounts(['']);
        }
    }, [contributionType]);

    // Helper to map backend error messages to user-friendly messages
    const getFriendlyErrorMessage = (error) => {
        // MongoDB duplicate key error (community name exists)
        if (typeof error === "string" && error.includes("E11000 duplicate key error") && error.includes("communityName")) {
            return "A community with this name already exists. Please choose a different name.";
        }
        // Validation errors (add more as needed)
        if (typeof error === "string" && error.toLowerCase().includes("validation")) {
            return "Some required fields are missing or invalid. Please check your input.";
        }
        // General fallback
        if (typeof error === "string" && error.length < 120) {
            return error;
        }
        return "Failed to create community. Please try again.";
    };

    const handleSubmitCommunity = async () => {
        try {
            let info = {
                communityName,
                communityDescription: description,
                communityDetailedDescription: aboutDescription,
                category,
                visibility,
                logo, // can be File or null
                contributionType,
                contributionAmounts: contributionType === 'custom' ? contributionAmounts.filter(amount => amount.trim() !== '') : contributionAmounts,
                contributionWallet: customContributionWallet ? customContributionWallet : currentUserWallet,
                ownerWallet: currentUserWallet,
            }

            // Send to backend
            const response = await CommunityService.createCommunity(info);

            if (response.status === 201) {
                setShowAboutCommunity(false);
                setShowSuccessfulSubmit(true);
            }
            else {
                toast.error(getFriendlyErrorMessage(response.data?.error));
            }
        } catch (error) {
            toast.error(getFriendlyErrorMessage(error.response?.data?.error));
        }
    }

  return (
    <div className="">
        <div className="bg-BP-dark-grayish-blue">
            <HeaderNew />
        </div>

        <div className="py-10 px-2 sm:px-4 md:px-8">
            
            <div className="flex items-center justify-center text-2xl sm:text-3xl md:text-4xl text-[#2C5282] gap-3 sm:gap-4 md:gap-5 w-fit mx-auto">
                <IoIosAddCircleOutline className='text-3xl sm:text-4xl md:text-5xl' /> Create New Community
            </div>

            <p className="text-center text-[#2C5282] w-full sm:w-[80%] md:w-fit mt-3 mx-auto md:mx-[30%] text-sm sm:text-base">
                Launch your own decentralized community with custom governance rules and shared purpose.
            </p>

            {/* Steps */}
            <div className="flex items-center justify-center my-8 sm:my-10 flex-wrap w-fit mx-auto">
                {/* Step 1 */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full text-lg sm:text-2xl font-medium
                    ${showConnectWalletUI ? 'bg-[#2C5282] text-white' : !showConnectWalletUI ? 'bg-[#4FD1C5] text-white' : 'bg-[#2C5282] text-white'}`}>
                    {showConnectWalletUI ? '1' : <IoCheckmarkSharp className="text-lg sm:text-xl" />}
                </div>
                {/* Line 1-2 */}
                <div
                    style={{ width: '2.8rem', height: '4px', marginLeft: '-2px', marginRight: '-2px' }}
                    className={`transition-all duration-300 ${!showConnectWalletUI ? 'bg-[#4FD1C5]' : 'bg-[#4FD1C533]'}`}
                />
                {/* Step 2 */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full text-lg sm:text-2xl font-medium
                    ${showDaoSetup ? 'bg-[#2C5282] text-white' : (!showConnectWalletUI && !showDaoSetup) ? 'bg-[#4FD1C5] text-white' : 'bg-white text-[#2C5282]'}`}>
                    {(!showConnectWalletUI && !showDaoSetup) ? <IoCheckmarkSharp className="text-lg sm:text-xl" /> : '2'}
                </div>
                {/* Line 2-3 */}
                <div
                    style={{ width: '2.8rem', height: '4px', marginLeft: '-2px', marginRight: '-2px' }}
                    className={`transition-all duration-300 ${(showContributionSettings || showConfirmDaoDetails || showAboutCommunity || showSuccessfulSubmit) ? 'bg-[#4FD1C5]' : 'bg-[#4FD1C533]'}`}
                />
                {/* Step 3 */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full text-lg sm:text-2xl font-medium
                    ${showContributionSettings ? 'bg-[#2C5282] text-white' : (showConfirmDaoDetails || showAboutCommunity || showSuccessfulSubmit) ? 'bg-[#4FD1C5] text-white' : 'bg-white text-[#2C5282]'}`}>
                    {(showConfirmDaoDetails || showAboutCommunity || showSuccessfulSubmit) ? <IoCheckmarkSharp className="text-lg sm:text-xl" /> : '3'}
                </div>
                {/* Line 3-4 */}
                <div
                    style={{ width: '2.8rem', height: '4px', marginLeft: '-2px', marginRight: '-2px' }}
                    className={`transition-all duration-300 ${(showConfirmDaoDetails || showAboutCommunity || showSuccessfulSubmit) ? 'bg-[#4FD1C5]' : 'bg-[#4FD1C533]'}`}
                />
                {/* Step 4 */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full text-lg sm:text-2xl font-medium
                    ${showConfirmDaoDetails ? 'bg-[#2C5282] text-white' : (showAboutCommunity || showSuccessfulSubmit) ? 'bg-[#4FD1C5] text-white' : 'bg-white text-[#2C5282]'}`}>
                    {(showAboutCommunity || showSuccessfulSubmit) ? <IoCheckmarkSharp className="text-lg sm:text-xl" /> : '4'}
                </div>
                {/* Line 4-5 */}
                <div
                    style={{ width: '2.8rem', height: '4px', marginLeft: '-2px', marginRight: '-2px' }}
                    className={`transition-all duration-300 ${showAboutCommunity || showSuccessfulSubmit ? 'bg-[#4FD1C5]' : 'bg-[#4FD1C533]'}`}
                />
                {/* Step 5 */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full text-lg sm:text-2xl font-medium
                    ${showAboutCommunity ? 'bg-[#2C5282] text-white' : showSuccessfulSubmit ? 'bg-[#4FD1C5] text-white' : 'bg-white text-[#2C5282]'}`}>
                    {showSuccessfulSubmit ? <IoCheckmarkSharp className="text-lg sm:text-xl" /> : '5'}
                </div>
            </div>
            {/* Step content rendering */}
            { showConnectWalletUI && (

            <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 space-y-6 md:space-y-10 rounded-xl mx-auto lg:mx-[30%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">

                <div className="bg-BP-lightbaige p-2 rounded-full sm:p-2.5">
                    <RxLockClosed className='text-[#2C5282] text-2xl sm:text-3xl' />
                </div>

                <div className="w-full sm:w-[80%] md:w-[60%] space-y-2">
                    <p className="font-semibold text-lg sm:text-xl md:text-2xl text-center text-[#2C5282]">Connect Your Wallet</p>
                    <p className="text-xs sm:text-sm text-center text-[#2C5282]">To create a DAO, you need to connect a wallet that holds our governance token.</p>
                </div>

                { currentUserWallet && (
                    <div className="w-full space-y-4">

                        {bPnthrBalance < 100000 ? (

                        // Insufficient balance message

                        <div className="flex flex-col sm:flex-row justify-evenly items-center gap-3 bg-[rgb(252,247,186)] rounded-xl px-4 py-3 w-full">
                            <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-yellow-200">
                                <GoAlertFill className="text-BP-yellow text-2xl" />
                            </div>
                            <div className="flex flex-col space-y-2 text-center">
                                <span className="text-[#2C5282] font-medium text-base sm:text-lg">Wallet connected successfully!</span>
                                <span className="text-[#58759D] text-sm ">You don't have the required tokens to proceed</span>
                                <span className="text-[#58759D] text-sm font-semibold">Minimum BPNTHRQ required : 100,000</span>
                                <span className="text-[#58759D] text-sm "> Proceed with buying BPNTHRQ first </span>
                                <span className='text-[#2C5282] font-semibold text-sm sm:text-base text-center bg-BP-black/10 px-2 py-1 rounded-md'> <span className='text-[#58759D]'>Your Balance : </span> {bPnthrBalance} BPNTHRQ</span>
                            </div>
                        </div>

                        ):(
                        
                        // Success message

                        <div className="flex flex-col sm:flex-row justify-evenly items-center gap-3 bg-[#E6FAF3] rounded-xl px-4 py-3 w-full">
                            <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-[#4FD1C533]">
                                <IoCheckmarkSharp className="text-[#4FD1C5] text-2xl" />
                            </div>
                            <div className="flex flex-col space-y-2 text-center">
                                <span className="text-[#2C5282] font-medium text-base sm:text-lg">Wallet connected successfully!</span>
                                <span className="text-[#58759D] text-sm sm:text-base">You have the required tokens to proceed</span>
                                <span className='text-[#2C5282] font-semibold text-sm sm:text-base text-center bg-BP-black/10 px-2 py-1 rounded-md'> <span className='text-[#58759D]'>Your Balance : </span> {bPnthrBalance} BPNTHRQ</span>
                            </div>
                        </div>

                        )}

                        {/* Wallet address box */}
                        <div className="flex items-center gap-3 bg-BP-lightbaige rounded-xl px-4 py-2 w-full">
                            <span className="flex items-center justify-center w-8 h-8 rounded-full bg-[#2C52821A]">
                                <LuUsers className="text-[#2C5282] text-xl" />
                            </span>
                            <span className="text-[#2C5282] text-base font-medium flex-1">{formatWalletAddress(currentUserWallet)}</span>

                            <span
                                className='cursor-pointer hover:bg-[#2C52821A] rounded-full p-1.5 transition duration-300 flex items-center justify-center'
                                onClick={() => {
                                    navigator.clipboard.writeText(currentUserWallet);
                                    setCopied(true);
                                    setTimeout(() => setCopied(false), 1200);
                                }}
                                title={copied ? "Copied!" : "Copy address"}
                            >
                                {copied ? (
                                    <IoCheckmarkSharp className="text-[#2C5282] text-lg" />
                                ) : (
                                    <FiCopy className="text-[#2C5282] text-lg" />
                                )}
                            </span>
                        </div>
                    </div>
                )}

                { currentUserWallet ? (

                    bPnthrBalance > 100000 ? (

                    <a

                    href="https://pancakeswap.finance/swap?chain=bsc&outputCurrency=******************************************&inputCurrency=BNB&chainOut=bsc"
                    target="_blank"

                    className="bg-[#2C5282] text-white hover:text-white flex items-center justify-center space-x-5 px-4 sm:px-5 py-2 sm:py-2.5 rounded-lg mt-5 hover:bg-[#1d385a] transition duration-300 w-full text-lg font-medium shadow"
                    >
                        <span>Proceed To pancakeswap</span>
                        <MdNavigateNext className="text-2xl" />
                    </a>

                    ) : (

                    <button
                        onClick={() => {setShowDaoSetup(true); setShowConnectWalletUI(false);}}
                        className="bg-[#2C5282] text-white flex items-center justify-center space-x-5 px-4 sm:px-5 py-2 sm:py-2.5 rounded-lg mt-5 hover:bg-[#1d385a] transition duration-300 w-full text-lg font-medium shadow"
                    >
                        <span>Connect to DAO setup</span>
                        <MdNavigateNext className="text-2xl" />
                    </button>

                    )

                ):(
                    <button onClick={() => connectWallet()} className="bg-[#2C5282] text-white px-4 sm:px-5 py-2 sm:py-2.5 rounded-lg mt-5 hover:bg-[#1d385a] transition duration-300 w-full max-w-xs">
                        Connect Wallet
                    </button>
                )}

            </div>

            )}

            { showDaoSetup && (
                <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 space-y-6 md:space-y-10 rounded-xl mx-auto lg:mx-[20%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">
                    {/* DAO Details Form */}
                    <form className="w-full max-w-xl space-y-6">
                        <div className="flex items-center gap-2 mb-2">
                            <span className="text-[#2C5282] text-xl"><LuPen /></span>
                            <span className="text-[#2C5282] text-lg font-semibold">DAO Details</span>
                        </div>
                        {/* Community Name */}
                        <div>
                            <label className="block text-[#2C5282] text-sm font-medium mb-5">
                                Community Name <span className="text-[#2C5282]">*</span>
                            </label>
                            <input
                                type="text"
                                className="w-full border border-[#E2E8F0] rounded-xl px-4 py-3 text-[#2C5282] placeholder-[#A0AEC0] bg-white focus:outline-none focus:ring-2 focus:ring-[#2C5282] transition"
                                placeholder="e.g Web 3 Innovators collective"
                                value={communityName}
                                onChange={e => setCommunityName(e.target.value)}
                            />
                        </div>
                        {/* Description */}
                        <div>
                            <label className="block text-[#2C5282] text-sm font-medium mb-5">
                                Description <span className="text-[#2C5282]">*</span>
                            </label>
                            <textarea
                                rows={3}
                                className="w-full border border-[#E2E8F0] rounded-xl px-4 py-3 text-[#2C5282] placeholder-[#A0AEC0] bg-white focus:outline-none focus:ring-2 focus:ring-[#2C5282] transition resize-none"
                                placeholder="What is the purpose of the community"
                                value={description}
                                onChange={e => setDescription(e.target.value)}
                            />
                        </div>
                        {/* Category */}
                        <div>
                            <label className="block text-[#2C5282] text-sm font-medium mb-5">
                                # Category
                            </label>
                            <select
                                className="w-full border border-[#E2E8F0] rounded-xl px-4 py-3 text-[#2C5282] bg-white focus:outline-none focus:ring-2 focus:ring-[#2C5282] transition"
                                value={category}
                                onChange={e => setCategory(e.target.value)}
                            >
                                <option value="" disabled>Select category</option>
                                <option value="finance">Finance</option>
                                <option value="technology">Technology</option>
                                <option value="art">Art</option>
                                <option value="gaming">Gaming</option>
                                <option value="social">Social</option>
                                <option value="media">Media</option>
                                <option value="music">Music</option>
                                <option value="real-estate">Real Estate</option>
                                <option value="education">Education</option>
                                <option value="research">Research</option>
                                <option value="philanthropy">Philanthropy</option>
                                <option value="climate">Climate</option>
                                <option value="legal">Legal</option>
                                <option value="infrastructure">Infrastructure</option>
                                <option value="investment">Investment</option>
                                <option value="governance">Governance</option>
                                <option value="marketing">Marketing</option>
                                <option value="venture">Venture</option>
                                <option value="defi">DeFi</option>
                                <option value="science">Science</option>
                                <option value="health">Health</option>
                                <option value="ai">AI</option>
                                <option value="fashion">Fashion</option>
                                <option value="sports">Sports</option>
                                <option value="culture">Culture</option>
                                <option value="identity">Identity</option>
                                <option value="content-creation">Content Creation</option>
                                <option value="dao-tooling">DAO Tooling</option>
                                <option value="supply-chain">Supply Chain</option>
                                <option value="community">Community</option>
                                <option value="wellness">Wellness</option>
                                <option value="politics">Politics</option>
                                <option value="events">Events</option>
                                <option value="metaverse">Metaverse</option>
                                <option value="open-source">Open Source</option>
                                <option value="spiritual">Spiritual</option>
                                <option value="activism">Activism</option>
                                <option value="publishing">Publishing</option>
                                <option value="storytelling">Storytelling</option>
                                <option value="environment">Environment</option>
                                <option value="human-rights">Human Rights</option>
                                <option value="cybersecurity">Cybersecurity</option>
                                <option value="networking">Networking</option>
                                <option value="lifestyle">Lifestyle</option>
                                <option value="knowledge">Knowledge</option>
                                <option value="development">Development</option>
                                <option value="utilities">Utilities</option>
                                <option value="public-goods">Public Goods</option>
                            </select>
                        </div>
                        {/* Visibility */}
                        <div>
                            <label className="block text-[#2C5282] text-sm font-medium mb-5">
                                <span className="inline-flex items-center gap-1">
                                    <TbWorld className="inline-block" /> Visibility <span className="text-[#2C5282]">*</span>
                                </span>
                            </label>
                            <div className="flex items-center max-sm:gap-3 gap-8">
                                {/* Custom radio: Public */}
                                <label onClick={() => setVisibility('public')} className="flex items-center max-sm:gap-1 gap-2 cursor-pointer select-none">
                                    <span
                                        className={`w-5 max-sm:w-4 max-sm:h-4 h-5 flex items-center justify-center rounded-full border-2 transition
                                            ${visibility === 'public' ? 'border-[#2C5282]' : 'border-[#A0AEC0]'}`}
                                    >
                                        <span className={`w-2.5 h-2.5 max-sm:w-2 max-sm:h-2 rounded-full transition
                                            ${visibility === 'public' ? 'bg-[#2C5282]' : 'bg-white'}`}></span>
                                    </span>
                                    <span className="text-[#2C5282] text-sm max-sm:text-xs">Public (anyone can view)</span>
                                </label>
                                {/* Custom radio: Private */}
                                <label onClick={() => setVisibility('private')} className="flex items-center max-sm:gap-1 gap-2 cursor-pointer select-none">
                                    <span
                                        className={`w-5 max-sm:w-4 max-sm:h-4 h-5 flex items-center justify-center rounded-full border-2 transition
                                            ${visibility === 'private' ? 'border-[#2C5282]' : 'border-[#A0AEC0]'}`}
                                    >
                                        <span className={`w-2.5 h-2.5 max-sm:w-2 max-sm:h-2 rounded-full transition
                                            ${visibility === 'private' ? 'bg-[#2C5282]' : 'bg-white'}`}></span>
                                    </span>
                                    <span className="text-[#2C5282] text-sm max-sm:text-xs">Private (members only)</span>
                                </label>
                            </div>
                        </div>
                        {/* Community Logo */}
                        <div>
                            <label className="block text-[#2C5282] text-sm font-medium mb-5">
                                Community Logo
                            </label>
                            <div className="flex items-center gap-4">
                                <span className="w-12 h-12 rounded-full bg-BP-lightbaige flex items-center justify-center text-[#2C5282] text-2xl border border-[#E2E8F0] overflow-hidden">
                                    {logo ? (
                                        <img
                                            src={URL.createObjectURL(logo)}
                                            alt="Community Logo"
                                            className="object-cover w-full h-full"
                                        />
                                    ) : (
                                        <LuUsers />
                                    )}
                                </span>
                                <input
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                    id="community-logo-upload"
                                    onChange={e => {
                                        if (e.target.files && e.target.files[0]) setLogo(e.target.files[0]);
                                    }}
                                />
                                <label
                                    htmlFor="community-logo-upload"
                                    className="bg-BP-lightbaige text-[#2C5282] px-6 py-2 rounded-xl shadow text-base font-medium hover:bg-[#2C5282] hover:text-white transition cursor-pointer"
                                >
                                    {logo ? "Change" : "Upload"}
                                </label>
                                {logo && (
                                    <button
                                        type="button"
                                        className="ml-2 bg-red-100 text-red-400 px-4 py-2 rounded-xl shadow text-base font-medium hover:bg-red-200 transition cursor-pointer"
                                        onClick={() => setLogo(null)}
                                    >
                                        Remove
                                    </button>
                                )}
                            </div>
                            {logo && (
                                <div className="text-xs text-[#2C5282] mt-1 ml-[4.5rem] break-all max-w-[180px]">
                                    {
                                        (() => {
                                            const name = logo.name;
                                            const ext = name.substring(name.lastIndexOf('.'));
                                            const base = name.substring(0, name.length - ext.length);
                                            return base.length > 14
                                                ? `${base.slice(0, 8)}...${base.slice(-2)}${ext}`
                                                : name;
                                        })()
                                    }
                                </div>
                            )}
                        </div>
                        {/* Navigation Buttons */}
                        <div className="flex justify-end gap-4 pt-2">
                            <button
                                type="button"
                                className="border border-[#2C5282] text-[#2C5282] px-7 py-2 rounded-xl font-medium bg-white hover:bg-[#2C52824d] transition"
                                onClick={() => { setShowDaoSetup(false); setShowConnectWalletUI(true); }}
                            >
                                Back
                            </button>
                            <button
                                type="button"
                                className={`bg-[#2C5282] text-white px-8 py-2 rounded-xl font-medium transition ${
                                    (!communityName || !description || !category || !visibility )
                                        ? 'opacity-50 cursor-not-allowed'
                                        : 'hover:bg-[#1d385a]'
                                }`}
                                onClick={() => {
                                    if (communityName && description && category && visibility ) {
                                        setShowDaoSetup(false);
                                        setShowContributionSettings(true);
                                    }
                                }}
                                disabled={!communityName || !description || !category || !visibility }
                            >
                                Continue
                            </button>
                        </div>
                    </form>
                </div>
            )}

            { showContributionSettings && (
                <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 rounded-xl mx-auto lg:mx-[30%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">
                    <div className="flex flex-col items-center w-full max-w-md mx-auto">
                        {/* Check icon */}
                        <div className="w-14 h-14 rounded-full bg-[#4FD1C5] flex items-center justify-center mb-4 mt-2">
                            <IoCheckmarkSharp className="text-white text-3xl" />
                        </div>
                        {/* Heading */}
                        <div className="text-[#2C5282] text-2xl font-semibold text-center mb-1">Contribution Settings</div>
                        <div className="text-[#58759D] text-sm text-center mb-6">Configure how members will contribute to your community</div>
                        
                        {/* Contribution Type */}
                        <div className="w-full mb-6">
                            <label className="block text-[#2C5282] text-base font-semibold mb-3">
                                Contribution Type <span className="text-[#2C5282]">*</span>
                            </label>
                            <div className="flex gap-3">
                                <button
                                    type="button"
                                    className={`flex-1 px-4 py-2 rounded-xl border border-[#E2E8F0] font-medium transition focus:outline-none focus:ring-2 focus:ring-[#2C5282] ${
                                        contributionType === 'fixed'
                                            ? 'bg-[#2C5282] text-white'
                                            : 'bg-[#F9F7F2] text-[#2C5282]'
                                    }`}
                                    onClick={() => setContributionType('fixed')}
                                >
                                    Fixed <span className="block text-xs font-normal">Same for all</span>
                                </button>
                                <button
                                    type="button"
                                    className={`flex-1 px-4 py-2 rounded-xl border border-[#E2E8F0] font-medium transition focus:outline-none focus:ring-2 focus:ring-[#2C5282] ${
                                        contributionType === 'minimum'
                                            ? 'bg-[#2C5282] text-white'
                                            : 'bg-[#F9F7F2] text-[#2C5282]'
                                    }`}
                                    onClick={() => setContributionType('minimum')}
                                >
                                    Minimum <span className="block text-xs font-normal">Flexible</span>
                                </button>
                                <button
                                    type="button"
                                    className={`flex-1 px-4 py-2 rounded-xl border border-[#E2E8F0] font-medium transition focus:outline-none focus:ring-2 focus:ring-[#2C5282] ${
                                        contributionType === 'custom'
                                            ? 'bg-[#2C5282] text-white'
                                            : 'bg-[#F9F7F2] text-[#2C5282]'
                                    }`}
                                    onClick={() => setContributionType('custom')}
                                >
                                    Custom <span className="block text-xs font-normal]">Options</span>
                                </button>
                            </div>
                        </div>

                        {/* Contribution Amount(s) */}
                        <div className="w-full mb-2">
                            <label className="block text-[#2C5282] text-base font-semibold mb-2">
                                {contributionType === 'fixed' && <>Fixed Contribution Amount (USD) <span className="text-[#2C5282]">*</span></>}
                                {contributionType === 'minimum' && <>Minimum Contribution Amount (USD) <span className="text-[#2C5282]">*</span></>}
                                {contributionType === 'custom' && <>Custom Contribution Amounts (USD) <span className="text-[#2C5282]">*</span></>}
                            </label>
                            {contributionType === 'custom' ? (
                                <div className="flex flex-col gap-2">
                                    {contributionAmounts.map((amt, idx) => (
                                        <div key={idx} className="flex items-center border border-[#E2E8F0] rounded-xl px-3 py-2 bg-white">
                                            <span className="text-[#A0AEC0] text-lg mr-2">$</span>
                                            <input
                                                type="number"
                                                className="flex-1 bg-transparent outline-none text-[#2C5282] text-base"
                                                placeholder={`Option ${idx + 1}`}
                                                value={amt}
                                                min={0}
                                                onChange={e => {
                                                    const newAmounts = [...contributionAmounts];
                                                    newAmounts[idx] = e.target.value;
                                                    setContributionAmounts(newAmounts);
                                                }}
                                            />
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="flex items-center border border-[#E2E8F0] rounded-xl px-3 py-2 bg-white">
                                    <span className="text-[#A0AEC0] text-lg mr-2">$</span>
                                    <input
                                        type="number"
                                        className="flex-1 bg-transparent outline-none text-[#2C5282] text-base"
                                        placeholder={contributionType === 'fixed' ? "Fixed" : "Minimum"}
                                        value={contributionAmounts[0]}
                                        min={0}
                                        onChange={e => setContributionAmounts([e.target.value])}
                                    />
                                </div>
                            )}
                            <div className="text-xs text-[#58759D] mt-1">
                                {contributionType === 'fixed' && "All members will contribute this exact amount each month."}
                                {contributionType === 'minimum' && "Members must contribute at least this amount each month."}
                                {contributionType === 'custom' && "Members can choose from these custom contribution options."}
                            </div>
                        </div>

                        {/* Contribution Wallet */}
                        <div className="w-full mb-6">
                            <label className="block text-[#2C5282] text-base font-semibold mb-2">
                                Contribution Wallet <span className="text-[#2C5282]">*</span>
                            </label>
                            <div className="flex items-center border border-[#E2E8F0] rounded-xl px-3 py-2 bg-[#F9F7F2]">
                                {isEditingWallet ? (
                                    <>
                                        <input
                                            type="text"
                                            className="flex-1 bg-transparent outline-none text-[#2C5282] text-base"
                                            value={customContributionWallet}
                                            onChange={e => setCustomContributionWallet(e.target.value)}
                                            placeholder="Enter new wallet address"
                                            autoFocus
                                        />
                                        <span
                                            className="ml-2 cursor-pointer flex items-center justify-center bg-[#2C52821A] rounded-full p-1.5 transition duration-300"
                                            onClick={() => {
                                                setIsEditingWallet(false);
                                                // If blank, fallback to currentUserWallet
                                                setCustomContributionWallet(
                                                    customContributionWallet.trim() ? customContributionWallet.trim() : currentUserWallet
                                                );
                                            }}
                                            title="Save address"
                                        >
                                            <IoCheckmarkSharp className="text-[#2C5282] text-lg" />
                                        </span>
                                    </>
                                ) : (
                                    <>
                                        <input
                                            type="text"
                                            className="flex-1 bg-transparent outline-none text-[#2C5282] text-base"
                                            value={customContributionWallet || currentUserWallet || ''}
                                            readOnly
                                        />
                                        <span
                                            className={`ml-2 cursor-pointer flex items-center justify-center rounded-full p-1.5 transition duration-300 ${copied ? 'bg-[#2C52821A]' : ''}`}
                                            onClick={() => {
                                                const addr = customContributionWallet || currentUserWallet;
                                                if (addr) {
                                                    navigator.clipboard.writeText(addr);
                                                    setCopied(true);
                                                    setTimeout(() => setCopied(false), 1200);
                                                }
                                            }}
                                            title={copied ? "Copied!" : "Copy address"}
                                        >
                                            {copied ? (
                                                <IoCheckmarkSharp className="text-[#2C5282] text-lg transition-all duration-200" />
                                            ) : (
                                                <FiCopy className="text-[#2C5282] text-lg transition-all duration-200" />
                                            )}
                                        </span>
                                    </>
                                )}
                            </div>


                            {/* ---------------This was the button when click enabled the option to change the contribution wallet, uncomment when needed */}

                            {/* <div
                                className="text-xs text-[#2C5282] mt-2 flex items-center gap-1 cursor-pointer select-none"
                                onClick={() => {
                                    setIsEditingWallet(true);
                                    setCustomContributionWallet('');
                                }}
                            >
                                Change wallet address <MdNavigateNext className="inline text-base" />
                            </div> */}
                        </div>

                        {/* Buttons */}
                        <div className="flex justify-between w-full mt-2">
                            <button
                                type="button"
                                className="border border-[#2C5282] text-[#2C5282] px-7 py-2 rounded-xl font-medium bg-white hover:bg-[#2C52824d] transition"
                                onClick={() => { setShowContributionSettings(false); setShowDaoSetup(true); }}
                            >
                                Back
                            </button>
                            <button
                                type="button"
                                className={`bg-[#2C5282] text-white px-8 py-2 rounded-xl font-medium hover:bg-[#1d385a] transition flex items-center gap-2 ${
                                        contributionAmounts.some(a => !a || isNaN(Number(a)) || Number(a) <= 0)
                                        ? 'opacity-50 cursor-not-allowed'
                                        : 'hover:bg-[#1d385a]'
                                }`}
                                onClick={() => {
                                    setShowContributionSettings(false);
                                    setShowConfirmDaoDetails(true);
                                }}
                                disabled={
                                    contributionAmounts.some(a => !a || isNaN(Number(a)) || Number(a) <= 0)
                                }
                            >
                                Continue
                            </button>
                        </div>
                    </div>
                </div>
            )}

            { showConfirmDaoDetails && (
                <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 rounded-xl mx-auto lg:mx-[30%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">
                    <div className="flex flex-col items-center w-full max-w-md mx-auto">
                        {/* Check icon */}
                        <div className="w-14 h-14 rounded-full bg-[#E6FAF3] flex items-center justify-center mb-4 mt-2">
                            <IoCheckmarkSharp className="text-[#4FD1C5] text-3xl" />
                        </div>
                        {/* Heading */}
                        <div className="text-[#2C5282] text-2xl font-semibold text-center mb-1">Confirm DAO Details</div>
                        <div className="text-[#58759D] text-sm text-center mb-6">Review your community information before creation</div>
                        {/* Profile Picture */}
                        <div className="w-20 h-20 rounded-full bg-BP-lightbaige flex items-center justify-center text-[#2C5282] text-4xl border border-[#E2E8F0] overflow-hidden mb-4">
                            {logo ? (
                                <img
                                    src={URL.createObjectURL(logo)}
                                    alt="Community Logo"
                                    className="object-cover w-full h-full"
                                />
                            ) : (
                                <LuUsers />
                            )}
                        </div>
                        {/* Community Name */}
                        <div className="text-[#2C5282] text-xl font-semibold text-center mb-1">
                            {communityName || '-'}
                        </div>
                        {/* Description */}
                        <div
                            className="text-[#58759D] text-base text-left mb-4 w-full overflow-hidden"
                            style={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                maxWidth: '100%',
                                whiteSpace: 'normal'
                            }}
                            title={description}
                        >
                            {description || '-'}
                        </div>
                        {/* Details Table */}
                        <div className="w-full">
                            <div className="flex justify-between items-center py-3 border-b border-[#E2E8F0]">
                                <span className="text-[#58759D] text-base">Category:</span>
                                <span className="text-[#2C5282] font-medium text-base text-right">
                                    {category ? category.charAt(0).toUpperCase() + category.slice(1) : '-'}
                                </span>
                            </div>
                            <div className="flex justify-between items-center py-3 border-b border-[#E2E8F0]">
                                <span className="text-[#58759D] text-base">Visibility:</span>
                                <span className="text-[#2C5282] font-medium text-base text-right">
                                    {visibility ? visibility.charAt(0).toUpperCase() + visibility.slice(1) : '-'}
                                </span>
                            </div>
                            <div className="flex justify-between items-center py-3 border-b border-[#E2E8F0]">
                                <span className="text-[#58759D] text-base"> Contribution:</span>
                                <span className="text-[#2C5282] font-medium text-base text-right">
                                    ${contributionAmounts.filter(a => a).join(', ')}
                                </span>
                            </div>
                            <div className="flex justify-between items-center py-3 border-b border-[#E2E8F0]">
                                <span className="text-[#58759D] text-base"> Wallet Address:</span>
                                <span className="text-[#2C5282] font-medium text-base text-right">
                                    {formatWalletAddress(customContributionWallet || currentUserWallet)}
                                </span>
                            </div>
                        </div>
                        {/* Warning box */}
                        {/* <div className="bg-BP-lightbaige text-[#58759D] text-xs rounded-lg px-4 py-3 mt-6 mb-4 w-full text-center">
                            Creating a DAO will initiate a smart contract deployment. This action cannot be undone.
                        </div> */}
                        {/* Buttons */}
                        <div className="flex justify-end gap-4 w-full mt-2">
                            <button
                                type="button"
                                className="border border-[#2C5282] text-[#2C5282] px-7 py-2 rounded-xl font-medium bg-white hover:bg-[#2C52824d] transition"
                                onClick={() => { setShowConfirmDaoDetails(false); setShowContributionSettings(true); }}
                            >
                                Back
                            </button>
                            <button
                                type="button"
                                className="bg-[#2C5282] text-white px-8 py-2 rounded-xl font-medium hover:bg-[#1d385a] transition flex items-center gap-2"
                                onClick={() => { setShowConfirmDaoDetails(false); setShowAboutCommunity(true); }}
                            >
                                Continue
                            </button>
                        </div>
                    </div>
                </div>
            )}

            { showAboutCommunity && (
                <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 rounded-xl mx-auto lg:mx-[30%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">
                    <div className="flex flex-col items-center w-full max-w-md mx-auto">
                        {/* Check icon */}
                        <div className="w-14 h-14 rounded-full bg-[#4FD1C5] flex items-center justify-center mb-4 mt-2">
                            <IoCheckmarkSharp className="text-white text-3xl" />
                        </div>
                        {/* Heading */}
                        <div className="text-[#2C5282] text-2xl font-semibold text-center mb-1">Tell Us About Your Community</div>
                        <div className="text-[#58759D] text-sm text-center mb-8">Help us understand your community's purpose and goals</div>
                        {/* Detailed Description */}
                        <div className="w-full mb-2">
                            <label className="block text-[#2C5282] text-base font-semibold mb-2">
                                Detailed Description <span className="text-[#2C5282]">*</span>
                            </label>
                            <textarea
                                rows={15}
                                className="w-full border border-[#E2E8F0] rounded-xl px-4 py-3 text-[#2C5282] placeholder-[#A0AEC0] bg-white focus:outline-none focus:ring-2 focus:ring-[#2C5282] transition resize-none text-base"
                                placeholder="Explain what your community is about, its goals, and why it should be approved..."
                                value={aboutDescription}
                                onChange={e => setAboutDescription(e.target.value)}
                            />
                        </div>
                        <div className="w-full text-xs text-[#58759D] mt-2 mb-6">
                            This information will help our team review your submission.
                        </div>
                        {/* Buttons */}
                        <div className="flex justify-between w-full mt-2">
                            <button
                                type="button"
                                className="border border-[#2C5282] text-[#2C5282] px-7 py-2 rounded-xl font-medium bg-white hover:bg-[#2C52824d] transition"
                                onClick={() => { setShowAboutCommunity(false); setShowConfirmDaoDetails(true); }}
                            >
                                Back
                            </button>
                            <button
                                type="button"
                                className="bg-[#2C5282] text-white px-8 py-2 rounded-xl font-medium hover:bg-[#1d385a] transition flex items-center gap-2"
                                onClick={() => {
                                    handleSubmitCommunity();
                                }}
                            >
                                Submit for approval <MdNavigateNext className="text-xl" />
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {showSuccessfulSubmit && (
                <div className="bg-BP-opacited-white p-4 sm:p-6 md:p-10 rounded-xl mx-auto lg:mx-[30%] flex flex-col items-center w-full sm:w-[90%] md:w-auto">
                    <div className="flex flex-col items-center w-full max-w-md mx-auto">
                        {/* Check icon */}
                        <div className="w-14 h-14 rounded-full bg-[#4FD1C5] flex items-center justify-center mb-4 mt-2 shadow-lg">
                            <IoCheckmarkSharp className="text-white text-3xl" />
                        </div>
                        {/* Heading */}
                        <div className="text-[#2C5282] text-2xl font-semibold text-center mb-2">
                            Submission Successful
                        </div>
                        <div className="text-[#58759D] text-base text-center mb-6">
                            Your community creation request has been received and is now <span className="font-semibold text-[#2C5282]">under review</span> by our team.
                        </div>
                        <div className="bg-[#E6FAF3] border border-[#4FD1C5] rounded-lg px-4 py-3 mb-4 w-full text-center text-[#2C5282] text-sm shadow">
                            <span className="font-semibold">What happens next?</span>
                            <ul className="list-disc list-inside mt-2 text-left text-[#58759D]">
                                <li>Our team will carefully review your submission to ensure it meets our guidelines.</li>
                                <li>If approved, your community will be published and you will be notified.</li>
                                <li>If we find any issues, your submission may be rejected with clear reasons provided.</li>
                                <li>You can edit and reapply if changes are needed.</li>
                            </ul>
                        </div>
                        <div className="text-[#2C5282] text-xs text-center mt-2">
                            Thank you for contributing to the Black Panther !
                        </div>
                        <button
                             type="button"
                             className="bg-[#2C5282] text-white px-8 py-2 rounded-md my-3 font-medium hover:bg-[#1d385a] transition flex items-center gap-2"
                             onClick={() => {navigate('/');}}
                         >
                            Back to Home
                         </button>
                    </div>
                </div>
            )}

        </div>

    </div>
  );
};