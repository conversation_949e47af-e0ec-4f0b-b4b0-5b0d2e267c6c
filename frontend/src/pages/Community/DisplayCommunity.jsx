import React, { useEffect, useState } from "react";
import { CommunityService } from "../../services/CommunityService";
import { Card, Spin, Empty } from "antd";

export const DisplayCommunity = () => {
  const [communities, setCommunities] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCommunities = async () => {
      setLoading(true);
      const data = await CommunityService.getAllCommunityInfoToApprove();
      setCommunities(data.data.communities || []);
      console.log("Fetched Communities:", data.data.communities);
      setLoading(false);
    };
    fetchCommunities();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[40vh]">
        <Spin size="large" />
      </div>
    );
  }

  if (!communities.length) {
    return (
      <div className="flex justify-center items-center min-h-[40vh]">
        <Empty description="No communities found." />
      </div>
    );
  }

  return (
    <div className="p-6 bg-[#f7f9fa] min-h-screen">
      <h2 className="text-3xl font-bold text-center mb-8 text-[#2C5282]">All Communities</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {communities.map((community) => (
          <Card
            key={community._id}
            className="shadow-lg rounded-xl overflow-hidden"
            styles={{ body: { padding: 0 } }}
          >
            <div className="flex flex-col h-full">
              {/* Logo/Image */}
              <div className="bg-[#e6f0fa] flex items-center justify-center h-48">
                {community.logo ? (
                  <img
                    src={community.logo}
                    alt={community.communityName}
                    className="object-cover h-40 w-40 rounded-full border-4 border-white shadow"
                    crossOrigin="anonymous"
                  />
                ) : (
                  <div className="h-40 w-40 rounded-full bg-gray-200 flex items-center justify-center text-4xl text-gray-400">
                    <span role="img" aria-label="No Logo">👥</span>
                  </div>
                )}
              </div>
              {/* Info */}
              <div className="p-5 flex-1 flex flex-col">
                <h3 className="text-xl font-bold text-[#2C5282] mb-2">{community.communityName}</h3>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Category:</span> {community.category}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Visibility:</span> {community.visibility}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Contribution Type:</span> {community.contributionType}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Contribution Amount(s):</span>{" "}
                  {Array.isArray(community.contributionAmounts)
                    ? community.contributionAmounts.join(", ")
                    : community.contributionAmounts}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Contribution Wallet:</span> {community.contributionWallet}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Members Limit:</span> {community.communityMembers}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Registered Members:</span> {community.registeredMembers?.length || 0}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Status:</span> {community.communityAcceptanceStatus}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Admin:</span>{" "}
                  {community.communityAdmin
                    ? `${community.communityAdmin.firstName || ""} ${community.communityAdmin.lastName || ""} (${community.communityAdmin.email || ""})`
                    : "N/A"}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Short Description:</span> {community.communityDescription}
                </div>
                <div className="text-sm text-[#58759D] mb-2">
                  <span className="font-semibold">Detailed Description:</span>
                  <div className="bg-[#f9fafb] rounded p-2 mt-1 text-xs text-[#2C5282] max-h-32 overflow-y-auto">
                    {community.communityDetailedDescription}
                  </div>
                </div>
                {community.rejectionReason && (
                  <div className="text-xs text-red-500 mt-2">
                    <span className="font-semibold">Rejection Reason:</span> {community.rejectionReason}
                  </div>
                )}
                <div className="text-xs text-gray-400 mt-4">
                  Created: {new Date(community.createdAt).toLocaleString()}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};