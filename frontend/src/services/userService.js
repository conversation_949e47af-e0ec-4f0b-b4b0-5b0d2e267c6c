import { toast } from 'react-toastify';
import httpClient from '../components/httpClient/httpClient';

export const UserService = {
  UpdateUserDetails: async (payload, getCurrentUser) => {
    try {
      const response = await httpClient.put('user/update_user', payload);
      if (response?.status === 200) {
        toast.success('Profile updated successfully');
        await getCurrentUser();
        return true;
      }
      toast.error(response?.data?.message || 'Update failed');
      return false;
    } catch (error) {
      console.error('Update user error:', error);
      if (error.response?.status === 404) {
        toast.error('User not found');
      } else if (error.code === 'ERR_NETWORK') {
        toast.error(
          'Cannot connect to server. Please check if the backend is running.'
        );
      } else {
        toast.error(
          error.response?.data?.message ||
            'An error occurred while updating profile'
        );
      }

      return false;
    }
  },
  ChangePasswordWithVerification: async (payload) => {
    try {
      // Validate payload before sending
      if (
        !payload.currentPassword ||
        !payload.newPassword ||
        !payload.confirmPassword
      ) {
        throw new Error('All fields are required');
      }
      const response = await httpClient.put(
        'user/change_password_with_verification',
        payload
      );

      if (response?.status === 200) {
        return true;
      }

      throw new Error(response?.data?.message || 'Failed to change password');
    } catch (error) {
      console.error('Change password error details:', {
        status: error.response?.status,
        message: error.response?.data?.message,
        error: error.message,
      });

      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error(error.message || 'Failed to update password');
      }
      return false;
    }
  },

  getAllUsers: async () => {
    try {
      const response = await httpClient.get('user/all');
      if (response?.status === 200) {
        return response.data.users;
      }
      // toast.error(response?.data?.message || 'Failed to fetch users');
      return [];
    } catch (error) {
      console.error('Fetch users error:', error);
      toast.error(
        // error.response?.data?.message || 'Failed to fetch users'
      );
      return [];
    }
  },

  getAllUserCount: async () => {
    try {
      const response = await httpClient.get('user/get_all_user_count');
      if (response?.status === 200) {
        return response.data.count;
      }
      toast.error(response?.data?.message || 'Failed to fetch user count');
      return 0;
    } catch (error) {
      console.error('Fetch user count error:', error);
      toast.error(
        error.response?.data?.message || 'Failed to fetch user count'
      );
      return 0;
    }
  },
  
  promoteToAdmin: async (userId) => {
    try {
      const response = await httpClient.post('user/promote', { userId });
      if (response?.status === 200) {
        toast.success('User promoted to Admin successfully');
        return response.data.user;
      }
      toast.error(response?.data?.message || 'Failed to promote user');
      return null;
    } catch (error) {
      console.error('Promote user error:', error);
      toast.error(
        error.response?.data?.message || 'Failed to promote user'
      );
      return null;
    }
  },

  removeUser: async (userId) => {
    try {
      const response = await httpClient.post('user/remove', { userId });
      if (response?.status === 200) {
        toast.success('User removed successfully');
        return response.data.user;
      }
      toast.error(response?.data?.message || 'Failed to remove user');
      return null;
    } catch (error) {
      console.error('Remove user error:', error);
      toast.error(
        error.response?.data?.message || 'Failed to remove user'
      );
      return null;
    }
  },
  demoteToMember: async (userId) => {
    try {
      const response = await httpClient.post('user/demote', { userId });
      if (response?.status === 200) {
        toast.success('Admin demoted to Member successfully');
        return response.data.user;
      }
      toast.error(response?.data?.message || 'Failed to demote admin');
      return null;
    } catch (error) {
      console.error('Demote admin error:', error);
      toast.error(
        error.response?.data?.message || 'Failed to demote admin'
      );
      return null;
    }
  },
};
