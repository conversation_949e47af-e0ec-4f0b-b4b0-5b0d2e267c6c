import httpClient from "../components/httpClient/httpClient";

class AffiliateService {
  
  
  // Generate individual affiliate code
  async generateAffiliateCode(config = {}) {
    try {
      const response = await httpClient.post("affiliate/generate-code", config);
      return response.data;
    } catch (error) {
      console.error("Error generating affiliate code:", error);
      throw error;
    }
  }

  // Regenerate affiliate code (creates new link when old one expires/fills up)
  async regenerateAffiliateCode(config = {}) {
    try {
      const response = await httpClient.post("affiliate/regenerate-code", config);
      return response.data;
    } catch (error) {
      console.error("Error regenerating affiliate code:", error);
      throw error;
    }
  }

 
}

export default new AffiliateService();
