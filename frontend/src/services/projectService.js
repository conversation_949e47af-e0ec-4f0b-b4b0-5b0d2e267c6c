import { toast } from 'react-toastify';
import httpClient from '../components/httpClient/httpClient';

export class ProjectService {
  static async getOngoingProjects() {
    try {
      const response = await httpClient.get('project/ongoing');
      if (response.status === 200) {
        return response.data.projects;
      }
      return [];
    } catch (error) {
      console.error('Error fetching ongoing projects:', error);
      toast.error('Failed to fetch ongoing projects. Please try again.');
      return [];
    }
  }

  static async getCompletedProjects() {
    try {
      const response = await httpClient.get('project/completed');
      if (response.status === 200) {
        return response.data.projects;
      }
      return [];
    } catch (error) {
      console.error('Error fetching completed projects:', error);
      toast.error('Failed to fetch completed projects. Please try again.');
      return [];
    }
  }

 static async completeProject(projectId) {
  try {
    console.log('Calling API to complete project:', projectId); // <-- Add this
    const response = await httpClient.post('/project/complete', { projectId }); // <-- Use leading slash
    if (response.status === 200) {
      toast.success('Project marked as completed');
      return true;
    }
    toast.error(response.data?.message || 'Failed to complete project');
    return false;
  } catch (error) {
    console.error('Error completing project:', error);
    toast.error('Failed to complete project. Please try again.');
    return false;
  }
}

  static async addUpdate(projectId, text, image = null) {
    try {
      let response;
      if (image) {
        const formData = new FormData();
        formData.append('text', text);
        formData.append('uploaded_file', image);
        response = await httpClient.post(
          `project/${projectId}/updates`,
          formData,
          {
            headers: { 'Content-Type': 'multipart/form-data' },
          }
        );
      } else {
        response = await httpClient.post(`project/${projectId}/updates`, {
          text,
        });
      }
      if (response.status === 200) {
        // toast.success('Update submitted successfully');
        return response.data;
      }
      toast.error(response.data?.message || 'Failed to add update');
      throw new Error('Failed to add update');
    } catch (error) {
      console.error('Error adding update:', error);
      toast.error('Failed to add update. Please try again.');
      throw error;
    }
  }

  static async updateProjectUpdate(projectId, updateId, text, image = null) {
    try {
      let response;
      if (image) {
        const formData = new FormData();
        formData.append('text', text);
        formData.append('uploaded_file', image); 
        response = await httpClient.put(
          `project/${projectId}/updates/${updateId}`,
          formData,
          {
            headers: { 'Content-Type': 'multipart/form-data' },
          }
        );
      } else {
        response = await httpClient.put(
          `project/${projectId}/updates/${updateId}`,
          { text }
        );
      }
      if (response.status === 200) {
        return response.data;
      }
      throw new Error(response.data?.error || 'Failed to edit update');
    } catch (error) {
      throw error;
    }
  }
  static async deleteProjectUpdate(projectId, updateId) {
    try {
      const response = await httpClient.delete(
        `project/${projectId}/delete/${updateId}`
      );
      if (response.status === 200) {
        toast.success('Update deleted successfully');
        return response.data;
      }
      toast.error(response.data?.error || 'Failed to delete update');
      throw new Error('Failed to delete update');
    } catch (error) {
      console.error('Error deleting update:', error);
      toast.error('Failed to delete update. Please try again.');
      throw error;
    }
  }

   static async updateProjectImage(projectId, file) {
    try {
      const formData = new FormData();
      formData.append('uploaded_file', file);

      const response = await httpClient.put(
        `vote/${projectId}/image`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        }
      );
      if (response.status === 200) {
        toast.success('Project image updated successfully');
        return response.data.project;
      }
      toast.error(response.data?.message || 'Failed to update project image');
      throw new Error('Failed to update project image');
    } catch (error) {
      console.error('Error updating project image:', error);
      toast.error('Failed to update project image. Please try again.');
      throw error;
    }
  }

  static async updateProjectUpdateImage(projectId, updateId, file) {
    try {
      const formData = new FormData();
      formData.append('uploaded_file', file);

      const response = await httpClient.put(
        `project/${projectId}/updates/${updateId}/image`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
        }
      );
      if (response.status === 200) {
        toast.success('Update image updated successfully');
        return response.data.project;
      }
      toast.error(response.data?.message || 'Failed to update update image');
      throw new Error('Failed to update update image');
    } catch (error) {
      console.error('Error updating update image:', error);
      toast.error('Failed to update update image. Please try again.');
      throw error;
    }
  }

  static async updateProjectUpdateImageAndText(projectId, updateId, text, file) {
  try {
    const formData = new FormData();
    if (text !== undefined) formData.append('text', text);
    if (file) formData.append('uploaded_file', file);

    const response = await httpClient.put(
      `project/${projectId}/updates/${updateId}/image`,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      }
    );
    if (response.status === 200) {
      toast.success('Update edited successfully');
      return response.data.project;
    }
    // toast.error(response.data?.message || 'Failed to edit update');
    // throw new Error('Failed to edit update');
  } catch (error) {
    console.error('Error editing update:', error);
    toast.error('Failed to edit update. Please try again.');
    throw error;
  }
}

  /**
   * Fetch multiple projects by their IDs.
   * @param {string[]} projectIds - Array of project IDs.
   * @returns {Promise<Array>} Array of project objects.
   */
  static async getProjectsByIds(projectIds) {
    try {
      const response = await httpClient.post('/projectid', { projectIds });
      if (response.status === 200) {
        return response.data.projects;
      }
      return [];
    } catch (error) {
      console.error('Error fetching projects by IDs:', error);
      return [];
    }
  }
}
