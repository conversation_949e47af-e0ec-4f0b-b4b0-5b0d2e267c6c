// Currency API service for backend integration
import { convertUSDToLocalCurrency, getCurrencyInfo, formatCurrencyAmount } from '../utils/currencyUtils';

/**
 * Currency Service for handling all currency-related operations
 */
class CurrencyService {
    /**
     * Convert USD amount to target country's local currency
     * @param {number|string} usdAmount - Amount in USD
     * @param {string} countryName - Name of the target country
     * @returns {Promise<Object>} Conversion result with local currency details
     */
    static async convertToLocalCurrency(usdAmount, countryName) {
        try {
            const conversion = await convertUSDToLocalCurrency(usdAmount, countryName);
            
            return {
                success: conversion.success,
                originalAmount: {
                    value: parseFloat(usdAmount),
                    currency: 'USD',
                    formatted: `$${parseFloat(usdAmount).toFixed(2)}`
                },
                convertedAmount: {
                    value: conversion.convertedAmount,
                    currency: conversion.currencyInfo.code,
                    symbol: conversion.currencyInfo.symbol,
                    formatted: conversion.formattedAmount
                },
                exchangeRate: {
                    rate: conversion.rate,
                    fromCurrency: 'USD',
                    toCurrency: conversion.currencyInfo.code,
                    timestamp: conversion.timestamp
                },
                country: countryName,
                error: conversion.error || null
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                originalAmount: {
                    value: parseFloat(usdAmount),
                    currency: 'USD',
                    formatted: `$${parseFloat(usdAmount).toFixed(2)}`
                },
                convertedAmount: null,
                exchangeRate: null,
                country: countryName
            };
        }
    }

    /**
     * Get currency information for a country
     * @param {string} countryName - Name of the country
     * @returns {Object} Currency information
     */
    static getCurrencyInfo(countryName) {
        return getCurrencyInfo(countryName);
    }

    /**
     * Format currency amount with proper symbol and formatting
     * @param {number} amount - Amount to format
     * @param {string} currencyCode - Currency code (e.g., 'KES', 'USD')
     * @returns {string} Formatted currency string
     */
    static formatAmount(amount, currencyCode) {
        return formatCurrencyAmount(amount, currencyCode);
    }

    /**
     * Prepare payment data with currency conversion for backend
     * @param {Object} paymentData - Original payment data
     * @param {string} countryName - Selected country name
     * @returns {Promise<Object>} Enhanced payment data with currency conversion
     */
    static async preparePaymentData(paymentData, countryName) {
        const { amount } = paymentData;
        
        try {
            const currencyConversion = await this.convertToLocalCurrency(amount, countryName);
            
            return {
                ...paymentData,
                currency: {
                    original: currencyConversion.originalAmount,
                    converted: currencyConversion.convertedAmount,
                    exchangeRate: currencyConversion.exchangeRate,
                    conversionSuccess: currencyConversion.success
                },
                country: countryName,
                // Amount to actually charge (can be in local currency or USD based on your needs)
                chargeAmount: currencyConversion.success 
                    ? currencyConversion.convertedAmount.value 
                    : currencyConversion.originalAmount.value,
                chargeCurrency: currencyConversion.success 
                    ? currencyConversion.convertedAmount.currency 
                    : 'USD'
            };
        } catch (error) {
            console.error('Error preparing payment data:', error);
            return {
                ...paymentData,
                currency: {
                    original: {
                        value: parseFloat(amount),
                        currency: 'USD',
                        formatted: `$${parseFloat(amount).toFixed(2)}`
                    },
                    converted: null,
                    exchangeRate: null,
                    conversionSuccess: false,
                    error: error.message
                },
                country: countryName,
                chargeAmount: parseFloat(amount),
                chargeCurrency: 'USD'
            };
        }
    }

    /**
     * Validate currency conversion before processing payment
     * @param {number} amount - Amount to validate
     * @param {string} countryName - Country name
     * @returns {Promise<Object>} Validation result
     */
    static async validateCurrencyConversion(amount, countryName) {
        if (!amount || amount <= 0) {
            return {
                valid: false,
                error: 'Invalid amount provided'
            };
        }

        if (!countryName) {
            return {
                valid: false,
                error: 'Country name is required'
            };
        }

        try {
            const conversion = await this.convertToLocalCurrency(amount, countryName);
            
            return {
                valid: conversion.success,
                conversion: conversion,
                error: conversion.error
            };
        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }
}

export default CurrencyService;
