import httpClient from "../components/httpClient/httpClient";

export async function mpesaSTK(data) {
    try {
        
        const res = await httpClient.post("/payments/mpesaSTK", data);
        return res.data;
    } catch (error) {
        console.error("M-Pesa STK Error:", error);
        // Return error information for proper handling
        throw error.response?.data || { errorMessage: "Network error occurred" };
    }
}

export async function mpesaStatus(sessionID) {
    try {
        const res = await httpClient.get("/payments/mpesaStatus", {
            params: { sessionID }
        });
        return res.data;
    } catch (error) {
        console.error("M-Pesa Status Error:", error);
        // Return error information for proper handling
        throw error.response?.data || { errorMessage: "Failed to check payment status" };
    }
}

export async function getBNBValue() {
    try {
        const res = await httpClient.get("/supply/value_of_one_bnb");
        return res.data.value;
    } catch (error) {
        console.error("BNB Value Error:", error);
        throw error.response?.data || { errorMessage: "Failed to fetch BNB value" };
    }
}

export async function getListOfTransactions() {
    try {
        const res = await httpClient.get("/supply/transactions_bnb");
        if(res.status !== 200) {
            throw new Error("Failed to fetch transactions");
        } else {
            return res.data.list;
        }
    } catch (error) {
        console.error("Transactions List Error:", error);
        throw error.response?.data || { errorMessage: "Failed to fetch transactions" };
    }
}