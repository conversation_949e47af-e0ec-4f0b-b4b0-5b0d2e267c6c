import { toast } from "react-toastify";
import httpClient from "../components/httpClient/httpClient";

export class VoteService {
 
  static async createVote(payload, isFormData = false) {
  try {
    const config = isFormData
      ? { headers: { 'Content-Type': 'multipart/form-data' } }
      : {};
    const response = await httpClient.post('vote/create', payload, config);
    return response.status === 201;
  } catch (error) {
    if (error.response) {
      console.log('Backend error:', error.response.data);
      return error.response.data;
    }
    return false;
  }
}


  static async getAllVotes() {
    try {
      const response = await httpClient.get("vote/get_vote");
      if (response.status === 200) {
        return response.data.votes; // Array of votes
      }
      return [];
    } catch (error) {
      console.error("Error fetching all votes:", error);
      return [];
    }
  }
  static async saveSelectedVote(payload) {
    try {
      const response = await httpClient.post("vote/save_selected_vote", payload);
      if (response.status === 200) {
        toast.success("Vote submitted successfully");
        return true;
      }
      toast.error(response.data?.message || "Failed to save selected vote");
      return false;
    } catch (error) {
      console.error("Error saving selected vote:", error);
      toast.error("Failed to save selected vote. Please try again.");
      return false;
    }
  }
 static async getActiveVotes() {
  try {
    const response = await httpClient.get("vote/get_active_votes");
    if (response.status === 200 && Array.isArray(response.data.votes)) {
      // Ensure each vote and its projects are normalized (id field, etc.)
      return response.data.votes.map(vote => ({
        ...vote,
        id: vote.id || vote._id,
        projects: (vote.projects || []).map(project => ({
          ...project,
          id: project.id || project._id,
        })),
      }));
    }
    toast.error(response.data?.message || "No ongoing votes found.");
    return [];
  } catch (error) {
    console.error("Error fetching ongoing votes:", error);
    toast.error(
      error.response?.data?.message ||
      "Failed to fetch ongoing votes. Please try again."
    );
    return [];
  }
}


static async extendVote(voteId, showToast = true) {
  try {
    const response = await httpClient.patch(`vote/extend_vote/${voteId}`);
    if (response.status === 200) {
      return response.data;
    }
    if (showToast) toast.error(response.data?.message || "Failed to extend vote");
    return null;
  } catch (error) {
    console.error("Error extending vote:", error);
    if (showToast) toast.error(error.response?.data?.message || "Failed to extend vote. Please try again.");
    return null;
  }
}


static async updateVote(payload) {
  try {
    const response = await httpClient.post("vote/update_vote", payload);
    if (response.status === 200) {
      toast.success("Vote updated successfully");
      return response.data;
    }
    toast.error(response.data?.message || "Failed to update vote");
    return response;
  } catch (error) {
    console.error("Error updating vote:", error);
    toast.error("Failed to update vote. Please try again.");
    return { 
      status: 500, 
      data: { 
        message: error.response?.data?.message || "Failed to update vote" 
      } 
    };
  }
}





  static async incrementVoteCount(payload) {
    try {
      const response = await httpClient.post("vote/increment_vote_count", payload);
      if (response.status === 200) {
        toast.success("Vote count updated successfully");
        return true;
      }
      toast.error(response.data?.message || "Failed to increment vote count");
      return false;
    } catch (error) {
      console.error("Error incrementing vote count:", error);
      toast.error("Failed to increment vote count. Please try again.");
      return false;
    }
  }

// End a vote early
static async endVoteEarly(payload, showToast = true) {
  try {
    const response = await httpClient.post("vote/end_vote_early", payload);
    if (response.status === 200) {
      return true;
    }
    if (showToast) toast.error(response.data?.message || "Failed to end vote early");
    return false;
  } catch (error) {
    console.error("Error ending vote early:", error);
    if (showToast) toast.error("Failed to end vote early. Please try again.");
    return false;
  }
}

// Fetch all ended votes
static async getEndedVotes() {
  try {
    const response = await httpClient.get("vote/get_ended_votes");
    if (response.status === 200 && Array.isArray(response.data.votes)) {
      return { votes: response.data.votes };
    }
    toast.error(response.data?.message || "Failed to fetch ended votes");
    return { votes: [] };
  } catch (error) {
    console.error("Error fetching ended votes:", error);
    toast.error(error.response?.data?.message || "Failed to fetch ended votes. Please try again.");
    return { votes: [] };
  }
}

// Start a project from a completed vote
static async startProject(voteId) {
  try {
    const response = await httpClient.post("vote/start_project", { voteId });
    if (response.status === 200) {
      toast.success("Project started successfully");
      return true;
    }
    toast.error(response.data?.message || "Failed to start project");
    return false;
  } catch (error) {
    console.error("Error starting project:", error);
    toast.error("Failed to start project. Please try again.");
    return false;
  }
}
static async submitMultiVote({ voteId, projectId, vote }) {
  if (!voteId || !projectId) {
    return { error: 'Both voteId and projectId are required.' };
  }
  try {
    const response = await httpClient.post(
      `/vote/submit_multi_vote/${voteId}`,
      { voteId, projectId, vote } // Always send both IDs in the body
    );
    if (response.status === 200) {
      return response.data; // contains updated voteCount, userVote, etc.
    }
    return { error: response.data?.error || 'Failed to submit vote.' };
  } catch (error) {
    console.error('Error submitting multi-vote:', error);
    return { error: error.response?.data?.error || 'Failed to submit vote.' };
  }
}


static async submitRankedVote({ voteId, rankings }) {
    if (!voteId || !Array.isArray(rankings)) {
      return { error: 'voteId and rankings array are required.' };
    }
    try {
      const response = await httpClient.post(
        `/vote/submit_ranked_vote/${voteId}`,
        { rankings }
      );
      if (response.status === 200) {
        return response.data;
      }
      return { error: response.data?.error || 'Failed to submit ranked vote.' };
    } catch (error) {
      console.error('Error submitting ranked vote:', error);
      return { error: error.response?.data?.error || 'Failed to submit ranked vote.' };
    }
  }

static async updateVoteDates(voteId, startDate, endDate) {
  if (!voteId || !startDate || !endDate) {
    toast.error("Vote ID, start date, and end date are required.");
    return { error: "Missing required fields." };
  }
  try {
    const response = await httpClient.patch(
      `/vote/update_vote_dates/${voteId}/dates`,
      { startDate, endDate }
    );
    if (response.status === 200) {
      toast.success("Vote dates updated successfully.");
      return response.data.vote;
    }
    toast.error(response.data?.message || "Failed to update vote dates.");
    return { error: response.data?.message || "Failed to update vote dates." };
  } catch (error) {
    console.error("Error updating vote dates:", error);
    toast.error(
      error.response?.data?.message || "Failed to update vote dates. Please try again."
    );
    return { error: error.response?.data?.message || "Failed to update vote dates." };
  }
}

  /**
   * Update vote details (admin only).
   * For pending: can update startDate, endDate, voteTitle, totalFund, fundingDistribution, numberOfWinningProjects.
   * For active: can update voteTitle, totalFund, and for multi: fundingDistribution, numberOfWinningProjects.
   * @param {string} voteId
   * @param {object} payload - fields to update
   * @returns {object|false} updated vote or error object
   */
  static async updateVoteDetails(voteId, payload) {
    if (!voteId || typeof payload !== "object" || !Object.keys(payload).length) {
      toast.error("Vote ID and at least one field are required.");
      return { error: "Missing required fields." };
    }
    try {
      const response = await httpClient.patch(
        `/vote/update_vote_details/${voteId}`,
        payload
      );
      if (response.status === 200) {
        toast.success("Vote updated successfully.");
        return response.data.vote;
      }
      toast.error(response.data?.message || "Failed to update vote.");
      return { error: response.data?.message || "Failed to update vote." };
    } catch (error) {
      console.error("Error updating vote details:", error);
      toast.error(
        error.response?.data?.message || "Failed to update vote. Please try again."
      );
      return { error: error.response?.data?.message || "Failed to update vote." };
    }
  }
}

