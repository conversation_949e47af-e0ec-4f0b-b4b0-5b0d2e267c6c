import httpClient from "../components/httpClient/httpClient";
import { toast } from 'react-toastify';

class AffiliateLinkService {
  
  // Validate affiliate link before signup/login
  async validateAffiliateLink(affiliateCode, token) {
    try {
      const response = await httpClient.get(`affiliate/validate?ref=${affiliateCode}&token=${token}`);
      return response.data;
    } catch (error) {
      console.error("Error validating affiliate link:", error);
      console.error("Error status:", error.response?.status);
      console.error("Error data:", error.response?.data);
      
      if (error.response?.status === 400 || error.response?.status === 404) {
        const backendMessage = error.response.data.message || "Invalid affiliate link";
        throw new Error(backendMessage);
      }
      
      // Generic network or other error
      throw new Error("Unable to validate affiliate link. Please try again.");
    }
  }

  // Track successful registration with affiliate link
  async trackAffiliateSignup(affiliateCode, token, userId) {
    try {
      const payload = {
        ref: affiliateCode,
        token,
        userId
      };
      const response = await httpClient.post("affiliate/process-signup", payload);
      if (response.data?.success) {
      } else {
        console.warn("Backend returned success=false");
      }
      
      return response.data;
    } catch (error) {
      console.error("Error tracking affiliate signup:", error);
      console.error(" Error details:", error.response?.data);
      console.error("Error status:", error.response?.status);
      throw error;
    }
  }
}

export default new AffiliateLinkService();





