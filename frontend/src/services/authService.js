/**
 * @fileoverview Authentication service module that handles all authentication related operations
 * @module AuthService
 */

import { toast } from "react-toastify";
import httpClient from "../components/httpClient/httpClient";

/**
 * Authentication service object containing all auth-related methods
 * @namespace
 */

export const AuthService = {
    /**
     * Register a new user
     * @async
     * @param {Object} payload - User registration data
     * @param {string} payload.firstName - User's first name
     * @param {string} payload.lastName - User's last name
     * @param {string} payload.email - User's email address
     * @param {string} payload.password - User's password
     * @returns {Promise<Object>} Registration result
     */
    SignUpAuth: async (payload) => {
        try {
            
            const response = await httpClient.post("user/signup", payload);
            
            if (response.status === 201) {
                toast.success("User registered successfully");
                
                // Return the response data with user info
                return {
                    success: true,
                    user: response.data.user, // This now contains _id, email, firstName, lastName
                    message: response.data.message
                };
            } else {
                toast.error(response.data.message);
                return { success: false };
            }
        } catch (error) {
            console.error("AuthService.SignUpAuth error:", error);
            if (error.response?.data?.message) {
                toast.error(error.response.data.message);
            } else {
                toast.error("Server error, please try again. If this persists please contact the administrator");
            }
            return { success: false };
        }
    },

    /**
     * Sign in user
     * @async
     * @param {Object} payload - Login credentials
     * @param {string} payload.email - User's email
     * @param {string} payload.password - User's password
     * @returns {Promise<Object>} Login result
     */
    SignInAuth: async (payload, getCurrentUser) => {
        try {
            const response = await httpClient.post("user/signin", payload)
            if (response.status === 200) {
                localStorage.setItem("token_key_Bpnthr", response.data.token)
                const expiresInSeconds = response.data.expiresIn;
                const expiresAt = Date.now() + expiresInSeconds * 1000; // Convert to ms
                localStorage.setItem("expiresAt_Bpnthr", expiresAt.toString());

                await getCurrentUser()

               
                return true
            } else {
                toast.error(response.data.message)
                return false
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },

    /**
     * Send email verification code
     * @async
     * @param {Object} payload - Email data
     * @param {string} payload.email - User's email
     * @returns {Promise<Object>} Send confirmation result
     */
    sendVerificationCode: async (payload) => {
        try {
            const response = await httpClient.post("user/send_signup_confirm_code", payload);
            if (response.status === 200) {
                toast.success("Code sent successfully, check your email for the verification code.")
                return true
            } else {
                toast.error(response.data.message)
                return false
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },

    /**
 * Send signin verification code
 * @async
 * @param {Object} payload - Email data
 * @param {string} payload.email - User's email
 * @returns {Promise<Object>} Send confirmation result
 */
sendSigninCode: async (payload) => {
    try {
        const response = await httpClient.post("user/send_signin_code", payload);
        if (response.status === 200) {
            toast.success("Signin code sent successfully, check your email for the verification code.");
            return true;
        } else {
            toast.error(response.data.message);
            return false;
        }
    } catch (error) {
        toast.error("Server error, please try again. If this persists please contact the administrator");
        console.log(error);
    }
},




checkPassword: async (payload) => {
  try {
    const response = await httpClient.post("user/check_password", payload);
    if (response.status === 200 && typeof response.data.valid === "boolean") {
      return response.data.valid;
    }
    return false;
  } catch (error) {
    // Optionally log error for debugging
    // console.error("checkPassword error:", error);
    return false;
  }
},

    /**
     * Verify email confirmation code
     * @async
     * @param {Object} payload - Verification data
     * @param {string} payload.email - User's email
     * @param {string} payload.code - Verification code
     * @returns {Promise<Object>} Verification result
     */
    verifyCode: async (payload) => {
        try {
            const response = await httpClient.put("user/verify_code", payload);

            if (response.status === 200) {
                toast.success("Email verified successfully")
                return true
            } else {
                toast.error(response.data.message)
                return false
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },



    ForgotPassword: async (data) => {
        try {
            const response = await httpClient.put('user/forgot_password', data); // Changed from POST to PUT

            if (response.status === 200) {
                toast.success("Password reset code sent successfully")
                return true
            } else {
                toast.error(response.data.message)
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },


    /**
    * Change user's password
    * @async
    * @param {Object} payload - Password change data
    * @param {string} payload.email - User's email
    * @param {string} payload.newPassword - New password
    * @returns {Promise<Object>} Password change result
    * @throws {Error} If validation fails or passwords don't match
    */

    ChangePassword: async (payload) => {
        try {
            // Password strength validation
            const passwordCheck = AuthService.verifyPasswordStrength(payload.newPassword);
            if (!passwordCheck.isValid) {
                toast.error("Password strenth is week")
                return false
            }

            const response = await httpClient.put("user/change_password", payload);

            if (response.status === 200) {
                toast.success("Password changed successfully")
                return true
            } else {
                toast.error(response.data.message)
                return false
            }

        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },

    ResendConfirmCode: async (payload) => {
        try {
            const response = await httpClient.post("user/resend_code", payload);

            if (response.status === 200) {
                toast.success("A new confirmation code has been sent.")
                return true
            } else {
                toast.error(response.data.message)
                return false
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },

    ResendResetCode: async (payload) => {
        try {
            const response = await httpClient.post("user/resend_password_reset", payload);

            if (response.status === 200) {
                toast.success("A new reset code has been sent.")
                return true
            } else {
                toast.error(response.data.message)
                return false
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },


    /**
   * Verify password strength against requirements
   * @param {string} password - Password to verify
   * @returns {Object} Validation result with requirements status
   * @returns {boolean} result.isValid - Overall validation result
   * @returns {Object} result.requirements - Individual requirement checks
   * @returns {string} result.message - Validation message
   */

    verifyPasswordStrength: (password) => {
        const requirements = {
            minLength: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumber: /\d/.test(password),
            hasSpecialChar: /[@$!%*?&]/.test(password)
        };

        return {
            isValid: Object.values(requirements).every(Boolean),
            requirements,
            message: !Object.values(requirements).every(Boolean)
                ? "Password must contain at least 8 characters, one uppercase letter, one lowercase letter, one number and one special character"
                : "Password meets all requirements"
        };
    },
    
    DeleteUserAccount: async () => {
        try {
            const response = await httpClient.delete("user/delete_account");
            if (response.status === 200) {
                toast.success("Your account has been deleted successfully.");
                return true;
            } else {
                toast.error(response.data.message);
                return false;
            }
        } catch (error) {
            toast.error("Failed to delete account. Please try again.");
            console.error("Error deleting account:", error);
            return false;
        }
    },
    
    refreshToken: async () => {
        const token = localStorage.getItem("token_key_Bpnthr");
    
        if (!token) {
            console.warn("No token available for refresh");
            return null;
        }
    
        try {
            const response = await httpClient.post("user/refresh-token", null, {
                headers: { Authorization: `Bearer ${token}` },
            });
    
            const newToken = response.data.token;
            const expiresInSeconds = response.data.expiresIn;
            const expiresAt = Date.now() + expiresInSeconds * 1000; // Calculate expiration timestamp
    
            localStorage.setItem("token_key_Bpnthr", newToken);
            localStorage.setItem("expiresAt_Bpnthr", expiresAt.toString()); // Store expiration timestamp
    
            return newToken;
        } catch (error) {
            console.error("Failed to refresh token:", error);
            throw error;
        }
    },
};








































