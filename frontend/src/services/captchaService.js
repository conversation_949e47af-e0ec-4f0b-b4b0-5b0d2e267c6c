import { createRef } from 'react';

class CaptchaService {
  static currentRoute = '';
  static tokenExpiryTime = 110 * 1000; // 110 seconds
  
  // Routes requiring CAPTCHA verification with their types
  static protectedRoutes = new Map([
    // Authentication routes - 'auth' type
    ['/login', 'auth'],
    ['/signup', 'signup'],
    ['/register', 'auth'],
    ['/forgot-password', 'auth'],
    ['/changepassword', 'auth'],
    ['/resetpassword', 'auth'],
    ['/verifyemail', 'auth'],
    
    // Payment and contribution routes - 'payment' type
    ['/monthly-contribution', 'payment'],
    ['/monthly-contribution-update', 'payment'],
    ['/pre-sale', 'payment'],
    ['/presale-auth', 'payment'],
    ['/mypoints', 'payment'],
    ['/count', 'payment'],
    ['/failed_payment', 'payment'],
    
    // Dashboard routes - 'basic' type
    ['/dashboard', 'basic'],
    ['/dashboard/account', 'basic'],
    ['/dashboard/events', 'basic'],
    ['/dashboard/ongoing-projects', 'basic'],
    ['/dashboard/project-history', 'basic'],
    ['/dashboard/project-management', 'basic'],
    ['/dashboard/initiate-vote', 'basic'],
    ['/dashboard/ongoing-vote', 'basic'],
    ['/dashboard/vote-results', 'basic'],
    ['/dashboard/manage-community', 'basic'],
    ['/dashboard/project-multi-vote', 'basic'],
    ['/dashboard/project-rank-vote', 'basic'],
    ['/dashboard/project-votes', 'basic'],
    ['/dashboard/event-management', 'basic'],
    ['/dashboard/profile', 'basic'],
    ['/dashboard/wallet', 'basic'],
    ['/dashboard/transactions', 'basic'],
    ['/dashboard/settings', 'basic'],
    ['/dashboard/community', 'basic'],
    ['/dashboard/contributions', 'basic'],
    
    // Sahelion dashboard routes - 'basic' type
    ['/dashboard-sahelion/portfolio', 'basic'],
    
    // DAO transaction routes - 'basic' type
    ['/daotransactions', 'basic'],
  ]);

  static isRecaptchaEnabled() {
    return import.meta.env.VITE_CAPTCHA_ENABLED === 'true';
  }

  static getSiteKey() {
    return import.meta.env.VITE_RECAPTCHA_SITE_KEY;
  }

  static setCurrentRoute(route) {
    this.currentRoute = route;
  }

  static shouldShowCaptcha(route = null) {
    if (!this.isRecaptchaEnabled()) {
      return false;
    }
    
    const targetRoute = route || this.currentRoute;
    
    // Check for exact match first
    if (this.protectedRoutes.has(targetRoute)) {
      return true;
    }
    
    // Check for prefix matches (for nested routes)
    for (const [protectedRoute] of this.protectedRoutes) {
      if (targetRoute.startsWith(protectedRoute)) {
        return true;
      }
    }
    
    return false;
  }

  static getRouteType(route = null) {
    const targetRoute = route || this.currentRoute;
    
    // Check for exact match first
    if (this.protectedRoutes.has(targetRoute)) {
      return this.protectedRoutes.get(targetRoute);
    }
    
    // Check for prefix matches (for nested routes)
    for (const [protectedRoute, type] of this.protectedRoutes) {
      if (targetRoute.startsWith(protectedRoute)) {
        return type;
      }
    }
    
    return null;
  }

  // Get CAPTCHA token for API requests
  static getTokenForRequest() {
    if (!this.isRecaptchaEnabled() || !this.shouldShowCaptcha()) {
      return null;
    }

    const tokenData = localStorage.getItem('captchaToken');
    if (!tokenData) {
      return null;
    }

    try {
      const { token, timestamp } = JSON.parse(tokenData);
      const now = Date.now();
      
      if (now - timestamp < this.tokenExpiryTime) {
        return token;
      } else {
        this.clearCaptchaToken();
        return null;
      }
    } catch (error) {
      console.error('Error parsing CAPTCHA token:', error);
      this.clearCaptchaToken();
      return null;
    }
  }

  static setCaptchaToken(token) {
    if (!this.isRecaptchaEnabled()) {
      return;
    }

    const tokenData = {
      token,
      timestamp: Date.now()
    };
    
    localStorage.setItem('captchaToken', JSON.stringify(tokenData));
    
    window.dispatchEvent(new CustomEvent('captchaTokenUpdated', { 
      detail: { token } 
    }));
  }

  static clearCaptchaToken() {
    localStorage.removeItem('captchaToken');
    window.dispatchEvent(new CustomEvent('captchaTokenCleared'));
  }

  static isTokenValid() {
    return this.getTokenForRequest() !== null;
  }

  static getTimeUntilExpiry() {
    const tokenData = localStorage.getItem('captchaToken');
    if (!tokenData) {
      return 0;
    }

    try {
      const { timestamp } = JSON.parse(tokenData);
      const now = Date.now();
      const expiryTime = timestamp + this.tokenExpiryTime;
      
      return Math.max(0, expiryTime - now);
    } catch (error) {
      console.error('Error calculating token expiry:', error);
      return 0;
    }
  }

  static dispatchCaptchaRequired(error, originalRequest) {
    window.dispatchEvent(new CustomEvent('captchaRequired', {
      detail: { error, originalRequest }
    }));
  }
}

export default CaptchaService; 