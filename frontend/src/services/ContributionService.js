import httpClient from '../components/httpClient/httpClient';
import { toast } from 'react-toastify';

export const ContributionService = {
    /**
     * Get user's contribution payment history
     * @param {number} page - Page number for pagination (default: 1)
     * @param {number} limit - Number of items per page (default: 10)
     * @returns {Promise<Object>} Contribution history data
     */
    getUserContributionHistory: async (page = 1, limit = 10) => {
        try {
            const response = await httpClient.get(`/contribution/history?page=${page}&limit=${limit}`);
            
            if (response.status === 200) {
                return {
                    success: true,
                    data: response.data.data
                };
            } else {
               
                return {
                    success: false,
                    message: 'Failed to fetch contribution history'
                };
            }
        } catch (error) {
            console.error('Error fetching contribution history:', error);
            
            if (error.response?.status === 404) {
                return {
                    success: false,
                    message: 'No contribution history found',
                    data: {
                        payments: [],
                        pagination: {
                            currentPage: 1,
                            totalPages: 0,
                            totalItems: 0,
                            itemsPerPage: limit
                        }
                    }
                };
            }
            
           
            return {
                success: false,
                message: 'Error fetching contribution history',
                error: error.message
            };
        }
    },

    /**
     * Get user's contribution history with their current contribution details
     * @param {string} userId - User ID
     * @param {number} page - Page number for pagination (default: 1) 
     * @param {number} limit - Number of items per page (default: 10)
     * @returns {Promise<Object>} User's contribution history and current amount
     */
    getUserContributionByUserId: async (userId, page = 1, limit = 10) => {
        try {
            const response = await httpClient.get(`/contribution/history/${userId}?page=${page}&limit=${limit}`);
            
            if (response.status === 200) {
                return {
                    success: true,
                    data: response.data
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to fetch contribution data'
                };
            }
        } catch (error) {
            console.error('Error fetching contribution data:', error);
            
            if (error.response?.status === 404) {
                return {
                    success: false,
                    message: 'No contribution found for this user',
                    data: {
                        contributions: [],
                        pagination: {
                            total: 0,
                            page: 1,
                            pages: 0
                        }
                    }
                };
            }
            
            return {
                success: false,
                message: 'Error fetching contribution data',
                error: error.message
            };
        }
    },

    /**
     * Get current user's active contribution amount using contribution status endpoint
     * @returns {Promise<Object>} Current contribution amount
     */
    getCurrentContribution: async () => {
        try {
            // First get the current user
            const userResponse = await httpClient.get('/user/get_user');
            console.log('User response:', userResponse.data);
            console.log('Full user object:', userResponse.data.user);
            
            if (userResponse.status === 200 && userResponse.data.user) {
                // Try different possible user ID fields
                const userId = userResponse.data.user.id || 
                              userResponse.data.user._id || 
                              userResponse.data.user.userId;
                console.log('User ID:', userId);

                if (!userId) {
                    return {
                        success: false,
                        message: 'User ID not found in user object',
                        data: null
                    };
                }

                // Use the contribution status endpoint to get current contribution
                const statusResponse = await httpClient.get(`/contribution/status/${userId}`);
                console.log('Contribution status response:', statusResponse.data);
                
                if (statusResponse.status === 200) {
                    const { status, contribution } = statusResponse.data;
                    
                    // If contribution exists and is active
                    if (contribution && status === 'active') {
                        return {
                            success: true,
                            data: {
                                amount: contribution.amount.toString(),
                                currency: contribution.currency,
                                status: status,
                                tier: contribution.tier,
                                paymentMethod: contribution.paymentMethod,
                                nextPaymentDate: contribution.nextPaymentDate,
                                lastPaymentDate: contribution.lastPaymentDate,
                                amountUpgrade: contribution.amountUpgrade
                            }
                        };
                    } else if (contribution && status === 'inactive') {
                        // Still show the contribution details even if inactive
                        return {
                            success: true,
                            data: {
                                amount: contribution.amount.toString(),
                                currency: contribution.currency,
                                status: status,
                                tier: contribution.tier,
                                paymentMethod: contribution.paymentMethod,
                                nextPaymentDate: contribution.nextPaymentDate,
                                lastPaymentDate: contribution.lastPaymentDate,
                                amountUpgrade: contribution.amountUpgrade
                            }
                        };
                    } else {
                        return {
                            success: false,
                            message: 'No contribution found',
                            data: null
                        };
                    }
                } else {
                    return {
                        success: false,
                        message: 'Failed to fetch contribution status'
                    };
                }
            } else {
                return {
                    success: false,
                    message: 'Failed to fetch user profile'
                };
            }
        } catch (error) {
            console.error('Error fetching current contribution:', error);
            
            // If the status endpoint doesn't exist, fall back to the original method
            if (error.response?.status === 404) {
                console.log('Status endpoint not found, trying fallback method...');
                try {
                    const userResponse = await httpClient.get('/user/get_user');
                    if (userResponse.status === 200 && userResponse.data.user) {
                        const userId = userResponse.data.user.id || 
                                      userResponse.data.user._id || 
                                      userResponse.data.user.userId;
                        console.log('Fallback User ID:', userId);
                        
                        if (userId) {
                            const contributionResult = await ContributionService.getUserContributionByUserId(userId, 1, 1);
                            
                            if (contributionResult.success && contributionResult.data.contributions.length > 0) {
                                const latestContribution = contributionResult.data.contributions[0];
                                return {
                                    success: true,
                                    data: {
                                        amount: latestContribution.amount.toString(),
                                        currency: latestContribution.currency,
                                        status: latestContribution.status,
                                        tier: latestContribution.tier
                                    }
                                };
                            }
                        }
                    }
                } catch (fallbackError) {
                    console.error('Fallback method also failed:', fallbackError);
                }
            }
            
            return {
                success: false,
                message: 'Error fetching current contribution',
                error: error.message
            };
        }
    },


    /**
     * Activate contribution subscription
     * @param {Object} contributionData - Contribution activation data
     * @returns {Promise<Object>} Activation result
     */
    activateContribution: async (contributionData) => {
        try {
            const response = await httpClient.post('/contribution/activate', contributionData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
                },
            });

            const data = response.data;

            if (response.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                throw new Error(data.message || 'Failed to activate contribution');
            }
        } catch (error) {
            console.error('Activation error:', error);
            return {
                success: false,
                message: error.message || 'Failed to activate contribution',
                error: error
            };
        }
    },

   getDashboardSummary: async () => {
  try {
    const response = await httpClient.get('/contribution/summary');
    if (response.status === 200) {
      return { success: true, data: response.data };
    }
    return { success: false, message: 'Failed to fetch summary' };
  } catch (error) {
    return { success: false, message: error.message };
  }
},

    /**
     * Generate payment intent for fiat payments
     * @param {Object} paymentData - Payment intent data
     * @returns {Promise<Object>} Payment intent result
     */
    generatePaymentIntent: async (paymentData) => {
        try {
            const response = await httpClient.post('/contribution/generate-intent', paymentData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
                },
            });

            const data = response.data;

            if (response.status === 200) {
                return {
                    success: true,
                    data
                };
            } else {
                throw new Error(data.message || 'Failed to generate payment intent');
            }
        } catch (error) {
            console.error('Payment intent error:', error);
            return {
                success: false,
                message: error.message || 'Failed to generate payment intent',
                error: error
            };
        }
    },

    /**
     * Verify crypto payment
     * @param {Object} verificationData - Crypto payment verification data
     * @returns {Promise<Object>} Verification result
     */
    verifyCryptoPayment: async (verificationData) => {
        try {
            const response = await httpClient.post('/contribution/verify-crypto', verificationData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
                }
            });

            const data = response.data;

            if (data.success) {
                return {
                    success: true,
                    data: data
                };
            } else {
                let errorMessage = data.message + " " + `Please copy the transaction hash ${data?.details?.transactionHash} and contact support.`;
                throw new Error(errorMessage || 'Crypto payment verification failed');
            }
        } catch (error) {
            console.error('Crypto verification error:', error);
            return {
                success: false,
                message: error.message || 'Crypto payment verification failed',
                error: error
            };
        }
    },

    /**
     * Update contribution (amount, date, or both)
     * @param {Object} updateData - Update data: { userId, amount, currency, paymentMethod, updateType, subscriptionId, newPlanId, scheduledDate, paymentIntentId }
     * @returns {Promise<Object>} Update result
     */
    updateContribution: async (updateData) => {
        try {
            const response = await httpClient.post('/contribution/update', updateData, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
                },
            });
            const data = response.data;
            if (response.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                throw new Error(data.message || 'Failed to update contribution');
            }
        } catch (error) {
            console.error('Update contribution error:', error);
            return {
                success: false,
                message: error.message || 'Failed to update contribution',
                error: error
            };
        }
    },
    getUserScheduledContribution: async (userId) => {
        try {
            const response = await httpClient.get(`/contribution/scheduled-payment`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token_key_Bpnthr')}`
                },
                params: { userId }
            });
            const data = response.data;
            if (response.status === 200) {
                return {
                    success: true,
                    data: data
                };
            } else {
                throw new Error(data.message || 'Failed to fetch scheduled contribution');
            }
        } catch (error) {
            console.error('Error fetching scheduled contribution:', error);
            return {
                success: false,
                message: error.message || 'Failed to fetch scheduled contribution',
                error: error
            };
        }
    }   
};