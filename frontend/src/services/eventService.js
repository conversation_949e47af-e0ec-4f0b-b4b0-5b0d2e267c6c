import { toast } from "react-toastify";
import httpClient from "../components/httpClient/httpClient";

export class EventService {
  // Create a new event
  static async createEvent(payload) {
    try {
      const response = await httpClient.post("event/create", payload);
      if (response.status === 201) {
        toast.success("Event created successfully");
        return response.data.event;
      }
      throw new Error(response.data?.message || "Failed to create event");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to create event. Please try again."
      );
      return null;
    }
  }

  // Get all events (optionally filter by type: 'upcoming' or 'past')
  static async getAllEvents(type = "") {
    try {
      const url = type ? `event/list?type=${type}` : "event/list";
      const response = await httpClient.get(url);
      if (response.status === 200) {
        return response.data;
      }
      throw new Error(response.data?.message || "Failed to fetch events");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch events. Please try again."
      );
      return [];
    }
  }

  // Get a single event by ID
  static async getEventById(eventId) {
    try {
      const response = await httpClient.get(`event/details/${eventId}`);
      if (response.status === 200) {
        return response.data;
      }
      throw new Error(response.data?.message || "Failed to fetch event");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to fetch event. Please try again."
      );
      return null;
    }
  }

  // Update an event
  static async updateEvent(eventId, payload) {
    try {
      const response = await httpClient.put(`event/update/${eventId}`, payload);
      if (response.status === 200) {
        toast.success("Event updated successfully");
        return response.data.event;
      }
      throw new Error(response.data?.message || "Failed to update event");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to update event. Please try again."
      );
      return null;
    }
  }

  // Delete an event
  static async deleteEvent(eventId) {
    try {
      const response = await httpClient.delete(`event/delete/${eventId}`);
      if (response.status === 200) {
        toast.success("Event deleted successfully");
        return true;
      }
      throw new Error(response.data?.message || "Failed to delete event");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to delete event. Please try again."
      );
      return false;
    }
  }

  // Attend an event
  static async attendEvent(eventId, payload) {
    try {
      const response = await httpClient.post(`event/attend/${eventId}`, payload);
      if (response.status === 200) {
        toast.success("Attendance confirmed");
        return response.data.attendees;
      }
      throw new Error(response.data?.message || "Failed to attend event");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to attend event. Please try again."
      );
      return null;
    }
  }

  // Submit a question for an event
  static async submitQuestion(eventId, payload) {
    try {
      const response = await httpClient.post(`event/question/${eventId}`, payload);
      if (response.status === 200) {
        return response.data.questions;
      }
      throw new Error(response.data?.message || "Failed to submit question");
    } catch (error) {
      return null;
    }
  }

  static async duplicateEvent(eventId) {
  try {
    const response = await httpClient.post(`event/duplicate/${eventId}`);
    if (response.status === 201) {
      return response.data.event;
    }
    throw new Error(response.data?.message || "Failed to duplicate event");
  } catch (error) {
    toast.error(
      error.response?.data?.message ||
      error.message ||
      "Failed to duplicate event. Please try again."
    );
    return null;
  }
}

  // Subscribe for event reminder
  static async subscribeReminder(eventId, email) {
    try {
      const response = await httpClient.post(`event/${eventId}/subscribe-reminder`, { email });
      if (response.status === 200) {
        // toast.success("Subscribed! You will receive a reminder before the event.");
        return true;
      }
      throw new Error(response.data?.message || "Failed to subscribe for reminder");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to subscribe for reminder. Please try again."
      );
      return false;
    }
  }

  // Unsubscribe from event reminder
  static async unsubscribeReminder(eventId, email) {
    try {
      const response = await httpClient.post(`event/${eventId}/unsubscribe-reminder`, { email });
      if (response.status === 200) {
       // toast.info("Unsubscribed. You will not receive a reminder for this event.");
        return true;
      }
      throw new Error(response.data?.message || "Failed to unsubscribe from reminder");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to unsubscribe from reminder. Please try again."
      );
      return false;
    }
  }

   static async addRecordingLink(eventId, link) {
    try {
      const response = await httpClient.post(`event/${eventId}/recording-link`, { link });
      if (response.status === 200) {
        return response.data.recordingLinks;
      }
      throw new Error(response.data?.message || "Failed to add recording link");
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to add recording link. Please try again."
      );
      return null;
    }
  }

  static async updateRecordingLink(eventId, index, link) {
  try {
    const response = await httpClient.put(`event/${eventId}/recording-link`, { index, link });
    if (response.status === 200) {
      return response.data; // ✅ Return the whole object, including .success
    }
    throw new Error(response.data?.message || "Failed to update recording link");
  } catch (error) {
    toast.error(
      error.response?.data?.message ||
      error.message ||
      "Failed to update recording link. Please try again."
    );
    throw error; 
  }
}

}