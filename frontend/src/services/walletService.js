import { toast } from "react-toastify"
import httpClient from "../components/httpClient/httpClient"

export const WalletService = {
    AddWalletAddress: async (payload, getCurrentUser) => {
        try {
            const response =  await httpClient.post("user/add_wallet_address", payload)
            if(response.status === 201) {
                toast.success("Wallet address added successfully")
                await getCurrentUser()
                return true
            } else {
                toast.error(response.data.message)
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    },

    UpdateWalletAddress: async (payload, getCurrentUser) => {
        try {
            const response =  await httpClient.put("user/update_wallet_address", payload)
            if(response.status === 200) {
                toast.success("Wallet address updated successfully")
                await getCurrentUser()
                return true
            } else {
                toast.error(response.data.message)
            }
        } catch (error) {
            toast.error("Server error, please try again. If this persist please contact the administrator")
            console.log(error)
        }
    }
}