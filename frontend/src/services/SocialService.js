import httpClient from '../components/httpClient/httpClient';
import { toast } from 'react-toastify';

export class SocialService {
  // Send invite to a single email address
  static async inviteFriend(email, message) {
    try {
      const response = await httpClient.post('/invite', { email, message });
      if (response.status === 200) {
        toast.success('Invite sent successfully!');
        return response.data;
      }
      // Optionally throw error for non-200
      throw new Error(response.data?.message || 'Failed to send invite');
    } catch (error) {
      console.error('Error sending invite:', error);
      throw error;
    }
  }

  static async generateShareLink(
    expiration = '1 day',
    maxUsers = '',
    customLink = '',
    email = '',
    message = ''
  ) {
    try {
      const response = await httpClient.post('/generate-link/generate-link', {
        expiration,
        maxUsers,
        customLink,
        email,
        message,
      });

      if (response && response.status === 200) {
        toast.success('Invite link generated!');
        return {
          inviteLink: response.data.inviteLink, 
          baseUrl: response.data.baseUrl,
          linkId: response.data.linkId,
        };
      }

      throw new Error(
        response?.data?.message || 'Failed to generate invite link'
      );
    } catch (error) {
      console.error('Error generating invite link:', error);
      throw error;
    }
  }

  static async inviteFriendLink(emails, message, linkId, expiration) {
    try {
      const response = await httpClient.post('/generate-link/send-invite-emails', {
        emails,
        message,
        linkId,
        expiration,
      });

      if (response.status === 200) {
        toast.success(
          `Successfully sent ${response.data.successful.length} invitation(s)`
        );
        return response.data;
      }

      throw new Error(response.data?.message || 'Failed to send invite');
    } catch (error) {
      console.error('Error sending invite:', error);
      throw error;
    }
  }
}
