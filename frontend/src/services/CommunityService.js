import httpClient from "../components/httpClient/httpClient";
import { toast } from "react-toastify";

export class CommunityService {
  static async editCommunity(communityId, communityName, communityDescription) {
    const response = await httpClient.put(
      `/community/${communityId}/edit`,
      { communityName, communityDescription }
    );
    return response.data;
  }

  static async createCommunity(payload) {
    try {
      let response;
      if (payload.logo && payload.logo instanceof File) {
        // Use FormData for file upload
        const formData = new FormData();
        // Append all fields except logo
        Object.entries(payload).forEach(([key, value]) => {
          if (key !== "logo") {
            if (Array.isArray(value)) {
              value.forEach((v) => formData.append(key, v));
            } else {
              formData.append(key, value);
            }
          }
        });
        // Append logo file as 'uploaded_file'
        formData.append("uploaded_file", payload.logo);
        response = await httpClient.post("community/create", formData, {
          headers: { "Content-Type": "multipart/form-data" },
        });
      } else {
        // No file, send as JSON
        response = await httpClient.post("community/create", payload);
      }
      return response;
    } catch (error) {
      if (error.response) {
        toast.error(error.response.data?.error || "Failed to create community.");
        return error.response;
      }
      toast.error("Failed to create community. Please try again.");
      throw error;
    }
  }

  static async initiateTransfer(communityId, newOwnerId) {
    try {
      const response = await httpClient.post("/community-transfer/initiate", {
        communityId,
        newOwnerId,
      });
      toast.success("Transfer initiated. The new owner will receive an email.");
      return response.data;
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to initiate transfer."
      );
      throw error;
    }
  }

  static async acceptTransfer(transferToken) {
    try {
      const response = await httpClient.post("/community-transfer/accept", {
        transferToken,
      });
      toast.success("Community ownership accepted successfully.");
      return response.data;
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to accept transfer."
      );
      throw error;
    }
  }

  static async getPendingTransfers() {
    try {
      const response = await httpClient.get("/community-transfer/pending");
      return response.data.transfers || [];
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to fetch pending transfers."
      );
      return [];
    }
  }

   static async validateTransfer(token) {
    try {
      const response = await httpClient.get(
        `/community-transfer/validate?token=${encodeURIComponent(token)}`
      );
      return response.data;
    } catch (error) {
      toast.error(
        error.response?.data?.message || "Failed to validate transfer."
      );
      throw error;
    }
  }



  // static async getAllCommunities() {
  //   try {
  //     const response = await httpClient.get("community/all");
  //     if (response.status === 200) {
  //       return response.data.communities;
  //     }
  //     toast.error(response.data?.error || "Failed to fetch communities.");
  //     return [];
  //   } catch (error) {
  //     toast.error(error.response?.data?.error || "Failed to fetch communities.");
  //     return [];
  //   }
  // }

  static async getAllCommunityInfoToApprove() {
    try {
      const response = await httpClient.get("community/get_all_community_info_to_approve");
      if (response.status === 200) {
        return response;
      }
      toast.error(response.data?.error || "Failed to fetch communities.");
      return [];
    } catch (error) {
      toast.error(error.response?.data?.error || "Failed to fetch communities.");
      return [];
    }
  }

  static async approveCommunity(communityId) {
  try {
    const response = await httpClient.patch(`community/approve_community/${communityId}`);
    if (response.status === 200) {
      toast.success("Community approved successfully.");
    } else {
      toast.error(response.data?.message || "Failed to approve community.");
    }
    return response;
  } catch (error) {
    toast.error(error.response?.data?.message || "Error approving community.");
    throw error;
  }
}

static async rejectCommunity(communityId, rejectionReason) {
  try {
    const response = await httpClient.patch(`community/reject_community/${communityId}`, {
      rejectionReason,
    });
    if (response.status === 200) {
      toast.success("Community rejected successfully.");
    } else {
      toast.error(response.data?.message || "Failed to reject community.");
    }
    return response;
  } catch (error) {
    toast.error(error.response?.data?.message || "Error rejecting community.");
    throw error;
  }
}


}