import { Spin } from "antd";
import React from "react";
import { Route, Routes } from "react-router-dom";
import { Managecommunity } from "../pages/Dashboard/managecommunity.jsx";
import { PresaleAuth } from "../pages/presaleAuth.jsx";
import { EnsureLogin } from "../components/utils/ensureLogin.jsx";
import { LoadingScreen } from "../components/utils/loader.jsx";
import BuySahelion from '../pages/DashboardSahelion/BuySahelion';
import SahelionPortfolio from '../pages/DashboardSahelion/SahelionPortfolio';
import NotFound from '../pages/NotFound';

// Doing lazy loading here
const NewHomePage = React.lazy(async () => ({
  default: (await import("../pages/HomePage/NewHome.jsx")).NewHome,
}));

const AboutPage = React.lazy(async () => ({
  default: (await import("../pages/About.jsx")).About,
}));

const PresaleAuthPage = React.lazy(async () => ({
  default: (await import("../pages/presaleAuth.jsx")).PresaleAuth,
}));

const LotteryPage = React.lazy(async () => ({
  default: (await import("../pages/Lottery.jsx")).Lottery,
}));

const GamePage = React.lazy(async () => ({
  default: (await import("../pages/Game.jsx")).Game,
}));

const DaoPage = React.lazy(async () => ({
  default: (await import("../pages/Dao.jsx")).Dao,
}));

const DaoLandingPg = React.lazy(async () => ({
  default: (await import("../pages/DaoLandingPg.jsx")).DaoLandingPg,
}));

const PresaleOldPage = React.lazy(async () => ({
  default: (await import("../pages/HomePage/landingPage.jsx")).LandingPage,
}));

const BuyTokenPage = React.lazy(async () => ({
  default: (await import("../components/BuyTokens/BuyToken.jsx")).BuyToken,
}));

const PaymentFailedPage = React.lazy(async () => ({
  default: (await import("../components/BuyTokens/PaymentFailed.jsx")).PaymentFailed,
}));

const SahelionPage = React.lazy(async () => ({
  default: (await import("../pages/Sahelion.jsx")).Sahelion,
}));

const VerifyEmailPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/Verify.jsx")).VerifyEmail,
}));

const ForgotPasswordPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/forgot_password.jsx")).ForgotPassword,
}));

const ChangePasswordPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/change_password.jsx")).ChangePassword,
}));

const ProjectRankMultiVotes = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/projectVote.jsx")).ProjectRankMultiVotes,
}));

const LoginPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/Login.jsx")).Login,
}));

const SignupPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/Signup.jsx")).Signup,
}));

const Presale = React.lazy(async () => ({
  default: (await import("../pages/Presale/pre-sale.jsx")).Presale,
}));

const ResetPasswordPage = React.lazy(async () => ({
  default: (await import("../pages/Auth/reset_password.jsx")).ResetPassword,
}));
const DaoTransactionsPage = React.lazy(async () => ({
  default: (await import("../pages/DaoTransactions.jsx")).default,
}));

const MyPoints = React.lazy(async () => ({ 
  default: (await import("../pages/Presale/myPoints.jsx")).default,
}))

const Dashboard = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/dashboard.jsx")).Dashboard,
}));

const DashboardHome = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/dashboardhome.jsx")).DashboardHome,
}));

const Account = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/user.jsx")).Account,
}));

const Events = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/events.jsx")).Events,
}));

const OngoingProjects = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/ongoingprojects.jsx")).Ongoingprojects,
}));

const ProjectVote = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/projectRankVote.jsx")).Projectvote,
}));

const ProjectHistory = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/projecthistory.jsx")).Projecthistory,
}));

const ProjectManagement = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/project_management.jsx")).ProjectManagement,
}));

const InitiateVote = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/initiatevote.jsx")).Initiatevote,
}));

const ProjectMultiVote = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/projectMultiVote.jsx")).ProjectMultiVote,
}));



const ProjectRankVote = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/projectRankVote.jsx")).ProjectRankVote,
}));


const OngoingVote = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/ongoingvote.jsx")).Ongoingvote,
}));

const VoteResults = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/voteresults.jsx")).Voteresults,
}));

const ManageCommunity = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/managecommunity.jsx")).Managecommunity,
}));

const LotteryActivityPage = React.lazy(async () => ({
  default: (await import("../pages/LotteryActivity")).default,
}));

const PrivacyPolicyPage = React.lazy(async () => ({
  default: (await import("../pages/PrivacyPolicy")).PrivacyPolicy,
}));

const UserManual = React.lazy(async () => ({
  default: (await import("../pages/UserManual/UserManual")).UserManual,
}));

const MonthlyContribution = React.lazy(async () => ({
  default: (await import("../pages/MonthlyContribution.jsx")).MonthlyContribution,
}));

const MonthlyContributionUpdate = React.lazy(async () => ({
  default: (await import("../pages/MonthlyContributionUpdate.jsx")).MonthlyContributionUpdate,
}));

const MonthlyContributionChangeDate = React.lazy(async () => ({
  default: (await import("../pages/MonthlyContributionChangeDate.jsx")).MonthlyContributionChangeDate,
}));

const BuyMobileMoney = React.lazy(async () => ({
  default: (await import("../pages/BuyMobileMoney.jsx")).BuyMobileMoney,
}));

const ProjectDetails = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/ProjectDetails.jsx")).ProjectDetails,
}));
const EventManagement = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/eventManagement.jsx")).EventManagement,
}));
const CreateNewCommunity = React.lazy(async () => ({
  default: (await import("../pages/Community/createNewCommunity.jsx")).CreateNewCommunity,
}));
const CommunityRequests = React.lazy(async () => ({
  default: (await import("../pages/Community/communityRequests.jsx")).CommunityRequests,
}));
const ManageContributions = React.lazy(async () => ({
  default: (await import("../pages/Dashboard/manageContributions.jsx")).ManageContributions,
}));



export const AppRoutesPaths = {
  home: "/",
  about: "/about",
  presaleAuth: "/presale-auth",
  lottery: "/lottery",
  lotteryActivity: "/lottery-activity",
  game: "/game",
  dao: "/dao",
  bpDao: "/black_panther_dao",
  presale_old: "/presale_old",
  mypoints: "/mypoints",
  failedpayment: "/failed_payment",
  count: "/count",
  sahelion: "/sahelion",
  verifyemail: "/verifyemail",
  forgotpassword: "/forgot-password",
  changepassword: "/changepassword",
  login: "/login",
  signup: "/signup",
  presale: "/pre-sale",
  resetPassword: "/resetpassword",
  daotransactions: "/daotransactions",
  privacyPolicy: "/privacy-policy",
  userManual: "/user-manual",
  monthlyContribution: "/monthly-contribution",
  monthlyContributionUpdate: "/monthly-contribution-update",
  monthlyContributionChangeDate: "/monthly-contribution-change-Date",
  buyMobileMoney:"/buy-mobile-money",
  sahelionPortfolio: "/dashboard-sahelion/portfolio",
  createCommunity: "/create-community",
  // Update dashboard routes
  dashboard: {
    root: "/dashboard",
    account: "/dashboard/account",
    events: "/dashboard/events",
    ongoingProjects: "/dashboard/ongoing-projects",
    projectHistory: "/dashboard/project-history",
    projectManagement: "/dashboard/project-management",
    initiateVote: "/dashboard/initiate-vote",
    ongoingVote: "/dashboard/ongoing-vote",
    voteResults: "/dashboard/vote-results",
    managecommunity: "/dashboard/manage-community",
    managecontribution: "/dashboard/manage-contribution",
    projectMultiVote: "/dashboard/project-multi-vote",
    projectRankVote: "/dashboard/project-rank-vote",
    projectRankMultiVotes: "/dashboard/project-votes",
    ongoingProjectDetails: "ongoing-projects/:id",
    projectHistoryDetails: "project-history/:id",
    eventManagement: "/dashboard/event-management",
    communityRequests: "/dashboard/community-requests",
  },
};

const CenteredSpinner = () => (
  <div className="flex items-center justify-center h-screen">
    <Spin size="large" />
  </div>
);

export function AppRoute() {
  return (
    // <React.Suspense fallback={<CenteredSpinner />}> this was used previously as a loader
    <React.Suspense fallback={<LoadingScreen />}>
      <Routes>
        <Route path={AppRoutesPaths.home} element={<NewHomePage />} />
        <Route path={AppRoutesPaths.about} element={<AboutPage />} />
        <Route path={AppRoutesPaths.presale} element={
          // <EnsureLogin>
            <Presale />
          // </EnsureLogin>
        } />
        <Route path={AppRoutesPaths.lottery} element={<LotteryPage />} />
        <Route path={AppRoutesPaths.game} element={<GamePage />} />
        <Route path={AppRoutesPaths.dao} element={<DaoLandingPg />} />
        <Route path={AppRoutesPaths.bpDao} element={<DaoPage />} />
        <Route path={AppRoutesPaths.presale_old} element={<PresaleOldPage />} />
        <Route path={AppRoutesPaths.mypoints} element={
          // <EnsureLogin>
            <MyPoints />
          // </EnsureLogin>
        } />
        <Route path={AppRoutesPaths.failedpayment} element={<PaymentFailedPage />} />
        <Route path={AppRoutesPaths.sahelion} element={<SahelionPage />} />
        <Route path={AppRoutesPaths.login} element={<LoginPage />} />
        <Route path={AppRoutesPaths.verifyemail} element={<VerifyEmailPage />} />
        <Route path={AppRoutesPaths.signup} element={<SignupPage />} />
        <Route path={AppRoutesPaths.forgotpassword} element={<ForgotPasswordPage />} />
        <Route path={AppRoutesPaths.changepassword} element={<ChangePasswordPage />} />
        <Route path={AppRoutesPaths.presaleAuth} element={<PresaleAuthPage />} />
        <Route path={AppRoutesPaths.resetPassword} element={<ResetPasswordPage />} />
        <Route path={AppRoutesPaths.daotransactions} element={<DaoTransactionsPage />} />
        <Route path={AppRoutesPaths.lotteryActivity} element={<LotteryActivityPage />} />
        <Route path={AppRoutesPaths.privacyPolicy} element={<PrivacyPolicyPage />} />
        <Route path={AppRoutesPaths.userManual} element={<UserManual />} />
        <Route path={AppRoutesPaths.monthlyContribution} element={<MonthlyContribution />} />
        <Route path={AppRoutesPaths.monthlyContributionUpdate} element={<MonthlyContributionUpdate />} />
        <Route path={AppRoutesPaths.monthlyContributionChangeDate} element={<MonthlyContributionChangeDate />} />
        <Route path={AppRoutesPaths.buyMobileMoney} element={<BuyMobileMoney />} />
        <Route path={AppRoutesPaths.sahelionPortfolio} element={<SahelionPortfolio />} />
        <Route path={AppRoutesPaths.createCommunity} element={
          <EnsureLogin>
            <CreateNewCommunity />
          </EnsureLogin>
        } />
        
        {/* Dashboard routes */}
        <Route path={AppRoutesPaths.dashboard.root} element={
          <EnsureLogin>
            <Dashboard />
          </EnsureLogin>
        }>
          <Route index element={<DashboardHome />} />
          <Route path={AppRoutesPaths.dashboard.account} element={<Account />} />
          <Route path={AppRoutesPaths.dashboard.events} element={<Events />} />
          <Route path={AppRoutesPaths.dashboard.ongoingProjects} element={<OngoingProjects />} />
          <Route path={AppRoutesPaths.dashboard.projectMultiVote} element={<ProjectMultiVote />} />
          <Route path={AppRoutesPaths.dashboard.projectRankVote} element={<ProjectRankVote />} />
          <Route path={AppRoutesPaths.dashboard.projectHistory} element={<ProjectHistory />} />
          <Route path={AppRoutesPaths.dashboard.projectManagement} element={<ProjectManagement />} />
          <Route path={AppRoutesPaths.dashboard.initiateVote} element={<InitiateVote />} />
          <Route path={AppRoutesPaths.dashboard.ongoingVote} element={<OngoingVote />} />
          <Route path={AppRoutesPaths.dashboard.eventManagement} element={<EventManagement />} />
          <Route path={AppRoutesPaths.dashboard.projectRankMultiVotes} element={<ProjectRankMultiVotes />} />
          <Route path={AppRoutesPaths.dashboard.voteResults} element={<VoteResults />} />
          <Route path={AppRoutesPaths.dashboard.managecommunity} element={<ManageCommunity/>} />
          <Route path={AppRoutesPaths.dashboard.managecontribution} element={<ManageContributions/>} />
          <Route path={AppRoutesPaths.dashboard.ongoingProjectDetails} element={<ProjectDetails type="ongoing" />} />
          <Route path={AppRoutesPaths.dashboard.projectHistoryDetails} element={<ProjectDetails type="completed" />} />
          <Route path={AppRoutesPaths.dashboard.communityRequests} element={<CommunityRequests />} />
        </Route>
        <Route path="*" element={<NotFound />} />
      </Routes>
    </React.Suspense>
  );
}
