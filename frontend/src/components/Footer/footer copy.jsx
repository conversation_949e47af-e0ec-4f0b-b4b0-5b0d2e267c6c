// import React, { useState } from "react";
// import WalletApprovalPopup from "../Popups/walletApprovalPopup";
// import bgImage from "../../assets/images/bg7.png";
// import DonationPopup from "../Popups/donationPopup";

// const Footer = () => {
//   const [walletAddress, setWalletAddress] = useState("");
//   const [isPopupOpen, setIsPopupOpen] = useState(false);
//   const [isApproved, setIsApproved] = useState(false);
//   const [isDonationPopupOpen, setIsDonationPopupOpen] = useState(false);

//   const handleCheckWallet = () => {
//     setIsApproved(!!walletAddress);
//     setIsPopupOpen(true);
//   };

//   const handleDonate = () => {
//     setIsDonationPopupOpen(true);
//   };

//   const handlePopupClose = (type) => {
//     switch (type) {
//       case "wallet":
//         setIsPopupOpen(false);
//         break;
//       case "donation":
//         setIsDonationPopupOpen(false);
//         break;
//       default:
//         break;
//     }
//   };

//   return (
//     <footer className="bg-gray-800 py-12 px-4 md:px-8 w-full relative">
//       <div
//         className="absolute top-0 left-0 w-full h-full"
//         style={{ backgroundImage: `url(${bgImage})`, backgroundSize: "cover" }}
//       />
//       <div className="container mx-auto relative z-10 flex flex-col items-center justify-center">
//         <div className="w-3/4 md:w-1/2 lg:w-1/3">
//           {/* <p className="text-white text-center mb-4">
//             Check your wallet approval status
//           </p> */}
//           {/* Input Box Section */}
//           {/* <div className="flex items-center mb-8">
//             <input
//               type="text"
//               placeholder="Enter Wallet Address"
//               className="bg-gray-700 text-white py-4 px-6 rounded-lg focus:outline-none focus:bg-gray-600 w-full"
//               style={{ fontSize: "1.5rem" }}
//               value={walletAddress}
//               onChange={(e) => setWalletAddress(e.target.value)}
//             />
//             <button
//               onClick={handleCheckWallet}
//               className="bg-yellow-400 text-black font-bold px-6 py-4 ml-4 rounded-lg"
//             >
//               Check
//             </button>
//           </div> */}
//           {/* Donate Button */}
//           <div className="text-center">
//             <p className="text-white font-bold mb-4">
//               Click here to contribute and empower our impactful mission.
//             </p>
//             <button
//               onClick={handleDonate}
//               className="bg-yellow-400 text-black font-bold px-6 py-4 rounded-lg"
//             >
//               Donate
//             </button>
//           </div>
//         </div>
//         {/* Footer Bottom Section */}
//         <div className="text-center mt-8 items-center">
//           <p className="text-white">
//             &copy; {new Date().getFullYear()} Black Panther Coin. All rights
//             reserved.
//           </p>
//         </div>
//         {/* Render Wallet Approval Popup */}
//         <WalletApprovalPopup
//           isOpen={isPopupOpen}
//           onClose={() => handlePopupClose("wallet")}
//           isApproved={isApproved}
//         />
//         {/* Render Donation Popup */}
//         <DonationPopup
//           isOpen={isDonationPopupOpen}
//           onClose={() => handlePopupClose("donation")}
//         />
//       </div>
//     </footer>
//   );
// };

// export default Footer;

// components/specfic/Footer.jsx
import React from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTelegram,
  faXTwitter,
  faDiscord,
  faTiktok,
  faInstagram,
  faFacebook,
  faYoutube,
  faLinkedin,
} from "@fortawesome/free-brands-svg-icons";
import pantherLogo from "../../assets/images/queenpanther2.png";
import ChatComponent from "../Chat/chatComponent";
import { AppRoutesPaths } from '../../route/app_route';
import { useNavigate } from 'react-router-dom';
import manualIcon from "../../assets/UserManual/manual.png";
import { AiChatComp } from '../../pages/aiChatComp/AiChatComp';

const SocialIcons = () => (
  <div className="flex flex-wrap justify-center mt-4 mb-9 space-x-4">
    <a href="https://t.me/blackpanthertkn" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faTelegram} size="2x" />
    </a>
    <a href="https://x.com/bpnthrx" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faXTwitter} size="2x" />
    </a>
    <a href="https://discord.com/invite/wVA7dqGDu2" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faDiscord} size="2x" />
    </a>
    <a href="https://www.tiktok.com/@blackpanthertkn" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faTiktok} size="2x" />
    </a>
    <a href="https://www.facebook.com/profile.php?id=100063660784177" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faFacebook} size="2x" />
    </a>
    <a href="https://www.instagram.com/blackpanthercoin" target="_blank" className="text-yellow-500 hover:text-gray-300 my-2">
      <FontAwesomeIcon icon={faInstagram} size="2x" />
    </a>
    <a
      href="https://www.youtube.com/@blackpanthertoken"
      target="_blank"
      rel="noopener noreferrer"
      className="text-yellow-500 hover:text-gray-300 my-2"
    >
      <FontAwesomeIcon icon={faYoutube} size="2x" />
    </a>
    <a
      href="https://www.linkedin.com/company/blackpanthertkn/"
      target="_blank"
      rel="noopener noreferrer"
      className="text-yellow-500 hover:text-gray-300 my-2"
    >
      <FontAwesomeIcon icon={faLinkedin} size="2x" />
    </a>
  </div>
);

const Footer = () => {
  const navigate = useNavigate();
  return (<>
  <div className="relative bg-BP-dark-grayish-blue py-4">
    <div className="container mx-auto text-center">
      <img onClick={() => navigate(AppRoutesPaths.home)} src={pantherLogo} alt="PantherLogo" className="mx-auto mb-4 w-32 cursor-pointer" />
      <h4 className="text-white text-lg font-bold">BLACK PANTHER (BPNTHRQ)</h4>
      <SocialIcons />
    </div>

    <div onClick={() => navigate(AppRoutesPaths.userManual)} className="absolute right-5 bottom-5 my-auto h-fit w-fit p-2 border border-BP-nav-gray bg-BP-gray-100-opc hover:bg-white/50 rounded-full cursor-pointer">
      <img src={manualIcon} alt="" className="w-8 h-8" />
    </div>
    
    <AiChatComp />
    
    {/* <ChatComponent /> */}
  </div>
</>);
};

export default Footer;