import React, { useState } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTelegram,
  faXTwitter,
  faDiscord,
  faTiktok,
  faFacebook,
  faInstagram,
  faYoutube,
  faLinkedin,
} from "@fortawesome/free-brands-svg-icons";
import { ImCross } from "react-icons/im";
import pantherLogo from "../../assets/images/queenpanther2.png";
import { AiChatComp } from "../../pages/aiChatComp/AiChatComp";
import { Link } from "react-router-dom";
import pdfFile from "../../assets/The Panther White Paper.pdf";
import { Document, Page, pdfjs } from 'react-pdf';

// Set the workerSrc to the local file
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

const Footer = () => {
  const [showPreview, setShowPreview] = useState(false);
  const [numPages, setNumPages] = useState(null);

  const year = new Date().getFullYear();

  const handleOpenPreview = () => {
    setShowPreview(true);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  return (
    <footer className="bg-BP-dark-grayish-blue text-white pt-10 px-5 relative border-t border-BP-gray-50-opc mt-5">
      <div className="w-[90vw] mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8 items-start text-center lg:text-left">
        {/* Logo + Name + Socials */}
        <div className="space-y-4">
          <div className="flex flex-col lg:flex-row items-center lg:items-start lg:space-x-2">
              <img src={pantherLogo} alt="Logo" className="w-20 mb-2 lg:mb-0" />
            <div className="my-auto">
              <p className="text-xl font-semibold">BLACK PANTHER</p>
              <p className="">(BPNTHRQ)</p>
            </div>
          </div>
          <div className="flex flex-wrap justify-center lg:justify-start space-x-4">
            <a href="https://t.me/blackpanthertkn" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faTelegram} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://x.com/bpnthrx" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faXTwitter} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://discord.com/invite/wVA7dqGDu2" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faDiscord} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://www.tiktok.com/@blackpanthertkn" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faTiktok} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://www.facebook.com/profile.php?id=100063660784177" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faFacebook} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://www.instagram.com/blackpanthercoin" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faInstagram} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://www.youtube.com/@blackpanthertoken" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faYoutube} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
            <a href="https://www.linkedin.com/company/blackpanthertkn/" target="_blank" rel="noreferrer">
              <FontAwesomeIcon icon={faLinkedin} size="lg" className="text-yellow-500 hover:text-gray-300" />
            </a>
          </div>
        </div>

        {/* Links Section */}
        <div className="grid grid-cols-2 gap-8">
          {/* First Link Column */}
          <div className="space-y-2">
            <h4 className="font-semibold text-white">Links</h4>
            <ul className="space-y-1 text-gray-300">
              <li><Link to="/" className="hover:underline underline-offset-2 hover:text-white font-light">Home</Link></li>
              <li><Link to="/about" className="hover:underline underline-offset-2 hover:text-white font-light">About</Link></li>
              {/* <li><Link to="/dao" className="hover:underline underline-offset-2 hover:text-white font-light">DAO</Link></li> */}
              <li><Link to="/presale-auth" className="hover:underline underline-offset-2 hover:text-white font-light">Pre-Sale</Link></li>
              <li><Link to="/sahelion" className="hover:underline underline-offset-2 hover:text-white font-light">Sahelion</Link></li>
              <li><Link to="/game" className="hover:underline underline-offset-2 hover:text-white font-light">Game</Link></li>
            </ul>
          </div>

          {/* Second Link Column */}
          <div className="space-y-2">
            <h4 className="font-semibold text-white">Resources</h4>
            <ul className="space-y-1 text-gray-300">
              <li>
                {/* Link to user manual route */}
                <Link to="/user-manual" className="hover:underline underline-offset-2 hover:text-white font-light">User Manual</Link>
              </li>
              <li>
                <Link to="/privacy-policy" className="hover:underline underline-offset-2 hover:text-white font-light">Privacy Policy</Link>
              </li>
              <li>
                <div onClick={handleOpenPreview} className="hover:underline underline-offset-2 hover:text-white font-light hover:cursor-pointer">White Paper</div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-BP-dark-grayish-blue p-4 w-full h-[100vh] ">
            <div className="w-full h-full overflow-auto relative">
            <button
              onClick={handleClosePreview}
              className="fixed top-0 sm:top-1 right-0 sm:right-1 bg-red-500 text-white p-1.5 rounded-full hover:bg-red-600"
            >
              <ImCross />
            </button>
              <Document
                file={pdfFile}
                onLoadSuccess={onDocumentLoadSuccess}
                className="flex flex-col items-center"
              >
                {Array.from(new Array(numPages), (el, index) => (
                  <Page
                    key={`page_${index + 1}`}
                    pageNumber={index + 1}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    className="mb-4"
                    width={Math.min(800, window.innerWidth * 0.9)} // Responsive width
                  />
                ))}
              </Document>
            </div>
          </div>
        </div>
      )}

      {/* Bottom */}
      <div className="mt-10 text-center text-sm text-gray-400 border-t border-gray-700 py-2.5">
        &copy; {year} BLACK PANTHER. All rights reserved.
      </div>

      {/* Chat Icon (Floating) */}
      <AiChatComp />
    </footer>
  );
};

export default Footer;
