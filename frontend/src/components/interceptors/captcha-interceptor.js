import CaptchaService from '../../services/captchaService';

const CaptchaInterceptor = (axiosInstance) => {
  // URLs excluded from CAPTCHA validation
  const excludedUrls = [
    'user/refresh-token',
    'user/logout',
  ];

  // Request interceptor: Add CAPTCHA token to protected requests
  axiosInstance.interceptors.request.use(
    async (req) => {
      if (excludedUrls.some((url) => req.url.includes(url))) {
        return req;
      }

      if (!CaptchaService.isRecaptchaEnabled()) {
        return req;
      }

      try {
        const captchaToken = CaptchaService.getTokenForRequest();
        if (captchaToken) {
          req.headers['x-captcha-token'] = captchaToken;
        }
      } catch (error) {
        console.error('CAPTCHA token retrieval failed:', error.message);
      }

      return req;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor: Handle CAPTCHA errors
  axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response?.data?.code === 'CAPTCHA_TOKEN_MISSING' || 
          error.response?.data?.code === 'CAPTCHA_VERIFICATION_FAILED') {
        
        CaptchaService.clearCaptchaToken();
        CaptchaService.dispatchCaptchaRequired(error.response.data, error.config);
      }
      
      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

export default CaptchaInterceptor; 