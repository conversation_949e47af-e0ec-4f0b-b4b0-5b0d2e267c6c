import httpClient from "../httpClient/httpClient";
import Swal from 'sweetalert2';
import { AuthService } from "../../services/authService";
import {useAuthentication} from "../utils/provider";

// Function to decode a JWT token and extract its payload
const decodeJwt = (token) => {
  if (!token) return null; // Return null if no token is provided
  const [header, payload] = token.split('.'); // Split the token into header and payload
  if (!payload) return null; // Return null if payload is missing
  const base64Url = payload.replace(/-/g, '+').replace(/_/g, '/'); // Replace URL-safe characters
  const base64 = base64Url.replace(/(.{76})/g, '$1\n'); // Format the base64 string
  const jsonPayload = atob(base64); // Decode the base64 string
  return JSON.parse(jsonPayload); // Parse and return the JSON payload
};

// Function to handle session expiration
const handleSessionExpire = async () => {
  const result = await Swal.fire({
      title: 'Session Expired', // Alert title
      text: '', // Alert text
      icon: 'warning', // Alert icon
      confirmButtonText: 'Login' // Button text
  });

  if (result.isConfirmed) {
      window.location.href = '/login'; // Redirect to login page if confirmed
  }
};

// Function to refresh the access token
const refreshAccessToken = async (token) => {
  try {
      const response = await httpClient.post('user/refresh-token', null, {
          headers: { 'Authorization': `Bearer ${token}` } // Include the current token in the request
      });
      const newToken = response.data.token; // Extract the new token from the response
      const expirationDate = response.data.expiresIn; // Extract the expiration time

      // Store the new token and expiration time in localStorage
      localStorage.setItem('token_key_Bpnthr', newToken);
      localStorage.setItem("expiresIn", expirationDate);
      return newToken; // Return the new token
  } catch (error) {
      console.error('Failed to refresh token:', error); // Log the error
      await handleSessionExpire(); // Handle session expiration
      onLogout(); // Call the logout function
      throw new Error('Failed to refresh token'); // Throw an error
  }
};

// AuthInterceptor function to intercept and modify Axios requests
const AuthInterceptor = (axiosInstance) => {
  let isRefreshing = false; // Flag to track if a token refresh is in progress
  let refreshPromise = null; // Promise to handle concurrent refresh requests

  // List of URLs to exclude from token validation
  const excludedUrls = [
      'user/signup',
      'user/signin',
      'user/send_signup_confirm_code',
      'user/refresh-token',
      'user/verify_code',
      'user/resend_code',
      'user/forgot_password',
      'user/resend_password_reset',
  ];

  // Add a request interceptor to the Axios instance
  axiosInstance.interceptors.request.use(
      async (req) => {
          // Skip token validation for excluded URLs
          if (excludedUrls.some((url) => req.url.includes(url))) {
              return req; // Return the request as is
          }

          const token = localStorage.getItem("token_key_Bpnthr"); // Retrieve the token from localStorage

          if (token) {
              const decodedToken = decodeJwt(token); // Decode the token
              const currentTime = Date.now() / 1000; // Get the current time in seconds
              const tokenExpiryTime = decodedToken ? decodedToken.exp : 0; // Extract the token's expiration time

              // Handle expired token
              if (tokenExpiryTime < currentTime) {
                  localStorage.removeItem("token_key_Bpnthr"); // Remove the expired token
                  await handleSessionExpire(); 
                  onLogout();// Handle session expiration
                  return Promise.reject("Token expired, logging out."); // Reject the request
              }

              // Refresh the token if less than 20 minutes remain
              const timeUntilExpiry = tokenExpiryTime - currentTime;
              if (timeUntilExpiry < 1200) { // Less than 20 minutes
                  if (!isRefreshing) {
                      isRefreshing = true; // Set the refreshing flag
                      refreshPromise = AuthService.refreshToken().finally(() => {
                          isRefreshing = false; // Reset the refreshing flag
                      });
                  }
                  await refreshPromise; // Wait for the token to be refreshed
              }

              // Add the token to the Authorization header
              req.headers["Authorization"] = `Bearer ${localStorage.getItem("token_key_Bpnthr")}`;
          }

          return req; // Return the modified request
      },
      (error) => Promise.reject(error) // Handle request errors
  );

  return axiosInstance; // Return the modified Axios instance
};

export default AuthInterceptor;