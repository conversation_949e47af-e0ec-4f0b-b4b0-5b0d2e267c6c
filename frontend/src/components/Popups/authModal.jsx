import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FaSpinner, FaTimes } from 'react-icons/fa';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useAuthentication } from '../utils/provider';
import httpClient from '../httpClient/httpClient';
import affiliateLinkService from '../../services/affiliateLinkService';

const AuthModal = ({ isOpen, toggleModal }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    walletAddress: '',
  });
  const { currentUser, setIsAuthenticated, onLogout, getCurrentUser } = useAuthentication();
  const [confirmPassword, setConfirmPassword] = useState("")
  const [confirmPassError, setConfirmPassError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleFormSwitch = () => setIsLogin(!isLogin);

  const handleChange = (e) => setFormData({ ...formData, [e.target.name]: e.target.value });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      
      // Check for referral data
      const refCode = localStorage.getItem('referral_code');
      const token = localStorage.getItem('referral_token');
      
      let response;
      if (isLogin) {
        // For login, validate affiliate link if present
        if (refCode && token) {
          try {
            const validation = await affiliateLinkService.validateAffiliateLink(refCode, token);
            if (!validation.valid) {
              toast.error(validation.message);
              localStorage.removeItem('referral_code');
              localStorage.removeItem('referral_token');
              localStorage.removeItem('referral_link_used');
            }
          } catch (error) {
            // Link expired or invalid, but allow login to continue
            localStorage.removeItem('referral_code');
            localStorage.removeItem('referral_token');
            localStorage.removeItem('referral_link_used');
          }
        }
        
        response = await httpClient.post("user/signin", formData);
        if (response.status === 200) {
          localStorage.setItem("token_key_Bpnthr", response.data.token);
          localStorage.setItem("expiresIn__Bpnthr", response.data.expiresIn);
          
          // Track affiliate login if referral data exists
          if (refCode && token) {
            try {
              await affiliateLinkService.trackAffiliateSignup(refCode, token, response.data.user._id);
              toast.success("Welcome! You've joined through an invite link.");
            } catch (error) {
              console.error("Failed to track affiliate signup:", error);
            }
            // Clean up referral data
            localStorage.removeItem('referral_code');
            localStorage.removeItem('referral_token');
            localStorage.removeItem('referral_link_used');
          }
          
          toast.success(response.data.message);
          setIsAuthenticated(true);
          getCurrentUser();
          setIsLoading(false);
          toggleModal();
        }
      } else {
        // For signup, validate affiliate link before proceeding
        if (refCode && token) {
          try {
            const validation = await affiliateLinkService.validateAffiliateLink(refCode, token);
            if (!validation.valid) {
              toast.error(validation.message);
              localStorage.removeItem('referral_code');
              localStorage.removeItem('referral_token');
              localStorage.removeItem('referral_link_used');
              setIsLoading(false);
              return;
            } else {
              // Show referral info to user
              toast.info(`Joining through ${validation.data.referrerName}'s invite! ${validation.data.spotsRemaining} spots remaining.`);
            }
          } catch (error) {
            setIsLoading(false);
            return;
          }
        }
        
        // Add referral data to signup payload
        const signupData = { ...formData };
        if (refCode && token) {
          signupData.referralCode = refCode;
          signupData.referralToken = token;
        }
        
        response = await httpClient.post("user/signup", signupData);
        if (response.status === 201) {
          // Track successful signup with affiliate link
          if (refCode && token) {
            try {
              const trackingResult = await affiliateLinkService.trackAffiliateSignup(refCode, token, response.data.user._id);
              if (trackingResult.success) {
                const rewards = trackingResult.data.rewardsEarned;
                let message = "Account created successfully through invite link!";
                if (rewards.bonus) {
                  message += ` Bonus rewards unlocked for the referrer!`;
                }
                toast.success(message);
              }
            } catch (error) {
              console.error("Failed to track affiliate signup:", error);
              toast.success("Account created successfully!");
            }
            // Clean up referral data
            localStorage.removeItem('referral_code');
            localStorage.removeItem('referral_token');
            localStorage.removeItem('referral_link_used');
          } else {
            toast.success(response.data.message);
          }
          
          setIsLogin(true);
          setFormData({ email: '', password: '' });
          setIsLoading(false);
        }
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "An error occurred");
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const onSetConfirmPassword = (val) => {
    setConfirmPassword(val)
    if(val !== formData.password) {
      setConfirmPassError(true)
    } else {
      setConfirmPassError(false)
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className="bg-lightbaige p-8 rounded-lg shadow-lg relative w-full max-w-md">
        <button
          onClick={toggleModal}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <FaTimes className="h-6 w-6" />
        </button>
        {!currentUser ? (<>
          <h2 className="text-2xl font-bold mb-4 text-center text-purple-950">
            {isLogin ? 'Login' : 'Register'}
          </h2>
          <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
              Email address
            </label>
            <div className="mt-2">
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleChange}
                className="block w-full rounded-md border-0 py-2 text-black bg-[#FFFAE1] p-2 shadow-sm ring-1 ring-inset ring-gray-300 placeholder-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
              Password
            </label>
            <div className="mt-2">
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={formData.password}
                onChange={handleChange}
                className="block w-full rounded-md border-0 py-2 text-black bg-[#FFFAE1] p-2 shadow-sm ring-1 ring-inset ring-gray-300 placeholder-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          </div>
          {!isLogin && (
            <>
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium leading-6 text-gray-900">
                  Confirm Password
                </label>
                <div className="mt-2">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    value={confirmPassword}
                    onChange={(e) => onSetConfirmPassword(e.target.value)}
                    className="block w-full rounded-md border-0 py-2 text-black bg-[#FFFAE1] p-2 shadow-sm ring-1 ring-inset ring-gray-300 placeholder-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  />
                </div>
              </div>
              {confirmPassError && (<p className='text-red-500'>password does not match</p>)}
              <div>
                <label htmlFor="walletAddress" className="block text-sm font-medium leading-6 text-gray-900">
                  Wallet Address To Receive Tokens
                </label>
                <div className="mt-2">
                  <input
                    id="walletAddress"
                    name="walletAddress"
                    type="text"
                    autoComplete="walletAddress"
                    required
                    value={formData.walletAddress}
                    onChange={handleChange}
                    className="block w-full rounded-md border-0 py-2 text-black bg-[#FFFAE1] p-2 shadow-sm ring-1 ring-inset ring-gray-300 placeholder-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  />
                </div>
              </div>
            </>
          )}
          <div>
            {confirmPassError ? (<>
              <button
                disabled
                className="flex justify-center w-full rounded-md bg-yellow-500 px-3 py-2 text-sm font-semibold leading-6 text-black shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600"
              >
                Submit
              </button>
            </>) : (<>
              {isLoading ? (<>
                <button
                  className="flex justify-center w-full rounded-md bg-yellow-500 px-3 py-2 text-sm font-semibold leading-6 text-black shadow-sm hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600"
                >
                  <FaSpinner />
                </button>
              </>) : (<>
                <button
                  type="submit"
                  className="flex justify-center w-full rounded-md bg-yellow-500 px-3 py-2 text-sm font-semibold leading-6 text-black shadow-sm hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600"
                >
                  Submit
                </button>
              </>)}
            </>)}
          </div>
        </form>
        <p className="mt-4 text-center text-sm text-gray-500">
          {isLogin ? "Don't have an account?" : 'Already have an account?'}{' '}
          <button
            onClick={handleFormSwitch}
            className="font-semibold text-yellow-500 hover:text-yellow-600 focus:outline-none"
          >
            {isLogin ? 'Register' : 'Login'}
          </button>
        </p>
        </>) : (<>
          <h2 className="text-2xl font-bold mb-4 text-center text-purple-950">
            Log Out
          </h2>
          <button
            onClick={onLogout}
            className="flex justify-center w-full rounded-md bg-yellow-500 px-3 py-2 text-sm font-semibold leading-6 text-black shadow-sm hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-600"
          >
            Log Out
          </button>
        </>)}
      </div>
    </div>
  );
};

AuthModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  toggleModal: PropTypes.func.isRequired,
};

export default AuthModal;
