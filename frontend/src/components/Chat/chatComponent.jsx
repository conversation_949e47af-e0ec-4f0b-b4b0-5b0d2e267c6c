import React, { useState } from 'react';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from "@google/generative-ai";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPaperPlane, faComments, faTimes } from "@fortawesome/free-solid-svg-icons";

const PantherAIChat = () => {
    const genAI = new GoogleGenerativeAI('AIzaSyAzwP4sZgNjujWbyIs6OEZbDxudN4yibq4');

    const [messages, setMessages] = useState([]);
    const [inputText, setInputText] = useState('');
    const [loading, setLoading] = useState(false);
    const [isChatOpen, setIsChatOpen] = useState(false);

    const handleMessageSubmit = async () => {
        setLoading(true);
        const newMessages = [...messages, { role: 'user', text: inputText }];
        setMessages(newMessages);
        setInputText('');

        const model = genAI.getGenerativeModel({
          model: "gemini-1.5-flash-latest",
          systemInstruction: "You are PantherAI, a friendly assistant working for Black Panther Token (BPNTHRQ). The Black Panther is an animal full of symbolism and meaning in Africa and around the world. This powerful symbol inspired us to create a meme coin that symbolizes power, resilience, triumph, and overcoming challenges. This white paper introduces the \"Black Panther Token,\" which honors this symbol while raising awareness about health and wellness issues, particularly those that disproportionately affect people of African descent globally. Additionally, it aims to educate and bring more people into the crypto/Web3 space, empowering them with knowledge about investing in cryptocurrency.\n\n### Introduction\n\nIf the symbol of the Black Panther moves you, you will love the Black Panther token. We will dedicate 10% of the project to a charity wallet that will fund initiatives to educate and raise awareness about illnesses such as colon cancer, Sickle Cell Anemia, and other diseases that disproportionately affect people of African descent globally. Our community will contribute by donating to charities, hospitals, and research institutions, as well as providing direct support for treatment. The charity wallet will also fund global research into alternative treatments and promote the \"food as medicine\" movement, supporting doctors who use diet to treat lifestyle diseases.\n\n### Why Does Black Panther Coin Exist?\n\nBlack Panther Coin aims to provide a Web3/Blockchain platform that attracts new participants to the space through a meme coin. We believe the strength of the Black Panther symbol will draw people to our project's purpose. We seek to leverage the awareness generated by this meme coin to educate people on alternative treatments and the importance of health and wellness, as well as to introduce new users to the versatile world of crypto and Web3/Blockchain, fostering the creation of solutions for various issues.\n\n### Mission and Vision\n\nThe mission and vision of Black Panther Coin are to save lives by drawing attention to illnesses that disproportionately affect people of African descent, educating people on health and wellness, and offering financial literacy in crypto and Web3/Blockchain. We will deploy funds toward a massive education campaign on crypto, finance, and investing literacy, which will also help increase the uptake of the token and grow the project.\n\nSafe and Secure on Multi-Blockchain\n\nBlack Panther is deployed on secure multichain blockchain networks, including BSC & Ethereum, built for running smart contract-based applications. Running on multiple chains allows users to benefit from their preferred blockchain. We leverage networks that combine delegated proof-of-stake (PoS) and proof-of-authority (PoA) to ensure network consensus and maintain security. We chose chains with low fees, making it ideal for frequent trading with minimal transaction costs.\n\n### Tokenomics\n\n- Treasury: 10%\n- Marketing: 10%\n- Charity: 10%\n- Decentralized Exchanges (DeXs): 50%\n- Centralized Exchanges (CeXs): 10%\n- Development: 10%\n\nTotal Supply: 10,000,000,000 (10 Billion)\n\n### Roadmap\n\n**P1:**\n- DeX listing\n- Audit\n- Coinmarketcap listing\n- Coin Gecko listing\n- Exchange listing\n- Marketing campaign\n- Paid advertising\n- Influencer push\n- Airdrop campaign\n\n**P2:**\n- 2nd wave exchange listing\n- Continual influencer push\n- Continual paid advertising\n- Intensified marketing\n- Charity partnerships\n\n**P3:**\n- 2D NFT launch\n- Strategic partnerships\n- Continual influencer push\ncharity partnerships\n- Major exchange listing\n- Airdrop campaigns\n- Debit card launch\n\n**P4:**\nP2E game testing\n2nd major exchange listing\nfinal airdrop campaign\n3rd charity donation\nContinual marketing with strategically placed paid advertisements\nA-list influencer push\n\ncontact address of the Black Panther token is 0x12a55f6aBDfE13a44eF8b29a24964e20D21E0fA5\n\nthe current CEO of black panther token is Karanja Gacuca\nThe official symbol of the Black Panther Token is BPNTHRQ.\nThe lead developer of Black Panther Token is Mbuh Divine, \n\n\n\n\n\n",
        });

        const generationConfig = {
          temperature: 1,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 8192,
          responseMimeType: "text/plain",
        };

        const safetySettings = [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ];

        // const chatSession = model.startChat({
        //   generationConfig,
        //   safetySettings,
        //   history: newMessages.map(msg => ({
        //     role: msg.role,
        //     parts: [{ text: msg.text }]
        //   })),
        // });
        const chatHistory = newMessages.map(msg => ({
            role: 'user',
            parts: [{ text: msg.text }]
        }));
        
        const chatSession = model.startChat({
            generationConfig,
            safetySettings,
            history: chatHistory,
        });
        
        

        const result = await chatSession.sendMessage(inputText);
        const botMessage = { role: 'model', text: result.response.text() };

        setMessages([...newMessages, botMessage]);
        setLoading(false);
    };

    const toggleChat = () => {
        setIsChatOpen(!isChatOpen);
    };

    return (
        <div className="fixed bottom-4 right-4 flex flex-col items-end">
            {isChatOpen && (
                <div className="max-w-md w-full flex flex-col bg-white rounded-lg overflow-hidden shadow-lg mb-4">
                    <div className="flex items-center justify-between p-4 bg-yellow-500 text-white">
                        <h1 className="text-lg font-bold">PantherAI</h1>
                        <button className="focus:outline-none" onClick={toggleChat}>
                            <FontAwesomeIcon icon={faTimes} />
                        </button>
                    </div>
                    <div className="flex-1 p-4 overflow-y-auto" style={{ maxHeight: '400px' }}>
                        {messages.map((message, index) => (
                            <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} mb-2`}>
                                <div className={`rounded-lg p-3 ${message.role === 'user' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-900'} shadow-md max-w-xs`}>
                                    {message.text}
                                </div>
                            </div>
                        ))}
                        {loading && <p className="text-center italic text-gray-500">PantherAI is typing...</p>}
                    </div>
                    <div className="border-t border-gray-200 flex items-center p-2 bg-gray-100">
                        <input
                            className="flex-grow px-4 py-2 mr-2 bg-white border border-gray-300 rounded text-black focus:outline-none focus:ring-2 focus:ring-yellow-500"
                            type="text"
                            placeholder="Message PantherAI..."
                            value={inputText}
                            onChange={(e) => setInputText(e.target.value)}
                        />
                        <button
                            className="p-2 bg-yellow-500 text-white rounded-full focus:outline-none hover:bg-yellow-600 transition duration-300"
                            onClick={handleMessageSubmit}
                            disabled={!inputText.trim()}
                        >
                            <FontAwesomeIcon icon={faPaperPlane} className="w-5 h-5" />
                        </button>
                    </div>
                </div>
            )}
            <button
                className="p-4 bg-yellow-500 text-white rounded-full shadow-lg focus:outline-none hover:bg-yellow-600 transition duration-300"
                onClick={toggleChat}
            >
                <FontAwesomeIcon icon={faComments} className="w-6 h-6" />
            </button>
        </div>
    );
};

export default PantherAIChat;
