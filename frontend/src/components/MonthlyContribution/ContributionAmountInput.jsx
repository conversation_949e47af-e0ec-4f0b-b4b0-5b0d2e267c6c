import { useState } from 'react';

export const ContributionAmountInput = ({
  amountOption,
  setAmountOption,
  customAmount,
  setCustomAmount,
  amountError,
  setAmountError,
  paymentMethod = 'fiat',
}) => {
  return (
    <div className="">
      {/* Only show the custom input for new amount */}
      <div>
        <h2 className="text-lg mt-4 font-normal text-[#4B5563] flex items-center font-body text-[1rem]">
          New Amount
        </h2>
        <input
          onChange={(e) => {
            const value = e.target.value.replace(/[^0-9.]/g, '');
            const decimalCount = value.split('.').length - 1;
            if (decimalCount <= 1) {
              const numValue = parseFloat(value);
              if (value === '' || isNaN(numValue)) {
                setAmountOption(value);
                setAmountError('');
              } else if (numValue < 5) {
                setAmountOption(value);
                setAmountError('Minimum amount is $5');
              } else {
                setAmountOption(value);
                setAmountError('');
              }
            }
          }}
          type="text"
          inputMode="decimal"
          className="border border-[#E5E7EB] w-full md:h-[4vw] h-[15vw] md:rounded-[1vw] rounded-[3vw] text-center font-body font-light bg-[#505050]/10 focus:outline focus:outline-purple-400 focus:outline-1"
          placeholder='Enter new amount'
          value={amountOption}
        />
        {amountError && (
          <p className="text-red-500 text-sm mt-1 text-left">{amountError}</p>
        )}
      </div>
    </div>
  );
};

export default ContributionAmountInput; 