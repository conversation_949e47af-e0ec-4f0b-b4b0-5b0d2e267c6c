import React from "react";
import { useIdleTimer } from "react-idle-timer";
import { useAuthentication } from "../utils/provider";
import Swal from "sweetalert2";

// Component to handle automatic logout after a period of inactivity
export const AutoLogout = () => {
    // Extract the logout function from the authentication context
    const { onLogout, currentUser } = useAuthentication();

    // Define the idle time limit (30 minutes in milliseconds)
    const idleTimeLimit = 30 * 60 * 1000; // 30 minutes

    // Function to handle session expiration
    const handleSessionExpire = async () => {
        // Show a SweetAlert popup to notify the user about session expiration
        const result = await Swal.fire({
            title: "Session Expired", // Title of the alert
            text: "You have been logged out due to inactivity.", // Message to the user
            icon: "warning", // Warning icon
            confirmButtonText: "Login", // Button text
        });

        // If the user confirms, log them out and redirect to the login page
        if (result.isConfirmed) {
            onLogout(); // Call the logout function
            window.location.href = "/login"; // Redirect to the login page
        }
    };

    // Initialize the idle timer with the specified configuration
    const { reset } = useIdleTimer({
        timeout: idleTimeLimit, // Time limit for inactivity
        onIdle: () => {
            if (currentUser) handleSessionExpire(); // only if logged in
            
        },
        onActive: () => {
            // Only reset if user is logged in
            if (currentUser){
                reset();
            }
        },
        debounce: 500, // Debounce time to prevent rapid triggering
    });

    // This component does not render any visible UI
    return null;
};