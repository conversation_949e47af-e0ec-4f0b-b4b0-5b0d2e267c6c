import React from 'react';
import { useLocation } from 'react-router-dom';
import CaptchaComponent from './CaptchaComponent';
import CaptchaService from '../../services/captchaService';

const RouteBasedCaptcha = ({ 
  onVerify, 
  onExpired, 
  onError, 
  theme = 'light', 
  size = 'normal',
  className = '',
  children 
}) => {
  const location = useLocation();
  
  // Check if current route requires CAPTCHA
  const shouldShowCaptcha = CaptchaService.shouldShowCaptcha(location.pathname);
  
  if (!shouldShowCaptcha) {
    return children || null;
  }

  return (
    <div className="route-based-captcha">
      {children}
      <CaptchaComponent
        onVerify={onVerify}
        onExpired={onExpired}
        onError={onError}
        theme={theme}
        size={size}
        className={className}
      />
    </div>
  );
};

export default RouteBasedCaptcha; 