import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import ReCA<PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import CaptchaService from '../../services/captchaService';

const CaptchaComponent = ({ 
  onVerify, 
  onExpired, 
  onError, 
  theme = 'light', 
  size = 'normal',
  className = '',
  forceShow = false // Allow manual override
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Update current route in CaptchaService
    CaptchaService.setCurrentRoute(location.pathname);
    
    // Check if CAPTCHA should be shown for current route
    const shouldShow = forceShow || CaptchaService.shouldShowCaptcha(location.pathname);
    setIsVisible(shouldShow);
    
    // Check if we already have a valid token
    if (shouldShow) {
      setIsTokenValid(CaptchaService.isTokenValid());
    }
  }, [location.pathname, forceShow]);

  // Auto-refresh token before expiry
  useEffect(() => {
    if (!isVisible) return;

    const checkTokenValidity = () => {
      const valid = CaptchaService.isTokenValid();
      setIsTokenValid(valid);
      
      // If token is about to expire, reset CAPTCHA
      if (!valid && CaptchaService.recaptchaRef.current) {
        CaptchaService.resetRecaptcha();
      }
    };

    // Check every 30 seconds
    const interval = setInterval(checkTokenValidity, 30000);
    
    return () => clearInterval(interval);
  }, [isVisible]);

  const handleChange = (token) => {
    if (token) {
      CaptchaService.setCaptchaToken(token);
      setIsTokenValid(true);
      onVerify && onVerify(token);
    } else {
      CaptchaService.clearCaptchaToken();
      setIsTokenValid(false);
      onExpired && onExpired();
    }
  };

  const handleExpired = () => {
    CaptchaService.clearCaptchaToken();
    setIsTokenValid(false);
    onExpired && onExpired();
  };

  const handleError = () => {
    CaptchaService.clearCaptchaToken();
    setIsTokenValid(false);
    onError && onError();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`captcha-container ${className}`}>
      <ReCAPTCHA
        ref={CaptchaService.recaptchaRef}
        sitekey={CaptchaService.getSiteKey()}
        onChange={handleChange}
        onExpired={handleExpired}
        onError={handleError}
        theme={theme}
        size={size}
      />
      {isTokenValid && (
        <div className="text-green-600 text-sm mt-2 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Verified
        </div>
      )}
    </div>
  );
};

export default CaptchaComponent; 