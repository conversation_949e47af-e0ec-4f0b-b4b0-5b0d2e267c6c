import React, { useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import CaptchaService from '../../services/captchaService';
import { toast } from 'react-toastify';

const GlobalCaptcha = () => {
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [pendingRequests, setPendingRequests] = useState([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const location = useLocation();
  const { executeRecaptcha } = useGoogleReCaptcha();

  // Update route and execute CAPTCHA if needed
  useEffect(() => {
    CaptchaService.setCurrentRoute(location.pathname);
    
    const shouldShow = CaptchaService.shouldShowCaptcha(location.pathname);
    const hasValidToken = CaptchaService.isTokenValid();
    
    setIsTokenValid(hasValidToken);
    
    if (shouldShow && !hasValidToken && executeRecaptcha && !isExecuting) {
      executeReCaptcha();
    }
  }, [location.pathname, executeRecaptcha]);

  // Get CAPTCHA action based on route type
  const getActionForRoute = useCallback((route) => {
    const routeType = CaptchaService.getRouteType(route);
    
    const typeActionMap = {
      'signup': 'signup',           // Signup routes get signup action
      'auth': 'login',           // Authentication routes get login action
      'payment': 'checkout',     // Payment routes get checkout action
      'basic': 'page_view',       // Basic routes get page_view action
    };

    return typeActionMap[routeType] || 'page_view';
  }, []);

  // Execute reCAPTCHA v3 with route-specific action
  const executeReCaptcha = useCallback(async () => {
    if (!executeRecaptcha || isExecuting) {
      return;
    }

    setIsExecuting(true);
    
    try {
      const action = getActionForRoute(location.pathname);
      const token = await executeRecaptcha(action);
      
      if (token) {
        CaptchaService.setCaptchaToken(token);
        setIsTokenValid(true);
        retryPendingRequests();
      }
    } catch (error) {
      console.error('reCAPTCHA execution error:', error);
      toast.error('Security verification failed. Please refresh the page.');
    } finally {
      setIsExecuting(false);
    }
  }, [executeRecaptcha, isExecuting, location.pathname, getActionForRoute]);

  // Event listeners for CAPTCHA lifecycle
  useEffect(() => {
    const handleTokenUpdate = () => {
      setIsTokenValid(true);
      retryPendingRequests();
    };

    const handleTokenCleared = () => {
      setIsTokenValid(false);
      
      if (CaptchaService.shouldShowCaptcha() && executeRecaptcha && !isExecuting) {
        executeReCaptcha();
      }
    };

    const handleCaptchaRequired = (event) => {
      const { originalRequest } = event.detail;
      setPendingRequests(prev => [...prev, originalRequest]);
      
      if (!CaptchaService.isTokenValid() && executeRecaptcha && !isExecuting) {
        executeReCaptcha();
      }
    };

    window.addEventListener('captchaTokenUpdated', handleTokenUpdate);
    window.addEventListener('captchaTokenCleared', handleTokenCleared);
    window.addEventListener('captchaRequired', handleCaptchaRequired);

    return () => {
      window.removeEventListener('captchaTokenUpdated', handleTokenUpdate);
      window.removeEventListener('captchaTokenCleared', handleTokenCleared);
      window.removeEventListener('captchaRequired', handleCaptchaRequired);
    };
  }, [executeRecaptcha, isExecuting]);

  const retryPendingRequests = useCallback(() => {
    if (pendingRequests.length > 0) {
      pendingRequests.forEach(request => {
        if (request && typeof request.retry === 'function') {
          request.retry();
        }
      });
      setPendingRequests([]);
    }
  }, [pendingRequests]);

  // Auto-refresh token before expiry
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      if (CaptchaService.shouldShowCaptcha()) {
        const timeUntilExpiry = CaptchaService.getTimeUntilExpiry();
        
        if (timeUntilExpiry < 30000 && executeRecaptcha && !isExecuting) {
          executeReCaptcha();
        }
      }
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [executeRecaptcha, isExecuting]);

  // Show minimal loading state during verification
  if (isExecuting) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-700">Verifying security...</span>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default GlobalCaptcha; 