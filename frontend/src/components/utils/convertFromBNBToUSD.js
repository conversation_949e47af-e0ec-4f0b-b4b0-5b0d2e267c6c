import { ethers } from 'ethers'
import { GetChainlinkAggregatorV3InterfaceABI } from './getChainlinkAggregatorV3InterfaceABI'

export const ConvertFromBNBToUSD = async (bnb) => {
    const environment = import.meta.env.VITE_ENVIRONMENT
    const v3InterfaceABI = GetChainlinkAggregatorV3InterfaceABI

    const provider = new ethers.providers.JsonRpcProvider(
        "https://few-black-meadow.bsc.quiknode.pro/a8eccc53cb4b785e96a07e78b7f454b389aebf6a"
    )

    const priceFeedAddress = environment === "Production"
        ? "******************************************" // Mainnet BNB/USD
        : "******************************************"; // Testnet BNB/USD on BSC Testnet

    const priceFeed = new ethers.Contract(
        priceFeedAddress,
        v3InterfaceABI,
        provider    
    )

    const latestRoundData = await priceFeed.latestRoundData()
    const decimals = await priceFeed.decimals()

    const valueOfOneBNB = Number(
        (latestRoundData.answer.toString() / Math.pow(10, decimals)).toFixed(2)
    )
    
    console.log("valueOfOneBNB", valueOfOneBNB)
    
    return Number((bnb * valueOfOneBNB).toFixed(2))
}