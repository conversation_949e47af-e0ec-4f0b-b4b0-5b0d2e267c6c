import { ethers } from 'ethers';
import { GetChainlinkAggregatorV3InterfaceABI } from './getChainlinkAggregatorV3InterfaceABI';

export const ConvertFromUSDToBNB = async (usd) => {
    const environment = import.meta.env.VITE_ENVIRONMENT
    const v3InterfaceABI = GetChainlinkAggregatorV3InterfaceABI;

    const urlRPC = environment === "Production" ? "https://bnb-mainnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT" : "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

    const provider = new ethers.providers.JsonRpcProvider(
        urlRPC
    );

    const priceFeedAddress = environment === "Production"
        ? "******************************************" // Mainnet BNB/USD
        : "******************************************"; // Testnet BNB/USD on BSC Testnet

    const priceFeed = new ethers.Contract(
        priceFeedAddress, // Chainlink BNB/USD aggregator on BSC
        v3InterfaceABI,
        provider
    );

    const latestRoundData = await priceFeed.latestRoundData();
    const decimals = await priceFeed.decimals();

    const bnbUsdPrice = Number(latestRoundData.answer.toString()) / Math.pow(10, decimals);

    const valueInBNB = Number((usd / bnbUsdPrice).toFixed(6)); // More decimal precision for BNB
    console.log("valueInBNB", valueInBNB);

    return valueInBNB;
};
