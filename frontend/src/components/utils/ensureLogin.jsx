import { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthentication } from '../../components/utils/provider';
import { LoadingScreen } from './loader';

export const EnsureLogin = ({ children }) => {
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
    const { getCurrentUser } = useAuthentication()

  useEffect(() => {
    const checkLogin = async () => {
      const token = localStorage.getItem('token_key_Bpnthr');
      const expiresAt = parseInt(localStorage.getItem('expiresAt_Bpnthr'), 10);

      if (!token || !expiresAt || Date.now() >= expiresAt) {
        console.log('Token expired or not found');
        localStorage.setItem('login_origin', window.location.pathname);
        localStorage.removeItem('token_key_Bpnthr');
        setIsAuthenticated(false);
        setLoading(false);
      } else {
        try {
          await getCurrentUser();
          setIsAuthenticated(true);
        } catch (error) {
          console.error('Failed to fetch user:', error);
          setIsAuthenticated(false);
        } finally {
          setLoading(false);
        }
      }
    };

    checkLogin();
  }, []);

  if (loading) {
    return (
        <LoadingScreen />
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return children;
};
