import { useCallback, useEffect, useMemo, useState } from "react";
import constate from "constate";
import httpClient from "../httpClient/httpClient";
import { useAccount, useChainId, useDisconnect, useWalletClient } from "wagmi";
import { ethers } from "ethers";
import { useWeb3Modal } from "@web3modal/wagmi/react";
import { toast } from "react-toastify";
import { bpnthrABI, bpnthrToken, bscChainId, usdtABI, usdtAddress, preSaleABI, preSaleAddress, sahelionABI, sahelionAddress, sahelionReserveABI, sahelionReserveAddress, lotteryABI, lotteryAddress, lotteryTokenAddress, lotteryTokenABI } from "../../constants/constants";
import { GetValueOfUSDTInUSD } from "./getValueOfUSDTInUSD";

export const [LoginProvider, useAuthentication] = constate(
  useLogin,
  value => value.authMethods
);

function useLogin() {
  const [isAuthenticated, setIsAuthenticated] = useState(localStorage.getItem("token_key_Bpnthr") !== null);
  const [currentUser, setCurrentUser] = useState("");
  const [PointsToSave, setPointsToSave] = useState(localStorage.getItem("Bpnthr_pt_sv"));
  const [currentUserWallet, setCurrentUserWallet] = useState("")
  const [bPnthrBalance, setPBnthrBalance] = useState(0)
  const [shlnBalance, setSHLNBalance] = useState(0)
  const [originPath, setOriginPath] = useState(localStorage.getItem("login_origin") || "");
  const [usdEquivalent, setUsdEquivalent] = useState('0.00');
  const [shlnTousd, setSHLNToUsd] = useState('0.00');
  const [totalCryptoBacking, setTotalCryptoBacking] = useState(0);
  const [totalSHLNSupply, setTotalSHLNSupply] = useState(0);

  const { address, isConnected } = useAccount()
  const { open } = useWeb3Modal()
  const { disconnect } = useDisconnect()
  const { data: walletClient } = useWalletClient();
  const chainId = useChainId()

  useEffect(() => {
    if (!isAuthenticated) {
      // JICKS: This should take the user to the login page
      //window.location.href = "/";
    } else {
      if (!currentUser) {
        const getCurrentUserAsync = async () => {
          await getCurrentUser();
        };

        getCurrentUserAsync();
      }
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (address && walletClient) {
      getWeb3Signer()
      setCurrentUserWallet(address)
      getWalletBalance()
      getSHLNWalletBalance()
    } else {
      setCurrentUserWallet("")
    }
  }, [walletClient, address])

  useEffect(() => {
    const fetchUsdValue = async () => {
      if (shlnBalance) {
        const value = await GetValueOfUSDTInUSD(shlnBalance);
        setUsdEquivalent(value.toFixed(2));
      }
    };

    fetchUsdValue();
  }, [shlnBalance]);

  useEffect(() => {
    const shlnToUsd = async () => {
      const value = await GetValueOfUSDTInUSD(1);
      setSHLNToUsd(value.toFixed(2));
    };

    shlnToUsd();
  }, []);

  useEffect(() => {
    getTotalUSDTBacking().then((totalBacking) => {
      if (totalBacking) {
        setTotalCryptoBacking(totalBacking.toString());
      } else {
        setTotalCryptoBacking('0.00');
      }
    })
    getTotalSHLNSupply().then((supply) => {
      if (supply) {
        setTotalSHLNSupply(supply.toString());
      } else {
        setTotalSHLNSupply('0.00');
      }
    })
  }, []);

  const connectWallet = async () => {
    await open()
  }

  const disconnectWallet = async () => {
    disconnect()
    setCurrentUserWallet("")
    setPBnthrBalance(0)
  }

  const getWalletBalance = async () => {
    try {
      const web3Signer = await getWeb3Signer()
      const bPnthrSigner = new ethers.Contract(bpnthrToken, bpnthrABI, web3Signer);
      const _balance = await bPnthrSigner.balanceOf(address)
      setPBnthrBalance(ethers.utils.formatEther(_balance))
    } catch (error) {
      console.log(error)
    }
  }

  const getSHLNWalletBalance = async () => {
    try {
      const web3Signer = await getWeb3Signer()
      const shlnSigner = new ethers.Contract(sahelionAddress, sahelionABI, web3Signer);
      const _balance = await shlnSigner.balanceOf(address)
      setSHLNBalance(ethers.utils.formatEther(_balance));
    } catch (error) {
      console.log(error)
    }
  }

  const getCurrentUser = async () => {
    const { data: response } = await httpClient.get("user/get_user");
    setCurrentUser(response?.user);
    if (response?.user)
      setIsAuthenticated(true);
  };

  const onSavePointsLocally = (points) => {
    localStorage.setItem("Bpnthr_pt_sv", points);
  }


  const ensureLogin = useCallback(async () => {

    if (localStorage.getItem("token_key_Bpnthr") === null) {
      localStorage.setItem("login_origin", window.location.pathname);
      window.location.href = "/login";
    } else {
      const expiresAt = parseInt(localStorage.getItem("expiresAt_Bpnthr"), 10);

      if (!expiresAt || Date.now() >= expiresAt) {
        console.log("Token expired or not found");
        localStorage.setItem("login_origin", window.location.pathname);
        onLogout()
        window.location.href = "/login";
      } else {
        await getCurrentUser();
      }
    }
  }, []);

  const clearLocalStorage = () => {
    localStorage.removeItem("login_origin");
    localStorage.removeItem("token_key_Bpnthr");
    localStorage.removeItem("expiresIn_Bpnthr");
    localStorage.removeItem("Bpnthr_pt_sv");
  };

  const onLogout = () => {
    clearLocalStorage();
    setCurrentUser("");
    setCurrentUserWallet("")
    disconnect()
  };

  // Add this new method
  const getLoginOrigin = useCallback(() => {
    return localStorage.getItem("login_origin");
  }, []);

  const getWeb3Signer = async () => {
    if (chainId !== Number(bscChainId)) {
      disconnect()
      setCurrentUserWallet("")
      toast.error(`Change the network to Bsc`)
      return
    }

    if (!walletClient) {
      console.error("No signer available. Please connect a wallet first.");
      return;
    }

    const provider = new ethers.providers.Web3Provider(walletClient.transport)
    const signer = provider.getSigner()
    return signer;
  }

  const BuyTokenFromPresale = async (usdInputValue) => {
    try {
      const web3Signer = await getWeb3Signer()
      const presaleContract = new ethers.Contract(preSaleAddress, preSaleABI, web3Signer)
      const tx = await presaleContract.buyToken({ value: ethers.utils.parseEther(usdInputValue.toString()) })
      await tx.wait()
      getWalletBalance()
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Transaction failed:", error);

      // Fallback message
      let detailedMessage = "An error occurred";

      // Check for "execution reverted" in known places
      const rawMessage = error?.data?.message || error?.message;

      if (rawMessage && rawMessage.includes("execution reverted:")) {
        // Extract everything after "execution reverted:"
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage
      };
    }
  }

  const ApprovePaymentForCrypto = async (token, amount) => {
    let tokenAddress;
    let tokenABI;

    if (token === "usdt") {
      tokenAddress = usdtAddress;
      tokenABI = usdtABI;
    }

    if (token === "shln") {
      tokenAddress = sahelionAddress;
      tokenABI = sahelionABI;
    }

    try {
      const web3Signer = await getWeb3Signer()
      const tokenContract = new ethers.Contract(tokenAddress, tokenABI, web3Signer)
      const tx = await tokenContract.approve(sahelionReserveAddress, ethers.utils.parseEther(amount.toString()))
      await tx.wait()
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Transaction failed:", error);

      // Handle user rejection
      if (error.code === "ACTION_REJECTED") {
        return {
          success: false,
          message: "Transaction rejected by user",
          error: "You rejected the transaction in your wallet.",
        };
      }

      // Fallback error message
      let detailedMessage = "An error occurred";

      // Handle execution reverted errors
      const rawMessage = error?.data?.message || error?.message;
      if (rawMessage && rawMessage.includes("execution reverted:")) {
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage,
      };

    }
  }

  const ApprovePaymentForLotteryTicket = async (amount) => {   

    try {
      const web3Signer = await getWeb3Signer()
      const tokenContract = new ethers.Contract(lotteryTokenAddress, lotteryTokenABI, web3Signer)
      const tx = await tokenContract.approve(lotteryAddress, ethers.utils.parseEther(amount.toString()))
      await tx.wait()
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Transaction failed:", error);

      // Handle user rejection
      if (error.code === "ACTION_REJECTED") {
        return {
          success: false,
          message: "Transaction rejected by user",
          error: "You rejected the transaction in your wallet.",
        };
      }

      // Fallback error message
      let detailedMessage = "An error occurred";

      // Handle execution reverted errors
      const rawMessage = error?.data?.message || error?.message;
      if (rawMessage && rawMessage.includes("execution reverted:")) {
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage,
      };

    }
  }

  const BuySHLNUsingCrypto = async (vid, amount) => {
    try {
      const web3Signer = await getWeb3Signer()
      const sahelionReserveContract = new ethers.Contract(sahelionReserveAddress, sahelionReserveABI, web3Signer)
      const tx = await sahelionReserveContract.buyWithCrypto(Number(vid), Number(amount))
      await tx.wait()
      getSHLNWalletBalance()
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Transaction failed:", error);

      // Fallback message
      let detailedMessage = "An error occurred";

      // Check for "execution reverted" in known places
      const rawMessage = error?.data?.message || error?.message;

      if (rawMessage && rawMessage.includes("execution reverted:")) {
        // Extract everything after "execution reverted:"
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage
      };
    }
  }

  const SellSHLNUsingCrypto = async (vid, amount) => {
    try {
      const web3Signer = await getWeb3Signer()
      const sahelionReserveContract = new ethers.Contract(sahelionReserveAddress, sahelionReserveABI, web3Signer)
      const tx = await sahelionReserveContract.sellWithCrypto(Number(vid), Number(amount))
      await tx.wait()
      getSHLNWalletBalance()
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Transaction failed:", error);

      // Fallback message
      let detailedMessage = "An error occurred";

      // Check for "execution reverted" in known places
      const rawMessage = error?.data?.message || error?.message;

      if (rawMessage && rawMessage.includes("execution reverted:")) {
        // Extract everything after "execution reverted:"
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage
      };
    }
  }

  const getTotalUSDTBacking = async () => {
    const environment = import.meta.env.VITE_ENVIRONMENT
    const urlRPC = environment === "Production" ? "https://bnb-mainnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT" : "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

    const provider = new ethers.providers.JsonRpcProvider(
      urlRPC
    );
    const sahelionReserveContract = new ethers.Contract(sahelionReserveAddress, sahelionReserveABI, provider)

    const usdtBacking = await sahelionReserveContract.getTotalCryptoAmount(0);
    return usdtBacking;
  }

  const getTotalSHLNSupply = async () => {
    const environment = import.meta.env.VITE_ENVIRONMENT
    const urlRPC = environment === "Production" ? "https://bnb-mainnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT" : "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

    const provider = new ethers.providers.JsonRpcProvider(
      urlRPC
    );
    const sahelionContract = new ethers.Contract(sahelionAddress, sahelionABI, provider)

    const rawSupply = await sahelionContract.totalSupply();
    const decimals = await sahelionContract.decimals();
    const formattedSupply = parseFloat(ethers.utils.formatUnits(rawSupply, decimals));

    return formattedSupply;
  }

  // lottery

  const getLotteryPlayerRewards = async () => {
    //get user rewards
    const signer = await getWeb3Signer()
    const contract = new ethers.Contract(lotteryAddress, lotteryABI, signer);
    const playerRewards = await contract.getPlayersRewards(currentUserWallet);
    console.log(playerRewards)

    return playerRewards
  }

  const getPlayerRewardsByRound = async (_roundNumber) => {
    const signer = await getWeb3Signer()
    const contract = new ethers.Contract(lotteryAddress, lotteryABI, signer);
    const playerRewards = await contract.getPlayersResultsByRound(_roundNumber);

    console.log(playerRewards)
    return playerRewards
  }

  // claim reward
  const claimReward = async (_ticketId) => {
    try {
      const web3Signer = await getWeb3Signer()
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, web3Signer)
      const tx = await contract.claimReward(_ticketId)
      await tx.wait()
      console.log("Reward claimed successfully:", tx);
      return {
        success: true,
        message: "Reward claimed successfully",
        transactionHash: tx.hash
      }
    } catch (error) {
      console.error("Claim reward failed:", error);
      return {
        success: false,
        message: "Claim reward failed",
        error: error.message || "An error occurred"
      };
    }
  }

  const buyLotteryTicket = async (_numbers) => {
    try {
      const amount = 1 * _numbers.length
      const result = await ApprovePaymentForLotteryTicket(Number(amount));
      if (result.success) {
        const web3Signer = await getWeb3Signer()
        const contract = new ethers.Contract(lotteryAddress, lotteryABI, web3Signer)
        const tx = await contract.buyTicket(lotteryTokenAddress, currentUserWallet, _numbers, ethers.utils.parseEther(amount.toString()))
        await tx.wait()
        console.log("Ticket purchased successfully:", tx);
        return {
          success: true,
          message: "Ticket purchased successfully",
          transactionHash: tx.hash
        }

      } else {
        console.log("Approval failed:", result.error);
        return {
          success: false,
          message: "Approval failed",
          error: result.error || "An error occurred during approval"
        };
      }

    } catch (error) {
      console.log(error)
      console.error("Purchase ticket failed:", error.reason.split(":")[1]);
      return {
        success: false,
        message: "Purchase ticket failed",
        error: error.reason.split(":")[1] || "An error occurred"
      };
    }
  }

  const getPlayerUpcomingTickets = async () => {
    try {
      console.log("Getting the player upcoming tickets")
      const web3Signer = await getWeb3Signer()
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, web3Signer)
      const upcomingTickets = await contract.getPlayerUpcomingTickets(currentUserWallet);
      return upcomingTickets;
    } catch (error) {
      console.error("Failed to fetch upcoming tickets:", error);
      return [];
    }
  }

  const getRecentWinningPlayers = async () => {
    try {
      const urlRPC = "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

      const provider = new ethers.providers.JsonRpcProvider(
        urlRPC
      );
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, provider)
      const recentPlayers = await contract.getRecentPlayers();
      return recentPlayers;
    } catch (error) {
      console.error("Failed to fetch recent players:", error);
      return [];
    }
  }

  const getCurrentPrizePool = async () => {
    try {
      const urlRPC = "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

    const provider = new ethers.providers.JsonRpcProvider(
      urlRPC
    );

      //const web3Signer = await getWeb3Signer()
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, provider)
      const currentPrizePool = await contract.getCurrentPrizePool();
      return ethers.utils.formatEther(currentPrizePool);
    } catch (error) {
      console.error("Failed to get current PrizePool:", error);
      return 0;
    }
  }

  const getNextDrawDate = async () => {
    try {
      const urlRPC = "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

      const provider = new ethers.providers.JsonRpcProvider(
        urlRPC
      );
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, provider)
      const nextDrawDate = await contract.getNextSunday();
      return new Date(nextDrawDate * 1000); // Convert from seconds to milliseconds
    } catch (error) {
      console.error("Failed to fetch next draw date:", error);
      return null;
    }
  }

  const checkLotteryPaused = async () => {
    try {
      const urlRPC = "https://bnb-testnet.g.alchemy.com/v2/udvK46uI_q8J4NnqXMWW-huOTZ5D1NDT"

      const provider = new ethers.providers.JsonRpcProvider(
        urlRPC
      );
      const contract = new ethers.Contract(lotteryAddress, lotteryABI, provider)
      const isPaused = await contract.lotteryPaused();
      return isPaused;
    } catch (error) {
      console.error("Failed to check lottery paused status:", error);
      return false;
    }
  }
  const transferSHLNToCommunityWalletAddressForContribution = async (communityWalletAddress, amount) => {
    try {
      const web3Signer = await getWeb3Signer()
      const shlnContract = new ethers.Contract(sahelionAddress, sahelionABI, web3Signer);
      const tx = await shlnContract.transfer(communityWalletAddress, ethers.utils.parseEther(amount.toString()));
      await tx.wait();
      return {
        success: true,
        message: "Transaction successful",
        transactionHash: tx.hash
      };
    } catch (error) {
      console.error("Transaction failed:", error);

      // Fallback message
      let detailedMessage = "An error occurred";

      // Check for "execution reverted" in known places
      const rawMessage = error?.data?.message || error?.message;

      if (rawMessage && rawMessage.includes("execution reverted:")) {
        // Extract everything after "execution reverted:"
        const match = rawMessage.match(/execution reverted: ([^"]+)/);
        if (match && match[1]) {
          detailedMessage = `execution reverted: ${match[1]}`;
        }
      } else if (error?.data?.message) {
        detailedMessage = error.data.message;
      }

      return {
        success: false,
        message: "Transaction failed",
        error: detailedMessage
      };
    }
  }

  const BuyTestUSDTForTesting = async () => {
    try {
      const web3Signer = await getWeb3Signer()
      const testUSDTContract = new ethers.Contract(usdtAddress, usdtABI, web3Signer);
      const tx = await testUSDTContract.mintToken();
      await tx.wait();
      toast.success("Test USDT successfully sent to your wallet!");
    } catch (error) {
      console.error("Failed to buy test USDT:", error);
      toast.error("Failed to buy test USDT. Please try again.");
    }
  }



  const authMethods = useMemo(
    () => ({
      isAuthenticated,
      setIsAuthenticated,
      ensureLogin,
      currentUser,
      setCurrentUser,
      getCurrentUser,
      clearLocalStorage,
      onLogout,
      PointsToSave,
      setPointsToSave,
      onSavePointsLocally,
      currentUserWallet,
      getWeb3Signer,
      connectWallet,
      disconnectWallet,
      bPnthrBalance,
      BuyTokenFromPresale,
      originPath,
      setOriginPath,
      getLoginOrigin,
      shlnBalance,
      ApprovePaymentForCrypto,
      BuySHLNUsingCrypto,
      SellSHLNUsingCrypto,
      usdEquivalent,
      shlnTousd,
      totalCryptoBacking,
      totalSHLNSupply,
      getLotteryPlayerRewards,
      getPlayerRewardsByRound,
      claimReward,
      buyLotteryTicket,
      getPlayerUpcomingTickets,
      getRecentWinningPlayers,
      transferSHLNToCommunityWalletAddressForContribution,
      getCurrentPrizePool,
      getNextDrawDate,
      checkLotteryPaused,
      BuyTestUSDTForTesting
    }),
    [
      isAuthenticated,
      setIsAuthenticated,
      ensureLogin,
      currentUser,
      setCurrentUser,
      getCurrentUser,
      clearLocalStorage,
      onLogout,
      PointsToSave,
      setPointsToSave,
      onSavePointsLocally,
      currentUserWallet,
      getWeb3Signer,
      connectWallet,
      disconnectWallet,
      bPnthrBalance,
      BuyTokenFromPresale,
      originPath,
      getLoginOrigin,
      shlnBalance,
      ApprovePaymentForCrypto,
      BuySHLNUsingCrypto,
      SellSHLNUsingCrypto,
      usdEquivalent,
      shlnTousd,
      totalCryptoBacking,
      totalSHLNSupply,
      getLotteryPlayerRewards,
      getPlayerRewardsByRound,
      claimReward,
      buyLotteryTicket,
      getPlayerUpcomingTickets,
      getRecentWinningPlayers,
      transferSHLNToCommunityWalletAddressForContribution,
      getCurrentPrizePool,
      getNextDrawDate,
      checkLotteryPaused,
      BuyTestUSDTForTesting
    ]
  );

  return { authMethods };
}

