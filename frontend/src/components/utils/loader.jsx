import React from 'react';

export const LoadingScreen = () => {
  return (
    // <div className="fixed inset-0 bg-BP-lightbaige flex flex-col items-center justify-center px-10 z-50 text-center">
    <div className="fixed inset-0 bg-BP-dark-grayish-blue flex flex-col items-center justify-center px-10 z-50 text-center">
      {/* Text with Water Fill Animation */}
      <div className="relative w-full flex items-center justify-center">
        {/* <h1 className="relative text-[#a6a6a6] text-5xl md:text-7xl lg:text-8xl font-bold tracking-wider"> */}
        <h1 className="relative text-[#3a3a3a] text-5xl md:text-7xl lg:text-8xl font-bold tracking-wider">
          BLACK PANTHER
        </h1>

        {/* Water fill overlay */}
        {/* <h1 className="absolute text-BP-black text-5xl md:text-7xl lg:text-8xl font-bold tracking-wider animate-waterFill"> */}
        <h1 className="absolute text-BP-opacited-white text-5xl md:text-7xl lg:text-8xl font-bold tracking-wider animate-waterFill">
          BLACK PANTHER
          <div
            className="absolute inset-0 top-auto h-[10px] bg-black animate-waterWave"
            style={{
              transform: 'translateY(2px)',
              clipPath: 'path("M0,10 C25,4 75,14 100,8 L100,10 L0,10 Z")',
            }}
          />
        </h1>
      </div>

      {/* Three Bars Loader */}
      <div className="mt-8 flex space-x-2">
        <div className="w-2 h-6 bg-BP-purple animate-barBounce1"></div>
        {/* <div className="w-2 h-6 bg-BP-dark-grayish-blue animate-barBounce2"></div> */}
        <div className="w-2 h-6 bg-BP-opacited-white animate-barBounce2"></div>
        <div className="w-2 h-6 bg-BP-gold animate-barBounce3"></div>
      </div>

      <style>
        {`
          /* Water Fill Animation */
          @keyframes waterFill {
            from { clip-path: inset(100% 0 0 0); }
            to { clip-path: inset(0 0 0 0); }
          }

          /* Water Wave Animation */
          @keyframes waterWave {
            0%, 100% {
              clip-path: path('M0,10 C25,4 75,14 100,8 L100,10 L0,10 Z');
            }
            50% {
              clip-path: path('M0,10 C25,14 75,4 100,10 L100,10 L0,10 Z');
            }
          }

          /* Bars Bounce Animations with opacity */
          @keyframes barBounce {
            0%, 100% {
              transform: scaleY(1);
              opacity: 0.5; /* lighter when small */
            }
            50% {
              transform: scaleY(1.8);
              opacity: 1;   /* fully visible when tall */
            }
          }

          .animate-waterFill {
            animation: waterFill 1.5s linear forwards;
          }
          .animate-waterWave {
            animation: waterWave 2s ease-in-out infinite;
          }

          .animate-barBounce1 {
            animation: barBounce 1.2s infinite ease-in-out;
          }
          .animate-barBounce2 {
            animation: barBounce 1.2s infinite ease-in-out;
            animation-delay: 0.2s;
          }
          .animate-barBounce3 {
            animation: barBounce 1.2s infinite ease-in-out;
            animation-delay: 0.4s;
          }
        `}
      </style>
    </div>
  );
};
