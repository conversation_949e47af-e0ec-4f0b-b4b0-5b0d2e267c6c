import React from 'react';
import { useEffect } from "react";
import HeaderNew from '../../components/Header/HeaderNew'
import handImg from '../../assets/web/handImg.png';
import './btn.css';
import { useNavigate } from 'react-router-dom';
import { useAuthentication } from '../utils/provider';

const Hero = () => {

  
    const { currentUser } = useAuthentication();
    const navigate = useNavigate();

  const handleBuyClick = () => {
    // if (currentUser) {
    //   //  if user is logged in , navigate to the presale page
    //   navigate('/pre-sale');
    // } else {
    //   // if user is not logged in , navigate to the page
    //   navigate('/presale-auth');
    // }
    navigate('/presale-auth');
    setMenuOpen(false); 
  }

    return (
        <div className="bg-BP-black w-full max-sm:h-[765px] h-[700px]">

            {/* nav bar */}
            <HeaderNew />

            <div className="w-full h-[85%] relative">
                {/* Yellow Blurred Circle */}
                <div
                    className="w-56 h-56 md:w-[400px] md:h-[400px] xl:w-[500px] xl:h-[500px] mx-auto rounded-full blur-3xl absolute right-[10%] top-[20%] sm:right-[20%] md:right-[25%] md:top-[5%] z-0 opacity-50"
                    style={{
                      background: "radial-gradient(circle, #E1A80D -50%, #111111 100%)",
                    }}
                />
                {/* purple Blurred Circle */}
                <div
                    className="w-56 h-56 md:w-56 md:h-5w-56 xl:w-72 xl:h-72 mx-auto rounded-full blur-3xl absolute right-[5%] top-[50%] md:right-[5%] md:top-[50%] z-0"
                    style={{
                        background: "radial-gradient(circle, #571C86 -50%, #111111 100%)",
                    }}
                />
                <div className="absolute inset-0 z-10">
                    <img src={handImg} alt="" className="max-sm:w-80 max-sm:h-80 w-[450px] lg:w-[500px] xl:w-[600px] h-[450px] lg:h-[500px] xl:h-[600px] max-sm:opacity-65 absolute max-sm:-bottom-8 -bottom-2 md:bottom-1.5 min-[920px]:-bottom-5 xl:-bottom-5 right-0 md:right-10 lg:right-28" />

                    <div className="absolute max-sm:top-0 top-12 max-sm:left-[5%] left-[10%] max-sm:space-y-5 space-y-10">

                        <p className="max-sm:text-[42px] text-5xl lg:text-6xl text-BP-opacited-white">Welcome to <br /> the <span className='font-bold text-white'>Black</span> <br /> <span className='font-bold text-white'>Panther</span> <br /> Cryptosphere</p>

                        <p className="max-sm:pr-10 sm:w-[45%] md:w-[45%] xl:text-xl">
                          Step into the Black Panther Cryptosphere & unleash the power of the Black Panther Coin ($BPNTHRQ). Here, the first global crypto lottery, community DAO, play-to-earn gaming, exclusive NFTs, and real-world utilities thrive in a decentralized community.
                        </p>
                        
                        <button
                          onClick={handleBuyClick}
                          className="max-sm:p-1 p-2 bg-BP-gray-50-opc rounded-lg"
                        >
                          <div className="bg-BP-purple hover:bg-BP-gold max-sm:text-xs max-sm:px-3 px-8 py-2 rounded-lg shadow-lg cursor-pointer text-white hover:text-white font-bold">
                            Buy $BPNTHRQ
                          </div>
                        </button>
                    </div>
                </div>
                

            </div>

        </div>
    );
};


const GlowEffectButton = () => {
  useEffect(() => {
    function setGlowEffectRx() {
      const glowEffects = document.querySelectorAll(".glow-effect");

      glowEffects.forEach((glowEffect) => {
        const glowLines = glowEffect.querySelectorAll("rect");
        const rx = getComputedStyle(glowEffect).borderRadius;

        glowLines.forEach((line) => {
          line.setAttribute("rx", rx);
        });
      });
    }

    setGlowEffectRx();
  }, []);

  return (
    <div onClick={()=>{console.log('ok')}} className="">
      <div
        className="button glow-effect relative inline-flex items-center justify-center px-6 py-3 text-lg border border-BP-gold text-BP-gold rounded-lg overflow-hidden transition-all duration-300 ease-out hover:cursor-pointer"
      >
        Play & Earn
        <svg className="glow-container absolute inset-0 w-full h-full pointer-events-none">
          <rect pathLength="100" strokeLinecap="round" className="glow-blur"></rect>
          <rect pathLength="100" strokeLinecap="round" className="glow-line"></rect>
        </svg>
      </div>
    </div>
  );
};

export default Hero;
