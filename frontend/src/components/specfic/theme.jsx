import React from 'react'
import { CenterImage, Charity, CharityWhite, Coin, Dao, FacePeople, Lottery, Network, Nft, OldManImage, PeopleSmile, Transaction, Win, themeSecCenter } from '../../assets'
import { FiArrowRight } from 'react-icons/fi'
import { Link } from 'react-router-dom'

export const Theme = () => {
  return (
    <div className='max-sm:p-[5%] p-[10%] space-y-10'>
        <p className="text-BP-black font-bold font-title text-[8vw] min-[900px]:text-[6vw]">More Than a Theme, <br /> It’s a Movement!</p>

        <div className="relative space-y-10">

          <div className="bg-BP-black rounded-3xl min-[900px]:w-[77%] xl:w-[72%]">

            <div className="bg-BP-gray-50-opc border-y-4 border-x-8 border-BP-black p-5 sm:p-10 xl:p-14 rounded-3xl space-y-5 xl:space-y-10">
              <div className="space-y-2">

                {/* 1st 3 */}
                <div className="flex justify-between space-x-2">

                  <div className="relative px-5 py-2 lg:py-5 w-full rounded-xl overflow-hidden ">
                    {/* Gradient Border Effect */}
                    <div className="absolute inset-0 rounded-xl border-[1.6px] border-transparent"
                      style={{
                        background: "linear-gradient(#11111199, #11111199) padding-box, linear-gradient(to bottom right, transparent 60%, #E1A80D 100%) border-box",
                        borderImageSlice: 1,
                      }}
                    ><div className="bg-BP-gray-50-opc h-full rounded-[10.5px]"></div></div>

                    {/* Inner Content */}
                    <div className="relative flex items-center justify-center w-full h-full">
                      <img src={Network} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                    </div>
                  </div>

                  <div className="relative px-5 py-2 lg:py-5 w-full rounded-xl overflow-hidden ">
                    {/* Gradient Border Effect */}
                    <div className="absolute inset-0 rounded-xl border-[1.6px] border-transparent"
                      style={{
                        background: "linear-gradient(#11111199, #11111199) padding-box, linear-gradient(to bottom, transparent 60%, #E1A80D 100%) border-box",
                        borderImageSlice: 1,
                      }}
                    ><div className="bg-BP-gray-50-opc h-full rounded-[10.5px]"></div></div>

                    {/* Inner Content */}
                    <div className="relative flex items-center justify-center w-full h-full">
                      <img src={Charity} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                    </div>
                  </div>

                  <div className="relative px-5 py-2 lg:py-5 w-full rounded-xl overflow-hidden ">
                    {/* Gradient Border Effect */}
                    <div className="absolute inset-0 rounded-xl border-[1.6px] border-transparent"
                      style={{
                        background: "linear-gradient(#11111199, #11111199) padding-box, linear-gradient(to bottom left, transparent 60%, #E1A80D 100%) border-box",
                        borderImageSlice: 1,
                      }}
                    ><div className="bg-BP-gray-50-opc h-full rounded-[10.5px]"></div></div>

                    {/* Inner Content */}
                    <div className="relative flex items-center justify-center w-full h-full">
                      <img src={Nft} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                    </div>
                  </div>

                </div>

                {/* 2nd 3 */}
                <div className="flex justify-between space-x-2">

                  <div className="relative px-5 py-2 lg:py-5 w-full rounded-xl overflow-hidden ">
                    {/* Gradient Border Effect */}
                    <div className="absolute inset-0 rounded-xl border-[1.6px] border-transparent"
                      style={{
                        background: "linear-gradient(#11111199, #11111199) padding-box, linear-gradient(to top right, transparent 60%, #E1A80D 100%) border-box",
                        borderImageSlice: 1,
                      }}
                    ><div className="bg-BP-gray-50-opc h-full rounded-[10.5px]"></div></div>

                    {/* Inner Content */}
                    <div className="relative flex items-center justify-center w-full h-full">
                      <img src={Lottery} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                    </div>
                  </div>

                  <div className="bg-gradient-to-b from-BP-gold-gradient-start to-BP-gold-gradient-end  border-[3px] border-BP-black px-5 py-2 lg:py-5 w-full rounded-xl ">
                    <img src={Dao} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                  </div>

                  <div className="relative px-5 py-2 lg:py-5 w-full rounded-xl overflow-hidden ">
                    {/* Gradient Border Effect */}
                    <div className="absolute inset-0 rounded-xl border-[1.6px] border-transparent"
                      style={{
                        background: "linear-gradient(#11111199, #11111199) padding-box, linear-gradient(to top left, transparent 60%, #E1A80D 100%) border-box",
                        borderImageSlice: 1,
                      }}
                    ><div className="bg-BP-gray-50-opc h-full rounded-[10.5px]"></div></div>

                    {/* Inner Content */}
                    <div className="relative flex items-center justify-center w-full h-full">
                      <img src={Win} alt="" className="w-8 h-8 sm:w-16 sm:h-16 lg:w-20 lg:h-20 mx-auto my-auto" />
                    </div>
                  </div>

                </div>

                
              </div>
              <p className="text-[10px] sm:text-sm md:text-base lg:text-lg xl:text-xl font-thin">The $BPNTHRQ cryptosphere is an ecosystem in the theme of the legend of the Black Panther Queen, Mangou of the Azna people, a Hausa subgroup in the Sahel Region of Present day Niger. The project features a global lottery, a Charity and an investment DAO, as well as other real world utilities including a play-to-earn game & NFTs, a Crypto Wallet & much more!</p>
            </div>

          </div>

          <div className="max-md:space-y-5 md:flex justify-between md:space-x-5 min-[900px]:w-[77%] xl:w-[72%]">

            <div className="bg-BP-black rounded-3xl space-y-5 p-7 min-[450px]:p-16 sm:p-12 min-[900px]:p-5 lg:p-12 w-full">
                <div className="relative bg-BP-gray-50-opc rounded-full p-10 border-t-2 border-BP-gold">
                  <img src={themeSecCenter} alt="" className="rounded-full aspect-square object-cover border-[25px] border-BP-gray-100-opc shadow-[0px_-35px_50px_-35px_#E1A80D]" />
                  <div className="absolute right-12 min-[530px]:right-16 md:right-[51px] min-[900px]:right-12 top-12 min-[530px]:top-16 md:top-[51px] min-[900px]:top-12 p-4 md:p-3 min-[900px]:p-2 min-[930px]:p-2.5 lg:p-3 xl:p-5 bg-BP-purple rounded-full w-fit h-fit">
                    <img src={CharityWhite} alt="" className='w-7 min-[490px]:w-9 sm:w-12 md:w-5 h-7 min-[490px]:h-9 sm:h-12 md:h-5 lg:w-6 min-[1175px]:w-10 xl:w-8 lg:h-6 min-[1175px]:h-10 xl:h-8' />
                  </div>
                  <img src={OldManImage} alt="" className='rounded-full aspect-square object-cover w-12 h-12 sm:w-16 md:w-10 sm:h-16 md:h-10 min-[1175px]:w-12 min-[1175px]:h-12 absolute right-5 md:right-7 top-32 min-[505px]:top-36 min-[550px]:top-44 md:top-24 min-[800px]:top-28 lg:top-28 min-[1175px]:top-32 xl:top-[176px] xl:right-5' />
                  <img src={PeopleSmile} alt="" className='rounded-full aspect-square object-cover w-20 min-[505px]:w-24 md:w-[72px] lg:w-20 xl:w-[90px] h-20 min-[505px]:h-24 md:h-[72px] lg:h-20 xl:h-[90px] absolute left-0 top-0 ' />
                  <img src={FacePeople} alt="" className='rounded-full aspect-square object-cover w-20 min-[505px]:w-24 md:w-[72px] lg:w-20 xl:w-[90px] h-20 min-[505px]:h-24 md:h-[72px] lg:h-20 xl:h-[90px] absolute left-5 bottom-0 ' />
                </div>
              <p className="text-BP-opacited-white font-light">
                Join a decentralized community where you can contribute to governance and charitable causes.
              </p>
            </div>

            <div className="space-y-10 w-full">

              <div className="relative -z-20 overflow-y-hidden bg-gradient-to-tr from-BP-black from-[45%] via-BP-gray-100-opc to-[#7e8282ee] rounded-3xl h-72 xl:h-80">
                      <div className="absolute bottom-0 bg-gradient-to-tr from-BP-black from-[45%] to-[#7e8282ee] w-40 h-40 xl:w-56 xl:h-56 rounded-bl-3xl rounded-tr-full -z-10"/>
                      <div className="flex space-x-5 p-5 h-full">
                        <div className="relative w-full">
                        <div className="relative w-24 bg-BP-gray-100-opc z-20 rounded-tr-3xl rounded-b-3xl h-[75%] mt-5 mx-auto pt-5">
                          <img src={Coin} alt="" className="w-12 h-12 mx-auto" />
                          <p className="px-3 text-xs xl:mt-5">Binance Smart Chain</p>
                          <div className="absolute -bottom-10 left-1 h-[88px] w-[88px] rounded-full border-4 border-BP-black">
                            <img src={Transaction} alt="" className=" bg-BP-gray-100-opc p-4 rounded-full border-4 border-BP-opacited w-20 h-20" />
                          </div>
                        </div>
                          
                        </div>
                        <p className="w-full h-fit my-auto text-lg">
                          Powered by Binance Smart Chain (BSC) for fast, low-cost transactions.
                        </p>
                      </div>
              </div>

              <Link to="/about" className="relative text-white hover:text-white hover:cursor-pointer bg-BP-purple rounded-full h-12 ml-5 w-[50vw] sm:w-[25vw] md:w-[60%] lg:w-[50%] flex justify-around">
                  <div className="absolute right-0 h-full w-full bg-BP-gold rounded-full flex px-5 justify-end items-center transition-all duration-1000 hover:w-12 hover:px-4">
                    <FiArrowRight className='font-bold' />
                    <p className="absolute my-auto text-nowrap right-[20vw] sm:right-[10vw] min-[900px]:right-[6vw] lg:right-[4.5vw]">Learn More</p>
                  </div>
              </Link>
            
            </div>
          </div>

          <div className='absolute right-0 lg:right-10 xl:right-20 top-0 flex space-x-7 lg:space-x-12 xl:space-x-16 w-fit'>
              <div className='max-[900px]:hidden min-h-[70vh] xl w-[0.5px] px-[1px] bg-gradient-to-t from-[#15072000] via-[#9756cc] to-[#15072000]' />
              <div className='max-[900px]:hidden min-h-[70vh] xl w-[0.5px] px-[1px] bg-gradient-to-t from-[#15072000] via-[#9756cc] to-[#15072000]' />
              <div className='max-[900px]:hidden min-h-[70vh] xl w-[0.5px] px-[1px] bg-gradient-to-t from-[#15072000] via-[#9756cc] to-[#15072000]' />
          </div>

        </div>

    </div>
  )
}
