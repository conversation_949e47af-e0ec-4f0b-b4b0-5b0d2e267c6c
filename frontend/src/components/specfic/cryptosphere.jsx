import React from 'react'
import { Expand, Launch, NewProduct, World } from '../../assets'
import auction from '../../assets/web/auction.png'
import lotterywhite from '../../assets/web/lotterywhite.png'
import money from '../../assets/web/money.png'
import blockChain from '../../assets/web/block-chain.png'
import curvedLines from '../../assets/images/curvedLinesLong.svg'

export const Cryptosphere = () => {

  const steps = [
    {
      number: 1,
      title: "Token Launch, Presale & Stablecoin Integration (Q2 2025)",
      description: "Launch the project token through a presale while integrating the Sahelion stablecoin",
      icon: NewProduct,
    },
    {
      number: 2,
      title: "Legal Incorporation (Q2/Q3 2025)",
      description: "Incorporate Black Panther in a jurisdiction that permits lottery operations, such as Gibraltar, to ensure regulatory compliance.",
      icon: auction,
    },
    {
      number: 3,
      title: "Panther Lottery & DAO Governance Launch (Q3 2025)",
      description: "Launch the Panther Lottery alongside the DAO governance system",
      icon: lotterywhite,
    },
    {
      number: 4,
      title: "Game Development & NFT Release (Q4 2025)",
      description: "Develop the game and release NFTs to enhance the ecosystem and user engagement.",
      icon: Launch,
    },
    {
      number: 5,
      title: "Fiat-to-Crypto Platform & Market Expansion (2026)",
      description: "Launch a fiat-to-crypto platform and expand marketing efforts across Africa and globally for wider adoption",
      icon: money,
    },
    {
      number: 6,
      title: "Black Panther Blockchain Development (2027 - 2030)",
      description: "Build and launch the Black Panther Blockchain to support scalability and long-term growth.",
      icon: blockChain,
    },
  ];

  return (
    <div className='max-sm:p-[5%] p-[10%] space-y-10'>
      <p className="text-BP-black font-bold font-title text-[8vw] min-[1000px]:text-[6vw]">Road to the Black <br /> Panther Cryptosphere</p>
      <div className=" border-b-2 border-BP-gray-20-opc">
        <table className="md:w-[80%] lg:w-[75%] text-BP-black mx-auto">
          <tbody>
            {steps.map((step, index) => (
              <tr key={index} className='mb-4'>
                <td className="w-[40%]">
                  <div className="relative bg-BP-opacited-white p-3 min-[850px]:p-5 rounded-2xl lg:rounded-3xl font-semibold shadow-2xl">
                    <p className="absolute -top-9 md:-top-11 lg:-top-16 xl:-top-[5vw] left-[35%] text-5xl md:text-[7vw] font-bold bg-gradient-to-b from-BP-gray-50-opc to-BP-gray-20-opc bg-clip-text text-transparent">
                      {step.number}
                    </p>
                    <p className='text-[2.5vw] md:text-[2vw] lg:text-[1.75vw] xl:text-[1.5vw]'>{step.title}</p>
                    <div className="absolute -right-4 -top-4 bg-BP-purple p-2 rounded-full w-fit h-fit">
                      <img src={step.icon} alt={step.title} className="w-5 h-5 min-[850px]:w-[4vw] min-[850px]:h-[4vw] lg:w-[3vw] lg:h-[3vw] xl:w-[2.5vw] xl:h-[2.5vw]" />
                    </div>
                  </div>
                </td>
                {index === 0 && (
                  <td rowSpan={steps.length} className="py-6 px-4">

                      <img src={curvedLines} alt="" className="w-[200vw] lg:w-[100vw] xl:w-[75vw]" />
                      

                  </td>
                )}
                <td className="text-[3vw] md:text-[2vw] lg:text-[1.75vw] pl-4 py-6">{step.description}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
