import React, {useState} from 'react'
import { BlockChain2, BlockImage, Lottery2, PlayStation } from '../../assets'
import { ImCross } from "react-icons/im";
import { FaRocket } from 'react-icons/fa';
import { motion } from 'framer-motion';

export const UnleashPower = () => {

    const [showComingSoon, setShowComingSoon] = useState(false);

    return (
        <div className='max-sm:p-[5%] p-[10%] space-y-10'>

            <p className="text-BP-black font-bold font-title text-[8vw] min-[900px]:text-[6vw]">Unleash the Power <br /> Within</p>

            <div className="bg-BP-black w-full rounded-3xl p-5 py-10 space-y-5 min-[900px]:space-y-0 min-[900px]:space-x-5 min-[900px]:flex">
                <div className="bg-BP-gray-50-opc w-full min-[900px]:h-[710px] xl:h-[835px] rounded-3xl lg:space-y-7">
                    <div className="p-3 py-5">
                        <div className="p-5 min-[1500px]:p-10 rounded-full border-t-2 w-fit mx-auto border-BP-nav-gray">
                            <div className="p-5 min-[1500px]:p-10 rounded-full border-t-2 w-fit border-BP-nav-gray">
                                <div className="p-4 min-[1500px]:p-10 bg-gradient-to-b from-BP-gray-100-opc from-[10%] via-[#42424005] to-[#42424005] border-t border-BP-nav-gray w-fit rounded-full">
                                    <div className="relative w-fit h-fit p-[1px] rounded-full">
                                        {/* Faded Gold Border */}
                                        <div className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-transparent via-BP-gold to-transparent " />
                                        <div className="absolute inset-0 rounded-full bg-gradient-to-b from-BP-gray-100-opc from-[10%] via-[#424240] to-[#424240] border-2 m-[1px] border-transparent" />

                                        <div className="absolute -top-[52px] left-12"> 
                                            <div className="bg-gradient-to-b from-BP-gray-100-opc from-10% to-BP-opacited-white w-[1px] h-12 ml-[3.5px]" />
                                            <div className="w-2 h-2 rounded-full bg-white" />
                                        </div>

                                        {/* Inner Content */}
                                        <img src={Lottery2} alt="" className="bg-BP-gray-100-opc rounded-full m-3 p-3 w-20 h-20 relative shadow-[0px_0px_60px_5px_#000000]" />

                                        <div className="absolute -bottom-[52px] left-12"> 
                                            <div className="w-2 h-2 rounded-full bg-white" />
                                            <div className="bg-gradient-to-t from-BP-gray-100-opc from-10% to-BP-opacited-white w-[1px] h-12 ml-[3.5px]" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div className="mx-auto shadow-[0px_-35px_50px_35px_#E1A80D60]  xl:shadow-[0px_-25px_50px_40px_#E1A80D60] w-0"></div>

                        <div className="border-t border-BP-gold-gradient-end py-2 xl:py-4 px-8  xl:px-12 rounded-2xl w-fit mx-auto">
                            <div className="border border-BP-gold-gradient-end py-2 xl:py-4 px-8 xl:px-12 rounded-full w-fit">
                                <div className="bg-BP-gold-gradient-start rounded-full h-2 w-12 xl:w-16 xl:h-3" />
                            </div>
                        </div>
                    </div>

                    <div className="w-full p-5 lg:px-10 space-y-5  text-[5vw] md:text-[2vw]">
                        <p className="font-title font-semibold">Enter to Win</p>
                        <p className="font-light">Win big in the global Black Panther Lottery!</p>
                        <div onClick={()=>{setShowComingSoon(true)}} className='w-fit hover:cursor-pointer font-light border border-BP-nav-gray rounded-full py-1.5 px-7 hover:bg-BP-nav-gray mx-auto text-nowrap'>Join the lottery</div>
                    </div>
                </div>
                <div className=" w-full h-[full] space-y-3">
                    <div className="bg-BP-gray-50-opc w-full rounded-3xl p-5 xl:p-10 space-y-5 text-[5vw] md:text-[2vw]">
                        <img src={BlockChain2} alt="" className="bg-BP-black w-12 h-12 rounded-lg p-1.5 border-2 border-BP-gray-100-opc" />
                        <p className="font-title font-semibold">DAO Governance</p>
                        <p className="font-light">Help steer the future of the ecosystem with your votes.</p>
                        <button onClick={()=>{setShowComingSoon(true)}} className='font-light border-t border-BP-gold rounded-full py-1.5 px-6 hover:bg-BP-gold shadow-xl shadow-BP-gray-50-opc'>DAO Governance</button>
                    </div>

                    <div className="bg-BP-gray-50-opc w-full rounded-3xl p-5 space-y-5 text-[5vw] md:text-[2vw]">
                        <img src={PlayStation} alt="" className="bg-BP-black w-12 h-12 rounded-lg p-1.5 border-2 border-BP-gray-100-opc" />
                        <p className="font-title font-semibold">Play & Earn</p>
                        <p className="font-light">Earn $BPNTHRQ tokens by playing the Black Panther game.</p>
                        <button onClick={()=>{setShowComingSoon(true)}} className='font-light border border-BP-nav-gray rounded-full py-1.5 px-10 hover:bg-BP-nav-gray'>Play & Earn</button>
                    </div>
                    
                </div>
                <div className="bg-BP-gray-50-opc w-full min-[900px]:h-[710px] xl:h-[835px] rounded-3xl lg:space-y-20">
                    <img src={BlockImage} alt="" className="rounded-3xl max-lg:py-5 w-[90%] lg:mt-5" />
                    <div className="p-10 md:p-5 lg:p-10 space-y-2 text-[5vw] md:text-[2vw]">
                        <p className="font-title font-semibold">NFT Integration</p>
                        <p className="font-light">Mint, collect and trade exclusive NFTs tied to your game progress.</p>
                    </div>
                </div>
            </div>

            {showComingSoon && (

                <div style={{ zIndex: 1000 }} className="h-[110vh] w-full bg-BP-black bg-opacity-50 fixed -top-10 left-0 flex justify-center items-center">
                    <div className="w-[90vw] max-w-[500px] h-72 -mt-16 rounded-xl bg-BP-gray-100-opc relative">

                        <ImCross onClick={() => { setShowComingSoon(false) }} className='absolute right-3 top-3 rounded-full hover:bg-gray-500 p-1.5 hover:cursor-pointer' size={28} />


                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                            className="flex flex-col justify-center items-center h-full text-center px-4"
                        >
                            <FaRocket
                                className="text-6xl mb-4 text-BP-hovered-gray animate-bounce drop-shadow-lg"
                            />

                            <h1 className="text-4xl font-bold mb-2 text-BP-lightbaige drop-shadow-sm">
                                Coming Soon
                            </h1>

                            <div className="h-1 w-24 rounded-full mb-4 bg-BP-opacited-white opacity-70" />

                            <p className="text-base text-BP-opacited-white max-w-md">
                                We're working on something exciting behind the scenes.<br />
                                Thanks for your patience — we’ll be live soon!
                            </p>
                        </motion.div>

                    </div>
                </div>

            )}

        </div>
    )
}
