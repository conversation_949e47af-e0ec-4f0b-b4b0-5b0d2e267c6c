.glow-effect {
    --glow-line-color: #E1A80D;
    --glow-line-thickness: 2px;
    --glow-line-length: 20px;
    --glow-blur-color: #fff385;
    --glow-blur-size: 8px; /* Keeps strong glow */
    --glow-offset: -3px; /* Adjusted to align with border */
    --animation-speed: 1200ms;
    --container-offset: 106px;
    position: relative;
}

.glow-container {
    pointer-events: none;
    position: absolute;
    inset: calc(var(--container-offset) / -2);
    width: calc(100% + var(--container-offset));
    height: calc(100% + var(--container-offset));
    opacity: 0; /* Initially hidden */
    transition: opacity 0.3s ease-in-out;
}

.glow-blur,
.glow-line {
    width: calc(100% - 6px);  /* Shrink by 6px (3px per side) */
    height: calc(100% - 6px); /* Shrink by 6px (3px per side) */
    x: 3px;  /* Shift right by 3px */
    y: 3px;  /* Shift down by 3px */
    fill: transparent;
    stroke: black;
    stroke-width: 5px;
    stroke-dasharray: var(--glow-line-length) calc(50px - var(--glow-line-length));
    stroke-dashoffset: 0;
}

/* 🔥 Animation works continuously & resets */
.glow-effect:is(:hover, :focus) .glow-container {
    opacity: 1;
    animation: glow-visibility 1.2s ease-in-out forwards;
}

.glow-effect:is(:hover, :focus) .glow-line {
    stroke: var(--glow-line-color);
    stroke-width: var(--glow-line-thickness);
    animation: glow-line-animation var(--animation-speed) linear infinite;
}

.glow-effect:is(:hover, :focus) .glow-blur {
    filter: blur(var(--glow-blur-size));
    stroke: var(--glow-blur-color);
    stroke-width: var(--glow-blur-size);
    animation: glow-line-animation var(--animation-speed) linear infinite;
}

/* 🔄 Ensures animation completes & resets */
@keyframes glow-visibility {
    0%, 100% {
        opacity: 0;
    }
    25%, 75% {
        opacity: 1;
    }
}

@keyframes glow-line-animation {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: -50px;
    }
}
