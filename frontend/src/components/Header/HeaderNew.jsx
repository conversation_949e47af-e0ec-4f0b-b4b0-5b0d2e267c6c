import  { useState, useEffect } from 'react';
import  WebLogo  from '../../assets/images/queenpanther2.png';
import { FiMenu, FiX } from 'react-icons/fi';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthentication } from '../utils/provider';
import { AppRoutesPaths } from '../../route/app_route';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTelegram,
  faXTwitter,
  faDiscord,
  faTiktok,
} from "@fortawesome/free-brands-svg-icons";



// Utility function to check domain and env
// function checks if the current website's domain ends with .org and returns true if it does, otherwise it returns false.
// const isOrgDomain = () => {
//   if (typeof window !== "undefined"){
//     return window.location.hostname.endsWith('.org')
//   }
//   return false;
// }

// const getNavLinks = () => { 
//   const env = import.meta.env.VITE_ENVIRONMENT; // dev or pro
//   // note in dev will show all pages for easier development
//   if (env === 'dev'){
//     return ['Home', 'About', 'DAO', 'Lottery', 'Game', 'Sahelion', 'Presale'];
//   }
//   // in prod restrict .org to DAO pages only
//   if (isOrgDomain()){
//     return ['Home', 'About', 'DAO']
//   }
//   // .com in prod gets presale and the rest 
//   return ['Home', 'About', 'Presale'];
// }



//Returns navigation links based on environment and feature flags
const getNavLinks = () => {
  // Get environment (dev or prod)
  const env = import.meta.env.VITE_ENVIRONMENT;
  // Check if DAO feature is enabled via env variable
  const daoFeature = import.meta.env.VITE_FEATURE_DAO === 'dao';
  // Check if current domain ends with .org
  const isOrg = typeof window !== "undefined" && window.location.hostname.endsWith('.org');

  // In development, show all links, include DAO only if enabled
  if (env === 'dev') {
    return ['Home', 'About', ...(daoFeature ? ['DAO'] : []), 'Lottery', 'Game', 'Sahelion', 'BPNTHRQ'];
  }
  // On .org domains in production, show only Home, About, and DAO if enabled
  if (isOrg && daoFeature) {
    return ['Home', 'About', 'DAO'];
  }
  // On other domains in production, show Home, About, and Presale
  return ['Home', 'About', 'Sahelion', 'BPNTHRQ', 'Game'];
};

const HeaderNew = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [activeNav, setActiveNav] = useState("Home");
  const { currentUser } = useAuthentication();
  const navigate = useNavigate();
  const  navLinks = getNavLinks(); // Get navigation links based on environment

  useEffect(() => {
    const currentPath = window.location.pathname;
    let activeLink;
  
    if (currentPath === '/') {
      activeLink = 'Home';
    } else if (currentPath === '/presale-auth') {
      activeLink = 'BPNTHRQ';
    } else if (currentPath === '/dashboard-sahelion/portfolio') {
      activeLink = 'Sahelion';
    } 
    else {
      activeLink = currentPath.replace('/', '').charAt(0).toUpperCase() + currentPath.slice(2);
    }
  
    setActiveNav(activeLink);
  }, []);
  

  const toggleMenu = () => setMenuOpen(!menuOpen);

  const handleBuyClick = () => {
    // if (currentUser) {
    //   //  if user is logged in , navigate to the presale page
    //   navigate('/pre-sale');
    // } else {
    //   // if user is not logged in , navigate to the page
    //   navigate('/presale-auth');
    // }
    navigate('/presale-auth');
    setMenuOpen(false); 
  }

  // const handlePresaleClick = () => {
  //   if (currentUser) {
  //     navigate(AppRoutesPaths.presale); // Navigate to presale page if logged in
  //   } else {
  //     navigate(AppRoutesPaths.presaleAuth); // Navigate to presale-auth page if not logged in
  //   }
  //   setMenuOpen(false);
  // };



  

  return (
    <div className='w-full z-50 font-title'>
      <nav className="w-full flex justify-between px-6 md:px-10 items-center py-4">
        {/* Logo Section */}
        <div className="flex items-center space-x-2">
          <img onClick={() => navigate(AppRoutesPaths.home)} src={WebLogo} alt="Black Panther Token" className="h-12 cursor-pointer" />
          <div className="font-title">
            <p onClick={() => navigate(AppRoutesPaths.home)} className="text-white font-semibold hover:cursor-pointer text-lg">Black Panther</p>
            <p onClick={() => navigate(AppRoutesPaths.home)} className='text-BP-nav-gray hover:cursor-pointer'>Token</p>
          </div>
        </div>

        {/* Mobile Menu Icon */}
        <div className="md:hidden cursor-pointer" onClick={toggleMenu}>
          {menuOpen ? <FiX size={30} className="text-white" /> : <FiMenu size={30} className="text-white" />}
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-6 text-white font-medium">
          {/* {['Home', 'About', 'DAO', 'Lottery', 'Game', 'Sahelion', 'Presale'].map((item) => ( */}
          {navLinks.map((item) => (
            <Link
              key={item}
              to={item === 'BPNTHRQ' ? "#" : item === "Home" ? "/" : `/${item.toLowerCase()}`}
              className={`hover:text-purple-400 transition duration-300 ${activeNav.toLowerCase() === item.toLowerCase() ? 'border-b-2 border-purple-500' : ''
                }`}
              onClick={(e) => {
                if (item === 'BPNTHRQ'){
                  e.preventDefault();
                  setActiveNav(item);
                  handleBuyClick();
                } else {
                  setActiveNav(item)}
                }

              }
            >
              {item}
            </Link>

          ))}
        </div>

        {/* Buy Button */}

          <button
            onClick={handleBuyClick}
            className="hidden md:block p-2 bg-BP-gray-50-opc rounded-full"
          >
            <div className="bg-BP-purple hover:bg-BP-gold px-8 py-2 rounded-full shadow-lg cursor-pointer text-white hover:text-white font-bold  hover:outline outline-1 outline-offset-8 outline-white">
              Buy $BPNTHRQ
            </div>
          </button>

      </nav>

      <style>{`
        @keyframes slide-fade-in {
          0% {
            opacity: 0;
            transform: translateY(-10%) scale(0.95);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        .animate-slide-fade-in {
          animation: slide-fade-in 0.4s ease-out forwards;
        }
      `}</style>


      {/* Mobile Navigation */}

      {menuOpen && (
        <div className="absolute top-20 inset-x-0 z-40">
          <div className=" relative rounded-b-[2rem] bg-gradient-to-br from-black/60 via-black/50 to-black/60 border border-white/10 p-6 backdrop-blur-xl shadow-[0_20px_40px_rgba(0,0,0,0.6)] overflow-hidden animate-fade-in-slide">

            {/* Glow Corners */}
            <div className="absolute top-0 left-0 w-12 h-12 bg-purple-500 rounded-full blur-xl opacity-30 animate-ping-slow" />
            <div className="absolute bottom-0 right-0 w-16 h-16 bg-yellow-400 rounded-full blur-2xl opacity-20 animate-pulse" />

            {/* Inner Shine Layer */}
            <div className="absolute inset-0 rounded-b-[2rem] pointer-events-none border border-white/10">
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-b-[2rem] opacity-10 mix-blend-overlay animate-shimmer" />
            </div>

            {/* Animated Background Spiral */}
            <div className="absolute inset-0 z-0 pointer-events-none opacity-20">
              <div className="absolute w-80 h-80 bg-gradient-to-tr from-purple-500 via-yellow-400 to-pink-500 rounded-full blur-3xl animate-spin-slow -top-1/2 -left-1/2" />
              <div className="absolute w-60 h-60 bg-gradient-to-br from-yellow-400 to-purple-600 rounded-full blur-2xl animate-spin-reverse-slower top-1/3 left-1/2" />
            </div>

            {/* Nav Links */}
            <div className="flex flex-col items-center space-y-4 pb-6 border-b border-white/10 relative z-10">
              {/* {['Home', 'About', 'DAO', 'Lottery', 'Game', 'Sahelion', 'Presale'].map((item, index) => ( */}
              {navLinks.map((item, index) => (
                <Link
                  key={item}
                 to={item === 'BPNTHRQ' ? '#' : item === "Home" ? "/" : `/${item.toLowerCase()}`}
                  className={`
                  w-full text-center text-white text-lg font-semibold py-2 px-4 rounded-xl
                  transition-all duration-300 ease-in-out transform hover:scale-105 hover:rotate-1 hover:bg-purple-600/30 hover:text-yellow-300
                  ${activeNav.toLowerCase() === item.toLowerCase() ? 'bg-purple-700/30 text-yellow-300' : ''}
                  animate-spiral-in
                `}
                  style={{ animationDelay: `${index * 120}ms` }}
                  onClick={(e) => {
                    if (item === 'BPNTHRQ') {
                    e.preventDefault();
                      handleBuyClick();
                      setActiveNav(item);
                    } else {
                      setActiveNav(item)
                    }

                  }
                }
                >
                  {item}
                </Link>
              ))}
            </div>

            {/* Buy Button */}
            <div className="py-6 flex justify-center relative z-10">
              <button
                onClick={handleBuyClick}
                className="w-full bg-gradient-to-r from-BP-purple via-BP-black to-BP-purple text-white font-bold py-3 px-6 rounded-full
                    shadow-xl transition-transform duration-300 transform hover:scale-105
                    hover:shadow-[0_0_20px_#facc15] hover:ring-2 hover:ring-BP-opacited-white hover:ring-offset-2 backdrop-blur-md"
              >
                Buy $BPNTHRQ
              </button>
            </div>

            {/* Social Icons */}
            <div className="flex justify-center items-center space-x-6 border-t border-white/10 pt-6 relative z-10">
              {[faTelegram, faXTwitter, faDiscord, faTiktok, ].map((icon, index) => (
                <a
                  key={index}
                  href={
                    icon === faTelegram ? "https://t.me/blackpanthertkn" :
                      icon === faXTwitter ? "https://x.com/bpnthrx" :
                        icon === faDiscord ? "https://discord.com/invite/cQHYs5mUwJ" :
                          "https://www.tiktok.com/@blackpanthertkn"
                  }
                  target="_blank"
                  className="text-yellow-400 hover:text-white transition-transform transform hover:scale-125 animate-pulse-fast"
                >
                  <FontAwesomeIcon icon={icon} size="lg" />
                </a>
              ))}
            </div>
          </div>

          {/* Spiral / Animations */}
          <style>
            {`
              @keyframes spiral-in {
                0% {
                  opacity: 0;
                  transform: scale(0.5) rotate(-10deg) translateY(20px);
                }
                100% {
                  opacity: 1;
                  transform: scale(1) rotate(0deg) translateY(0);
                }
              }
              .animate-spiral-in {
                animation: spiral-in 0.6s ease-out forwards;
              }

              @keyframes spin-slow {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
              .animate-spin-slow {
                animation: spin-slow 20s linear infinite;
              }

              @keyframes spin-reverse-slower {
                0% { transform: rotate(360deg); }
                100% { transform: rotate(0deg); }
              }
              .animate-spin-reverse-slower {
                animation: spin-reverse-slower 30s linear infinite;
              }

              @keyframes fade-in-slide {
                0% {
                  opacity: 0;
                  transform: translateY(20px);
                }
                100% {
                  opacity: 1;
                  transform: translateY(0);
                }
              }
              .animate-fade-in-slide {
                animation: fade-in-slide 0.4s ease-out forwards;
              }

              @keyframes pulse-fast {
                0%, 100% {
                  transform: scale(1);
                }
                50% {
                  transform: scale(1.1);
                }
              }
              .animate-pulse-fast {
                animation: pulse-fast 2s infinite;
              }
            `}
          </style>
          <style>
            {`
              @keyframes shimmer {
                0% {
                  transform: translateX(-100%);
                }
                100% {
                  transform: translateX(100%);
                }
              }
              .animate-shimmer::before {
                content: '';
                position: absolute;
                top: 0;
                left: -50%;
                width: 200%;
                height: 100%;
                background: linear-gradient(
                  120deg,
                  transparent,
                  rgba(255, 255, 255, 0.2),
                  transparent
                );
                animation: shimmer 4s infinite linear;
              }

              @keyframes ping-slow {
                0% {
                  transform: scale(1);
                  opacity: 0.4;
                }
                50% {
                  transform: scale(1.2);
                  opacity: 0.1;
                }
                100% {
                  transform: scale(1);
                  opacity: 0.4;
                }
              }
              .animate-ping-slow {
                animation: ping-slow 6s infinite ease-in-out;
              }
            `}
          </style>

        </div>
      )}




    </div>
  );
};

export default HeaderNew;
