import axios from "axios";
import AuthInterceptor from "../interceptors/auth-interceptor";
import ErrorInterceptor from "../interceptors/error-interceptor";
import CaptchaInterceptor from "../interceptors/captcha-interceptor";

const httpClient = axios.create({
    baseURL: import.meta.env.VITE_API_URL 
});

AuthInterceptor(httpClient);
CaptchaInterceptor(httpClient);
ErrorInterceptor(httpClient);

export default httpClient;

