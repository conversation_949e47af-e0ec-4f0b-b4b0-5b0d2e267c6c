import React from "react";
import { BrowserRouter } from "react-router-dom";
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { AppRoute } from "./route/app_route";
import 'react-toastify/dist/ReactToastify.css';
import { LoginProvider } from "./components/utils/provider";
import { WagmiWrapper } from "./web3Provider";
import { AutoLogout } from "./components/auth/autoLogout";
import GlobalCaptcha from "./components/Captcha/GlobalCaptcha";

function App() {
  const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
  const isCaptchaEnabled = import.meta.env.VITE_CAPTCHA_ENABLED === 'true';

  return (
    <WagmiWrapper>
      <LoginProvider>
        {isCaptchaEnabled && recaptchaSiteKey ? (
          <GoogleReCaptchaProvider reCaptchaKey={recaptchaSiteKey}>
            <BrowserRouter>
              <AppRoute />
              <GlobalCaptcha />
            </BrowserRouter>
          </GoogleReCaptchaProvider>
        ) : (
          <BrowserRouter>
            <AppRoute />
          </BrowserRouter>
        )}
        {/* I have added auto logout */}
        <AutoLogout />
      </LoginProvider>
    </WagmiWrapper>
  );
}

export default App;