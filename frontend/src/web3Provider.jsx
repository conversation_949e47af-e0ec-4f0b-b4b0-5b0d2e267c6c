
import { createWeb3Modal } from "@web3modal/wagmi/react";
import { defaultWagmiConfig } from "@web3modal/wagmi/react/config";

import { WagmiProvider } from "wagmi";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { bsc, bscTestnet  } from "wagmi/chains"
import React from 'react'

// 0. Setup queryClient
const queryClient = new QueryClient()

// 1. Get projectId from https://cloud.reown.com
const projectId = '45ff7348e614a721653a8d6d577b43da'

// 2. Create a metadata object - optional
const metadata = {
  name: 'Blank Panther',
  description: 'Connect wallet for black panther',
  url: 'https://blackpanthertkn.com/', // origin must match your domain & subdomain
  icons: []
}

// 4. Create config
const config = defaultWagmiConfig({
    chains: [bsc, bscTestnet ], // required
    projectId, // required
    metadata, // required
  });

// 5. Create modal
createWeb3Modal({
    defaultChain: undefined,
    wagmiConfig: config,
    projectId,
    enableAnalytics: true, // Optional - defaults to your Cloud configuration
});

  export function WagmiWrapper({ children }) {
    return (
      <WagmiProvider config={config} reconnectOnMount={true}>
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      </WagmiProvider>
    );
}
    