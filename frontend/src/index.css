@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

/* Custom white scrollbar styles */
.white-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.white-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.white-scrollbar::-webkit-scrollbar-thumb {
  background-color: #64748b;
  border-radius: 4px;
}

/* Firefox support */
.white-scrollbar {
  scrollbar-color: #64748b transparent;
  scrollbar-width: thin;
}
