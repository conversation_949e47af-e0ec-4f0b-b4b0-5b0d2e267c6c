Stack trace:
Frame         Function      Args
0007FFFFABA0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFABA0, 0007FFFF9AA0) msys-2.0.dll+0x1FE8E
0007FFFFABA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE78) msys-2.0.dll+0x67F9
0007FFFFABA0  000210046832 (000210286019, 0007FFFFAA58, 0007FFFFABA0, 000000000000) msys-2.0.dll+0x6832
0007FFFFABA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABA0  000210068E24 (0007FFFFABB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE80  00021006A225 (0007FFFFABB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCA1540000 ntdll.dll
7FFCA05E0000 KERNEL32.DLL
7FFC9E880000 KERNELBASE.dll
7FFCA0E50000 USER32.dll
7FFC9F2B0000 win32u.dll
7FFCA1350000 GDI32.dll
7FFC9EE80000 gdi32full.dll
7FFC9F200000 msvcp_win.dll
7FFC9E730000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA06B0000 advapi32.dll
7FFCA1380000 msvcrt.dll
7FFCA02F0000 sechost.dll
7FFC9F330000 RPCRT4.dll
7FFC9DE40000 CRYPTBASE.DLL
7FFC9E690000 bcryptPrimitives.dll
7FFCA0230000 IMM32.DLL
